# Atlas
## Suggested Development Setup
1. Start Application using the `local` Spring Profile
2. Run the docker container `docker-compose up`
3. If this is the first time you are running the app or someone added a package, you will have to run `pnpm install`
4. Start webpack-dev-server with the command `pnpm watch`
5. Open browser to http://localhost:3001 \*
6. Begin development!  HMR (Hot Module Replacement) is enabled, so as you edit SCSS files and/or Vue files, you should see the changes WITHOUT refreshing.

>\*Entrypoints are auto-generated in the target/templates directory when `npm run watch` is run. 
If you get an error indicating that a template (such as login.html) does not exist, try re-running 
`npm run watch` after you are sure the target directory has been created.

### Typescript Support

Support was added using the Vue/CLI
```shell
vue add typescript
```
Both Store.js and Store.ts file types are supported. If you're planning on using *Typescript*
in a component ensure you set the script tag lang property to *ts*
```typescript
<script lang="ts">
    ...
```
files ending with *.js do not compile *typescript* to Javascript. Do not use *typescript* inside *.js files.

(**WARNING** Class-based components **NOT** supported.)

```typescript
// SomeComponent.vue
<template>
    ...
</template>
    
<script lang="ts">
import Vue, { Proptype } from "vue";    
import ...

interface Book {
    title: string,
    isbn: number    
}

export default Vue.extend({
    name: "SomeComponent",
    ...
});

<style lang="scss">
    ...
</style>
```
#### Configs

* Use class-style component syntax? ❌No
* Use Babel alongside TypeScript (required for modern mode, auto-detected plyfills, transiling JSX)? Yes
* Convert all .js files to .ts? ❌No
* Allow .js files to be compiled? ❌No
* Skip type checking of all declaration files (recommended for apps)? Yes
