version: '2'

services:
  cache:
    image: redis:5.0.5
    ports:
      - 6379:6379
  edge-api:
    depends_on:
      - cache
    image: crsvr/provisions-atlas-app:latest
    ports:
      - 3001:3001
    environment:
      - SPRING_PROFILES_ACTIVE=local,provisions
      - SPRING_REDIS_HOST=cache
      - DD_TRACE_ANALYTICS_ENABLED=false
    extra_hosts:
      - "api.internal.carsaver.com:*************"
