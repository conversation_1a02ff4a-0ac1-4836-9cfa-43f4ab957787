app:
  secrets: services/atlas-app/beta

vehicle-searches:
  table-name: ga4-eecu.analytics_433663821.events_*

client:
  api-host: https://api-beta.carsaver.com

  auth:
    host: ${client.api-host}/uaa/oauth
    auth-username: WEB
    auth-password: 9pA_G7soi9
    token-username: <EMAIL>
    token-password: AahdCzESGsDMDA52

  oauth:
    configurations:
      carsaver:
        url: ${client.api-host}/uaa/oauth/token
        grant-type: PASSWORD
        auth-username: WEB
        auth-password: 9pA_G7soi9
        token-username: <EMAIL>
        token-password: AahdCzESGsDMDA52

application:
  domain: https://atlas.carsaver.com
  loginPage: /login
server:
  servlet:
    session:
      timeout: 4H
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024
  forward-headers-strategy: native
  shutdown: graceful
warranty-service:
  nwan-client:
    rootUri: https://carsaver.naenwan.com/eBusinessApi
    username: PCS
    password: H8W76fgegWdXVJHvnpWxfGFvkUq7yJ
  providerCode: PCS
  e-remittance-portal:
    url: https://carsaver.naenwan.com/Portals/eBusiness/eRemittancePortal.aspx?oauth_token=
    role: f5b2da92-1811-4b0a-9f82-5e0b507b5077
  supply-order-portal:
    url: https://carsaver.naenwan.com/Portals/Organizations/OrderSupplies.aspx?oauth_token=
    role: 5d0b33ad-6a7b-4943-8331-15be54d61a7a
twilio:
  accountId: **********************************
  authToken: 8cf701badf19f0aed13451a3fc92afdb
  number: +***********
#todo - consider moving into spring.security.oauth2.* namespace
portal:
  authorizationUri: https://portal.carsaver.com/oauth2/authorize
  atlasRedirectUri: ${application.domain}/login/oauth2/code/portal
  clientId: portal
carsaver:
  dealJacketServiceEnabled: true
  paymentService:
    enabled: true
  cloud:
    kinesis:
      api-log-stream: api-log-stream-beta
quotes:
  lease:
    defaultDownPaymentPercent: 10
  finance:
    defaultDownPaymentPercent: 20
offerlogix:
  client:
    timeout: 30
    url: https://www.leaseasp.com
  defaultBeaconScore: 750
features-toggle:
  pre-approval-enable: true
  accessories-enable: true
  ariya-reservation-enable: true
  routeone-prefix-enable: true
  route-one-finance-and-insurance-enable: true
  buy-at-home-program-enable: true
  protection-products-enabled: true
  chat-enable: true
  liberty-mutual-enable: false
nissan-client:
  apigee:
    baseUri: https://api.na.nissancloud.com
    token-api: #used by payoff,incentives,etc., for oauth2 client_credentials grant types
      rootUri: ${nissan-client.apigee.baseUri}/identity/v1
      username: 0ooMGFrIHNh1KwVtOnQA9IWXwtgvjSko
      password: oFFyHtEinOiY8uke
    dealer-inventory:
      rootUri: https://api.na.nissancloud.com
  accessories:
    rootUri: https://api.na.nissancloud.com
finance-deep-link-program-exclusions: >
  d3a45b49-4387-4221-9055-24db52bf2368,
  b257486d-9b9a-46f6-85fd-601c452f7983,
  37ec98e2-c9e3-4a62-8e96-c883c3b91bed,
  eaed72e0-77a8-11ee-b962-0242ac120002,
  1e01da17-8c6b-40f8-bc1b-44093e49866d
splits:
  key: pbda4t4qifvfrk3j94f8fhg7i1n9lbefq3e0
  defaults:
    preQualifiedCtaFeature: off
    dealEditingCash: off
    offerAdjustmentFeature: off
    tradeInAdjustmentMultipleDealersFeature: off
    sellAtHomeFeature: off
    atlasDisplayOwnedProspectForDealerUsers: off
    NewConfigManager: off
    Announcekit-atlas: off
    AtlasEnhancedNoGroupFilters: off
    AtlasRangeSliderFilters: off
    AtlasSecondPhaseFilters: off
    nesnaFAndIFeature: off
    AdaptiveRetailPlayground: off
insurance-service:
  insurance-host: https://api-beta.carsaver.com/insurance
  route-one-uri: /api/routeone/deal-jacket
  route-one-secret-key-name: local/protection-products/api-keys
  insurance-callback-url: http://localhost-nissan:4000/routeone/thank-you.html
  default-route-one-dealer-id: FH1FG
  route-one-user-id: BHWANG
  route-one-hmac_id: F00CSM
service-api-uri: https://api-beta.carsaver.com
dynamoDB:
  dealerTable: upgrade_prospect_etl_dealer_staging
  session-heartbeat-table: digital-retail-session-heartbeat-staging
  prospect-leads-current-table: prospect-leads-current-beta

spring:
  redis:
    host: atlas-sessions.3f0onc.ng.0001.use1.cache.amazonaws.com
  session:
    store-type: redis
  application:
    name: atlas-app
  sendgrid:
    api-key: *********************************************************************
  jackson:
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: false
    deserialization:
      READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE: true
      ACCEPT_SINGLE_VALUE_AS_ARRAY: true
  main:
    allow-bean-definition-overriding: true
  resources:
    chain:
      strategy:
        content:
          enabled: true
          paths: /**
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${magellen-service-api-uri}/uaa/.well-known/jwks.json
      client:
        registration:
          carsaver:
            authorization-grant-type: password
            client-id: PORTAL
            client-secret: -Ku2Q_FCN2
        provider:
          carsaver:
            token-uri: ${magellen-service-api-uri}/uaa/oauth/token
            authorization-uri: ${magellen-service-api-uri}/uaa/oauth/authorize
            jwk-set-uri: ${magellen-service-api-uri}/uaa/.well-known/jwks.json
  thymeleaf:
    mode: HTML
  cloud:
    config:
      allow-override: true
      # override-system-properties: false
      override-none: true
  boot:
    admin:
      client:
        enabled: false
  jpa:
    database: postgresql
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQL95Dialect

elasticsearch:
  vega:
    version: 7.10.2
    scheme: https
    host: 33075f4b010942b582f4c267f20f1656.us-east-1.aws.found.io
    port: 9243
    username: elastic
    password: LiAAdKzdYN4CYphNJSsYyZwA
  gibson:
    version: 7.10.2
    scheme: https
    host: 33075f4b010942b582f4c267f20f1656.us-east-1.aws.found.io
    port: 9243
    username: elastic
    password: LiAAdKzdYN4CYphNJSsYyZwA
  inventory:
    version: 7.10.2
    scheme: https
    host: 33075f4b010942b582f4c267f20f1656.us-east-1.aws.found.io
    port: 9243
    username: elastic
    password: LiAAdKzdYN4CYphNJSsYyZwA
  nova:
    version: 7.10.2
    scheme: https
    host: 33075f4b010942b582f4c267f20f1656.us-east-1.aws.found.io
    port: 9243
    username: elastic
    password: LiAAdKzdYN4CYphNJSsYyZwA
  chrome:
    version: 7.10.2
    scheme: https
    port: 9243
    host: 33075f4b010942b582f4c267f20f1656.us-east-1.aws.found.io
    username: elastic
    password: Gw4NXSlVHHmuBui7kXPP06Bs
token-service:
  username: <EMAIL>
  password: AahdCzESGsDMDA52
  connection: postgres-staging
magellen-service-api-uri: https://api-beta.carsaver.com
management:
  metrics:
    export:
      datadog:
        enabled: false
tinyurl-client:
  api-uri: https://beta.csvr.co
quartz:
  enabled: false
logging:
  level:
    com.carsaver.magellan.client: DEBUG
    com.amazonaws.util.EC2MetadataUtils: error
cloud:
  aws:
    region:
      static: us-east-1
    stack:
      auto: false
sns:
  topic: platform-events-beta
  defaultSentById: 0bcbc757-38c8-4c42-8311-4ef2db2ed217
tenant:
  carsaver-id: 48a2b25e-1052-4ce0-889e-91a675c1a023
  default-id: 48a2b25e-1052-4ce0-889e-91a675c1a023
  express-id: bd2e3cae-cec1-48db-af20-c09bdc2f033e
  portal-id: ab1d6e3f-a0ee-4c46-a306-648f2c8d47f6
  nissan-id: c1f63a28-2967-473b-b452-3aef337046bd

program:
  carsaver-default: 6e05cc8e-6a1d-4cb0-a473-1e87b3689c3f
  nissan-buy-at-home: 5e922fe4-e1e9-468c-b100-5b8f7cffcef3
  express: d477f5f1-6f12-4a93-b9ba-e36cd52facc0
  carsaver: bff9e9b9-96b9-4aaa-8a05-be41ad422bee
  nissan-buy-at-home-id: 5e922fe4-e1e9-468c-b100-5b8f7cffcef3
  walmart-id: f4f8d14f-b0d1-404a-90f4-3f40d4ba9940
deal-desking:
  inventory-service:
    api-uri: https://api-beta.carsaver.com/inventory
features-subscription:
  rootUri: https://api-beta.carsaver.com/meridian/features-subscription
  liberty-mutual-feature-id: 023f5087-01bb-4755-961d-89b0ea8e15c5
  sell-at-home-feature-id: 11bcd5c0-2292-4089-9265-e95d1852bfd4
  nesna-f-and-i-feature-id: 76c3c23b-2b1e-4822-bd26-9ef735820641
  garage-alerts-feature-id: e3850360-96cd-4046-bd0c-0802ba9078e7
  sms_alerts-feature-id: f4ef0ad8-6cdc-44b0-87f4-c5f5d4c0386c
  in-app-alerts-feature-id: d0baa546-9c38-4951-a0ee-c3f7c89079a2
  email-alerts-feature-id: 09546078-30f4-4a21-b270-64a2ec93659a
  spanish-translation-feature-id: 3e9ea5f7-6b01-4419-aefc-3ccd97034ea4
  boost-features-feature-id: b41d832f-0d61-41fe-958c-ecf65b407c47
  carsaver-f-and-i-feature-id: bc9dd58b-01d6-4707-bfd3-f1788cb8706a

nissan:
  wiretap-enabled: true
inventory-service:
  api-uri: https://api-beta.carsaver.com/inventory
quote-service:
  endpoint-root: https://api-beta.carsaver.com/quote

apex:
  api-uri: ${magellen-service-api-uri}/apex
activity-service:
  api-uri: ${magellen-service-api-uri}/activity
vehicle-service:
  api-uri: ${magellen-service-api-uri}/vehicle
meridian-service:
  api-uri: ${magellen-service-api-uri}/meridian
harbor-service:
  api-uri: ${magellen-service-api-uri}/harbor
rebate-service:
  api-uri: ${magellen-service-api-uri}/rebates
rebates-service:
  api-uri: ${rebate-service.api-uri}
email-service:
  api-uri: ${magellen-service-api-uri}/email
dealer-service:
  api-uri: ${magellen-service-api-uri}/dealer
lead-service:
  api-uri: ${magellen-service-api-uri}/lead
finance-service:
  root-uri: ${magellen-service-api-uri}/finance
  api-uri: ${magellen-service-api-uri}/meridian
sale-service:
  api-uri: ${magellen-service-api-uri}/sales
appointment-service:
  api-uri: ${magellen-service-api-uri}/lead
auth-service:
  api-uri: ${magellen-service-api-uri}/uaa/api
marketing-service:
  api-uri: ${magellen-service-api-uri}/marketing
user-service:
  api-uri: ${magellen-service-api-uri}/users
accessories-service:
  api-uri: ${magellen-service-api-uri}/accessories
configuration-service:
  api-uri: ${magellen-service-api-uri}/configuration
user-vehicle-service:
  api-uri: ${magellen-service-api-uri}/user-vehicle
digital-retail:
  otp-api-uri: ${service-api-uri}/digital-retail/logins/dealer-otps
  campaign-id: 89344995-3a83-4ddf-98d6-j382kd92k3lf

atc:
  rootUri: https://svc.autotitling.com/AGFService/v1.0/
  userToken: Q2FyU2F2ZXJBR0Y6b2dFbzM1Y0JPbXJ3a3RGRWtPa2xRS1VhS1FYVEdibHVObTU1Zm1rYkJndz0=
  requestGranularity: 0

routeone:
  base-uri: https://testint.r1dev.com
  partner-id: F00CSV
  sso:
    password: csv$%*HBNF3ed+
  protection-products:
    feature-r-id: 2289A234-1E4D-4E80-84C6-F7853596BEB5

domo:
  client-id: 21e18b92-3d4c-42b4-8d43-4325a11cac63
  client-secret: 9bc9f629161c2992806945db5c1c3b7ebc663b98b87d1788b7456bf30257118b
  grant-type: client_credentials
  scopes: data,workflow,user,dashboard
  embed-id: l5rWr
  api-host: https://api.domo.com

export:
  customer:
    page-size: 100

in-showroom:
  minutes-threshold: 240
  lead-hour-threshold: 24
