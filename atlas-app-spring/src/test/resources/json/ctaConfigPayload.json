{"websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-family": "<PERSON><PERSON>", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-size": "16", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-weight": "bold", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/align-content": "center", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/width": "300", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/height": "50", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/padding": "10", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/margin": "15", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/radius": "5", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/display": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/enable-image-cta": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/button-text": "View Details Text", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/link-destination": "Vehicle Details Page", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/background-color": "#007BFF", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/hover-text-color": "#F0F0F0", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/hover-background-color": "#0056b3", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/display": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/enable-image-cta": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/button-text": "Get Prequalified Text", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/link-destination": "Get Prequalified", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/background-color": "#28A745", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/hover-text-color": "#F0F0F0", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/hover-background-color": "#218838", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/display": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/enable-image-cta": "false", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/button-text": "Get Trade Value Text", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/link-destination": "Get Trade Value", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/background-color": "#FFC107", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/hover-text-color": "#212529", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/hover-background-color": "#E0A800", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/display": "false", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/enable-image-cta": "false", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/button-text": "Sell@Home Text", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/link-destination": "Sell@Home", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/background-color": "#6C757D", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/hover-text-color": "#F0F0F0", "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/hover-background-color": "#5A6268", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-family": "<PERSON><PERSON>", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-size": "16", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-weight": "bold", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/align-content": "center", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/width": "null", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/height": "50", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/padding": "10", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/margin": "15", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/radius": "5", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/display": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/enable-image-cta": "false", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/button-text": "View Details Text", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/link-destination": "Vehicle Details Page", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/background-color": "#007BFF", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/hover-text-color": "#F0F0F0", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/hover-background-color": "#0056b3", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/display": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/enable-image-cta": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/button-text": "Get Prequalified Text", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/link-destination": "Get Prequalified", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/background-color": "#28A745", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/hover-text-color": "#F0F0F0", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/hover-background-color": "#218838", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/display": "true", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/enable-image-cta": "false", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/button-text": "Get Trade Value Text", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/link-destination": "Get Trade Value", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/background-color": "#FFC107", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/hover-text-color": "#212529", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/hover-background-color": "#E0A800", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/display": "false", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/enable-image-cta": "false", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/button-text": "Sell@Home Text", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/link-destination": "Sell@Home", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/text-color": "#FFFFFF", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/background-color": "#6C757D", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/hover-text-color": "#F0F0F0", "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/hover-background-color": "#5A6268"}