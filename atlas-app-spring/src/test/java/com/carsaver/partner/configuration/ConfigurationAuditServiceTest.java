package com.carsaver.partner.configuration;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.configuration.AuditDataFields;
import com.carsaver.partner.model.configuration.AuditResponseData;
import com.carsaver.partner.model.configuration.QrCodeTransformAuditResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ConfigurationAuditServiceTest {

    public static final String USER_ID = "ef00d55a-4434-47c6-a8c5-9984f942b3ab";
    private ConfigurationClient configurationClient;
    private UserClient userClient;
    private ConfigurationAuditService configurationAuditService;

    @BeforeEach
    void setUp() {
        configurationClient = mock(ConfigurationClient.class);
        userClient = mock(UserClient.class);
        configurationAuditService = new ConfigurationAuditService(configurationClient, userClient);
    }

    @Test
    void fetchPagedQRCodeAuditLogs_shouldReturnTransformedResponse() {
        String dealerId = "dealer123";
        int page = 1;
        int size = 10;
        String pageName = "testPage";

        AuditResponseData auditResponseData = new AuditResponseData();
        auditResponseData.setSize(size);
        auditResponseData.setPage(page);
        auditResponseData.setTotalCount(100);
        auditResponseData.setData(List.of(
            new AuditDataFields("domain1", "1625123456789", USER_ID, Map.of(
                "fieldName1", "fieldValue1",
                "oldValue1", "oldValue",
                "newValue1", "newValue"
            ))
        ));

        UserView userView = new UserView();
        userView.setFirstName("Test");
        userView.setLastName("User");

        when(configurationClient.getQRCodeAuditLog(dealerId, page, size, pageName)).thenReturn(createDummyJsonResponse());
        when(userClient.findById(USER_ID)).thenReturn(userView);

        QrCodeTransformAuditResponse response = configurationAuditService.fetchPagedQRCodeAuditLogs(dealerId, page, size, pageName);

        Assertions.assertNotNull(response);
        Assertions.assertEquals(size, response.getSize());
        Assertions.assertEquals(page, response.getPage());
        Assertions.assertEquals(12, response.getTotalCount());
        Assertions.assertEquals("Test User", response.getData().get(0).get("userName"));

        verify(configurationClient).getQRCodeAuditLog(dealerId, page, size, pageName);
        verify(userClient).findById(USER_ID);
    }

    @Test
    void fetchPagedQRCodeAuditLogs_shouldHandleException() {
        // Arrange
        String dealerId = "dealer123";
        int page = 1;
        int size = 10;
        String pageName = "testPage";

        when(configurationClient.getQRCodeAuditLog(dealerId, page, size, pageName)).thenThrow(new RuntimeException("Client error"));

        RuntimeException exception = org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, () ->
            configurationAuditService.fetchPagedQRCodeAuditLogs(dealerId, page, size, pageName)
        );

        Assertions.assertEquals("Error transforming audit data", exception.getMessage());
        verify(configurationClient).getQRCodeAuditLog(dealerId, page, size, pageName);
    }

    public static Map<String, Object> createDummyJsonResponse() {
        List<Map<String, Object>> dataRecords = new ArrayList<>();

        Map<String, Object> record1 = new HashMap<>();
        record1.put("domainName", "DEALER_4466d009-0e4d-4577-927a-b06cb66d1aa0");
        record1.put("fieldName1", "marketingSettings/smartLinkQrCode/one/sms/response");
        record1.put("newValue1", "This is the response SMS");
        record1.put("oldValue1", "This is the response SMS1");
        record1.put("time", "1731047518170");
        record1.put("userId", USER_ID);
        record1.put("label1", "Test Label");

        Map<String, Object> record2 = new HashMap<>();
        record2.put("domainName", "DEALER_4466d009-0e4d-4577-927a-b06cb66d1aa0");
        record2.put("fieldName1", "marketingSettings/smartLinkQrCode/one/sms/response");
        record2.put("newValue1", "This is the response SMS1");
        record2.put("oldValue1", "This is the response SMS");
        record2.put("time", "1731047513389");
        record2.put("userId", USER_ID);
        record1.put("label2", "Test Label2");

        dataRecords.add(record1);
        dataRecords.add(record2);

        Map<String, Object> response = new HashMap<>();
        response.put("totalCount", 12);
        response.put("size", 10);
        response.put("page", 1);
        response.put("data", dataRecords);

        return response;
    }
}
