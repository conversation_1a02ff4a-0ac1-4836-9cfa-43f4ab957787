package com.carsaver.partner.configuration.overlaysettings

import com.carsaver.partner.configuration.ConfigurationClient
import com.carsaver.partner.configuration.ctaconfig.CtaConfigRequest
import com.carsaver.partner.configuration.ctaconfig.CtaConfigResponse
import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigPayloadMapper
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.ArgumentCaptor
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.util.Optional

class OverlaySettingsServiceTest {
    private val configurationClient: ConfigurationClient = mock()
    private val overlaySettingsService = OverlaySettingsService(configurationClient)

    @Test
    fun `saveOverlaySettingByDealer should call configurationClient with correct parameters`() {
        // Given
        val dealerId = "dealerId"
        val userId = "userId"
        val payload = mapOf(
            "integrationMethod" to "OVERLAY",
            "displayType" to "COMPACT"
        )

        // When
        overlaySettingsService.saveOverlaySettingByDealer(dealerId, userId, payload)

        // Then
        val requestCaptor = argumentCaptor<String>()

        //
        verify(configurationClient).saveCtaConfig(
            org.mockito.kotlin.eq(dealerId),
            org.mockito.kotlin.eq(userId),
            org.mockito.kotlin.eq(null),
            requestCaptor.capture(),
            org.mockito.kotlin.eq(null)
        )
    }

    @Test
    fun `getOverlaySettingsByDealer should return overlay settings response from client`() {
        // Given
        val dealerId = "dealerId"
        val userId = "userId"
        val domain = null

        // Mock CtaConfigRequest with expected values
        val integrationMethod = Optional.of("OVERLAY")
        val displayType = Optional.of("EXPANDED")

        whenever(configurationClient.getCtaConfig(dealerId, userId, domain)).thenReturn(
            CtaConfigResponse(
                integrationMethod = integrationMethod,
                displayType = displayType
            )
        )

        // When
        val result = overlaySettingsService.getOverlaySettingsByDealer(dealerId, userId, domain)

        // Then
        assertEquals("OVERLAY", result.integrationMethod)
        assertEquals("EXPANDED", result.displayType)

        // Verify the client was called with correct parameters
        verify(configurationClient).getCtaConfig(dealerId, userId, domain)
    }

    @Test
    fun `getOverlaySettingsByDealer should handle empty values from client`() {
        // Given
        val dealerId = "dealerId"
        val userId = "userId"
        val domain = null

        // Mock empty response from ConfigurationClient
        whenever(configurationClient.getCtaConfig(dealerId, userId, domain)).thenReturn(
            CtaConfigResponse()
        )

        // When
        val result = overlaySettingsService.getOverlaySettingsByDealer(dealerId, userId, domain)

        // Then
        assertEquals("", result.integrationMethod)
        assertEquals("", result.displayType)

        // Verify the client was called with correct parameters
        verify(configurationClient).getCtaConfig(dealerId, userId, domain)
    }
}
