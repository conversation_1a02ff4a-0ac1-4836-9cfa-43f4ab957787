package com.carsaver.partner.configuration.theming

import com.carsaver.configuration.v2.api.theme.ThemeResponse
import com.carsaver.configuration.v2.api.theme.ThemingModel
import com.carsaver.configuration.v2.api.theme.TopNavigationBarResponse
import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.configuration.utils.FlatMapUtils
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.mockito.kotlin.whenever
import org.mockito.kotlin.any
import org.junit.jupiter.api.Assertions.assertEquals
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.mock

class ThemingConfigServiceTest {

    private var configServiceClient: ConfigServiceClient = mock()
    private var mapper: ThemingResponseMapper = mock()
    private var themingConfigService= ThemingConfigService(configServiceClient, mapper)

    private val dealerId = "dealer123"
    private val userId = "user456"
    private val domain = "example.com"

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Test
    fun `getThemingConfiguration should return flattened theme map`() {
        // Given
        val themeResponse = ThemeResponse(topNavigationBar = TopNavigationBarResponse(
            active = true,
            backgroundColor = "#FFFFFF",
            textColor = "#000000"
        ))
        val themingModel = ThemingModel(
            navigationBarActive = true,
            navigationBarBackgroundColorVal = "#FFFFFF",
            navigationBarTextColorVal = "#000000"
        )
        val expectedMap = mapOf(
            "accentValuesActive" to null,
            "accentValuesColorVal" to null,
            "firstButtonActive" to null,
            "firstButtonBackgroundColorVal" to null,
            "firstButtonBorderColorVal" to null,
            "firstButtonHighlightRolloverBackgroundColorVal" to null,
            "firstButtonHighlightRolloverBorderColorVal" to null,
            "firstButtonHighlightRolloverTextColorVal" to null,
            "firstButtonTextColorVal" to null,
            "navigationBarActive" to true,
            "navigationBarBackgroundColorVal" to "#FFFFFF",
            "navigationBarTextColorVal" to "#000000",
            "secondButtonActive" to null,
            "secondButtonBackgroundColorVal" to null,
            "secondButtonBorderColorVal" to null,
            "secondButtonHighlightRolloverBackgroundColorVal" to null,
            "secondButtonHighlightRolloverBorderColorVal" to null,
            "secondButtonHighlightRolloverTextColorVal" to null,
            "secondButtonTextColorVal" to null,
            "thirdButtonActive" to null,
            "thirdButtonBackgroundColorVal" to null,
            "thirdButtonBorderColorVal" to null,
            "thirdButtonHighlightRolloverBackgroundColorVal" to null,
            "thirdButtonHighlightRolloverBorderColorVal" to null,
            "thirdButtonHighlightRolloverTextColorVal" to null,
            "thirdButtonTextColorVal" to null
        )

        whenever(configServiceClient.getThemingValues(any(), any())).thenReturn(themeResponse)
        whenever(mapper.toModel(themeResponse)).thenReturn(themingModel)


        // When
        val result = themingConfigService.getThemingConfiguration(dealerId, domain)

        // Then
        assertEquals(expectedMap, result)
        verify(configServiceClient).getThemingValues(dealerId, domain)
        verify(mapper).toModel(themeResponse)

    }

    @Test
    fun `saveThemingConfiguration should convert map to model and save values`() {
        // Given
        val requestMap = mapOf("property1" to "value1", "property2" to "true")
        val themingModel = ThemingModel()
        val themeRequest = ThemeRequest()

        // Use Mockito's static mocking for FlatMapUtils
       whenever(mapper.toRequest(themingModel)).thenReturn(themeRequest)

        // When
        themingConfigService.saveThemingConfiguration(dealerId, userId, domain, requestMap)

        // Then
        verify(configServiceClient).saveThemingValues(dealerId, userId, domain, themeRequest)
        verify(mapper).toRequest(themingModel)
    }

    @Test
    fun `saveThemingConfiguration should convert from map to model`() {
        // Given
        val requestMap = mapOf(
            "accentValuesActive" to null,
            "accentValuesColorVal" to null,
            "firstButtonActive" to null,
            "firstButtonBackgroundColorVal" to null,
            "firstButtonBorderColorVal" to null,
            "firstButtonHighlightRolloverBackgroundColorVal" to null,
            "firstButtonHighlightRolloverBorderColorVal" to null,
            "firstButtonHighlightRolloverTextColorVal" to null,
            "firstButtonTextColorVal" to null,
            "navigationBarActive" to "true",
            "navigationBarBackgroundColorVal" to "#FFFFFF",
            "navigationBarTextColorVal" to "#000000",
            "secondButtonActive" to null,
            "secondButtonBackgroundColorVal" to null,
            "secondButtonBorderColorVal" to null,
            "secondButtonHighlightRolloverBackgroundColorVal" to null,
            "secondButtonHighlightRolloverBorderColorVal" to null,
            "secondButtonHighlightRolloverTextColorVal" to null,
            "secondButtonTextColorVal" to null,
            "thirdButtonActive" to null,
            "thirdButtonBackgroundColorVal" to null,
            "thirdButtonBorderColorVal" to null,
            "thirdButtonHighlightRolloverBackgroundColorVal" to null,
            "thirdButtonHighlightRolloverBorderColorVal" to null,
            "thirdButtonHighlightRolloverTextColorVal" to null,
            "thirdButtonTextColorVal" to null
        )

        val expected = ThemingModel(
            navigationBarActive = true,
            navigationBarBackgroundColorVal = "#FFFFFF",
            navigationBarTextColorVal = "#000000"
        )

        val result = FlatMapUtils.mapToModel(requestMap, ThemingModel())
        assertEquals(expected, result)

    }
}
