package com.carsaver.partner.configuration.ctaconfig

import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigFileUtils.Companion.overrideFileNameAndSanitize
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.mock.web.MockMultipartFile

class CtaConfigFileUtilsTest {

    @Test
    fun `test overrideFileNames with valid files`() {
        // Arrange
        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp())
        val file2 = MockMultipartFile("file2", "original2.png", "image/png", TestUtils.vlp())


        // Assert
        assertEquals(
            "DEALER_ID-vdp-cta-1.jpg",
            overrideFileNameAndSanitize(file1, 0, "DEALER_ID", "vdp").originalFilename
        )
        assertEquals(
            "DEALER_ID-vdp-cta-2.png",
            overrideFileNameAndSanitize(file2, 1, "DEALER_ID", "vdp").originalFilename
        )
    }

    @Test
    fun `test overrideFileNames with null files`() {
        // Arrange
        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp())

        // Assert
        assertEquals(
            "DEALER_ID-vlp-cta-1.jpg",
            overrideFileNameAndSanitize(file1, 0, "DEALER_ID", "vlp").originalFilename
        )
    }

    @Test
    fun `test overrideFileNames with mixed file types`() {
        // Arrange
        val file1 = MockMultipartFile("file1", "original1.jpg", "image/jpeg", TestUtils.vlp())
        val file2 = MockMultipartFile("file2", "original2.pdf", "application/pdf", TestUtils.vlp())
        val file3 = MockMultipartFile("file3", "original3.svg", "image/svg+xml", TestUtils.vlp())

        // Assert
        assertEquals(
            "DEALER_ID-test-cta-1.jpg",
            overrideFileNameAndSanitize(file1, 0, "DEALER_ID", "test").originalFilename
        )
        assertEquals(
            "DEALER_ID-test-cta-2.pdf",
            overrideFileNameAndSanitize(file2, 1, "DEALER_ID", "test").originalFilename
        )
        assertEquals(
            "DEALER_ID-test-cta-3.svg",
            overrideFileNameAndSanitize(file3, 2, "DEALER_ID", "test").originalFilename
        )
    }
}
