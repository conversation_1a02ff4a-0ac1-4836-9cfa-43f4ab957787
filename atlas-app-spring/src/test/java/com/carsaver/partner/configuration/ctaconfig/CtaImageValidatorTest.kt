package  com.carsaver.partner.configuration.ctaconfig

import com.carsaver.partner.exception.BadRequestException
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.io.FileInputStream

class CtaImageValidatorTest {

    @Test
    fun `is valid image type`() {

        CtaImageValidator.validateImageType(
            FileInputStream("src/test/resources/images/original-vlp.png"),
            "image/png"
        )

        assertThrows<BadRequestException> {
            // wrong format
            CtaImageValidator.validateImageType(
                FileInputStream("src/test/resources/images/sanitized.jpg"),
                "image/bad"
            )
        }


        assertThrows<BadRequestException> {
            // wrong format
            CtaImageValidator.validateImageType(
                FileInputStream("src/test/resources/images/sanitized.jpg"),
                "image/png"
            )
        }

        assertThrows<BadRequestException> {
            // wrong format
            CtaImageValidator.validateImageType(
                FileInputStream("src/test/resources/images/original-vlp-corrupted.png"),
                "image/png"
            )
        }

        CtaImageValidator.validateImageType(
            FileInputStream("src/test/resources/images/sanitized.jpg"),
            "image/jpg"
        )

    }
}
