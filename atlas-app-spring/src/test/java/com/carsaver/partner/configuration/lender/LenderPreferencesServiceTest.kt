package com.carsaver.partner.configuration.lender

import com.carsaver.core.DealerStatus
import com.carsaver.magellan.client.ProgramSubscriptionClient
import com.carsaver.magellan.model.DealerView
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView
import com.carsaver.partner.client.FeatureSubscriptionsClient
import com.carsaver.partner.client.ProgramSubscriptionsClient
import com.carsaver.partner.filter.DealerUserAccessFilter.DEALER_LIST
import com.carsaver.partner.model.FeatureSubscriptionResponse
import com.carsaver.partner.model.ProgramSubscription
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.hateoas.CollectionModel
import org.springframework.security.core.Authentication
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import javax.servlet.http.HttpSession

private const val LMS_FEATURE_ID = "lmsFeatureId"

private const val BUY_AT_HOME = "buyAtHome"

private const val DEALER_ID = "dealerId"

class LenderPreferencesServiceTest() {
    private val featureSubscriptionsClient: FeatureSubscriptionsClient = mock()
    private val programSubscriptionClient: ProgramSubscriptionClient = mock()
    private val session: HttpSession = mock()
    private val preferencesService = LenderPreferencesService(
        featureSubscriptionsClient,
        programSubscriptionClient,
        session, BUY_AT_HOME, LMS_FEATURE_ID
    )

    @Test
    fun test() {

        val context: SecurityContext = mock()
        val auth: Authentication = mock()
        whenever(context.authentication).thenReturn(auth)
        SecurityContextHolder.setContext(context)
        val list = mutableListOf(SimpleGrantedAuthority("ROLE_ADMIN"))
        whenever(auth.authorities).thenReturn(list)
        var lmsOptions = preferencesService.getLmsOptions(DEALER_ID)
        assertEquals(200, lmsOptions.statusCodeValue)
        assertEquals(preferencesService.onlyRouteOne, lmsOptions.body)


        list.clear()
        list.add(SimpleGrantedAuthority("ROLE_DEALER"))
        lmsOptions = preferencesService.getLmsOptions(DEALER_ID)
        assertEquals(403, lmsOptions.statusCodeValue)



        whenever(session.getAttribute(DEALER_LIST)).thenReturn(listOf(DealerView().apply {
            id = DEALER_ID
        }))
        lmsOptions = preferencesService.getLmsOptions(DEALER_ID)
        assertEquals(200, lmsOptions.statusCodeValue)
        assertEquals(preferencesService.onlyRouteOne, lmsOptions.body)



        val buyAtHome = ProgramSubscriptionView().apply {
            programId = BUY_AT_HOME
            status = DealerStatus.LIVE
        }
        whenever(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(CollectionModel.of(listOf(buyAtHome)))
        lmsOptions = preferencesService.getLmsOptions(DEALER_ID)
        assertEquals(200, lmsOptions.statusCodeValue)
        assertEquals(preferencesService.both, lmsOptions.body)



        val other = ProgramSubscriptionView().apply {
            programId = "other"
            status = DealerStatus.LIVE
        }
        whenever(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(CollectionModel.of(listOf(other)))
        whenever(featureSubscriptionsClient.getFeatureSubscription(any())).thenReturn(
            FeatureSubscriptionResponse()
        )
        lmsOptions = preferencesService.getLmsOptions(DEALER_ID)
        assertEquals(200, lmsOptions.statusCodeValue)
        assertEquals(preferencesService.both, lmsOptions.body)



        val notLive = ProgramSubscriptionView().apply {
            programId = "other"
            status = DealerStatus.ONBOARDING
        }
        whenever(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(CollectionModel.of(listOf(notLive)))
        whenever(featureSubscriptionsClient.getFeatureSubscription(any())).thenReturn(
            FeatureSubscriptionResponse()
        )
        lmsOptions = preferencesService.getLmsOptions(DEALER_ID)
        assertEquals(200, lmsOptions.statusCodeValue)
        assertEquals(preferencesService.onlyRouteOne, lmsOptions.body)
    }
}
