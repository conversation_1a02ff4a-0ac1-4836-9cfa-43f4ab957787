package com.carsaver.partner.configuration.ctaconfig

import com.carsaver.partner.client.mapper.JsonMapper
import com.carsaver.partner.configuration.ConfigurationClient
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.springframework.mock.web.MockMultipartFile
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.FileInputStream
import java.util.*

class CtaConfigServiceTest {
    private val configurationClient: ConfigurationClient = mock()
    private val ctaConfigService = CtaConfigService(configurationClient)

    private val emptyFiles: MutableList<MultipartFile?> = mutableListOf(null, null, null, null)

    @Test
    fun saveCtaConfigByDealer() {
        // Given
        val dealerId = "dealerId"
        val userId = "userId"
        val payload = emptyMap<String, String>()
        val files = emptyArray<MultipartFile>()

        val ctaConfig = "{}"

        // When
        ctaConfigService.saveCtaConfigByDealer(dealerId, userId, payload, emptyFiles, emptyFiles)

        // Then
        verify(configurationClient).saveCtaConfig(dealerId, userId, null, ctaConfig, files)
    }

    @Test
    fun `saveCtaConfigByDealer should properly pass files to configurationClient`() {
        // Given
        val dealerId = "dealerId"
        val userId = "userId"
        val payload = emptyMap<String, String>()

        // Create mock files
        val vdpFile = MockMultipartFile(
            "vdpFile", "original-vdp.png", "image/png",
            FileInputStream("src/test/resources/images/original-vdp.png")
        )
        val vlpFile = MockMultipartFile(
            "vlpFile", "original-vlp.png", "image/png",
            FileInputStream("src/test/resources/images/original-vlp.png")
        )

        val vdpFiles = mutableListOf<MultipartFile?>(vdpFile, null, null, null)
        val vlpFiles = mutableListOf<MultipartFile?>(vlpFile, null, null, null)

        // File list capture
        val filesCaptor = argumentCaptor<Array<MultipartFile>>()
        val configCaptor = argumentCaptor<String>()

        // When
        ctaConfigService.saveCtaConfigByDealer(dealerId, userId, payload, vdpFiles, vlpFiles)

        // Then
        verify(configurationClient).saveCtaConfig(
            anyString(),
            anyString(),
            anyOrNull(),
            configCaptor.capture(),
            filesCaptor.capture()
        )

        // Verify files are passed correctly
        val passedFiles = filesCaptor.firstValue as Array<MultipartFile>
        assertEquals(2, passedFiles.size, "Should have 2 files (one VDP, one VLP)")

        // Check that file names were properly overridden
        val ctaConfig: Optional<CtaConfigRequest> = JsonMapper.getObjectMapper().readValue(configCaptor.firstValue)
        val vdpImage = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageFileName
        val vlpImage = ctaConfig.get().listings?.get()?.primaryButton?.get()?.imageFileName

        // Verify the image names contain the expected prefixes
        assertEquals(true, vdpImage?.get()?.contains("vdp"), "VDP file name should be overridden")
        assertEquals(true, vlpImage?.get()?.contains("vlp"), "VLP file name should be overridden")
    }

    @Test
    fun `saveCtaConfigByDealer remove file`() {
        // Given
        val dealerId = "dealerId"
        val userId = "userId"
        val payload = mapOf(
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-name" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-size" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-url" to "",
        )

        // Create mock files
        val vdpFiles = mutableListOf<MultipartFile?>(null, null, null, null)
        val vlpFiles = mutableListOf<MultipartFile?>(null, null, null, null)

        // File list capture
        val filesCaptor = argumentCaptor<Array<MultipartFile>>()
        val configCaptor = argumentCaptor<String>()

        // When
        ctaConfigService.saveCtaConfigByDealer(dealerId, userId, payload, vdpFiles, vlpFiles)

        // Then
        verify(configurationClient).saveCtaConfig(
            anyString(),
            anyString(),
            anyOrNull(),
            configCaptor.capture(),
            filesCaptor.capture()
        )
        // Check that file names were properly overridden
        val ctaConfig: Optional<CtaConfigRequest> = JsonMapper.getObjectMapper().readValue(configCaptor.firstValue)
        val vdpImageFile = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageFileName!!
        val vdpImageSize = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageSize!!
        val vdpImage = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageName!!


        // Verify the image names contain the expected prefixes
        assertTrue( vdpImageFile.isEmpty, "VDP file name should be overridden")
        assertTrue( vdpImageSize.isEmpty, "VDP file name should be overridden")
        assertTrue( vdpImage.isEmpty, "VDP file name should be overridden")
    }

    @Test
    fun `saveCtaConfigByDealer clone image file`() {
        // Given
        val dealerId = "dealerId"
        val userId = "userId"
        val payload = mapOf(
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-name" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-size" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-url" to "https://cta-images-bucket-staging.s3.amazonaws.com/500f87d74af4b50002000027-vlp-cta-1.jpg",

            // Pass image multipart file and image Url. Multipart file should triumph
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-name" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-size" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-url" to "https://cta-images-bucket-staging.s3.amazonaws.com/500f87d74af4b50002000027-vlp-cta-1.jpg",


            //Pass image multipart file
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image-name" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image-size" to "",
            "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image-url" to "",
        )

        // Create mock files
        val vdpSecondButtonFile = MockMultipartFile("vdpFile", "original-second-button-vdp.png", "image/png", TestUtils.vdp())
        val vdpFourthFile = MockMultipartFile("vdpFile", "original-vdp.png", "image/png", TestUtils.vdp())
        val vdpFiles = mutableListOf<MultipartFile?>(null, vdpSecondButtonFile, null, vdpFourthFile)
        val vlpFiles = mutableListOf<MultipartFile?>(null, null, null, null)

        // File list capture
        val filesCaptor = argumentCaptor<Array<MultipartFile>>()
        val configCaptor = argumentCaptor<String>()

        // When
        ctaConfigService.saveCtaConfigByDealer(dealerId, userId, payload, vdpFiles, vlpFiles)

        // Then
        verify(configurationClient).saveCtaConfig(
            anyString(),
            anyString(),
            anyOrNull(),
            configCaptor.capture(),
            filesCaptor.capture()
        )
        // Check that file names were properly overridden
        val ctaConfig: Optional<CtaConfigRequest> = JsonMapper.getObjectMapper().readValue(configCaptor.firstValue)
        val vdpPrimaryImageFileName = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageFileName!!
        val vdpPrimaryImageUrl = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageUrl!!
        val vdpPrimaryImageSize = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageSize!!
        val vdpPrimaryImage = ctaConfig.get().vdp?.get()?.primaryButton?.get()?.imageName!!

        // Verify the image names contain the expected prefixes
        assertEquals(
            "https://cta-images-bucket-staging.s3.amazonaws.com/500f87d74af4b50002000027-vlp-cta-1.jpg",
            vdpPrimaryImageUrl.get(),
            "Image should be cloned from the URL"
        )
        assertTrue( vdpPrimaryImageFileName.isEmpty, "VDP file should be overridden")
        assertTrue( vdpPrimaryImageSize.isEmpty, "VDP file name should be overridden")
        assertTrue( vdpPrimaryImage.isEmpty, "VDP file name should be overridden")

        val vdpSecondImageFileName = ctaConfig.get().vdp?.get()?.secondButton?.get()?.imageFileName
        val vdpSecondImageUrl = ctaConfig.get().vdp?.get()?.secondButton?.get()?.imageUrl
        val vdpSecondImageSize = ctaConfig.get().vdp?.get()?.secondButton?.get()?.imageSize!!
        val vdpSecondImage = ctaConfig.get().vdp?.get()?.secondButton?.get()?.imageName

        assertEquals("dealerId-vdp-cta-2.png", vdpSecondImageFileName?.get(), "Image should be cloned from the URL")
        assertEquals(null, vdpSecondImageUrl?.get(), "VDP file should be overridden")
        assertEquals("220749", vdpSecondImageSize.get(), "VDP file name should be overridden")
        assertEquals("original-second-button-vdp.png", vdpSecondImage?.get(), "VDP file name should be overridden")

        val vdpFourthImageFileName = ctaConfig.get().vdp?.get()?.fourthButton?.get()?.imageFileName
        val vdpFourthImageUrl = ctaConfig.get().vdp?.get()?.secondButton?.get()?.imageUrl
        val vdpFourthImageSize = ctaConfig.get().vdp?.get()?.secondButton?.get()?.imageSize!!
        val vdpFourthImage = ctaConfig.get().vdp?.get()?.secondButton?.get()?.imageName

        assertEquals("dealerId-vdp-cta-4.png", vdpFourthImageFileName?.get(), "Image should be cloned from the URL")
        assertEquals(null, vdpFourthImageUrl?.get(), "VDP file should be overridden")
        assertEquals("220749", vdpFourthImageSize.get(), "VDP file name should be overridden")
        assertEquals("original-second-button-vdp.png", vdpFourthImage?.get(), "VDP file name should be overridden")
    }
}
