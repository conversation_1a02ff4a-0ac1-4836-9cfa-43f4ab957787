package com.carsaver.partner

import com.carsaver.partner.client.mapper.JsonMapper
import com.fasterxml.jackson.databind.SerializationFeature
import kong.unirest.HttpMethod
import kong.unirest.HttpResponse
import kong.unirest.Unirest
import org.apache.http.HttpHeaders
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.web.server.LocalServerPort
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.test.context.ActiveProfiles
import java.time.Instant


private const val TEST_ACCESS_TOKEN = "test-access-token"
const val ROLE_DEALER = "ROLE_DEALER"
const val ROLE_USER = "ROLE_USER"
const val ROLE_ADMIN = "ROLE_ADMIN"
const val ROLE_PROGRAM = "ROLE_PROGRAM"
const val SERVICE_ACCOUNT_NAME = "<EMAIL>"

const val CUSTOMER_USER_ID = "customerUserId"
const val DEALER_USER_ID = "dealerUserId"


@ActiveProfiles("e2e")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
open class ControllerE2ETestBase {

    @LocalServerPort
    var port: Int? = null

    val mapper = JsonMapper.getObjectMapper().disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)

    var baseUrl = ""

    @BeforeEach
    fun setup() {
        baseUrl = "http://localhost:$port"
    }

    fun runTest(
        endpoint: String? = baseUrl,
        method: HttpMethod,
        expectedStatus: Int? = HttpStatus.BAD_REQUEST.value(),
        expectedReturnClass: Class<*>? = null,
        requestBody: String? = "{}",
        pathParams: Map<String, String>? = mapOf(),
        queryParams: Map<String, String>? = mapOf()
    ): HttpResponse<*> {
        var endpointWithPathParams = endpoint
        pathParams?.keys?.forEach {
            endpointWithPathParams += "/{$it}"
        }

        val headers = mapOf(
            HttpHeaders.AUTHORIZATION to "Bearer $TEST_ACCESS_TOKEN",
            HttpHeaders.CONTENT_TYPE to MediaType.APPLICATION_JSON.toString(),
            HttpHeaders.ACCEPT to MediaType.APPLICATION_JSON.toString()
        )

        when (method) {
            HttpMethod.GET -> {
                val request = Unirest.get(endpointWithPathParams)
                    .headers(headers)
                    .routeParam(pathParams)
                    .queryString(queryParams)

                val response =
                    if (expectedReturnClass != null) request.asObject(expectedReturnClass) else request.asString()
                Assertions.assertEquals(expectedStatus, response.status)

                return response
            }

            HttpMethod.POST -> {
                val response = Unirest.post(endpointWithPathParams)
                    .headers(headers)
                    .routeParam(pathParams)
                    .body(requestBody)
                    .asObject(expectedReturnClass)
                Assertions.assertEquals(expectedStatus, response.status)
                return response
            }

            HttpMethod.PUT -> {
                val response = Unirest.put(endpointWithPathParams)
                    .headers(headers)
                    .routeParam(pathParams)
                    .body(requestBody)
                    .asObject(expectedReturnClass)
                Assertions.assertEquals(expectedStatus, response.status)
                return response
            }

            HttpMethod.PATCH -> {
                val response = Unirest.patch(endpointWithPathParams)
                    .headers(headers)
                    .routeParam(pathParams)
                    .body(requestBody)
                    .asObject(expectedReturnClass)
                Assertions.assertEquals(expectedStatus, response.status)
                return response
            }

            HttpMethod.DELETE -> {
                val response = Unirest.delete(endpointWithPathParams)
                    .headers(headers)
                    .routeParam(pathParams)
                    .asObject(expectedReturnClass)
                Assertions.assertEquals(expectedStatus, response.status)
                return response
            }
        }
        throw IllegalArgumentException("Unsupported call.")
    }


    // TODO: For testing E2E cases where roles matter, we can extend the atlas context filter and jwtDecoder and use these
    fun customerUserJwt(): Jwt {
        return userJwt(CUSTOMER_USER_ID, ROLE_USER)
    }

    fun dealerUserJwt(): Jwt {
        return userJwt(DEALER_USER_ID, ROLE_DEALER)
    }

    fun programUserJwt(): Jwt {
        return userJwt(DEALER_USER_ID, ROLE_ADMIN)
    }

    fun serviceAccountJwt(): Jwt {
        return userJwt("serviceAccount", "ROLE_SERVICE_ACCOUNT")
    }

    fun userJwt(id: String, role: String): Jwt {
        val claims = mapOf(
            "tenant_id" to "tenant_id",
            "user_name" to "<EMAIL>",
            "scope" to listOf("public"),
            "id" to id,
            "authorities" to listOf(role)
        )

        return Jwt.withTokenValue("mock-token")
            .headers { headers -> headers["alg"] = "none" }
            .claims { it.putAll(claims) }
            .issuedAt(Instant.now())
            .expiresAt(Instant.now().plusSeconds(120))
            .build()
    }
}
