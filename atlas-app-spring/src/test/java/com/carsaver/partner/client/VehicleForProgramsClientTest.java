package com.carsaver.partner.client;

import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.FinanceConfig;
import com.carsaver.magellan.model.financiers.FinancierConfig;
import com.carsaver.magellan.model.financiers.PaymentConfig;
import com.carsaver.partner.client.inventory.VehicleForProgramsClient;
import com.carsaver.partner.model.desking.VehicleModelForPrograms;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.hateoas.CollectionModel;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class VehicleForProgramsClientTest {

    @InjectMocks
    VehicleForProgramsClient vehicleForProgramsClient;
    @Mock
    CampaignClient campaignClient;
    @Mock
    FinancierClient financierClient;

    @Test
    void retriveAvailableDealTypesTest() {
        String programId = UUID.randomUUID().toString();
        String vin =  "Good Vin";

        VehicleModelForPrograms vehicleModelForPrograms =
            VehicleModelForPrograms.builder()
                .vin(vin)
                .model("model")
                .make("make").inventoryId(UUID.randomUUID().toString())
                .availableDealTypes(new ArrayList<>())
                .build();
        FinanceConfig financeConfig = new FinanceConfig();
        financeConfig.setEnabledFinancier(123);
        financeConfig.setEnableDealerFinanciers(false);
        CampaignView campaignView = CampaignView
            .builder()
            .programId("123")
            .financeConfig(financeConfig)
            .build();

        FinancierView financierView = new FinancierView();
        financierView.setId(153);
        financierView.setName("test");
        when(campaignClient.findByProgramId(programId)).thenReturn(CollectionModel.of(List.of(campaignView)));

        when(financierClient.findById(Long.valueOf("123"))).thenReturn(Optional.of(financierView));
        vehicleForProgramsClient.retriveAvailableDealTypes(vehicleModelForPrograms,programId);
        // for Finance only
        assertEquals(vehicleModelForPrograms.getAvailableDealTypes().size(), 1);
        // for Lease and finance
        PaymentConfig paymentConfig = new PaymentConfig();
        paymentConfig.setLeaseEnabled(true);
        FinancierConfig config = new FinancierConfig();
        config.setPaymentConfig(paymentConfig);
        financierView.setConfig(config);
        vehicleForProgramsClient.retriveAvailableDealTypes(vehicleModelForPrograms,programId);
        assertEquals(vehicleModelForPrograms.getAvailableDealTypes().size(), 2);
    }
}
