package com.carsaver.partner.client;

import com.carsaver.partner.model.FeatureSubscriptionRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class FeatureSubscriptionClientTest {

    @InjectMocks
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    private static final String ROOT_URI = "http://localhost:8080";

    public static final String UPSERT_FEATURE_SUBSCRIPTION_PATH = "/upsert";

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(featureSubscriptionsClient, "rootUri", "http://localhost:8080");
    }

    @Test
    public void testGetUri_DefaultContext() {
        FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
        String testUri = ROOT_URI + UPSERT_FEATURE_SUBSCRIPTION_PATH;

        String actualUri = featureSubscriptionsClient.getUri(request);
        assertEquals(testUri, actualUri);
    }

    @Test
    public void testGetUri_SpanishContext() {
        FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
        request.setContext("spanish");
        String testUri = ROOT_URI + UPSERT_FEATURE_SUBSCRIPTION_PATH + "?type=spanish";

        String actualUri = featureSubscriptionsClient.getUri(request);
        assertEquals(testUri, actualUri);
    }
}

