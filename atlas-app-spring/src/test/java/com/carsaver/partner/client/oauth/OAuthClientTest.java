package com.carsaver.partner.client.oauth;

import com.carsaver.magellan.auth.CarSaverJWTToken;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.partner.http.HttpService;
import org.junit.jupiter.api.Test;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class OAuthClientTest {

    @Test
    void withToken() {
        HttpService httpService = mock(HttpService.class);
        OAuthClient oAuthClient = new OAuthClient(httpService, new OAuthProperties());
        SecurityContextHolder.setContext(new SecurityContextImpl());
        CarSaverJWTToken token = mock(CarSaverJWTToken.class);
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setAccessToken("saved");
        when(token.getTokenResponse()).thenReturn(tokenResponse);
        SecurityContextHolder.getContext().setAuthentication(token);
        assertEquals("saved", oAuthClient.getAccessToken());
    }

    @Test
    void withoutToken() {
        SecurityContextHolder.setContext(mock(SecurityContext.class));
        HttpService httpService = mock(HttpService.class);
        OAuthProperties oAuthProperties = new OAuthProperties();
        OAuthProperties.OAuthConfig oAuthConfig = new OAuthProperties.OAuthConfig();
        oAuthConfig.setGrantType(OAuthGrant.PASSWORD);
        oAuthConfig.setUrl("http://localhost:8080/oauth/token");
        oAuthConfig.setAuthPassword("auth-password");
        oAuthConfig.setAuthUsername("auth-username");
        oAuthConfig.setTokenPassword("token-password");
        oAuthConfig.setTokenUsername("token-username");
        oAuthProperties.setConfigurations(Map.of(OAuthType.CARSAVER, oAuthConfig));
        OAuthToken oAuthToken = new OAuthToken();
        oAuthToken.setAccessToken("received");
        oAuthToken.setExpiresIn(300);
        when(httpService.getSuccessResponse(any(), any())).thenReturn(oAuthToken);

        OAuthClient oAuthClient = new OAuthClient(httpService, oAuthProperties);
        assertEquals("received", oAuthClient.getAccessToken());
    }

}
