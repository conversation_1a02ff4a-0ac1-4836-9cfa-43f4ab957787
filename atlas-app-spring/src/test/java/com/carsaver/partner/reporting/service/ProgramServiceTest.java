package com.carsaver.partner.reporting.service;

import com.carsaver.partner.model.ProgramModel;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
class ProgramServiceTest {
    ProgramService programService = new ProgramService();

    @Test
    void testBuyAtHomeProgram() {
        List<ProgramModel> userProgramAccessList = new ArrayList<>();
        ProgramModel programModel = new ProgramModel();
        programModel.setId("5e922fe4-e1e9-468c-b100-5b8f7cffcef3");
        programModel.setName("Nissan Buy@Home");
        userProgramAccessList.add(programModel);

        ProgramModel programModelTwo = new ProgramModel();
        programModel.setId("79b324fa-98b7-478c-b62e-cb9f2f43b293");
        programModel.setName("Nissan Upgrade");
        userProgramAccessList.add(programModelTwo);

        assertTrue(programService.doesBuyAtHomeProgramExist(userProgramAccessList));
    }

    @Test
    void testBuyAtHomeProgramDoesNotExist() {
        List<ProgramModel> userProgramAccessList = new ArrayList<>();

        ProgramModel upgradeProgramModel = new ProgramModel();
        upgradeProgramModel.setId("79b324fa-98b7-478c-b62e-cb9f2f43b293");
        upgradeProgramModel.setName("Nissan Upgrade");
        userProgramAccessList.add(upgradeProgramModel);

        assertFalse(programService.doesBuyAtHomeProgramExist(userProgramAccessList));
    }

    @Test
    void testNoProgramsExist() {
        List<ProgramModel> userProgramAccessList = new ArrayList<>();
        assertFalse(programService.doesBuyAtHomeProgramExist(userProgramAccessList));
    }

    @Test
    void testNullProgram() {
        assertFalse(programService.doesBuyAtHomeProgramExist(null));
    }
}
