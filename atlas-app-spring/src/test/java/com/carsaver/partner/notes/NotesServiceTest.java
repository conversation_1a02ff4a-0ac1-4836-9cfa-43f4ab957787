package com.carsaver.partner.notes;

import com.carsaver.partner.security.SecurityUtils;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

class NotesServiceTest {

    public static final String USER_ID = "user_id";
    public static final String DEALER_ID = "dealer_id";
    private final NotesClient notesClient = mock(NotesClient.class);

    private final NotesService notesService = new NotesService(notesClient);

    @Test
    void getUserNotes_emptyList() {
        when(notesClient.getAllNotes(USER_ID, DEALER_ID)).thenReturn(List.of());
        List<UserNoteResponse> list =  notesService.getUserNotes(USER_ID, DEALER_ID);
        assertTrue(list.isEmpty());
    }

    @Test
    void getUserNotes_DealerUser() {
        try (MockedStatic<SecurityUtils> mockedStatic = mockStatic(SecurityUtils.class)) {
            mockedStatic.when(SecurityUtils::isDealerUser).thenReturn(true);
            when(notesClient.getAllNotes(USER_ID, DEALER_ID)).thenReturn(
                List.of(UserNoteResponse.builder().id("1").content("test 1").dealerVisible(true).build(),
                    UserNoteResponse.builder().id("2").content("test 2").dealerVisible(false).build()
                ));
            List<UserNoteResponse> list = notesService.getUserNotes(USER_ID, DEALER_ID);
            assertEquals(1, list.size());
        }
    }

    @Test
    void getUserNotes_AdminOrProgramUser() {
        try (MockedStatic<SecurityUtils> mockedStatic = mockStatic(SecurityUtils.class)) {
            mockedStatic.when(SecurityUtils::isDealerUser).thenReturn(false);
            when(notesClient.getAllNotes(USER_ID, DEALER_ID)).thenReturn(
                List.of(UserNoteResponse.builder().id("1").content("test 1").dealerVisible(true).build(),
                    UserNoteResponse.builder().id("2").content("test 2").dealerVisible(false).build()
                ));
            List<UserNoteResponse> list = notesService.getUserNotes(USER_ID, DEALER_ID);
            assertEquals(2, list.size());
        }
    }
}
