package com.carsaver.partner;

import com.carsaver.partner.client.oauth.OAuthGrant;
import com.carsaver.partner.client.oauth.OAuthProperties;
import com.carsaver.partner.client.oauth.OAuthType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.SneakyThrows;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueResponse;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;

public class TestUtils {

    @Data
    public static class Creds {
        private String oauthUsername;
        private String oauthPassword;
        private String oauthSecret;
        private String oauthClientId;
    }

    @SneakyThrows
    public static OAuthProperties getBetaOauthProperties() {
        SecretsManagerClient secretsManagerClient = SecretsManagerClient.create();
        GetSecretValueResponse secretValue = secretsManagerClient.getSecretValue(builder -> builder.secretId(
            "carsaver-api-credentials/dev")
        );
        String secret = secretValue.secretString();
        Creds creds = new ObjectMapper().readValue(secret, Creds.class);

        var properties = new OAuthProperties();

        properties.setConfigurations(Map.of(
            OAuthType.CARSAVER, OAuthProperties.OAuthConfig.builder()
                .url("https://api-beta.carsaver.com/uaa/oauth/token")
                .grantType(OAuthGrant.PASSWORD)
                .authUsername(creds.getOauthClientId())
                .authPassword(creds.getOauthSecret())
                .tokenUsername(creds.getOauthUsername())
                .tokenPassword(creds.getOauthPassword())
                .build()
        ));
        return properties;
    }

    public static final PodamFactory podamFactory = new PodamFactoryImpl();

    public static <T> T generate(Class<T> clazz, Type... types) {
        return podamFactory.manufacturePojo(clazz, types);
    }

    @SneakyThrows
    public static <T> T truncate(T instance) {
        return truncate(instance, ChronoUnit.SECONDS);
    }

    public static <T> T truncate(T instance, ChronoUnit chronoUnit) throws IllegalAccessException {
        // Replace "YourClass" with the name of the class you want to inspect
        Class<?> clazz = instance.getClass();

        // Get all fields of the class
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            // Check if the field is of type LocalDateTime
            if (field.getType() == LocalDateTime.class) {
                // Make the field accessible (if it's private, protected, etc.)
                field.setAccessible(true);

                // Retrieve the field value from the instance
                LocalDateTime dateTime = (LocalDateTime) field.get(instance);

                // Check if the field value is not null
                if (dateTime != null) {
                    // Truncate the LocalDateTime to seconds
                    LocalDateTime truncatedDateTime = dateTime.truncatedTo(chronoUnit);

                    // Set the truncated value back to the field
                    field.set(instance, truncatedDateTime);
                }
            } else if (field.getType() == ZonedDateTime.class) {
                field.setAccessible(true);

                // Retrieve the field value from the instance
                ZonedDateTime dateTime = (ZonedDateTime) field.get(instance);

                // Check if the field value is not null
                if (dateTime != null) {
                    // Truncate the LocalDateTime to seconds
                    ZonedDateTime truncatedDateTime = dateTime.truncatedTo(chronoUnit)
                        .withZoneSameInstant(ZoneId.of("UTC"));

                    // Set the truncated value back to the field
                    field.set(instance, truncatedDateTime);
                }
            }
        }
        return instance;
    }
}
