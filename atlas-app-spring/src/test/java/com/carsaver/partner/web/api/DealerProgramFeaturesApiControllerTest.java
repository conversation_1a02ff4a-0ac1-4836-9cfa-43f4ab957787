package com.carsaver.partner.web.api;

import com.carsaver.partner.exception.BadRequestException;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.model.protection_products.response.ErrorResponse;
import com.carsaver.partner.service.DealerProgramFeaturesService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
class DealerProgramFeaturesApiControllerTest {

    private MockMvc mockMvc;

    private DealerProgramFeaturesService dealerProgramFeaturesService;

    private ObjectMapper objectMapper;

    @InjectMocks
    private DealerProgramFeaturesApiController dealerProgramFeaturesApiController;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        dealerProgramFeaturesService = mock(DealerProgramFeaturesService.class);
        dealerProgramFeaturesApiController = new DealerProgramFeaturesApiController(dealerProgramFeaturesService);
        mockMvc = MockMvcBuilders.standaloneSetup(dealerProgramFeaturesApiController)
            .defaultRequest(put("/").contentType(MediaType.APPLICATION_JSON))
            .build();
    }

    @Test
    void testUpdateDealerProgramFeature_Success() throws Exception {
        DealerProgram dealerProgram = DealerProgram.builder().name("programName").build();
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();

        when(dealerProgramFeaturesService.updateDealerProgramFeature(anyString(), anyString(), any(ToggleConfigRequest.class)))
            .thenReturn(dealerProgram);

        mockMvc.perform(put("/api/dealer/dealerId/programs/programId/features/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(toggleConfigRequest)))
            .andExpect(status().isOk());
    }

    @Test
    void testUpdateDealerProgramFeature_BadRequest() throws Exception {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();

        when(dealerProgramFeaturesService.updateDealerProgramFeature(anyString(), anyString(), any(ToggleConfigRequest.class)))
            .thenThrow(new BadRequestException(ErrorResponse.builder().build()));

        mockMvc.perform(put("/api/dealer/dealerId/programs/programId/features/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(toggleConfigRequest)))
            .andExpect(status().isBadRequest());
    }

    @Test
    void testUpdateDealerProgramFeature_InternalServerError() throws Exception {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();

        when(dealerProgramFeaturesService.updateDealerProgramFeature(anyString(), anyString(), any(ToggleConfigRequest.class)))
            .thenThrow(new InternalServerError(ErrorResponse.builder().build()));

        mockMvc.perform(put("/api/dealer/dealerId/programs/programId/features/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(toggleConfigRequest)))
            .andExpect(status().isInternalServerError());
    }
}
