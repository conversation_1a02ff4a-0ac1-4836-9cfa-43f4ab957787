package com.carsaver.partner.web.api.user;

import com.carsaver.magellan.client.LoanClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.deal.Summary;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.partner.converter.DRLoanRequestViewToUserLoanRequestConverter;
import com.carsaver.partner.converter.LoanRequestViewToUserLoanRequestConverter;
import com.carsaver.partner.model.user.UserLoanRequestDTO;
import com.carsaver.partner.service.DealerValidationService;
import com.carsaver.partner.service.LoggedInUserProgramService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.api.SecurityHelperService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.hateoas.CollectionModel;

import javax.servlet.http.HttpSession;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserFinanceApiControllerTest {

    @InjectMocks
    private UserFinanceApiController userFinanceApiController;

    @Mock
    private LoanClient loanClient;

    @Mock
    private LoanRequestViewToUserLoanRequestConverter loanRequestViewToUserLoanRequestConverter;

    @Mock
    private SecurityHelperService securityHelperService;

    @Mock
    private DealerValidationService dealerValidationService;

    @Mock
    private LoggedInUserProgramService loggedInUserProgramService;

    @Mock
    DRLoanRequestViewToUserLoanRequestConverter drLoanRequestViewToUserLoanRequestConverter;

    @Mock
    SplitFeatureFlags splitFeatureFlags;

    @Mock
    private HttpSession httpSession;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetUserFinanceAppsV2_withDealerId() {
        String userId = "testUser";
        String dealerId = "dealer1";
        String programId = null;


        LoanRequestView loanRequest = mock(LoanRequestView.class);
        CertificateView certificateView = mock(CertificateView.class);
        UserLoanRequestDTO userLoanRequest = mock(UserLoanRequestDTO.class);


        CollectionModel<LoanRequestView> loanRequests = CollectionModel.of(List.of(loanRequest));
        when(loanClient.findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED)).thenReturn(loanRequests);

        when(loanRequest.getCertificate()).thenReturn(certificateView);
        when(certificateView.getDealerId()).thenReturn(dealerId);
        when(loanRequestViewToUserLoanRequestConverter.convert(loanRequest)).thenReturn(userLoanRequest);

        List<UserLoanRequestDTO> result = userFinanceApiController.getUserFinanceAppsV2(dealerId, programId, userId, httpSession);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(userLoanRequest, result.get(0));

        verify(loanClient, times(1)).findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED);
    }

    @Test
    void testGetUserFinanceAppsV2_withProgramId() {
        String userId = "testUser";
        String dealerId = null;
        String programId = "program1";

        List<String> dealerIds = List.of("dealer1", "dealer2");

        LoanRequestView loanRequest1 = mock(LoanRequestView.class);
        LoanRequestView loanRequest2 = mock(LoanRequestView.class);

        CertificateView certificateView1 = mock(CertificateView.class);
        CertificateView certificateView2 = mock(CertificateView.class);

        UserLoanRequestDTO userLoanRequest1 = mock(UserLoanRequestDTO.class);
        UserLoanRequestDTO userLoanRequest2 = mock(UserLoanRequestDTO.class);

        when(dealerValidationService.getDealerIdsFromProgram(programId, userId)).thenReturn(dealerIds);
//        when(securityHelperService.getFinalListOfDealerAndValidateCustomerPermissions(dealerIds, httpSession, securityHelperService)).thenReturn(finalDealerIds);

        CollectionModel<LoanRequestView> loanRequests = CollectionModel.of(List.of(loanRequest1, loanRequest2));
        when(loanClient.findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED)).thenReturn(loanRequests);

        when(loanRequest1.getCertificate()).thenReturn(certificateView1);
        when(certificateView1.getDealerId()).thenReturn("dealer1");

        when(loanRequest2.getCertificate()).thenReturn(certificateView2);
        when(certificateView2.getDealerId()).thenReturn("dealer2");

        when(loanRequestViewToUserLoanRequestConverter.convert(loanRequest1)).thenReturn(userLoanRequest1);
        when(loanRequestViewToUserLoanRequestConverter.convert(loanRequest2)).thenReturn(userLoanRequest2);

        List<UserLoanRequestDTO> result = userFinanceApiController.getUserFinanceAppsV2(dealerId, programId, userId, httpSession);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(userLoanRequest1));
        assertTrue(result.contains(userLoanRequest2));

        verify(loanClient, times(1)).findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED);
    }


    @Test
    void testGetUserFinanceAppsV2_WithSummaryDetails_UsesDrLoanRequestConverter() {
        String userId = "testUser";
        String dealerId = null;
        String programId = "program1";

        List<String> dealerIds = List.of("dealer1", "dealer2");
        List<String> finalDealerIds = List.of("dealer1", "dealer2");

        LoanRequestView loanRequest = mock(LoanRequestView.class);
        CertificateView certificateView = mock(CertificateView.class);
        Summary summary = mock(Summary.class);
        UserLoanRequestDTO userLoanRequest = mock(UserLoanRequestDTO.class);

        when(dealerValidationService.getDealerIdsFromProgram(programId, userId)).thenReturn(dealerIds);

        CollectionModel<LoanRequestView> loanRequests = CollectionModel.of(List.of(loanRequest));
        when(loanClient.findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED)).thenReturn(loanRequests);

        when(loanRequest.getCertificate()).thenReturn(certificateView);
        when(certificateView.getDealerId()).thenReturn("dealer1");
        when(certificateView.getSummaryDetails()).thenReturn(summary);
        when(splitFeatureFlags.isFinanceNegativeTest()).thenReturn(true);

        when(drLoanRequestViewToUserLoanRequestConverter.convert(loanRequest)).thenReturn(userLoanRequest);

        List<UserLoanRequestDTO> result = userFinanceApiController.getUserFinanceAppsV2(dealerId, programId, userId, httpSession);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(userLoanRequest));

        // Verify interactions
        verify(loanClient).findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED);
        verify(drLoanRequestViewToUserLoanRequestConverter).convert(loanRequest);
        verify(loanRequestViewToUserLoanRequestConverter, times(0)).convert(loanRequest);
    }

    @Test
    void testGetUserFinanceAppsV2_WithoutSummaryDetails_UsesLoanRequestConverter() {
        String userId = "testUser";
        String dealerId = null;
        String programId = "program1";

        List<String> dealerIds = List.of("dealer1", "dealer2");

        LoanRequestView loanRequest = mock(LoanRequestView.class);
        CertificateView certificateView = mock(CertificateView.class);
        UserLoanRequestDTO userLoanRequest = mock(UserLoanRequestDTO.class);

        when(dealerValidationService.getDealerIdsFromProgram(programId, userId)).thenReturn(dealerIds);

        CollectionModel<LoanRequestView> loanRequests = CollectionModel.of(List.of(loanRequest));
        when(loanClient.findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED)).thenReturn(loanRequests);

        when(loanRequest.getCertificate()).thenReturn(certificateView);
        when(certificateView.getDealerId()).thenReturn("dealer1");
        when(certificateView.getSummaryDetails()).thenReturn(null); // No summary details

        when(loanRequestViewToUserLoanRequestConverter.convert(loanRequest)).thenReturn(userLoanRequest);

        List<UserLoanRequestDTO> result = userFinanceApiController.getUserFinanceAppsV2(dealerId, programId, userId, httpSession);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(userLoanRequest));

        verify(loanClient).findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED);
        verify(loanRequestViewToUserLoanRequestConverter).convert(loanRequest);
        verify(drLoanRequestViewToUserLoanRequestConverter, times(0)).convert(loanRequest);
        verifyNoMoreInteractions(drLoanRequestViewToUserLoanRequestConverter, loanRequestViewToUserLoanRequestConverter);
    }

    @Test
    void testGetUserFinanceAppsV2_NoValidDealerId_ReturnsEmptyList() {
        String userId = "testUser";
        String dealerId = null;
        String programId = "program1";

        List<String> dealerIds = List.of("dealer1", "dealer2");

        LoanRequestView loanRequest = mock(LoanRequestView.class);
        CertificateView certificateView = mock(CertificateView.class);

        when(dealerValidationService.getDealerIdsFromProgram(programId, userId)).thenReturn(dealerIds);

        CollectionModel<LoanRequestView> loanRequests = CollectionModel.of(List.of(loanRequest));
        when(loanClient.findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED)).thenReturn(loanRequests);

        when(loanRequest.getCertificate()).thenReturn(certificateView);
        when(certificateView.getDealerId()).thenReturn("invalidDealer");

        List<UserLoanRequestDTO> result = userFinanceApiController.getUserFinanceAppsV2(dealerId, programId, userId, httpSession);

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(loanClient).findLoanRequestsByUserAndStatusGreaterThan(userId, LoanRequestView.STATUS_REQUEST_REGISTERED);
        verifyNoInteractions(drLoanRequestViewToUserLoanRequestConverter, loanRequestViewToUserLoanRequestConverter);
    }

}
