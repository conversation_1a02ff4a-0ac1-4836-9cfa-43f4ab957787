package com.carsaver.partner.web.api.notifications;

import com.carsaver.partner.model.Notification;
import com.carsaver.partner.model.UpdateNotificationEvent;
import com.carsaver.partner.service.NotificationService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationApiControllerTest {
    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private NotificationApiController notificationApiController;

    @Test
    void testGetNotifications() {
        String dealerId = "dealer123";
        String userId = "user123";

        Notification notification1 = new Notification();
        notification1.setUserId("user1");
        notification1.setFullName("John Doe");
        notification1.setPriority(1);

        Notification notification2 = new Notification();
        notification2.setUserId("user2");
        notification2.setFullName("Jane Smith");
        notification2.setPriority(2);

        List<Notification> notifications = Arrays.asList(notification1, notification2);

        when(notificationService.getNotificationsForDealerUser(dealerId, userId)).thenReturn(notifications);

        List<Notification> result = notificationApiController.getNotifications(dealerId, userId);

        assertEquals(2, result.size());
        assertEquals("John Doe", result.get(0).getFullName());
        assertEquals("Jane Smith", result.get(1).getFullName());

        verify(notificationService, times(1)).getNotificationsForDealerUser(dealerId, userId);
    }

    @Test
    void testUpdateNotification_Success() {
        UpdateNotificationEvent event = new UpdateNotificationEvent();
        event.setEventType("displayed");
        event.setUserId("user123");
        event.setTime("2024-12-22T10:00:00Z");

        Notification updatedNotification = new Notification();
        updatedNotification.setUserId(event.getUserId());
        updatedNotification.setFullName("John Doe");
        updatedNotification.setPriority(1);

        when(notificationService.updateNotification(event)).thenReturn(updatedNotification);

        ResponseEntity<Notification> response = notificationApiController.updateNotification(event);

        assertEquals(200, response.getStatusCodeValue()); // HTTP 200 OK
        assertNotNull(response.getBody());
        assertEquals("John Doe", response.getBody().getFullName());

        verify(notificationService, times(1)).updateNotification(event);
    }

    @Test
    void testUpdateNotification_NotFound() {
        UpdateNotificationEvent event = new UpdateNotificationEvent();
        event.setEventType("displayed");
        event.setUserId("user123");
        event.setTime("2024-12-22T10:00:00Z");

        when(notificationService.updateNotification(event)).thenReturn(null);

        ResponseEntity<Notification> response = notificationApiController.updateNotification(event);

        assertEquals(404, response.getStatusCodeValue());
        assertNull(response.getBody());

        verify(notificationService, times(1)).updateNotification(event);
    }
}
