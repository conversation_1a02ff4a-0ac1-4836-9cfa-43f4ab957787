package com.carsaver.partner.web.api;

import com.carsaver.magellan.client.converter.StringToUserViewConverter;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.deal.CustomerDealSummaryRequest;
import com.carsaver.partner.model.retail.CustomerDealSummaryResponse;
import com.carsaver.partner.model.retail.CustomerTagsResponse;
import com.carsaver.partner.model.retail.UserTags;
import com.carsaver.partner.service.CustomerDealSummaryService;
import com.carsaver.partner.service.CustomerTagService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.support.FormattingConversionService;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CustomerDealSummaryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Mock
    private CustomerDealSummaryService customerDealSummaryService;

    @Mock
    private CustomerTagService customerTagService;

    @InjectMocks
    private CustomerDealSummaryController customerDealSummaryController;

    @BeforeEach
    void setUp() {
        FormattingConversionService conversionService = new FormattingConversionService();
        StringToUserViewConverter stringToUserViewConverter = mock(StringToUserViewConverter.class);
            when(stringToUserViewConverter.convert(any(String.class))).thenReturn(new UserView());
        conversionService.addConverter(stringToUserViewConverter);

        mockMvc = MockMvcBuilders.standaloneSetup(customerDealSummaryController)
            .setConversionService(conversionService)
            .build();
    }

    @Test
    void testGetCustomerDealSummary() throws Exception {
        String dealerId = "12345";
        CustomerDealSummaryResponse mockResponse = new CustomerDealSummaryResponse();
        mockResponse.setPurchaseType("LEASE");

        when(customerDealSummaryService.getDealSummary(any(UserView.class), anyString()))
            .thenReturn(mockResponse);

        mockMvc.perform(get("/api/customers/{user}/deal-summary", "11")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("dealerId", dealerId))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.purchaseType", is("LEASE")));
    }

    @Test
    void testGetCustomerDealSummaryV2() throws Exception {
        String dealerId = "12345";
        CustomerDealSummaryResponse mockResponse = new CustomerDealSummaryResponse();
        mockResponse.setPurchaseType("LEASE");

        when(customerDealSummaryService.getDealSummaryV2(any(UserView.class), any(CustomerDealSummaryRequest.class)))
            .thenReturn(mockResponse);

        mockMvc.perform(post("/api/customers/v2/{user}/deal-summary", "11")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(new CustomerDealSummaryRequest(List.of(dealerId))))
            )
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.purchaseType", is("LEASE")));
    }

    @Test
    void testFetchCustomerTags() throws Exception {
        String userId = "user123";
        List<String> dealerIds = List.of("dealer1", "dealer2");

        CustomerTagsResponse mockTagsResponse = new CustomerTagsResponse();
        UserTags userTags = UserTags.builder()
            .name("Guaranteed Trade Value")
            .userId(userId)
            .build();
        mockTagsResponse.setCustomerTags(List.of(userTags));

        when(customerTagService.fetchCustomerTags(userId, dealerIds)).thenReturn(mockTagsResponse);

        mockMvc.perform(post("/api/customers/{user-id}/tags", userId)
                .content(new ObjectMapper().writeValueAsString(dealerIds))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.customerTags[0].name", is("Guaranteed Trade Value")));
    }

}
