package com.carsaver.partner.web.api;

import com.carsaver.partner.model.domo.DomoRequest;
import com.carsaver.partner.model.domo.DomoResponse;
import com.carsaver.partner.service.DomoReportService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
public class DomoApiControllerTest {
    private MockMvc mockMvc;
    private final DomoReportService domoReportService= mock(DomoReportService.class);

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        DomoApiController domoApiController = new DomoApiController(domoReportService);
        mockMvc = MockMvcBuilders.standaloneSetup(domoApiController)
            .defaultRequest(put("/").contentType(MediaType.APPLICATION_JSON))
            .build();
    }

    @Test
    public void testGetDomoData() throws Exception {
        DomoRequest domoRequest = DomoRequest.builder().dealerId("testDealerId").build();
        DomoResponse domoResponse = DomoResponse.builder().embedId("embedTestId").embedToken("mock_embed_token").build();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(SerializationFeature.WRAP_ROOT_VALUE, false);
        ObjectWriter ow = mapper.writer().withDefaultPrettyPrinter();
        String requestJson=ow.writeValueAsString(domoRequest );
        doReturn(domoResponse).when(domoReportService).domoEmbedTokenGeneration(anyString(), anyString(), anyString());
        mockMvc.perform(post("/api/domo/generate-token").contentType(MediaType.APPLICATION_JSON)
            .content(requestJson))
            .andExpect(status().isOk());
    }
}
