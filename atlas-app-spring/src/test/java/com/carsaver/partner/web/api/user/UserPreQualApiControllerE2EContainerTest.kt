package com.carsaver.partner.web.api.user

import com.carsaver.partner.CUSTOMER_USER_ID
import com.carsaver.partner.ControllerE2ETestBase
import com.carsaver.partner.TestUtils.podamFactory
import com.carsaver.partner.prequalification.CreditResponseStatus
import com.carsaver.partner.prequalification.CreditResponseStatusDTO
import com.carsaver.partner.prequalification.PreQualHistoryDynamoRecord
import com.carsaver.partner.prequalification.PreQualHistoryRepo
import com.carsaver.partner.prequalification.PreQualHistoryRepoDynamoTestContainer
import com.carsaver.partner.prequalification.PreQualRecord
import com.fasterxml.jackson.core.type.TypeReference
import kong.unirest.HttpMethod
import kong.unirest.HttpStatus
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import java.time.OffsetDateTime

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserPreQualApiControllerE2EContainerTest() : ControllerE2ETestBase() {

    @TestConfiguration()
    open class UserPreQualApiControllerE2EContainerTestConfig {
        @Primary
        @Bean("preQualHistoryRepoContainer")
        open fun preQualHistoryRepoContainer(): PreQualHistoryRepo {
            return PreQualHistoryRepoDynamoTestContainer.getContainerClient()
        }
    }

    @AfterAll
    fun teardown() {
        PreQualHistoryRepoDynamoTestContainer.stopContainerClient()
    }

    @Autowired
    @Qualifier("preQualHistoryRepoContainer")
    lateinit var preQualHistoryRepo: PreQualHistoryRepo

    @AfterEach
    fun cleanUp() {
        val records = preQualHistoryRepo.fetchHistoryRecordsByCustomerId(CUSTOMER_USER_ID)
        for (rec in records) {
            preQualHistoryRepo.repo.deleteItem(rec)
        }
    }

    @Order(1)
    @Test
    fun `test fetch customer records returns empty list if no records`() {
        val actualCustomerResponse = runTest(
            endpoint = "$baseUrl/api/users/$CUSTOMER_USER_ID/pre-qual",
            method = HttpMethod.GET,
            expectedStatus = HttpStatus.OK,
        )
        val fetchedPreQualHistoryRecords = mapper.readValue(
            actualCustomerResponse.body.toString(),
            object : TypeReference<List<PreQualRecord>>() {})
        assertEquals(0, fetchedPreQualHistoryRecords.size)
    }

    @Order(2)
    @Test
    fun `test fetch customer records should only fetch records for the customer`() {

        val record = baseGoodCustomerRecord()

        val otherCustomerRecord = baseGoodCustomerRecord().apply {
            customerId = "OtherCustomerId"
        }

        preQualHistoryRepo.repo.putItem(record)
        preQualHistoryRepo.repo.putItem(otherCustomerRecord)

        val actualCustomerResponse = runTest(
            endpoint = "$baseUrl/api/users/$CUSTOMER_USER_ID/pre-qual",
            method = HttpMethod.GET,
            expectedStatus = HttpStatus.OK,
        )
        val fetchedPreQualHistoryRecords = mapper.readValue(
            actualCustomerResponse.body.toString(),
            object : TypeReference<List<PreQualRecord>>() {})
        assertEquals(1, fetchedPreQualHistoryRecords.size)
        assertEquals(CUSTOMER_USER_ID, fetchedPreQualHistoryRecords.first().customerId)
    }

    @Order(3)
    @Test
    fun `should map ResponseStatus to correct DTO values`() {
        val passedRecord = baseGoodCustomerRecord().apply {
            responseStatus = CreditResponseStatus.SUCCESS
            expirationDate = OffsetDateTime.now().plusDays(1)
        }

        val expiredRecord = baseGoodCustomerRecord().apply {
            responseStatus = CreditResponseStatus.SUCCESS
            expirationDate = OffsetDateTime.now().minusDays(1)
        }

        val lockedRecord = baseGoodCustomerRecord().apply {
            responseStatus = CreditResponseStatus.LOCKED
            expirationDate = OffsetDateTime.now().plusDays(1)
        }

        val failedRecord = baseGoodCustomerRecord().apply {
            responseStatus = CreditResponseStatus.FAILURE
            expirationDate = OffsetDateTime.now().plusDays(1)
        }

        val notFoundFailedRecord = baseGoodCustomerRecord().apply {
            responseStatus = CreditResponseStatus.NOT_FOUND
            expirationDate = OffsetDateTime.now().plusDays(1)
        }

        val noStatusRecord = baseGoodCustomerRecord().apply {
            responseStatus = null
            expirationDate = OffsetDateTime.now().plusDays(1)
        }

        preQualHistoryRepo.repo.putItem(passedRecord)
        preQualHistoryRepo.repo.putItem(expiredRecord)
        preQualHistoryRepo.repo.putItem(lockedRecord)
        preQualHistoryRepo.repo.putItem(failedRecord)
        preQualHistoryRepo.repo.putItem(notFoundFailedRecord)
        preQualHistoryRepo.repo.putItem(noStatusRecord)

        val actualCustomerResponse = runTest(
            endpoint = "$baseUrl/api/users/$CUSTOMER_USER_ID/pre-qual",
            method = HttpMethod.GET,
            expectedStatus = HttpStatus.OK,
        )

        val fetchedPreQualRecords = mapper.readValue(
            actualCustomerResponse.body.toString(),
            object : TypeReference<List<PreQualRecord>>() {})

        assertEquals(6, fetchedPreQualRecords.size)

        assert(fetchedPreQualRecords.any { it.responseStatus == CreditResponseStatusDTO.PASSED }) {
            "Expected at least one PASSED record"
        }

        assert(fetchedPreQualRecords.any { it.responseStatus == CreditResponseStatusDTO.EXPIRED }) {
            "Expected at least one EXPIRED record"
        }

        assert(fetchedPreQualRecords.count { it.responseStatus == CreditResponseStatusDTO.FAIL } == 2) {
            "Expected exactly 2 FAIL records, but found ${fetchedPreQualRecords.count { it.responseStatus == CreditResponseStatusDTO.FAIL }}"
        }

        assert(fetchedPreQualRecords.any { it.responseStatus == CreditResponseStatusDTO.LOCKED }) {
            "Expected at least one LOCKED record"
        }

        assert(fetchedPreQualRecords.any { it.responseStatus == null }) {
            "Expected at least one record with null responseStatus"
        }
    }


    private fun baseGoodCustomerRecord(): PreQualHistoryDynamoRecord {
        return podamFactory.manufacturePojo(PreQualHistoryDynamoRecord::class.java).apply {
            customerId = CUSTOMER_USER_ID
            timestampCustomerId = "${OffsetDateTime.now()}#$CUSTOMER_USER_ID"
        }
    }
}
