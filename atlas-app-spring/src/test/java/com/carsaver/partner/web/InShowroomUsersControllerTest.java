package com.carsaver.partner.web;


import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.partner.service.InShowroomUsersService;
import com.carsaver.partner.web.api.user.elasticsearch.DealerUserElasticSearchService;
import com.carsaver.partner.web.api.user.elasticsearch.model.InShowRoomUsers;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@ExtendWith(MockitoExtension.class)
public class InShowroomUsersControllerTest {

    private MockMvc mockMvc;

    @Mock
    private DealerUserElasticSearchService dealerUserElasticSearchService;

    @Mock
    private InShowroomUsersService inShowroomUsersService;

    @InjectMocks
    private InShowroomUsersController inShowroomUsersController;

    @BeforeEach
    void setup() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(inShowroomUsersController)
            .setMessageConverters(new MappingJackson2HttpMessageConverter())
            .build();
    }

    @Test
    public void testGetInShowroomUsersDetails() throws Exception {
        String dealerId = "dealer123";

        SearchResults<UserAndProspectDoc> searchResults = mock(SearchResults.class);

        InShowRoomUsers inShowRoomUser = new InShowRoomUsers();
        List<InShowRoomUsers> inShowRoomUsersList = Arrays.asList(inShowRoomUser);

        when(dealerUserElasticSearchService.searchInShowroomUsers(dealerId)).thenReturn(searchResults);
        when(inShowroomUsersService.findInShowroomUsers(searchResults, dealerId)).thenReturn(inShowRoomUsersList);

        mockMvc.perform(get("/api/showroom-users/dealers/{dealerId}", dealerId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$[0]").exists());

        verify(dealerUserElasticSearchService).searchInShowroomUsers(dealerId);
        verify(inShowroomUsersService).findInShowroomUsers(searchResults, dealerId);
    }

    @Test
    public void testGetInShowroomUsersDetails_DealerConnections() throws Exception {
        String dealerId = "dealer123";


        InShowRoomUsers inShowRoomUser = new InShowRoomUsers();
        List<InShowRoomUsers> inShowRoomUsersList = Arrays.asList(inShowRoomUser);

        when(inShowroomUsersService.findInShowroomUsers(anyString())).thenReturn(inShowRoomUsersList);

        mockMvc.perform(get("/api/showroom-users/v2/dealers/{dealerId}", dealerId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$").isArray())
            .andExpect(jsonPath("$[0]").exists());

        verify(inShowroomUsersService).findInShowroomUsers(dealerId);
    }


}
