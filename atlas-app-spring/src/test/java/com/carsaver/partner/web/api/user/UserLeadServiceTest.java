package com.carsaver.partner.web.api.user;

import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.elasticsearch.model.LeadCertificate;
import com.carsaver.elasticsearch.model.LeadDoc;
import com.carsaver.elasticsearch.service.LeadDocService;
import com.carsaver.magellan.api.deal.DealSheetService;
import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.user.UserLead;
import com.carsaver.partner.service.adf.AdfService;
import com.carsaver.search.support.SearchResults;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class UserLeadServiceTest {

    private UserClient userClient = mock(UserClient.class);
    private CertificateClient certificateClient = mock(CertificateClient.class);
    private LeadDocService leadDocService = mock(LeadDocService.class);
    private DealSheetService dealSheetService = mock(DealSheetService.class);
    private AdfService adfService = mock(AdfService.class);

    private UserLeadService userLeadService = new UserLeadService(userClient, leadDocService, adfService, certificateClient, dealSheetService);

    @Test
    void getUserLeadData_adfServiceCalledCorrectly() {
        String userId = "123";
        List<String> dealerIds = List.of("dealer1", "dealer2");
        Pageable pageable = Pageable.unpaged();

        UserView user = new UserView();
        user.setId(userId);

        LeadDoc leadDoc = buildLeadDoc();

        //add certificate to leadDoc
        LeadCertificate leadCertificate = new LeadCertificate();
        leadCertificate.setId(1221);
        leadDoc.setCertificate(leadCertificate);

        CertificateView certificateView = new CertificateView();
        certificateView.setId(1221);
        certificateView.setInventoryId("12548");

        SearchResults<LeadDoc> searchResults = mock(SearchResults.class);
        when(searchResults.getContent()).thenReturn(List.of(leadDoc));

        when(userClient.findById(userId)).thenReturn(user);
        when(certificateClient.findById(anyInt())).thenReturn(certificateView);
        when(leadDocService.search(any(), any(Pageable.class))).thenReturn(searchResults);
        when(dealSheetService.toDealJacket(any())).thenReturn(Optional.of(new DealJacket()));
        when(adfService.fetchDealerLeadTransactionAdf(eq("leadDocId123"))).thenReturn("mockAdfPayload");

        UserLead result = userLeadService.getUserLeads(dealerIds, userId, pageable);

        verify(adfService).fetchDealerLeadTransactionAdf("leadDocId123");
        verify(certificateClient).findById(certificateView.getId());
        verify(dealSheetService).toDealJacket(certificateView);
        assertNotNull(result);
        assertFalse(result.getLeads().isEmpty());
        assertEquals("mockAdfPayload", result.getLeads().get(0).getAdfPayload());
    }

    @Test
    void getUserLeadData_noLeadsFound() {
        String userId = "123";
        List<String> dealerIds = List.of("dealer1", "dealer2");
        Pageable pageable = Pageable.unpaged();

        SearchResults<LeadDoc> emptyResults = mock(SearchResults.class);
        when(emptyResults.getContent()).thenReturn(Collections.emptyList());
        when(leadDocService.search(any(), eq(pageable))).thenReturn(emptyResults);

        UserLead result = userLeadService.getUserLeads(dealerIds, userId, pageable);

        verify(adfService, never()).fetchDealerLeadTransactionAdf(anyString());
        assertNull(result);
    }

    @Test
    void getUserLeads_noCertificateInLead() {
        String userId = "123";
        List<String> dealerIds = List.of("dealer1");
        Pageable pageable = Pageable.unpaged();

        LeadDoc leadDoc = buildLeadDoc();
        leadDoc.setId("leadDocId124");

        SearchResults<LeadDoc> searchResults = mock(SearchResults.class);
        when(searchResults.getContent()).thenReturn(List.of(leadDoc));
        when(leadDocService.search(any(), any(Pageable.class))).thenReturn(searchResults);
        when(adfService.fetchDealerLeadTransactionAdf(eq("leadDocId124"))).thenReturn("mockAdfPayload");

        UserLead result = userLeadService.getUserLeads(dealerIds, userId, pageable);

        verify(certificateClient, never()).findById(anyInt());
        verify(dealSheetService, never()).toDealJacket(any());
        assertNotNull(result);
        assertFalse(result.getLeads().isEmpty());
        assertEquals("mockAdfPayload", result.getLeads().get(0).getAdfPayload());
    }

    @Test
    void getUserLeads_dummyCertificateInLead() {
        String userId = "123";
        List<String> dealerIds = List.of("dealer1");
        Pageable pageable = Pageable.unpaged();

        LeadDoc leadDoc = buildLeadDoc();

        //add certificate to leadDoc
        LeadCertificate leadCertificate = new LeadCertificate();
        leadCertificate.setId(1221);
        leadDoc.setCertificate(leadCertificate);

        CertificateView certificateView = new CertificateView();
        certificateView.setId(1221);

        SearchResults<LeadDoc> searchResults = mock(SearchResults.class);
        when(searchResults.getContent()).thenReturn(List.of(leadDoc));
        when(leadDocService.search(any(), any(Pageable.class))).thenReturn(searchResults);
        when(adfService.fetchDealerLeadTransactionAdf(eq("leadDocId123"))).thenReturn("mockAdfPayload");
        when(certificateClient.findById(anyInt())).thenReturn(certificateView);

        UserLead result = userLeadService.getUserLeads(dealerIds, userId, pageable);

        verify(certificateClient, times(1)).findById(certificateView.getId());
        verify(dealSheetService, never()).toDealJacket(certificateView);
        assertNotNull(result);
        assertFalse(result.getLeads().isEmpty());
        assertEquals("mockAdfPayload", result.getLeads().get(0).getAdfPayload());
    }

    @NotNull
    private static LeadDoc buildLeadDoc() {
        LeadDoc leadDoc = new LeadDoc();
        leadDoc.setId("leadDocId123");
        leadDoc.setLeadType("connection");

        DealerDoc dealerDoc = new DealerDoc();
        dealerDoc.setTimeZone("America/Los_Angeles");
        leadDoc.setDealer(dealerDoc);
        return leadDoc;
    }
}
