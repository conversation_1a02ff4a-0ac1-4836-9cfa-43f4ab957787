package com.carsaver.partner.web.api;

import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.elasticsearch.service.VehicleDocService;
import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.TinyUrlClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.TinyUrlView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.InventoryConfig;
import com.carsaver.partner.client.campaign.suppressions.CampaignModelSuppression;
import com.carsaver.partner.client.campaign.suppressions.CampaignModelSuppressionClient;
import com.carsaver.partner.elasticsearch.criteria.VehicleDealerProgramPair;
import com.carsaver.partner.search.facets.VehicleDocFacets;
import com.carsaver.partner.service.DealerProgramService;
import com.carsaver.partner.web.api.VehicleApiController.DealerVehicleSearchCriteria;
import com.carsaver.search.support.DocFacets;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.PageMetadata;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.domain.Pageable;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@WebMvcTest(useDefaultFilters = false, includeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = VehicleApiController.class))
@ActiveProfiles("test")
class VehicleApiControllerTest {
    public static final PodamFactory FACTORY = new PodamFactoryImpl();

    @MockBean
    private VehicleDocService docService;

    @MockBean
    TinyUrlClient tinyUrlClient;

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DealerProgramService dealerProgramService;

    @MockBean
    private SecurityHelperService securityHelperService;

    @MockBean
    private CampaignModelSuppressionClient campaignModelSuppressionClient;

    @MockBean
    private CampaignClient campaignClient;

    @Test
    void findTest() throws Exception {
        final DocFacets facets = FACTORY.manufacturePojo(DocFacets.class);
        final VehicleDoc doc1 = FACTORY.manufacturePojo(VehicleDoc.class);
        final Collection<VehicleDoc> content = Collections.checkedCollection(Collections.singletonList(doc1),VehicleDoc.class);
        final PageMetadata pageMetadata = new PageMetadata(1, 1, 1);
        final SearchResults<VehicleDoc> result = FACTORY.manufacturePojo(SearchResults.class, VehicleDoc.class);
        result.setContent(content);
        result.setPageMetadata(pageMetadata);
        result.setFacets(facets);

        Mockito.when(docService.search(any(), (Pageable) any())).thenReturn(result);

        MockHttpSession sessionAttr = getMockHttpSession();

        mockMvc.perform(post("/api/vehicles/search")
                        .queryParam("page", "1")
                        .queryParam("sort", "year,desc")
                        .queryParam("size", "20")
                        .content("{\"searchMethods\":{},\"includes\":null,\"excludes\":null}")
                        .session(sessionAttr)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // OLD ENDPOINT - 404
        mockMvc.perform(post("/api/dealer/dealerId/vehicles/search")
                        .queryParam("page", "1")
                        .queryParam("sort", "year,desc")
                        .queryParam("size", "20")
                        .content("{\"searchMethods\":{},\"includes\":null,\"excludes\":null}")
                        .session(sessionAttr)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    private MockHttpSession getMockHttpSession() {
        MockHttpSession sessionAttr = new MockHttpSession();
        DealerView dealer1 = new DealerView();
        dealer1.setId("DEALER_ID_1");
        sessionAttr.putValue("userDealerAccessList", List.of(dealer1));
        return sessionAttr;
    }

    @Test
    void findFacetTest() throws Exception {
        VehicleDocFacets facets = FACTORY.manufacturePojo(VehicleDocFacets.class);
        FacetInfoResult facetInfoResult = FacetInfoResult.builder().results(facets).build();
        Mockito.when(docService.facets(any(), any(), any())).thenReturn(facetInfoResult);

        MockHttpSession sessionAttr = getMockHttpSession();
        mockMvc.perform(post("/api/vehicles/facet_info")
                        .queryParam("id", "1")
                        .content("{\"searchMethods\":{},\"includes\":null,\"excludes\":null}")
                        .session(sessionAttr)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // OLD ENDPOINT - 404
        mockMvc.perform(post("/api/dealer/dealerId/vehicles/facet_info")
                        .queryParam("page", "1")
                        .queryParam("sort", "year,desc")
                        .queryParam("size", "20")
                        .content("{\"searchMethods\":{},\"includes\":null,\"excludes\":null}")
                        .session(sessionAttr)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void shortVehicleUrlTest() throws Exception {
        TinyUrlView tinyUrlView = FACTORY.manufacturePojo(TinyUrlView.class);
        Mockito.when(tinyUrlClient.create(any())).thenReturn(tinyUrlView);

        mockMvc.perform(post("/api/vehicles/shortVehicleURL")
                        .content("{\"targetUrl\":\"www.carsaver.com\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(tinyUrlView.getRequestUrl()));

        // OLD ENDPOINT - 404
        mockMvc.perform(post("/api/dealer/dealerId/vehicles/shortVehicleURL")
                        .content("{\"targetUrl\":\"www.carsaver.com\"}")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void handleModelSuppression() {
        VehicleApiController vehicleApiController = new VehicleApiController();
        ReflectionTestUtils.setField(vehicleApiController, "campaignModelSuppressionClient", campaignModelSuppressionClient);
        ReflectionTestUtils.setField(vehicleApiController, "campaignClient", campaignClient);

        String programIdA = "programIdA";
        String programIdB = "programIdB";

        VehicleDealerProgramPair pairA = VehicleDealerProgramPair.builder().onlyLivePrograms(Set.of(programIdA)).build();
        VehicleDealerProgramPair pairB = VehicleDealerProgramPair.builder().onlyLivePrograms(Set.of(programIdA, programIdB)).build();

        DealerVehicleSearchCriteria searchCriteria = new DealerVehicleSearchCriteria();

        CampaignView campaignA = new CampaignView();
        campaignA.setId("campaignIdA");

        CampaignView campaignB = new CampaignView();
        campaignB.setId("campaignIdB");

        when(campaignClient.findByProgramId(programIdA)).thenReturn(CollectionModel.of(List.of(campaignA, campaignB)));
        when(campaignClient.findByProgramId(programIdB)).thenReturn(CollectionModel.of(List.of(campaignA, campaignB)));

        CampaignModelSuppression modelSuppressionA = new CampaignModelSuppression();
        modelSuppressionA.setModelId(1);

        CampaignModelSuppression modelSuppressionB = new CampaignModelSuppression();
        modelSuppressionB.setModelId(2);

        CampaignModelSuppression modelSuppressionC = new CampaignModelSuppression();
        modelSuppressionC.setModelId(3);

        when(campaignModelSuppressionClient.findByCampaignId(campaignA.getId())).thenReturn(CollectionModel.of(List.of(modelSuppressionA, modelSuppressionB)));
        when(campaignModelSuppressionClient.findByCampaignId(campaignB.getId())).thenReturn(CollectionModel.of(List.of(modelSuppressionB, modelSuppressionC)));

        vehicleApiController.handleModelSuppression(searchCriteria, List.of(pairA, pairB));

        assertThat(searchCriteria.getSuppressionModelIds()).isNotNull().containsExactlyInAnyOrder(1, 2, 3);
    }

    @Test
    void isNonLTWVehicleEnabled() {
        VehicleApiController vehicleApiController = new VehicleApiController();
        ReflectionTestUtils.setField(vehicleApiController, "campaignClient", campaignClient);

        // no programId
        assertThat(vehicleApiController.isNonLTWVehicleEnabled(new DealerVehicleSearchCriteria())).isFalse();

        String programId = "f208d5b0-1103-43a1-b41a-e7635d2991a5";
        DealerVehicleSearchCriteria searchCriteria = new DealerVehicleSearchCriteria();
        searchCriteria.setProgramIds(List.of(programId));

        // no campaign found
        assertThat(vehicleApiController.isNonLTWVehicleEnabled(searchCriteria)).isFalse();

        CampaignView campaignView = new CampaignView();
        when(campaignClient.findByProgramId(programId)).thenReturn(CollectionModel.of(List.of(campaignView)));

        // campaign without NonLTWVehicleEnabled configuration
        assertThat(vehicleApiController.isNonLTWVehicleEnabled(searchCriteria)).isFalse();

        InventoryConfig inventoryConfig = new InventoryConfig();
        inventoryConfig.setNonLTWVehicleEnabled(true);
        campaignView.setInventoryConfig(inventoryConfig);

        assertThat(vehicleApiController.isNonLTWVehicleEnabled(searchCriteria)).isTrue();
    }
}
