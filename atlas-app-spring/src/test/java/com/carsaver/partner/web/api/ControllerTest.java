package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.partner.filter.DealerUserAccessFilter;
import com.carsaver.partner.model.ProgramModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.ActiveProfiles;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.util.List;

@ActiveProfiles("test")
public class ControllerTest {

    static final PodamFactory FACTORY = new PodamFactoryImpl();
    static final ObjectMapper mapper = new ObjectMapper();

    MockHttpSession getMockHttpSessionForDealerUser() {
        MockHttpSession sessionAttr = new MockHttpSession();
        DealerView dealer1 = new DealerView();
        dealer1.setId("DEALER_ID_1");
        sessionAttr.putValue("userDealerAccessList", List.of(dealer1));
        return sessionAttr;
    }

    MockHttpSession getMockHttpSessionForProgramUser() {
        MockHttpSession sessionAttr = new MockHttpSession();
        DealerView dealer1 = new DealerView();
        dealer1.setId("DEALER_ID_1");
        sessionAttr.putValue(DealerUserAccessFilter.DEALER_LIST, List.of(dealer1));

        ProgramModel programModel1 = new ProgramModel();
        programModel1.setId("PROGRAM_1");
        programModel1.setName("PROGRAM_NAME");
        sessionAttr.putValue(DealerUserAccessFilter.PROGRAMS_LIST, List.of(programModel1));

        return sessionAttr;
    }
}
