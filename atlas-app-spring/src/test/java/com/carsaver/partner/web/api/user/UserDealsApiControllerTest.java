package com.carsaver.partner.web.api.user;

import com.carsaver.partner.exception.DealNotFoundException;
import com.carsaver.partner.model.desking.ClonedDealResponse;
import com.carsaver.partner.service.desking.CloneDealService;
import com.carsaver.partner.web.advice.RestGlobalExceptionHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class UserDealsApiControllerTest {
    private static final String DEAL_NOTES_URL = "http://localhost:3001";
    @InjectMocks
    UserDealsApiController userDealsApiController;

    CloneDealService cloneDealService = mock(CloneDealService.class);
    MockMvc mockMvc;
    ObjectMapper objectMapper;
    RestGlobalExceptionHandler exceptionHandler = mock(RestGlobalExceptionHandler.class);

    @BeforeEach
    void init(){
        this.mockMvc = MockMvcBuilders.standaloneSetup(userDealsApiController).setControllerAdvice(exceptionHandler).build();
        objectMapper = new ObjectMapper();
        ReflectionTestUtils.setField(cloneDealService, "dealerServiceUrl", DEAL_NOTES_URL);
    }

    @Test
    void retrieveClonedDealById_SuccessTest() throws Exception {
        ClonedDealResponse clonedDealResponse = buildCloneDealResponse();
        when(cloneDealService.retrieveClonedDealById(anyLong())).thenReturn(clonedDealResponse);
        mockMvc.perform(get("/api/dealer/clone/{id}", 1))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(objectMapper.writeValueAsString(clonedDealResponse)));
    }

    @Test
    void retrieveClonedDealById_DealNotFoundExceptionTest() throws Exception {
        when(cloneDealService.retrieveClonedDealById(anyLong())).thenThrow(new DealNotFoundException());
        mockMvc.perform(get("/api/dealer/clone/{id}", 1211))
            .andExpect(status().is(HttpStatus.NOT_FOUND.value()));
    }

    @Test
    void retrieveClonedDealByCertificateId_SuccessTest() throws Exception {
        ClonedDealResponse response = buildCloneDealResponse();
        when(cloneDealService.retrieveClonedDealByCertificateId(anyInt())).thenReturn(response);
        mockMvc.perform(get("/api/dealer/clone/certificate/{certificateId}", 1211))
            .andExpect(status().isOk())
            .andExpect(content().json(objectMapper.writeValueAsString(response)));

    }

    @Test
    void retrieveClonedDealByCertificateId_DealNotFoundExceptionTest() throws Exception {
        when(cloneDealService.retrieveClonedDealByCertificateId(anyInt())).thenThrow( new DealNotFoundException());
        mockMvc.perform(get("/api/dealer/clone/certificate/{certificateId}", 1211))
            .andExpect(status().is(HttpStatus.NOT_FOUND.value()));

    }

    ClonedDealResponse buildCloneDealResponse() {
        ClonedDealResponse result = ClonedDealResponse.builder()
            .capCostReduction(new ClonedDealResponse.CapCostReduction())
            .id(1001L)
            .clonedBy("Test")
            .dealerId(UUID.randomUUID().toString())
            .certificateId(10000)
            .dueAtSigning(new ClonedDealResponse.DueAtSigning())
            .dueAtSigningTotal(100.0d)
            .financeDetails(new ClonedDealResponse.FinanceDetails())
            .capCostReduction(new ClonedDealResponse.CapCostReduction())
            .capCostReductionTotal(100.0)
            .customerRebates(new ClonedDealResponse.CustomerRebates())
            .rebatesTotal(100.0)
            .inceptions(new ClonedDealResponse.Inceptions())
            .inceptionsTotal(100.0)
            .grossCapitalCost(new ClonedDealResponse.GrossCapitalCost())
            .grossCapitalCostTotal(100.0)
            .monthlyPayment(new ClonedDealResponse.MonthlyPayment())
            .monthlyPaymentTotal(100.0)
            .tradeAllowance(new ClonedDealResponse.TradeAllowance())
            .tradeAllowanceTotal(100.0)
            .build();
        return result;
    }
}
