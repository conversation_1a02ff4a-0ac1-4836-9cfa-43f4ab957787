package com.carsaver.partner.web.api;

import com.carsaver.magellan.auth.CarSaverJWTToken;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.partner.model.DealProgramAndDomain;
import com.carsaver.partner.service.ProgramApiService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.nio.file.Files;
import java.security.Principal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@WebMvcTest(useDefaultFilters = false, includeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = ProgramApiController.class))
@ActiveProfiles("test")
public class ProgramApiControllerTest {

    public static final PodamFactory FACTORY = new PodamFactoryImpl();
    public static final String PROGRAM_ID = "PROGRAM_ID";
    public static final String DEALER_ID = "dealer id";
    public static final String USER_ID = "USER_ID";
    public static final String DOMAIN = "domain";

    @MockBean
    private ProgramClient programClient;
    @MockBean
    private UserClient userClient;

    @MockBean
    private ProgramApiService programApiService;

    @InjectMocks
    private ProgramApiController programApiController;

    @Autowired
    private MockMvc mockMvc;

    @Value("${dynamoDB.dealerTable}")
    private String dealerTable;

    @Test
    void getDealProgram() throws Exception {
        SecurityContextHolder.setContext(new SecurityContextImpl((Authentication) createPrincipalForProgramUser()));
        String response = Files.readString(new ClassPathResource("json/programApiController/dealProgramDomain.json").getFile().toPath());

        DealProgramAndDomain dealProgramAndDomain = DealProgramAndDomain
            .builder().programId(PROGRAM_ID)
            .domain(DOMAIN)
            .programName("test")
            .build();

        final String userId = USER_ID;
        when(programApiService.getDealProgramAndDomain(userId, PROGRAM_ID, DEALER_ID, dealerTable)).thenReturn(dealProgramAndDomain);

        mockMvc.perform(get("/api/deal-program-domain?campaignId=" + PROGRAM_ID + "&dealerId=" + DEALER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(response));
    }

    @Test
    void testGetDealProgramByUserId() throws Exception {
        SecurityContextHolder.setContext(new SecurityContextImpl((Authentication) createPrincipalForProgramUser()));
        String response = Files.readString(new ClassPathResource("json/programApiController/dealProgramDomain.json").getFile().toPath());

        DealProgramAndDomain dealProgramAndDomain = DealProgramAndDomain
            .builder().programId(PROGRAM_ID)
            .domain(DOMAIN)
            .programName("test")
            .build();

        final CampaignView campaign = FACTORY.manufacturePojo(CampaignView.class);
        campaign.setId("campaignId");
        campaign.setDomain(DOMAIN);
        campaign.setProgramId(PROGRAM_ID);

        final Optional<ProgramView> program = Optional.of(FACTORY.manufacturePojo(ProgramView.class));
        program.get().setId(PROGRAM_ID);
        program.get().setName("test");
        ProductView product = new ProductView();
        product.setId(103);
        program.get().setProduct(product);

        UserView user = spy(new UserView());
        user.setId(USER_ID);

        when(userClient.findById(USER_ID)).thenReturn(user);
        when(user.getCampaign()).thenReturn(campaign);
        when(programClient.findById(any())).thenReturn(program);

        when(programApiService.getDealProgramAndDomain(eq(USER_ID),eq(DEALER_ID), any())).thenReturn(dealProgramAndDomain);
        mockMvc.perform(get("/api/deal-program-domain/" + USER_ID + "?dealerId=" + DEALER_ID)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(response));
    }

    private Principal createPrincipalForProgramUser() {
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setId(USER_ID);

        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_PROGRAM"));
        return new CarSaverJWTToken(tokenResponse, authorities);
    }
}
