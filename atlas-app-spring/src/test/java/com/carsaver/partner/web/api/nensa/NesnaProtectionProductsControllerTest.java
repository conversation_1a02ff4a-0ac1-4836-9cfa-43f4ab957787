package com.carsaver.partner.web.api.nensa;

import com.carsaver.partner.model.nensa.DealerNesnaProductRequest;
import com.carsaver.partner.model.nensa.DealerNesnaProductResponse;
import com.carsaver.partner.model.nensa.DealerNesnaProducts;
import com.carsaver.partner.service.nesna.NesnaProtectionProductsService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
class NesnaProtectionProductsControllerTest {

    private MockMvc mockMvc;

    private NesnaProtectionProductsService nesnaProtectionProductsService;

    private ObjectMapper objectMapper;

    @InjectMocks
    private NesnaProtectionProductsController nesnaProtectionProductsController;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        nesnaProtectionProductsService = mock(NesnaProtectionProductsService.class);
        nesnaProtectionProductsController = new NesnaProtectionProductsController(nesnaProtectionProductsService);
        mockMvc = MockMvcBuilders.standaloneSetup(nesnaProtectionProductsController)
            .defaultRequest(post("/").contentType(MediaType.APPLICATION_JSON))
            .defaultRequest(get("/").accept(MediaType.APPLICATION_JSON))
            .build();

    }


    @Test
    void testSaveProtectionProducts() throws Exception {
        DealerNesnaProducts product = DealerNesnaProducts.builder()
            .id("prod1")
            .dealerId("dealer1")
            .masterNesnaProductId("master1")
            .enabled(true)
            .name("Test Product")
            .adjustedValue("1000")
            .adjustmentType("percentage")
            .markupAmount(10)
            .levels(Collections.emptyList())
            .productTypeCode("type1")
            .productTypeDescription("Type 1 Description")
            .coverageGroupCode("group1")
            .coverageGroupDescription("Group 1 Description")
            .description("Product Description")
            .icon("icon.png")
            .brochureThumbnails("thumbnail.png")
            .videoLink("video.mp4")
            .stateSpecificDetails("Details")
            .build();

        DealerNesnaProductRequest request = DealerNesnaProductRequest.builder()
            .nesnaProtectionProducts(Collections.singletonList(product))
            .build();

        DealerNesnaProductRequest response = DealerNesnaProductRequest.builder()
            .nesnaProtectionProducts(Collections.singletonList(product))
            .build();

        when(nesnaProtectionProductsService.saveProtectionProducts(anyString(), any(DealerNesnaProductRequest.class)))
            .thenReturn(response);

        mockMvc.perform(post("/api/nesna/dealers/{dealerId}/nesna-products", "123")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.nesnaProtectionProducts[0].name").value("Test Product"));
    }

    @Test
    void testRetrieveNesnaProtectionProducts() throws Exception {
        DealerNesnaProducts product = DealerNesnaProducts.builder()
            .id("prod1")
            .dealerId("dealer1")
            .masterNesnaProductId("master1")
            .enabled(true)
            .name("Test Product")
            .adjustedValue("1000")
            .adjustmentType("percentage")
            .markupAmount(10)
            .levels(Collections.emptyList())
            .productTypeCode("type1")
            .productTypeDescription("Type 1 Description")
            .coverageGroupCode("group1")
            .coverageGroupDescription("Group 1 Description")
            .description("Product Description")
            .icon("icon.png")
            .brochureThumbnails("thumbnail.png")
            .videoLink("video.mp4")
            .stateSpecificDetails("Details")
            .build();

        DealerNesnaProductResponse response = DealerNesnaProductResponse.builder()
            .dealerProductDetailsResponse(Collections.singletonList(product))
            .build();

        when(nesnaProtectionProductsService.retrieveNesnaProtectionProducts(anyString()))
            .thenReturn(response);

        mockMvc.perform(get("/api/nesna/dealers/{dealerId}/nesna-products", "123"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.dealerProductDetailsResponse[0].name").value("Test Product"));
    }
}
