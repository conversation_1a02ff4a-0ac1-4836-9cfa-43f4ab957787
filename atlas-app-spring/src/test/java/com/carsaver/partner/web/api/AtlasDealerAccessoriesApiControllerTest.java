package com.carsaver.partner.web.api;

import com.carsaver.accessories.api.AccessoryDetail;
import com.carsaver.accessories.api.DealerAccessory;
import com.carsaver.partner.exception.InvalidRequestException;
import com.carsaver.partner.model.dealer.ModelDetails;
import com.carsaver.partner.service.AtlasDealerAccessoriesService;
import com.carsaver.partner.web.advice.RestGlobalExceptionHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.sql.Date;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.IntStream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
class AtlasDealerAccessoriesApiControllerTest {

    private static final String DEALER_ID = "5308" ;
    private static final String MODEL_LINE_CODE = "ALT";
    private static final String MODEL_YEAR = "MY20";
    private static final String PART_NUMBER = "T99J1-6CA5A";
    private static final String NAME = "Trash Bin - Small";
    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private final AtlasDealerAccessoriesService atlasDealerAccessoriesService=mock(AtlasDealerAccessoriesService.class);

    @InjectMocks
    private  AtlasDealerAccessoriesApiController dealerAccessoriesApiController;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(dealerAccessoriesApiController).setControllerAdvice(new RestGlobalExceptionHandler()).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("Get model List Test")
    void getModelListTest() throws Exception {
        int elements = 5;
        List<ModelDetails> modelCodeList = getModelCodeList(elements);

        when(atlasDealerAccessoriesService.getModelList()).thenReturn(modelCodeList);

        mockMvc.perform(get("/api/dealer/accessories/models",DEALER_ID, MODEL_LINE_CODE, MODEL_YEAR)
        .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(modelCodeList)));

    }


    @Test
    @DisplayName("Get accessories by Model Code and year test")
    void getAccessoriesByModelCodeAndYearTest() throws Exception {
        int elements = 5;
        List<DealerAccessory> dealerAccessoryResponseList = getDealerAccessoryList(elements);

        when(atlasDealerAccessoriesService.getAccessoriesByModelLineCodeAndYear(anyString(), anyString(), anyString())).thenReturn(dealerAccessoryResponseList);

        mockMvc.perform(get("/api/dealer/accessories/{dealerId}/{modelLineCode}/{year}", DEALER_ID, MODEL_LINE_CODE, MODEL_YEAR)
        .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(dealerAccessoryResponseList)));

    }

    @Test
    @DisplayName(" Enable or Disable dealer accessory success test")
    void enableOrDisableAccessoryForDealerSuccessTest() throws Exception {
        DealerAccessory result = getDealerAccessoryResponse();
        AccessoryDetail request = getAccessoryDetail();

        when(atlasDealerAccessoriesService.enableOrDisableAccessoryForDealer(anyString(),any(AccessoryDetail.class))).thenReturn(result);

        mockMvc.perform(put("/api/dealer/accessories/{dealerId}", DEALER_ID)
        .contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().isOk())
        .andExpect(content().contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(objectMapper.writeValueAsString(result)));

    }

    @Test
    @DisplayName(" Enable or Disable dealer accessory Invalid Request test")
    void enableOrDisableAccessoryForDealerInvalidRequestTest() throws Exception {
        AccessoryDetail request = getAccessoryDetail();

        when(atlasDealerAccessoriesService.enableOrDisableAccessoryForDealer(anyString(), any(AccessoryDetail.class))).thenThrow(new InvalidRequestException());

        mockMvc.perform(put("/api/dealer/accessories/{dealerId}", DEALER_ID)
        .contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(request)))
        .andExpect(status().is4xxClientError());

    }

    private AccessoryDetail getAccessoryDetail() {
        AccessoryDetail request = AccessoryDetail.builder().partNumber(PART_NUMBER).modelLineCode(MODEL_LINE_CODE).modelYear(MODEL_YEAR).toggle(Boolean.FALSE).build();
        return request;
    }

    private List<ModelDetails> getModelCodeList(int elements) {
        List<ModelDetails> modelDetailsList=new ArrayList<>();
        IntStream.range(0,elements).forEach( i -> {
            ModelDetails currentModel = ModelDetails.builder()
            .modelLineCode(MODEL_LINE_CODE + i)
            .modelLineName(NAME + i).build();

            modelDetailsList.add(currentModel);
        });

        return modelDetailsList;
    }

    private DealerAccessory getDealerAccessoryResponse() {
        return DealerAccessory.builder().dealerId(DEALER_ID)
        .partNumber(PART_NUMBER)
        .name(NAME)
        .isAvailable(Boolean.TRUE)
        .isEnabled(Boolean.FALSE)
        .modelLineCode(MODEL_LINE_CODE)
        .build();
    }

    public List<DealerAccessory> getDealerAccessoryList(Integer elements){

        List<DealerAccessory> dealerAccessories=new ArrayList<>();

        IntStream.range(0,elements).forEach( i -> {
            DealerAccessory currentAccessory = DealerAccessory.builder()
                .id(UUID.randomUUID().toString())
                .dealerId(DEALER_ID)
                .isAvailable(Boolean.TRUE)
                .isEnabled(Boolean.TRUE)
                .modelLineCode(MODEL_LINE_CODE)
                .modelYear("MY" + (22 - i))
                .partNumber(PART_NUMBER + i)
                .createdDate(Date.from(Instant.now()))
                .updatedDate(Date.from(Instant.now()))
                .build();

            dealerAccessories.add(currentAccessory);
        });

        return  dealerAccessories;
    }

}
