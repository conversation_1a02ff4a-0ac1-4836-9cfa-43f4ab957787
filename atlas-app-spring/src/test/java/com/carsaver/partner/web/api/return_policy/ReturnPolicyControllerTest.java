package com.carsaver.partner.web.api.return_policy;

import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.partner.model.return_policy.ReturnPolicyRequest;
import com.carsaver.partner.model.return_policy.ReturnPolicyResponse;
import com.carsaver.partner.service.return_policy.ReturnPolicyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@AutoConfigureMockMvc(addFilters = false)
@ExtendWith(MockitoExtension.class)
class ReturnPolicyControllerTest {
    private static final String DEALER_ID = UUID.randomUUID().toString();
    private static final String PROGRAM_ID = UUID.randomUUID().toString();
    private static final String BUY_AT_HOME_PROGRAM_ID = "5e922fe4-e1e9-468c-b100-5b8f7cffcef3";
    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Mock
    private ReturnPolicyService dealerReturnPolicyService;

    @InjectMocks
    private ReturnPolicyController dealerReturnPolicyController;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        dealerReturnPolicyController = new ReturnPolicyController(dealerReturnPolicyService);
        mockMvc = MockMvcBuilders.standaloneSetup(dealerReturnPolicyController).build();
        ReflectionTestUtils.setField(dealerReturnPolicyController, "buyAtHomeProgramId", BUY_AT_HOME_PROGRAM_ID);
    }

    @Test
    void retrievePolicyByDealerIdAndProgramId() throws Exception {
        ReturnPolicyResponse policy = buildReturnPolicy();

        when(dealerReturnPolicyService.retrieveReturnPoliciesForDealer(anyString(), anyString())).thenReturn(policy);
        mockMvc.perform(MockMvcRequestBuilders.get("/api/return-policy/{dealerId}", DEALER_ID)
                .contentType(MediaType.APPLICATION_JSON
                ))
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

   @Test
    void retrievePolicyByDealerIdAndProgramIdNotFoundExceptionTest() throws Exception {

        when(dealerReturnPolicyService.retrieveReturnPoliciesForDealer(anyString(), anyString())).thenThrow( new NotFoundException());
        mockMvc.perform(MockMvcRequestBuilders.get("/api/return-policy/{dealerId}", DEALER_ID, PROGRAM_ID)
                .contentType(MediaType.APPLICATION_JSON
                ))
            .andExpect(MockMvcResultMatchers.status().isNotFound());
    }

    @Test
    void upsertReturnPolicyTest() throws Exception {
        ReturnPolicyResponse dealerReturnPolicyRes = buildReturnPolicyResponse();
        when(dealerReturnPolicyService.upsertDealerReturnPolicy(any())).thenReturn(dealerReturnPolicyRes);
        mockMvc.perform(MockMvcRequestBuilders.post("/api/return-policy")
                .contentType(MediaType.APPLICATION_JSON
                ).content(objectMapper.writeValueAsString(buildDealerReturnPolicyRequest()))
            )
            .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void testRetrieveReturnPoliciesWithNullProgramId() {
        String dealerId = "testDealerId";
        ReturnPolicyResponse response = new ReturnPolicyResponse();
        when(dealerReturnPolicyService.retrieveReturnPoliciesForDealer(dealerId, BUY_AT_HOME_PROGRAM_ID)).thenReturn(response);

        dealerReturnPolicyController.retrieveReturnPolicies(dealerId, null);

        verify(dealerReturnPolicyService).retrieveReturnPoliciesForDealer(dealerId, BUY_AT_HOME_PROGRAM_ID);
    }

    @Test
    void testRetrieveReturnPoliciesWithEmptyProgramId() {
        String dealerId = "testDealerId";
        ReturnPolicyResponse response = new ReturnPolicyResponse();
        when(dealerReturnPolicyService.retrieveReturnPoliciesForDealer(dealerId, BUY_AT_HOME_PROGRAM_ID)).thenReturn(response);

        dealerReturnPolicyController.retrieveReturnPolicies(dealerId, "");

        verify(dealerReturnPolicyService).retrieveReturnPoliciesForDealer(dealerId, BUY_AT_HOME_PROGRAM_ID);
    }

    @Test
    void testRetrieveReturnPoliciesWithRequestedProgramId() {
        String dealerId = "testDealerId";
        ReturnPolicyResponse response = new ReturnPolicyResponse();
        when(dealerReturnPolicyService.retrieveReturnPoliciesForDealer(dealerId, PROGRAM_ID)).thenReturn(response);

        dealerReturnPolicyController.retrieveReturnPolicies(dealerId, PROGRAM_ID);

        verify(dealerReturnPolicyService).retrieveReturnPoliciesForDealer(dealerId, PROGRAM_ID);
    }
    @Test
    void testUpsertDealerReturnPolicySetsProgramIdWhenRequestIsNull() {
        ReturnPolicyRequest request = new ReturnPolicyRequest();
        request.setProgramId(null);

        ReturnPolicyResponse response = new ReturnPolicyResponse();
        when(dealerReturnPolicyService.upsertDealerReturnPolicy(any(ReturnPolicyRequest.class))).thenReturn(response);

        dealerReturnPolicyController.upsertDealerReturnPolicy(request);

        assertNotNull(request.getProgramId());
        assertEquals(BUY_AT_HOME_PROGRAM_ID, request.getProgramId());
    }

    @Test
    void testUpsertDealerReturnPolicySetsProgramIdWhenRequestIsEmpty() {
        ReturnPolicyRequest request = new ReturnPolicyRequest();
        request.setProgramId("");

        ReturnPolicyResponse response = new ReturnPolicyResponse();
        when(dealerReturnPolicyService.upsertDealerReturnPolicy(any(ReturnPolicyRequest.class))).thenReturn(response);

        dealerReturnPolicyController.upsertDealerReturnPolicy(request);

        assertNotNull(request.getProgramId());
        assertEquals(BUY_AT_HOME_PROGRAM_ID, request.getProgramId());
    }

    ReturnPolicyRequest buildDealerReturnPolicyRequest() {
        ReturnPolicyRequest request = new ReturnPolicyRequest();
        request.setDealerId(DEALER_ID);
        request.setProgramId(PROGRAM_ID);
        return request;
    }

    ReturnPolicyResponse buildReturnPolicy() {
        ReturnPolicyResponse policy = new ReturnPolicyResponse();
        policy.setPolicyName("TEST");
        policy.setDealerId(DEALER_ID);
        policy.setPolicySubTitle("TEST_SUBTITLE");
        policy.setPolicyDescription("TEST_DESCRIPTION");
        policy.setDays(29);
        policy.setProgramId(PROGRAM_ID);
        policy.setId(UUID.randomUUID().toString());
        policy.setIsActive(Boolean.TRUE);
        return policy;
    }

    ReturnPolicyResponse buildReturnPolicyResponse() {
        ReturnPolicyResponse policy =  new ReturnPolicyResponse();
        policy.setPolicyName("TEST");
        policy.setDealerId(DEALER_ID);
        policy.setPolicySubTitle("TEST_SUBTITLE");
        policy.setPolicyDescription("TEST_DESCRIPTION");
        policy.setDays(29);
        policy.setProgramId(PROGRAM_ID);
        policy.setId(UUID.randomUUID().toString());
        policy.setIsActive(Boolean.TRUE);
        return policy;
    }

}
