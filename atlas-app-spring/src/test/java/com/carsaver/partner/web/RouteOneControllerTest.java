package com.carsaver.partner.web;

import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.magellan.model.foundation.ProgramView;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RouteOneControllerTest {

    @Mock
    private ProgramClient programClient;

    @InjectMocks
    private RouteOneController controller;

    @Test
    void conversationIdTest() {
        ProductView productView = new ProductView();
        ProgramView programView = new ProgramView();
        LoanRequestView loanRequestView = new LoanRequestView();
        final CampaignView campaignView = CampaignView.builder().programId("123").build();
        productView.setId(103);
        programView.setProduct(productView);
        loanRequestView.setAppId(1);

        CertificateView certificate = mock(CertificateView.class);

        when(programClient.findById(any())).thenReturn(Optional.of(programView));
        when(certificate.getCampaign()).thenReturn(campaignView);

        ReflectionTestUtils.setField(controller, "isRouteOnePrefixEnabled", true);
        String routeOneConversionId = ReflectionTestUtils.invokeMethod(controller, "getRouteOneConversationId", Optional.of(certificate), loanRequestView);
        assertEquals("CSU-1-1", routeOneConversionId);

        ReflectionTestUtils.setField(controller, "isRouteOnePrefixEnabled", false);
        routeOneConversionId = ReflectionTestUtils.invokeMethod(controller, "getRouteOneConversationId", Optional.of(certificate), loanRequestView);
        assertEquals("CSH-1-1", routeOneConversionId);
    }
}
