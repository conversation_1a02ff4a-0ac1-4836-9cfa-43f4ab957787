package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.magellan.auth.CarSaverJWTToken;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.CustomerLastLoginDetails;
import com.carsaver.partner.search.facets.UserDocFacets;
import com.carsaver.partner.service.OnlineUsersService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchRequest;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.model.SearchMethod;
import com.carsaver.search.support.DocFacets;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.PageMetadata;
import com.carsaver.search.support.SearchResults;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextImpl;
import org.springframework.test.web.servlet.MockMvc;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.nio.file.Files;
import java.security.Principal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc(addFilters = false)
@WebMvcTest(useDefaultFilters = false, includeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = UserElasticSearchApiController.class))
class UserElasticSearchApiControllerTest extends BaseControllerTest {

    public static final String DEALER_ID_1 = "DEALER_ID_1";
    public static final String FACET_ID = "facetId";
    public static final String FORBIDDEN_DEALER_ID = "FORBIDDEN_DEALER_ID";
    public static final String USER_ID = "jdoe";
    public static final String STAGE_TITLE = "Test";

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DealerUserElasticSearchService dealerUserElasticSearchService;
    @MockBean
    private ProgramUserElasticSearchService programUserElasticSearchService;
    @MockBean
    private DealerAndProgramAccessHelper dealerAndProgramAccessHelper;
    @MockBean
    private SaleStagesStatsService saleStagesStatsService;
    @MockBean
    private TraitsService traitsService;
    @MockBean
    private SplitFeatureFlags splitFeatureFlags;
    @MockBean
    private UserClient userClient;
    @MockBean
    private OnlineUsersService onlineUsersService;
    @MockBean
    private LocaleService localeService;

    @SneakyThrows
    @Test
    void getUserLoginDetails() {
        var doc = new UserAndProspectDoc();
        when(dealerUserElasticSearchService.search(ArgumentMatchers.eq("userId"))).thenReturn(Optional.of(doc));

        var expectedLoginDetails = new CustomerLastLoginDetails(ZonedDateTime.now(), 5, true);
        when(onlineUsersService.buildCustomerLastLoginDetails(doc))
            .thenReturn(expectedLoginDetails);

        mockMvc.perform(get("/api/users/{userId}/login-details?dealerId=dealerId", "userId")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.lastLogin").isNotEmpty())
            .andExpect(jsonPath("$.webSiteVisit").value(5))
            .andExpect(jsonPath("$.online").value(true));

        when(dealerUserElasticSearchService.search(ArgumentMatchers.eq("userId")))
            .thenReturn(Optional.empty());

        when(dealerUserElasticSearchService.search(ArgumentMatchers.eq("userId"))).thenReturn(Optional.empty());
        mockMvc.perform(get("/api/users/{userId}/login-details?dealerId=dealerId", "userId")
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());

    }

    @Test
    void findFacetForDealerUserDealerLinkStatusTestV2() throws Exception {
        setSecurityContextForDealerUser();
        UserDocFacets facets = new UserDocFacets();
        List<TermFacet> termFacets = new ArrayList<TermFacet>();
        FacetInfoResult facetInfoResult = FacetInfoResult.builder().results(facets).build();

        Mockito.when(splitFeatureFlags.isEnableElasticNestedFields()).thenReturn(true);
        Mockito.when(dealerUserElasticSearchService.findFacetInfo(any(), any())).thenReturn(facetInfoResult);

        FacetInfoResult mockDataTable = Mockito.mock(FacetInfoResult.class);

        Mockito.when(mockDataTable.getResults()).thenReturn(termFacets);
        MockHttpSession sessionAttr = getMockHttpSessionForDealerUser();

        SearchRequest searchCriteria = new SearchRequest();
        searchCriteria
            .setDealerIds(DEALER_ID_1);
        searchCriteria.setSearchMethods(Map.of("status", SearchMethod.NEGATIVE));
        final String contentRequest = mapper.writeValueAsString(searchCriteria);
        final String resultHappyPath = Files.readString(new ClassPathResource("json/UserElasticSearchApiController/customersFacets.json").getFile().toPath()).trim();
        // Happy Path Passing Dealer
        mockMvc.perform(post("/api/users/v2/facet_info")
                .principal(createPrincipalForDealerUser())
                .param("id", FACET_ID)
                .content(contentRequest)
                .session(sessionAttr)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().string(resultHappyPath));

    }

    @Test
    void findFacetForProgramUserDealerLinkStatusTestV2() throws Exception {
        setSecurityContextForProgramUser();
        UserDocFacets facets = new UserDocFacets();
        List<TermFacet> termFacets = new ArrayList<TermFacet>();
        FacetInfoResult facetInfoResult = FacetInfoResult.builder().results(facets).build();

        Mockito.when(splitFeatureFlags.isEnableElasticNestedFields()).thenReturn(true);
        Mockito.when(programUserElasticSearchService.findFacetInfo(any(), any(), any(), any())).thenReturn(facetInfoResult);
        FacetInfoResult mockDataTable = Mockito.mock(FacetInfoResult.class);

        Mockito.when(mockDataTable.getResults()).thenReturn(termFacets);
        MockHttpSession sessionAttr = getMockHttpSessionForDealerUser();

        SearchRequest searchCriteria = new SearchRequest();
        searchCriteria.setDealerIds(DEALER_ID_1);
        searchCriteria.setSearchMethods(Map.of("status", SearchMethod.NEGATIVE));

        final String contentRequest = mapper.writeValueAsString(searchCriteria);
        final String resultHappyPath = Files.readString(new ClassPathResource("json/UserElasticSearchApiController/customersFacets.json").getFile().toPath()).trim();
        mockMvc.perform(post("/api/users/v2/facet_info")
                .principal(createPrincipalForDealerUser())
                .param("id", FACET_ID)
                .content(contentRequest)
                .session(sessionAttr)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().string(resultHappyPath));


    }

    @Test
    void testUserSearchV2ForProgramUser() throws Exception {
        setSecurityContextForProgramUser();

        final DocFacets facets = FACTORY.manufacturePojo(DocFacets.class);
        final UserAndProspectDoc doc = new UserAndProspectDoc();
        doc.setId("USER_AND_PROSPECT_DOC_1");
        final Collection<UserAndProspectDoc> content = Collections.checkedCollection(Collections.singletonList(doc), UserAndProspectDoc.class);
        final PageMetadata pageMetadata = new PageMetadata(1, 1, 1);
        final SearchResults<UserAndProspectDoc> result = FACTORY.manufacturePojo(SearchResults.class, UserAndProspectDoc.class);
        result.setContent(content);
        result.setPageMetadata(pageMetadata);
        result.setFacets(facets);

        Mockito.when(programUserElasticSearchService.search(any(), anyList(), anyList(), (Pageable) any())).thenReturn(result);

        MockHttpSession sessionAttr = getMockHttpSessionForProgramUser();

        final SearchRequest searchRequest = SearchRequest.builder()
            .dealerIds(DEALER_ID_1)
            .build();


        final String contentRequest = mapper.writeValueAsString(searchRequest);

        final String expectedJsonResponse = Files.readString(new ClassPathResource("json/UserElasticSearchApiController/customersSearch.json").getFile().toPath());

        mockMvc.perform(post("/api/users/v2/search")
                .content(contentRequest)
                .session(sessionAttr)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(expectedJsonResponse));

        final SearchRequest searchRequestNullValues = SearchRequest.builder().build();
        final String contentRequestNullValues = mapper.writeValueAsString(searchRequestNullValues);

        mockMvc.perform(post("/api/users/v2/search")
                .content(contentRequestNullValues)
                .session(sessionAttr)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(expectedJsonResponse));
    }


    @Test
    void testUserSearchV2ForDealerUser() throws Exception {
        setSecurityContextForDealerUser();

        final DocFacets facets = FACTORY.manufacturePojo(DocFacets.class);
        final UserAndProspectDoc doc = new UserAndProspectDoc();
        doc.setId("USER_AND_PROSPECT_DOC_1");
        final Collection<UserAndProspectDoc> content = Collections.checkedCollection(Collections.singletonList(doc), UserAndProspectDoc.class);
        final PageMetadata pageMetadata = new PageMetadata(1, 1, 1);
        final SearchResults<UserAndProspectDoc> result = FACTORY.manufacturePojo(SearchResults.class, UserAndProspectDoc.class);
        result.setContent(content);
        result.setPageMetadata(pageMetadata);
        result.setFacets(facets);

        Mockito.when(dealerUserElasticSearchService.search(any(), (Pageable) any())).thenReturn(result);

        MockHttpSession sessionAttr = getMockHttpSessionForProgramUser();

        final SearchRequest searchRequest = SearchRequest.builder()
            .dealerIds(DEALER_ID_1)
            .build();


        final String contentRequest = mapper.writeValueAsString(searchRequest);

        final String expectedJsonResponse = Files.readString(new ClassPathResource("json/UserElasticSearchApiController/customersSearch.json").getFile().toPath());

        mockMvc.perform(post("/api/users/v2/search")
                .content(contentRequest)
                .session(sessionAttr)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().json(expectedJsonResponse));


    }


    private void setSecurityContextForProgramUser() {
        SecurityContextHolder.setContext(new SecurityContextImpl((Authentication) createPrincipalForProgramUser()));
        PodamFactory podamFactory = new PodamFactoryImpl();
        final UserView loggedInUser = podamFactory.manufacturePojo(UserView.class);
        loggedInUser.setType("PROGRAM");
        when(userClient.findById(USER_ID)).thenReturn(loggedInUser);
    }

    private void setSecurityContextForDealerUser() {
        SecurityContextHolder.setContext(new SecurityContextImpl((Authentication) createPrincipalForDealerUser()));
        PodamFactory podamFactory = new PodamFactoryImpl();
        final UserView loggedInUser = podamFactory.manufacturePojo(UserView.class);
        loggedInUser.setType("DEALER");
        when(userClient.findById(USER_ID)).thenReturn(loggedInUser);
    }

    private Principal createPrincipalForDealerUser() {
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setId(USER_ID);

        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_DEALER"));
        return new CarSaverJWTToken(tokenResponse, authorities);
    }


    private Principal createPrincipalForProgramUser() {
        TokenResponse tokenResponse = new TokenResponse();
        tokenResponse.setId(USER_ID);

        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        authorities.add(new SimpleGrantedAuthority("ROLE_PROGRAM"));
        return new CarSaverJWTToken(tokenResponse, authorities);
    }

}
