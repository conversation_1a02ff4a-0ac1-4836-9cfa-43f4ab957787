package com.carsaver.partner.extension

import com.carsaver.elasticsearch.ElasticProperties
import com.carsaver.partner.client.mapper.JsonMapper
import com.fasterxml.jackson.core.type.TypeReference
import org.apache.http.HttpHost
import org.apache.http.auth.AuthScope
import org.apache.http.auth.UsernamePasswordCredentials
import org.apache.http.client.CredentialsProvider
import org.apache.http.impl.client.BasicCredentialsProvider
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest
import org.elasticsearch.action.index.IndexRequest
import org.elasticsearch.action.support.WriteRequest
import org.elasticsearch.client.RequestOptions
import org.elasticsearch.client.RestClient
import org.elasticsearch.client.RestHighLevelClient
import org.elasticsearch.client.indices.CreateIndexRequest
import org.elasticsearch.common.xcontent.XContentType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.testcontainers.elasticsearch.ElasticsearchContainer
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths

class ChromeExtensionUsersIndexClientTest {

    @Test
    fun test() {
        ElasticsearchContainer("docker.elastic.co/elasticsearch/elasticsearch:7.10.2")
            .use { container ->


                // Start the container. This step might take some time...
                container.start()

                val credentialsProvider: CredentialsProvider = BasicCredentialsProvider()
                credentialsProvider.setCredentials(
                    AuthScope.ANY,
                    UsernamePasswordCredentials("elastic", "elastic")
                )

                val client = RestHighLevelClient(
                    RestClient
                        .builder(HttpHost.create(container.httpHostAddress))
                        .setHttpClientConfigCallback { httpClientBuilder: HttpAsyncClientBuilder ->
                            httpClientBuilder.setDefaultCredentialsProvider(
                                credentialsProvider
                            )
                        })


                client.indices().create(
                    CreateIndexRequest(USERS_AND_PROSPECTS_INDEX_NAME)
                        .mapping(
                            Files.readString(Paths.get("src/test/resources/elastic/users-and-prospects-mappings.json")),
                            XContentType.JSON
                        ).settings(
                            Files.readString(Paths.get("src/test/resources/elastic/users-and-prospects-settings.json")),
                            XContentType.JSON
                        ), RequestOptions.DEFAULT
                )

                val hits = JsonMapper.fromJson(
                    File("src/test/resources/elastic/users-and-prospects.json").readText(),
                    object : TypeReference<List<Map<String, Any>>>() {})


                hits.map {
                    it.get("_source") as Map<String, Any?>
                }.forEach {
                    save(it, client)
                }

                client.indices().refresh(
                    RefreshRequest(USERS_AND_PROSPECTS_INDEX_NAME),
                    RequestOptions.DEFAULT
                )

                val properties = ElasticProperties()
                properties.host = container.host
                properties.port = container.getMappedPort(9200)
                properties.scheme = "http"
                properties.password = "elastic"
                properties.username = "elastic"
                val vehicleElasticClientKotlin = ChromeExtensionUsersIndexClient(properties)

                val namesToSearch = listOf(
                    Name("Sum", "Anwar") // no fuzziness allowed
                )

                val results = vehicleElasticClientKotlin.matchForDealer(
                    MatchDetailsForDealerRequest(
                        names = namesToSearch,
                        emails = listOf("<EMAIL>"),
                        phoneNumbers = listOf("2222222222"),
                        dealerId = "4092489d-9ae0-45a3-a540-936cd2418e1d"
                    )
                )
                assertEquals(2, results.size)
            }
    }

    private fun save(doc: Map<String, Any?>, client: RestHighLevelClient) {
        client.index(
            IndexRequest()
                .index(USERS_AND_PROSPECTS_INDEX_NAME)
                .setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE)
                .source(
                    JsonMapper.toJson(doc),
                    XContentType.JSON
                ).id(doc["id"] as String), RequestOptions.DEFAULT
        )
    }
}
