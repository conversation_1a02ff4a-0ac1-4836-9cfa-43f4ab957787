package com.carsaver.partner.extension

import com.carsaver.core.DealerStatus
import com.carsaver.elasticsearch.model.UserAndProspectDoc
import com.carsaver.magellan.client.BasicUserAssociationClient
import com.carsaver.magellan.client.ProgramSubscriptionClient
import com.carsaver.magellan.model.UserView
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView
import com.carsaver.partner.TestUtils.generate
import com.carsaver.partner.config.rest.ApiException
import com.carsaver.partner.config.rest.CurrentUserInfo
import com.carsaver.partner.config.rest.UserType
import com.carsaver.partner.generate
import com.carsaver.partner.model.CustomerLastLoginDetails
import com.carsaver.partner.model.ProgramModel
import com.carsaver.partner.model.deal.TradeOfferModel
import com.carsaver.partner.model.retail.CustomerDealSummaryResponse
import com.carsaver.partner.model.retail.CustomerTagsResponse
import com.carsaver.partner.model.retail.UserTags
import com.carsaver.partner.model.user.UserTradeQuote
import com.carsaver.partner.reporting.service.ProgramService
import com.carsaver.partner.service.CustomerDealSummaryService
import com.carsaver.partner.service.CustomerTagService
import com.carsaver.partner.service.OnlineUsersService
import com.carsaver.partner.service.UserVehicleService
import com.carsaver.partner.service.crm.CrmService
import com.carsaver.partner.service.trade.DealerTradeQuoteService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.*
import org.springframework.hateoas.CollectionModel
import java.time.ZonedDateTime
import java.util.*

private const val CALLING_USER = "CallingUser"

private const val DEALER_ID = "DEALER_ID"

class ChromeExtensionServiceTest {

    private val onlineUsersService: OnlineUsersService = mock()
    private val usersIndexClient: ChromeExtensionUsersIndexClient = mock()
    private val basicUserAssociationClient: BasicUserAssociationClient = mock()
    private val programService: ProgramService = mock()
    private val programSubscriptionClient: ProgramSubscriptionClient = mock()
    private val customerDealSummaryService: CustomerDealSummaryService = mock()
    private val dealerTradeQuoteService: DealerTradeQuoteService = mock()
    private val userVehicleService: UserVehicleService = mock()
    private val customerTagService: CustomerTagService = mock()
    private val crmService: CrmService = mock()
    private val service = ChromeExtensionService(
        onlineUsersService, usersIndexClient,
        basicUserAssociationClient, programService, programSubscriptionClient,
        customerDealSummaryService, dealerTradeQuoteService, userVehicleService,
        customerTagService = customerTagService,
        crmService = crmService
    )


    @Test
    fun `test admin`() {
        val userDoc = UserAndProspectDoc()
        whenever(usersIndexClient.matchForDealer(any())).thenReturn(listOf(userDoc))
        val loginDetails = generate(CustomerLastLoginDetails::class.java)
        whenever(onlineUsersService.buildCustomerLastLoginDetails(userDoc)).thenReturn(loginDetails)
        val tags = generate<CustomerTagsResponse>()
        whenever(customerTagService.fetchCustomerTags(anyOrNull(), any())).thenReturn(tags)


        assertEquals(
            listOf(ChromeExtensionModel(userDoc, loginDetails, tags)),
            service.matchForDealer(
                generate(MatchDetailsForDealerRequest::class.java), CurrentUserInfo(
                    type = UserType.ADMIN
                )
            )
        )
    }


    @Test
    fun `test dealer`() {
        val userDoc = UserAndProspectDoc()
        whenever(usersIndexClient.matchForDealer(any())).thenReturn(listOf(userDoc))
        val loginDetails = generate(CustomerLastLoginDetails::class.java)
        whenever(onlineUsersService.buildCustomerLastLoginDetails(userDoc)).thenReturn(loginDetails)

        val currentUser = CurrentUserInfo(
            callingUserId = CALLING_USER,
            type = UserType.DEALER
        )

        val req = generate(MatchDetailsForDealerRequest::class.java).apply {
            dealerId = DEALER_ID
        }

        whenever(basicUserAssociationClient.getBasicUserAssociation(DEALER_ID, CALLING_USER))
            .thenReturn(Optional.empty())

        // no association
        assertThrows<ApiException> {
            service.matchForDealer(req, currentUser)
        }


        // now association
        whenever(basicUserAssociationClient.getBasicUserAssociation(DEALER_ID, CALLING_USER))
            .thenReturn(Optional.of(BasicDealerUserRoleAssociation().apply {
                dealerId = DEALER_ID
            }))
        val tags = generate<CustomerTagsResponse>()
        whenever(customerTagService.fetchCustomerTags(anyOrNull(), any())).thenReturn(tags)

        assertEquals(
            listOf(ChromeExtensionModel(userDoc, loginDetails, tags)),
            service.matchForDealer(req, currentUser)
        )
    }

    @Test
    fun `test program`() {
        val userDoc = UserAndProspectDoc()
        whenever(usersIndexClient.matchForDealer(any())).thenReturn(listOf(userDoc))
        val loginDetails = generate(CustomerLastLoginDetails::class.java)
        whenever(onlineUsersService.buildCustomerLastLoginDetails(userDoc)).thenReturn(loginDetails)

        val currentUser = CurrentUserInfo(
            callingUserId = CALLING_USER,
            type = UserType.PROGRAM
        )

        val req = generate(MatchDetailsForDealerRequest::class.java).apply {
            dealerId = DEALER_ID
        }

        val programs = mutableListOf<ProgramModel>()

        whenever(programService.getPrograms(CALLING_USER)).thenReturn(programs)
        whenever(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(CollectionModel.of(listOf()))

        // no association
        assertThrows<ApiException> {
            service.matchForDealer(req, currentUser)
        }


        programs.add(ProgramModel().apply {
            id = "PROGRAM_ID"
        })
        whenever(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(
            CollectionModel.of(
                listOf(
                    ProgramSubscriptionView().apply {
                        status = DealerStatus.LIVE
                        dealerId = DEALER_ID
                        programId = "PROGRAM_ID"
                    }
                )))
        val tags = CustomerTagsResponse(
            listOf(
                UserTags(
                    "userId", 22, "tagName", "category", "dealerId", "status", ZonedDateTime.now(), "me"
                )
            )
        )
        whenever(customerTagService.fetchCustomerTags(anyOrNull(), any())).thenReturn(tags)

        assertEquals(
            listOf(ChromeExtensionModel(userDoc, loginDetails, tags)),
            service.matchForDealer(req, currentUser)
        )
    }

    @Test
    fun `dealer getDealSummaryV2`() {
        val response: CustomerDealSummaryResponse = generate()
        whenever(customerDealSummaryService.getDealSummaryV2(any(), any())).thenReturn(response)

        val currentUser = CurrentUserInfo(
            callingUserId = CALLING_USER,
            type = UserType.DEALER
        )

        val associations = mutableListOf<BasicDealerUserRoleAssociation>()
        whenever(basicUserAssociationClient.getBasicUserAssociation(DEALER_ID, CALLING_USER))
            .thenReturn(Optional.empty())

        assertThrows<ApiException> {
            service.getDealSummaryV2(DEALER_ID, currentUser, UserView())
        }

        // now association
        whenever(basicUserAssociationClient.getBasicUserAssociation(DEALER_ID, CALLING_USER))
            .thenReturn(Optional.of(BasicDealerUserRoleAssociation().apply {
                dealerId = DEALER_ID
            }))

        assertEquals(
            response,
            service.getDealSummaryV2(DEALER_ID, currentUser, UserView())
        )
    }

    @Test
    fun addTradeQuote() {
        val tradeQuote = generate<UserTradeQuote>()
        val currentUser = CurrentUserInfo(
            callingUserId = CALLING_USER,
            type = UserType.DEALER
        )

        val permission = BasicDealerUserRoleAssociation().apply {
            dealerId = DEALER_ID
        }
        whenever(basicUserAssociationClient.getBasicUserAssociation(DEALER_ID, CALLING_USER))
            .thenReturn(Optional.of(permission))

        assertThrows<ApiException> {
            service.addTradeQuote(DEALER_ID, tradeQuote, currentUser)
        }

        // now with edit permissions
        permission.addPermission(CUSTOMER_EDIT_PERMISSION)
        val expected = TradeOfferModel.builder().build()
        whenever(userVehicleService.getTradeOffer(anyOrNull<String>(), any()))
            .thenReturn(expected)
        whenever(dealerTradeQuoteService.upsertDealerQuote(any(), any())).thenReturn(
            UserVehicleQuoteView()
        )
        // no exception
        assertEquals(expected, service.addTradeQuote(DEALER_ID, tradeQuote, currentUser))
    }

    @Test
    fun `test send to crm`() {
        val currentUser = CurrentUserInfo(
            callingUserId = CALLING_USER,
            type = UserType.DEALER
        )

        val permission = BasicDealerUserRoleAssociation().apply {
            dealerId = DEALER_ID
        }

        whenever(basicUserAssociationClient.getBasicUserAssociation(DEALER_ID, CALLING_USER))
            .thenReturn(Optional.of(permission))

        permission.addPermission(CUSTOMER_EDIT_PERMISSION)

        service.sendToCrm(DEALER_ID, "userId", currentUser = currentUser, adfLeadComment = null)

        verify(crmService, times(1)).sendToCrm(DEALER_ID, "userId", null)
    }
}
