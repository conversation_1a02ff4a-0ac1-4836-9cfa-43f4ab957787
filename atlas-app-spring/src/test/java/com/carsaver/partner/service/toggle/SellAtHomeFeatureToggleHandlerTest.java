package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpSession;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SellAtHomeFeatureToggleHandlerTest {

    @Mock
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @Mock
    private HttpSession session;

    @InjectMocks
    private SellAtHomeFeatureToggleHandler handler;

    private ToggleConfigRequest toggleConfigRequest;
    private FeatureSubscriptionResponse response;

    @BeforeEach
    void setUp() {
        toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);

        response = new FeatureSubscriptionResponse();
        response.setActive(true);
    }

    @Test
    void testHandleFeatureToggle_Success() {
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(response);
        when(splitFeatureFlags.isSellAtHomeFeatureEnabled()).thenReturn(true);

        DealerProgram result = handler.handleFeatureToggle("dealerId", "programId", toggleConfigRequest);

        assertTrue(result.getIsSellAtHomeEnabled());
        assertTrue(result.getIsSellAtHomeFeatureEnabled());
    }

    @Test
    void testHandleFeatureToggle_NullResponse() {
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(null);

        assertThrows(InternalServerError.class, () ->
                handler.handleFeatureToggle("dealerId", "programId", toggleConfigRequest));

    }

    @Test
    void testSupports() {
        assertTrue(handler.supports("sell_at_home"));
        assertFalse(handler.supports("other_feature"));
    }
}
