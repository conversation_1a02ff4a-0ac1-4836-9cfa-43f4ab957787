package com.carsaver.partner.service;

import com.carsaver.elasticsearch.model.vehicle.VehicleDoc;
import com.carsaver.elasticsearch.service.VehicleDocService;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.partner.web.api.VehicleApiController;
import com.carsaver.search.support.SearchResults;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class VehicleCriteriaSearchServiceTest {

    @InjectMocks
    VehicleDocService docService;

    private static final String DEALER_ID = "35bc2ba1-bcd5-4da0-bd96-6618483164f6" ;
    private static final List<String>  EXCLUDE_PROGRAM_IDS = new ArrayList<>(Arrays.asList("018897c6-0892-4255-a1de-25292bb86a7f"));

    private static final List<String>  PROGRAM_IDS = new ArrayList<>(Arrays.asList("2efa7b54-5b8f-4b98-9f03-69b720d02601"));
    private static final String VEHICLE_DOC_ID = "41c9ac58-daee-4e1e-b10b-57325a18c56e" ;

    @Test
    void filterVehiclesByProgramsTest() {

        VehicleDocService vehicleService = Mockito.spy(docService);
        VehicleApiController.DealerVehicleSearchCriteria searchForm = mock(VehicleApiController.DealerVehicleSearchCriteria.class);
        searchForm.setDealerIds(Collections.singletonList(DEALER_ID));

        List<VehicleDoc> vehicleDocList = this.getResultVehicleDocList();

        SearchResults<VehicleDoc> vehicleDocSearchResults = SearchResults.<VehicleDoc>builder().content(vehicleDocList).build();
        VehicleDoc vehicleDoc = VehicleDoc.builder().id(VEHICLE_DOC_ID).programIds(EXCLUDE_PROGRAM_IDS).build();

        Pageable pageable = mock(Pageable.class);
        when(pageable.getPageSize()).thenReturn(20);
        Sort sort = mock(Sort.class);
        when(pageable.getSort()).thenReturn(sort);

        Mockito.doReturn(vehicleDocSearchResults).when(vehicleService).search(any(VehicleApiController.DealerVehicleSearchCriteria.class), any(Pageable.class), any());
        SearchResults<VehicleDoc> result = vehicleService.search(searchForm, PageRequestUtils.asOneBasedPage(pageable));

        assertEquals(vehicleDocList.size(), result.getContent().size());
    }

    private List<VehicleDoc> getResultVehicleDocList( ) {

        List<VehicleDoc> vehicleDocList =new ArrayList<>();

        IntStream.range(0,5).forEach(i -> {
            VehicleDoc vehicleDoc = VehicleDoc.builder()
                .id(VEHICLE_DOC_ID + i)
                .programIds(PROGRAM_IDS)
                .build();

            vehicleDocList.add(vehicleDoc);
        });

        return vehicleDocList;
    }


}




