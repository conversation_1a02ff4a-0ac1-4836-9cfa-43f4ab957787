package com.carsaver.partner.service.activity;

import com.carsaver.partner.AtlasApplication;
import com.carsaver.partner.TestUtils;
import com.carsaver.partner.client.activity.ActivityClient;
import com.carsaver.partner.client.dealer.DealerClient;
import com.carsaver.partner.client.inventory.v2.InventoryClientV2;
import com.carsaver.partner.client.leads.v2.LeadClientV2;
import com.carsaver.partner.client.oauth.OAuthClient;
import com.carsaver.partner.client.vehicle.VehicleClientV2;
import com.carsaver.partner.http.HttpService;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;

@Tag("integration")
class IntegrationTest {

    private final OAuthClient oauthClient;
    private final LeadClientV2 leadClientV2;
    private final DealerClient dealerClient;
    private final InventoryClientV2 inventoryClientV2;
    private final VehicleClientV2 vehicleClientV2;
    private final ActivityLogsService service;
    private final ActivityClient activityClient;


    @SneakyThrows
    public IntegrationTest() {
        HttpService httpService = new HttpService(AtlasApplication.client());

        var properties = TestUtils.getBetaOauthProperties();



        this.oauthClient = new OAuthClient(httpService, properties);

        this.leadClientV2 = new LeadClientV2("https://api-beta.carsaver.com/lead", httpService, oauthClient);
        this.dealerClient = new DealerClient("https://api-beta.carsaver.com/dealer", httpService, oauthClient);
        this.inventoryClientV2 = new InventoryClientV2("https://api-beta.carsaver.com/inventory", httpService, oauthClient);
        this.vehicleClientV2 = new VehicleClientV2("https://api-beta.carsaver.com/vehicle", httpService, oauthClient);
        this.activityClient = new ActivityClient("https://api-beta.carsaver.com/activity", httpService, oauthClient);

        this.service = new ActivityLogsService(
            activityClient, dealerClient, new ActivityLogMapper(leadClientV2, dealerClient, inventoryClientV2, vehicleClientV2)
        );

    }

    @Tag("integration")
    @Test
    void test() {

        var process = service.process("22ce97b6-3823-4f62-9f53-3b047ad698c0", List.of("500f87d74af4b50002000027"));
        process.forEach(System.out::println);
    }

    @Test
    public void deals() {

    }

    @Test
    public void inventory() {

    }

}

