package com.carsaver.partner.service;

import com.carsaver.core.StockType;
import com.carsaver.magellan.model.dealer.DealType;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class DealerFeesServiceTest {

    @InjectMocks
    DealerFeesService dealerFeesService;

    @Test
    public void getDealTypeDealerFeeViews_emptyList_nullInputs() {
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(null, null, DealType.ALL_DEALS);
         assertTrue(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_emptyList_emptyListInputs() {
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(Collections.emptyList(), null, DealType.ALL_DEALS);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_emptyList_nullDealTypeInDealerFeesInputs() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, null, DealType.ALL_DEALS);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_emptyList_nullVehicleStockInputs() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, null, DealType.ALL_DEALS);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_valueInList_nullVehicleStockInFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(null).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, null, DealType.ALL_DEALS);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_valueInList_newStockTypeInputAndNulStockTypeInFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(null).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, StockType.NEW, DealType.ALL_DEALS);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_valueInList_usedStockTypeInputAndNulStockTypeInFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(null).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, StockType.USED, DealType.ALL_DEALS);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_valueInList_missMatchStockTypes() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, StockType.USED, DealType.ALL_DEALS);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_valueInList_leaseDealAllDealFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, StockType.NEW, DealType.LEASE);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_valueInList_leaseDealLeaseDealFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.LEASE).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, StockType.NEW, DealType.LEASE);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getDealTypeDealerFeeViews_valueInList_missMatchedDealType() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.FINANCE).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getDealTypeDealerFeeViews(list, StockType.NEW, DealType.LEASE);
        assertTrue(response.isEmpty());
    }

    /******************/

    @Test
    public void getCashDealerFeeViews_emptyList_nullInputs() {
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(null, null);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_emptyList_emptyListInputs() {
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(Collections.emptyList(), null);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_emptyList_nullDealTypeInDealerFeesInputs() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, null);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_emptyList_nullVehicleStockInputs() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, null);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_valueInList_nullVehicleStockInFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(null).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, null);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_valueInList_newStockTypeInputAndNulStockTypeInFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(null).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, StockType.NEW);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_valueInList_usedStockTypeInputAndNulStockTypeInFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(null).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, StockType.USED);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_valueInList_missMatchStockTypes() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, StockType.USED);
        assertTrue(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_valueInList_leaseDealAllDealFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.ALL_DEALS).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, StockType.NEW);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_valueInList_cashDealCashDealFees() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.CASH).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, StockType.NEW);
        assertFalse(response.isEmpty());
    }

    @Test
    public void getCashDealerFeeViews_valueInList_missMatchedDealType() {
        DealerFeeView dealerFeeView = DealerFeeView.builder().dealType(DealType.FINANCE).stockType(StockType.NEW).build();
        List<DealerFeeView> list = List.of(dealerFeeView);
        List<DealerFeeView> response = dealerFeesService.getCashDealerFeeViews(list, StockType.NEW);
        assertTrue(response.isEmpty());
    }

    /*******************/

    @Test
    public void getTotalFees_zero_nullInputs() {
        Double response = dealerFeesService.getTotalFees(null);
        assertEquals(0D, response);
    }

    @Test
    public void getTotalFees_zero_emptyListInputs() {
        Double response = dealerFeesService.getTotalFees(Collections.emptyList());
        assertEquals(0D, response);
    }

    @Test
    public void getTotalFees_zero_nullAmountInputs() {
        List<DealerFeeView> dealerFeeViewList = List.of(DealerFeeView.builder().build());
        Double response = dealerFeesService.getTotalFees(dealerFeeViewList);
        assertEquals(0D, response);
    }

    @Test
    public void getTotalFees_amountEntered_withAmountInputs() {
        List<DealerFeeView> dealerFeeViewList = List.of(DealerFeeView.builder().amount(10D).build());
        Double response = dealerFeesService.getTotalFees(dealerFeeViewList);
        assertEquals(10D, response);
    }


}
