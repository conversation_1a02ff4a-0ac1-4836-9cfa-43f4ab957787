package com.carsaver.partner.service;

import com.carsaver.magellan.client.prospect.UpgradeCampaignClient;
import com.carsaver.partner.model.ProspectDTO;
import com.carsaver.partner.model.user.ProspectUser;
import com.carsaver.partner.repository.ProspectLeadCurrentRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ProspectServiceTest {

    private final ProspectLeadCurrentRepository prospectLeadCurrentRepository = mock(ProspectLeadCurrentRepository.class);
    private final UpgradeCampaignClient upgradeCampaignClient = mock(UpgradeCampaignClient.class);
    ProspectService prospectService = new ProspectService(prospectLeadCurrentRepository, upgradeCampaignClient);

    @Test
    void findWhipsProspectById_ShouldReturnProspect() {
        when(prospectLeadCurrentRepository.findProspectById("prospect-id")).thenReturn(createProspectUser());

        Optional<ProspectDTO> prospectViewOpt = prospectService.findWhipsProspectById("prospect-id");

        ProspectDTO prospectView = prospectViewOpt.get();
        Assertions.assertNotNull(prospectView);
        Assertions.assertEquals("12345", prospectView.getId());
        Assertions.assertEquals("<EMAIL>", prospectView.getEmail());
        Assertions.assertEquals("John", prospectView.getFirstName());
        Assertions.assertEquals("Doe", prospectView.getLastName());
        Assertions.assertEquals("12345", prospectView.getZipCode());
        Assertions.assertEquals("2819374192", prospectView.getPhoneNumber());
        Assertions.assertEquals("CAMP-789", prospectView.getCampaignId().get());
        Assertions.assertEquals("123 Main Street", prospectView.getAddress());
        Assertions.assertEquals("NY", prospectView.getStateCode());
        Assertions.assertEquals("test-dealer-id", prospectView.getDealerId());
    }

    @Test
    void findWhipsProspectById_ShouldReturnNotProspect() {
        when(prospectLeadCurrentRepository.findProspectById("prospect-id")).thenReturn(Optional.empty());
        Assertions.assertNotNull(prospectService.findWhipsProspectById("prospect-id"));
    }

    private Optional<ProspectUser> createProspectUser() {
        ProspectUser prospectUser = new ProspectUser();
        prospectUser.setProspectLeadId("12345");
        prospectUser.setTimestamp(1739433209L);
        prospectUser.setEmail("<EMAIL>");
        prospectUser.setFirstName("John");
        prospectUser.setLastName("Doe");
        prospectUser.setPostalCode("12345");
        prospectUser.setCarsaverUserId("CS-001");
        prospectUser.setUserPhone("2819374192");
        prospectUser.setCampaignId("CAMP-789");
        prospectUser.setProspectStatus("Test");
        prospectUser.setAddress("123 Main Street");
        prospectUser.setState("NY");
        prospectUser.setDealerId("test-dealer-id");
        return Optional.of(prospectUser);
    }
}
