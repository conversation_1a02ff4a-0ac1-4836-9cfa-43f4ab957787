package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BoostFeaturesToggleHandlerTest {

    @Mock
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @InjectMocks
    private BoostFeaturesToggleHandler handler;

    private ToggleConfigRequest toggleConfigRequest;
    private FeatureSubscriptionResponse response;
    private final String dealerId = "testDealer";
    private final String programId = "testProgram";

    @BeforeEach
    void setUp() {
        toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);

        response = new FeatureSubscriptionResponse();
        response.setActive(true);
    }

    @Test
    void testSupports_WhenConfigTypeIsBoostFeatures_ShouldReturnTrue() {
        assertTrue(handler.supports("boost_features"));
    }

    @Test
    void testSupports_WhenConfigTypeIsBoostFeaturesUpperCase_ShouldReturnTrue() {
        assertTrue(handler.supports("BOOST_FEATURES"));
    }

    @Test
    void testSupports_WhenConfigTypeIsOther_ShouldReturnFalse() {
        assertFalse(handler.supports("other_feature"));
        assertFalse(handler.supports("sell_at_home"));
        assertFalse(handler.supports("chat"));
    }

    @Test
    void testHandleFeatureToggle_Success_EnabledTrue() {
        // Arrange
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(response);
        when(splitFeatureFlags.isBoostPlusFeaturesEnabled()).thenReturn(true);

        // Act
        DealerProgram result = handler.handleFeatureToggle(dealerId, programId, toggleConfigRequest);

        // Assert
        assertNotNull(result);
        assertTrue(result.getIsBoostFeaturesEnabled());
        assertTrue(result.getIsBoostFeaturesFeatureEnabled());
        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void testHandleFeatureToggle_Success_EnabledFalse() {
        // Arrange
        toggleConfigRequest.setIsEnabled(false);
        response.setActive(false);
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(response);
        when(splitFeatureFlags.isBoostPlusFeaturesEnabled()).thenReturn(true);

        // Act
        DealerProgram result = handler.handleFeatureToggle(dealerId, programId, toggleConfigRequest);

        // Assert
        assertNotNull(result);
        assertFalse(result.getIsBoostFeaturesEnabled());
        assertTrue(result.getIsBoostFeaturesFeatureEnabled());
        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void testHandleFeatureToggle_Success_FeatureFlagDisabled() {
        // Arrange
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(response);
        when(splitFeatureFlags.isBoostPlusFeaturesEnabled()).thenReturn(false);

        // Act
        DealerProgram result = handler.handleFeatureToggle(dealerId, programId, toggleConfigRequest);

        // Assert
        assertNotNull(result);
        assertTrue(result.getIsBoostFeaturesEnabled());
        assertFalse(result.getIsBoostFeaturesFeatureEnabled());
        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void testHandleFeatureToggle_NullResponse_ShouldThrowInternalServerError() {
        // Arrange
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(null);

        // Act & Assert
        assertThrows(InternalServerError.class, () ->
                handler.handleFeatureToggle(dealerId, programId, toggleConfigRequest));

        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void testHandleFeatureToggle_ResponseWithNullActive_ShouldThrowInternalServerError() {
        // Arrange
        response.setActive(null);
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(response);

        // Act & Assert
        assertThrows(InternalServerError.class, () ->
                handler.handleFeatureToggle(dealerId, programId, toggleConfigRequest));

        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void testHandleFeatureToggle_VerifyFeatureSubscriptionRequest() {
        // Arrange
        ArgumentCaptor<FeatureSubscriptionRequest> requestCaptor = ArgumentCaptor.forClass(FeatureSubscriptionRequest.class);
        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class))).thenReturn(response);
        when(splitFeatureFlags.isBoostPlusFeaturesEnabled()).thenReturn(true);

        // Act
        handler.handleFeatureToggle(dealerId, programId, toggleConfigRequest);

        // Assert - Verify the request was constructed correctly
        verify(featureSubscriptionsClient).saveFeatureSubscription(requestCaptor.capture());
        FeatureSubscriptionRequest capturedRequest = requestCaptor.getValue();

        assertEquals(dealerId, capturedRequest.getDealerId());
        assertEquals(programId, capturedRequest.getEntityId());
        assertEquals(true, capturedRequest.getActive());
    }
}
