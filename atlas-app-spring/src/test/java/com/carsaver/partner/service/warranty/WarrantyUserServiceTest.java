package com.carsaver.partner.service.warranty;

import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.ConnectionClient;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.client.PasswordClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerLinkRequest;
import com.carsaver.magellan.model.DealerLinkView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.partner.exception.BadRequestException;
import com.carsaver.partner.model.warranty.CreateCustomerForm;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.hateoas.CollectionModel;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

class WarrantyUserServiceTest {

    private final ConnectionClient connectionClient = Mockito.mock(ConnectionClient.class);
    private final CertificateClient certificateClient = Mockito.mock(CertificateClient.class);
    private final PasswordClient passwordClient = Mockito.mock(PasswordClient.class);
    private final UserClient userClient = Mockito.mock(UserClient.class);
    private final String expressTenantId = "EXPRESS_TENANT_ID";
    private final DealerLinkClient dealerLinkClient = Mockito.mock(DealerLinkClient.class);
    private final InventoryClient inventoryClient = Mockito.mock(InventoryClient.class);
    private final WarrantyUserService service = WarrantyUserService.builder()
            .connectionClient(connectionClient)
            .certificateClient(certificateClient)
            .passwordClient(passwordClient)
            .userClient(userClient)
            .expressTenantId(expressTenantId)
            .dealerLinkClient(dealerLinkClient)
            .inventoryClient(inventoryClient)
            .build();
    private static final PodamFactory FACTORY = new PodamFactoryImpl();

    @Test
    void createWarrantyCustomerEmptyDealerIdsTest() {
        CreateCustomerForm form = new CreateCustomerForm();
        assertThrows(BadRequestException.class, ()-> service.createWarrantyCustomer(form));

        form.setDealerIds(Collections.emptyList());
        assertThrows(BadRequestException.class, ()-> service.createWarrantyCustomer(form));
    }

    @Test
    void createWarrantyCustomerUserFoundAndVehicleFromVINTest() {
        CreateCustomerForm form = FACTORY.manufacturePojo(CreateCustomerForm.class);
        form.setDealerIds(List.of("DEALER_ID_1"));
        UserView userView = FACTORY.manufacturePojo(UserView.class);
        Mockito.when(userClient.findByTenantAndEmail(anyString(), anyString())).thenReturn(Optional.of(userView));
        Mockito.when(userClient.save(anyString(), any())).thenReturn(userView);

        final VehicleView vehicle1 = new VehicleView();
        vehicle1.setId("VEHICLE_ID_1");
        vehicle1.setDealerId("DEALER_ID_1");
        List<VehicleView> vehicles = List.of(vehicle1);
        Mockito.when(inventoryClient.getVehiclesByVin(anyString())).thenReturn(CollectionModel.of(vehicles));
        DealerLinkView dealerLinkView = FACTORY.manufacturePojo(DealerLinkView.class);
        Mockito.when(dealerLinkClient.create((DealerLinkRequest) any())).thenReturn(dealerLinkView);

        CertificateView certificateView = FACTORY.manufacturePojo(CertificateView.class);
        Mockito.when(certificateClient.save(any())).thenReturn(certificateView);

        assertDoesNotThrow(()->service.createWarrantyCustomer(form));
    }


}
