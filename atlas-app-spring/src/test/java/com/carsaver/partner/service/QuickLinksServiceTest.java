package com.carsaver.partner.service;

import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.TinyUrlClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.TinyUrlRequest;
import com.carsaver.magellan.model.TinyUrlView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.DealProgramAndDomain;
import com.carsaver.partner.model.QuickLink;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class QuickLinksServiceTest {

    @Mock
    private ProgramApiService programApiService;
    @Mock
    private TinyUrlClient tinyUrlClient;
    @Mock
    private UserClient userClient;
    @Mock
    private SplitFeatureFlags splitFeatureFlags;
    @Mock
    private DealerClient dealerClient;

    @InjectMocks
    private QuickLinksService quickLinksService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(quickLinksService, "dealerTable", "dummyDealerTable");
    }

    @Test
    void testGetDealerQuickLinkStatuses_NissanBuyAtHome() {
        String userId = UUID.randomUUID().toString();
        String dealerId = UUID.randomUUID().toString();;
        String campaignId = UUID.randomUUID().toString();

        mockDealProgramAndDomain(dealerId);
        mockUserClient(userId, "nissan-buy-at-home", campaignId);
        mockTinyUrlClient();

        List<QuickLink> quickLinks = quickLinksService.getDealerQuickLinkStatuses(userId, dealerId);

        assertNotNull(quickLinks);
        assertEquals(4, quickLinks.size());
    }

    @Test
    void testGetDealerQuickLinkStatuses_DigitalRetailV2() {
        String userId = UUID.randomUUID().toString();;
        String dealerId = UUID.randomUUID().toString();;
        String campaignId = UUID.randomUUID().toString();

        mockDealProgramAndDomain(dealerId);
        mockUserClient(userId, "digital-retail", campaignId);
        mockTinyUrlClient();

        List<QuickLink> quickLinks = quickLinksService.getDealerQuickLinkStatuses(userId, dealerId);

        assertNotNull(quickLinks);
        assertEquals(4, quickLinks.size());
    }

    @Test
    void testGetDealerQuickLinkStatuses_DigitalRetailV1_NoCampaignId() {
        String userId = UUID.randomUUID().toString();
        String dealerId = UUID.randomUUID().toString();

        mockDealProgramAndDomain(dealerId);
        mockUserClient(userId, null, null);
        mockTinyUrlClient();

        List<QuickLink> quickLinks = quickLinksService.getDealerQuickLinkStatuses(userId, dealerId);

        assertNotNull(quickLinks);
        assertEquals(4, quickLinks.size());
    }

    @Test
    void testGetDealerQuickLinkStatuses_UpgradeCampaign() {
        String userId = "userUpgrade";
        String dealerId = "dealer123";
        String campaignId = UUID.randomUUID().toString();

        mockDealProgramAndDomain(dealerId);
        mockUserClient(userId, "upgrade-campaign", campaignId);
        mockTinyUrlClient();

        List<QuickLink> quickLinks = quickLinksService.getDealerQuickLinkStatuses(userId, dealerId);

        assertNotNull(quickLinks);
        assertEquals(4, quickLinks.size());
    }

    private void mockDealProgramAndDomain(String dealerId) {
        DealProgramAndDomain dealProgramAndDomain = DealProgramAndDomain.builder()
            .programName("Test Program").domain("test.com").build();
        when(programApiService.getDealProgramAndDomain(anyString(), eq(dealerId), anyString()))
            .thenReturn(dealProgramAndDomain);
    }

    private void mockUserClient(String userId, String channel, String campaignId) {
        UserView userView = new UserView();
        Source source = new Source();
        source.setChannel(channel);
        source.setCampaignId(campaignId);
        userView.setSource(source);
        when(userClient.findById(userId)).thenReturn(userView);

        if (StringUtils.hasText(channel) && channel.equalsIgnoreCase("digital-retail")) {
            when(splitFeatureFlags.isDigitalRetailEnabledForDealer(anyString(), any())).thenReturn(true);
            when(dealerClient.findById(anyString())).thenReturn(new DealerView());
        } else {
            when(splitFeatureFlags.isDigitalRetailEnabledForDealer(anyString(), any())).thenReturn(false);
            when(dealerClient.findById(anyString())).thenReturn(new DealerView());
        }
    }

    private void mockTinyUrlClient() {
        TinyUrlView tinyUrlView = new TinyUrlView();
        tinyUrlView.setRequestUrl("http://tinyurl.com/aeuyf");
        when(tinyUrlClient.create(any(TinyUrlRequest.class))).thenReturn(tinyUrlView);
    }
}

