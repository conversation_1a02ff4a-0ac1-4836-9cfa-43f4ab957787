package com.carsaver.partner.service;

import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.model.DeliveryFee;
import com.carsaver.partner.model.DeliveryFees;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DealerDeliveryFeeServiceTest {

    @Mock
    private NissanWebClient nissanWebClient;

    @InjectMocks
    private DealerDeliveryFeeService dealerDeliveryFeesService;

    @BeforeEach
    public void setup() {
        dealerDeliveryFeesService = new DealerDeliveryFeeService(nissanWebClient);
    }

    @Test
    public void getDealerDeliveryFees_validDealerId_returnsDeliveryFees() {
        // Arrange
        String dealerId = "1234";
        List<DeliveryFee> expectedDeliveryFees = new ArrayList<>();

        DeliveryFee deliveryFeeOne = new DeliveryFee();
        deliveryFeeOne.setId("1");
        deliveryFeeOne.setAmount(200.00);
        deliveryFeeOne.setMin(0.00);
        deliveryFeeOne.setMax(50.00);
        expectedDeliveryFees.add(deliveryFeeOne);

        DeliveryFee deliveryFeeTwo = new DeliveryFee();
        deliveryFeeTwo.setId("2");
        deliveryFeeTwo.setAmount(400.00);
        deliveryFeeTwo.setMin(51.00);
        deliveryFeeTwo.setMax(100.00);
        expectedDeliveryFees.add(deliveryFeeTwo);

        DeliveryFees deliveryFees = new DeliveryFees();
        deliveryFees.setDeliveryFees(expectedDeliveryFees);

        doReturn(deliveryFees).when(nissanWebClient).get(Mockito.anyString(), eq(DeliveryFees.class), eq("getDeliveryFees"));

        // Act
        List<DeliveryFee> actualDeliveryFees = dealerDeliveryFeesService.getDealerDeliveryFees(dealerId);

        // Assert
        assertEquals(expectedDeliveryFees, actualDeliveryFees);
    }

    @Test
    public void getDealerDeliveryFees_invalidDealerId_ReturnsEmptyList() {
        // Arrange
        String dealerId = "invalid";
        DeliveryFees deliveryFees = new DeliveryFees();
        deliveryFees.setDeliveryFees(Collections.emptyList()); // set the delivery fees list to an empty list
        when(nissanWebClient.get(anyString(), eq(DeliveryFees.class), anyString())).thenReturn(deliveryFees);

        // Act
        List<DeliveryFee> result = dealerDeliveryFeesService.getDealerDeliveryFees(dealerId);

        // Assert
        Assertions.assertTrue(result.isEmpty());
    }

    @Test
    public void getDealerDeliveryFees_exception_throwsInternalServerError() {
        // Arrange
        String dealerId = "invalid";
        when(nissanWebClient.get(anyString(), eq(DeliveryFees.class), anyString())).thenThrow(new NotFoundException());

        // Act & Assert
        Exception exception = assertThrows(NotFoundException.class, () -> {
            dealerDeliveryFeesService.getDealerDeliveryFees(dealerId);
        });

        // Assert
        assertEquals("Failed to get delivery fees for dealer ID: " + dealerId, exception.getMessage());
    }

    @Test
    public void getDealerDeliveryFees_nullDealerId_throwsIllegalArgumentException() {

        // Act
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            dealerDeliveryFeesService.getDealerDeliveryFees(null);
        });

        // Assert
        assertEquals("Dealer ID cannot be null", exception.getMessage());
    }

    @Test
    public void test_createOrUpdateDeliveryFees_withNullDealerId() {
        // Arrange
        DeliveryFee deliveryFee = new DeliveryFee();

        // Act
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            dealerDeliveryFeesService.createOrUpdateDeliveryFees(null, deliveryFee);
        });

        // Assert
        assertEquals("Dealer ID and delivery fee cannot be null", exception.getMessage());
    }

    @Test
    public void test_createOrUpdateDeliveryFees_WithNullDeliveryFee() {
        // Arrange
        String dealerId = "ABC123";

        // Act
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            dealerDeliveryFeesService.createOrUpdateDeliveryFees(dealerId, null);
        });

        // Assert
        assertEquals("Dealer ID and delivery fee cannot be null", exception.getMessage());
    }

    @Test
    public void test_createOrUpdateDeliveryFees_Success() {
        // Arrange
        String dealerId = "ABC123";
        DeliveryFee deliveryFee = new DeliveryFee();
        deliveryFee.setMin(1.00);
        deliveryFee.setMax(10.00);
        deliveryFee.setAmount(1000.00);

        DeliveryFees expectedDeliveryFees = new DeliveryFees();
        expectedDeliveryFees.setDealerId(dealerId);
        expectedDeliveryFees.setDeliveryFees(List.of(deliveryFee));

        Mockito.when(nissanWebClient.post(Mockito.anyString(), Mockito.any(), Mockito.eq(DeliveryFees.class), Mockito.anyString())).thenReturn(expectedDeliveryFees);

        // Act
        DeliveryFees result = dealerDeliveryFeesService.createOrUpdateDeliveryFees(dealerId, deliveryFee);

        // Assert
        assertEquals(expectedDeliveryFees, result);
    }

    @Test
    public void test_deleteDeliveryFee_Success() {
        // Arrange
        String dealerId = "ABC123";
        String deliveryFeeId = "1";

        DeliveryFee deliveryFeeOne = new DeliveryFee();
        deliveryFeeOne.setId(deliveryFeeId);
        deliveryFeeOne.setAmount(200.00);
        deliveryFeeOne.setMin(0.00);
        deliveryFeeOne.setMax(50.00);

        DeliveryFee deliveryFeeTwo = new DeliveryFee();
        deliveryFeeTwo.setId("2");
        deliveryFeeTwo.setAmount(400.00);
        deliveryFeeTwo.setMin(51.00);
        deliveryFeeTwo.setMax(100.00);

        DeliveryFees deliveryFees = new DeliveryFees();
        deliveryFees.setDealerId(dealerId);
        deliveryFees.setDeliveryFees(List.of(deliveryFeeOne, deliveryFeeTwo));

        Mockito.when(nissanWebClient.delete(Mockito.anyString(), Mockito.any(), Mockito.anyString())).thenReturn(deliveryFees);

        // Act
        dealerDeliveryFeesService.deleteDeliveryFee(dealerId, deliveryFeeId);

        // Assert
        Mockito.verify(nissanWebClient, Mockito.times(1)).delete(Mockito.anyString(), Mockito.any(), Mockito.anyString());
    }

    @Test
    public void test_deleteDeliveryFee_withNullDealerId() {
        // Act
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            dealerDeliveryFeesService.deleteDeliveryFee(null, "123");
        });

        // Assert
        assertEquals("Dealer ID and delivery fee id cannot be null", exception.getMessage());
    }

    @Test
    public void test_deleteDeliveryFee_withNullDeliveryFeeId() {
        // Act
        Exception exception = assertThrows(IllegalArgumentException.class, () -> {
            dealerDeliveryFeesService.deleteDeliveryFee("ABC123", null);
        });

        // Assert
        assertEquals("Dealer ID and delivery fee id cannot be null", exception.getMessage());
    }

}
