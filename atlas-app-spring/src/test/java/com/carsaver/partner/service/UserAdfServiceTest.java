package com.carsaver.partner.service;

import com.carsaver.magellan.api.GarageService;
import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.ConnectionClient;
import com.carsaver.magellan.model.AdfLeadComment;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserAdfServiceTest {

    @Mock
    private ConnectionClient connectionClient;
    @Mock
    private SplitFeatureFlags splitFeatureFlags;
    @Mock
    private GarageService garageService;

    @InjectMocks
    private UserAdfService userAdfService;

    @Test
    void test() {

        UserView userView = mock(UserView.class);
        DealerView dealerView = mock(DealerView.class);
        CampaignClient campaignClient = mock(CampaignClient.class);

        Source source = new Source();
        source.setCampaignId("0");

        CertificateView certificateView = new CertificateView();
        certificateView.setLastModifiedDate(ZonedDateTime.now());
        certificateView.setCampaignClient(campaignClient);
        certificateView.setSource(source);

        AdfLeadComment adfLeadComment = new AdfLeadComment("Test Lead Message");

        List<Integer> makeFilters = new ArrayList<>();
        makeFilters.add(5);

        CampaignView campaignView = new CampaignView();
        campaignView.setMakeFilters(makeFilters);
        campaignView.setId("0");

        List<CertificateView> certificates = new ArrayList<>();
        certificates.add(certificateView);

        when(garageService.findVehiclesByUserAndDealer(any(), any())).thenReturn(certificates);

        assertDoesNotThrow(() -> userAdfService.createdQualifiedLead(dealerView, userView,adfLeadComment));
    }
}
