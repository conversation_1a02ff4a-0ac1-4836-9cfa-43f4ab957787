package com.carsaver.partner.service;

import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.exception.DealerCTANotFoundException;
import com.carsaver.partner.model.dealercta.AtlasCTAReqRes;
import com.carsaver.partner.model.dealercta.DealerCtaReqRes;
import com.carsaver.partner.model.dealercta.Global;
import com.carsaver.partner.model.dealercta.Style;
import com.carsaver.partner.model.dealercta.StyleCTA;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * CloneDealServiceTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class DealerCTAServiceTest {

    private static final String DEALERSERVICEURL = "https://api.carsaver.com/dealer";

    @Mock
    private NissanWebClient client;

    @InjectMocks
    private DealerCTAService dealerCTAService;

    @BeforeEach
    void init(){
        ReflectionTestUtils.setField(dealerCTAService, "dealerServiceUrl", DEALERSERVICEURL);
    }

    @Test
    void upsertDealTest() throws JsonProcessingException {
        List<StyleCTA> styleCTAS = new ArrayList<>();
        StyleCTA cta = new StyleCTA();
        cta.setCtaType("primary");
        cta.setName("primary");
        cta.setActive(true);
        cta.setDetailsHover(true);
        styleCTAS.add(cta);
        Global global = Global
            .builder()
            .padding("12")
            .margin("12")
            .height("12")
            .build();
        Style style = Style.builder()
            .styleCTAs(styleCTAS)
            .global(global)
            .build();
        AtlasCTAReqRes atlasCTAReqRes = AtlasCTAReqRes
            .builder()
            .style(style)
            .overlayEnabled(true)
            .build();
        DealerCtaReqRes dealReq = DealerCtaReqRes.builder().dealerId("123").programId("123").overlayEnabled(atlasCTAReqRes.isOverlayEnabled()).build();
        when(client.post(anyString(), any() ,any(),anyString()))
            .thenReturn(dealReq);
        AtlasCTAReqRes response = dealerCTAService.upsertDealerCTA(atlasCTAReqRes);
        assertNotNull(response);
    }

    @Test
    void upsertDealTest_emptyCTA() throws JsonProcessingException {
        List<StyleCTA> styleCTAS = new ArrayList<>();
        Global global = Global.builder().padding("12").margin("12").height("12").build();
        Style style = Style.builder().styleCTAs(styleCTAS).global(global).build();
        AtlasCTAReqRes atlasCTAReqRes = AtlasCTAReqRes.builder().style(style).build();
        DealerCtaReqRes dealReq = DealerCtaReqRes
            .builder()
            .dealerId("123")
            .programId("123")
            .build();
        when(client.post(anyString(), any() ,any(),anyString()))
            .thenReturn(null);
        assertThrows(DealerCTANotFoundException.class,() -> dealerCTAService
            .upsertDealerCTA(atlasCTAReqRes));
    }

    @Test
    void getDealerCTATest() throws JsonProcessingException {
        DealerCtaReqRes dealerCtaReqRes = new DealerCtaReqRes();
        dealerCtaReqRes.setDealerId("123");
        dealerCtaReqRes.setProgramId("123");
        when(client.get(anyString(), any(), anyString()))
            .thenReturn(dealerCtaReqRes);
        AtlasCTAReqRes response = dealerCTAService
            .getDealerCTA("123");
        assertNotNull(response);
    }

    @Test
    void getDealerCTATest_NullResponse() throws JsonProcessingException {
        DealerCtaReqRes dealerCtaReqRes = new DealerCtaReqRes();
        dealerCtaReqRes.setDealerId("123");
        dealerCtaReqRes.setProgramId("123");
        when(client.get(anyString(), any(), anyString()))
            .thenReturn(null);
        assertThrows(DealerCTANotFoundException.class,() -> dealerCTAService
            .getDealerCTA("123"));
    }

}
