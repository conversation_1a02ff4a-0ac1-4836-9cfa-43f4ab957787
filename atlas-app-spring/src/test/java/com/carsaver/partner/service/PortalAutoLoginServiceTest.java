package com.carsaver.partner.service;

import org.junit.jupiter.api.Test;
import org.springframework.security.core.context.SecurityContextHolder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class PortalAutoLoginServiceTest {

    public static boolean validateUrlAgainstPortalRefererList(String referer) {
        SecurityContextHolder.clearContext();

        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getHeader("referer")).thenReturn(referer);

        HttpSession sessionMock = mock(HttpSession.class);
        when(request.getSession(false)).thenReturn(sessionMock);


        return PortalAutoLoginService.isPortalAutoLoginRequest(request);
    }

    @Test
    void testLocalHostHasPortalRefererValid() {
        assertTrue(validateUrlAgainstPortalRefererList("http://localhost:3000/"));
    }

    @Test
    void testLocalHostHasPortalSAFARIRefererValid() {
        assertTrue(validateUrlAgainstPortalRefererList("http://localhost:3000/dealer/some-uuid-dealer-id"));
    }

    @Test
    void testLocalHostHasPortalRefererNOTValid() {
        assertFalse(validateUrlAgainstPortalRefererList("http://localhost:4000/"));
    }

    @Test
    void testBetaHasPortalRefererValid() {
        assertTrue(validateUrlAgainstPortalRefererList("https://portal.beta.carsaver.com/"));
    }

    @Test
    void testBetaNOTHTTPSHasPortalRefererValid() {
        assertFalse(validateUrlAgainstPortalRefererList("http://portal.beta.carsaver.com/"));
    }

    @Test
    void testBetaHasPortalSAFARIRefererValid() {
        assertTrue(validateUrlAgainstPortalRefererList("https://portal.beta.carsaver.com/dealer/some-uuid-dealer-id"));
    }

    @Test
    void testProdHasPortalRefererValid() {
        assertTrue(validateUrlAgainstPortalRefererList("https://portal.carsaver.com/"));
    }

    @Test
    void testProdNOTHTTPSHasPortalRefererValid() {
        assertFalse(validateUrlAgainstPortalRefererList("http://portal.carsaver.com"));
    }

    @Test
    void testProdHasPortalSAFARIRefererValid() {
        assertTrue(validateUrlAgainstPortalRefererList("https://portal.carsaver.com/dealer/some-uuid-dealer-id"));
    }
}
