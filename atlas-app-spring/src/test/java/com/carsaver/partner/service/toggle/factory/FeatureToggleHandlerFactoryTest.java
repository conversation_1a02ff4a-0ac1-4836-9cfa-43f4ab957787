package com.carsaver.partner.service.toggle.factory;


import com.carsaver.partner.client.DealerChatConfigClient;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.BadRequestException;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.service.toggle.CarsaverFAndIFeatureToggleHandler;
import com.carsaver.partner.service.toggle.ChatFeatureToggleHandler;
import com.carsaver.partner.service.toggle.DisableProtectionProductsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.EmailAlertsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.FeatureToggleHandler;
import com.carsaver.partner.service.toggle.GarageAlertsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.InAppAlertsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.LibertyMutualFeatureToggleHandler;
import com.carsaver.partner.service.toggle.NesnaFinanceAndInsuranceFeatureToggleHandler;
import com.carsaver.partner.service.toggle.RouteOneFinanceAndInsuranceFeatureToggleHandler;
import com.carsaver.partner.service.toggle.SellAtHomeFeatureToggleHandler;
import com.carsaver.partner.service.toggle.SmsAlertsFeatureToggleHandler;
import com.carsaver.partner.service.toggle.SpanishTranslationFeatureToggleHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpSession;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class FeatureToggleHandlerFactoryTest {

    @Mock
    private DealerChatConfigClient dealerChatConfigClient;

    @Mock
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Mock
    private HttpSession session;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @Mock
    private FeatureToggleHandlerFactory featureToggleHandlerFactory;

    @BeforeEach
    void setUp() {
        FeatureToggleHandler chatFeatureToggleHandler = new ChatFeatureToggleHandler(dealerChatConfigClient);

        FeatureToggleHandler disableProtectionProductsFeatureToggleHandler = new DisableProtectionProductsFeatureToggleHandler(
            featureSubscriptionsClient, session);

        FeatureToggleHandler libertyMutualFeatureToggleHandler = new LibertyMutualFeatureToggleHandler(
            featureSubscriptionsClient);

        FeatureToggleHandler nesnaFinanceAndFeatureToggleHandler = new NesnaFinanceAndInsuranceFeatureToggleHandler(
            featureSubscriptionsClient, session);
        FeatureToggleHandler routeOneFinanceAndFeatureToggleHandler = new RouteOneFinanceAndInsuranceFeatureToggleHandler(
            featureSubscriptionsClient, session);

        FeatureToggleHandler sellAtHomeFeatureToggleHandler = new SellAtHomeFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);

        FeatureToggleHandler inAppAlertsFeatureToggle = new InAppAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);

        FeatureToggleHandler emailAlertsFeatureToggle = new EmailAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);

        FeatureToggleHandler garageAlertsFeatureToggle = new GarageAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);

        FeatureToggleHandler smsAlertsFeatureToggle = new SmsAlertsFeatureToggleHandler(
            featureSubscriptionsClient, splitFeatureFlags);
        FeatureToggleHandler spanishTranslationFeatureToggleHandler = new SpanishTranslationFeatureToggleHandler(featureSubscriptionsClient);
        FeatureToggleHandler carsaverFAndIInsuranceHandler = new CarsaverFAndIFeatureToggleHandler(featureSubscriptionsClient, splitFeatureFlags);

        featureToggleHandlerFactory = new FeatureToggleHandlerFactory(
            List.of(
                chatFeatureToggleHandler,
                libertyMutualFeatureToggleHandler,
                nesnaFinanceAndFeatureToggleHandler,
                routeOneFinanceAndFeatureToggleHandler,
                disableProtectionProductsFeatureToggleHandler,
                sellAtHomeFeatureToggleHandler,
                inAppAlertsFeatureToggle,
                emailAlertsFeatureToggle,
                garageAlertsFeatureToggle,
                smsAlertsFeatureToggle,
                spanishTranslationFeatureToggleHandler,
                carsaverFAndIInsuranceHandler
            )
        );

    }

    @Test
    void testGetHandler_ChatFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("chat");
        assertEquals(ChatFeatureToggleHandler.class, result.getClass());
    }


    @Test
    void testGetHandler_SellAtHomeFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("sell_at_home");
        assertEquals(SellAtHomeFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_GarageAlertsFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("garage_alerts");
        assertEquals(GarageAlertsFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_InAppAlertsFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("in_app_alerts");
        assertEquals(InAppAlertsFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_SmsAlertsFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("sms_alerts");
        assertEquals(SmsAlertsFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_RouteOneFinanceAndInsuranceFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("route_one_finance_and_insurance");
        assertEquals(RouteOneFinanceAndInsuranceFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_NesnaFinanceAndInsuranceFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("nesna_finance_and_insurance");
        assertEquals(NesnaFinanceAndInsuranceFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_LibertyMutualFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("liberty_mutual");
        assertEquals(LibertyMutualFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_DisableProtectionProductsFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("disable_protection_products_global_toggle");
        assertEquals(DisableProtectionProductsFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_EmailAlertsFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("email_alerts");
        assertEquals(EmailAlertsFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_SpanishTranslationFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("spanish_translation");
        assertEquals(SpanishTranslationFeatureToggleHandler.class, result.getClass());
    }

    @Test
    void testGetHandler_FAndIFeature() {
        FeatureToggleHandler result = featureToggleHandlerFactory.getHandler("carsaver_f_and_i_feature");
        assertEquals(CarsaverFAndIFeatureToggleHandler.class, result.getClass());
    }



    @Test
    void testGetHandler_InvalidConfigType() {
        BadRequestException exception = assertThrows(BadRequestException.class, () -> {
            featureToggleHandlerFactory.getHandler("invalid_type");
        });
        assertEquals("Invalid config type: invalid_type", exception.getMessage());
    }


}
