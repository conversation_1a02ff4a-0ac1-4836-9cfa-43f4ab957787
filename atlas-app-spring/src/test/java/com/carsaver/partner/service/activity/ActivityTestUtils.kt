package com.carsaver.partner.service.activity

import com.carsaver.partner.client.activity.ActivityLog
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import lombok.SneakyThrows
import lombok.experimental.UtilityClass
import java.io.File
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import java.util.stream.Collectors

@UtilityClass
object ActivityTestUtils {
    private val objectMapper: ObjectMapper = ObjectMapper()
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        .findAndRegisterModules()

    private fun convertKeysToCamelCase(originalMap: Map<String, Any>): Map<String, Any> {
        val camelCaseMap: MutableMap<String, Any> = LinkedHashMap()
        for (entry in originalMap.entries) {
            val camelCaseKey = toCamelCase(entry.key)
            var value = entry.value

            // Recursively process nested maps
            if (value is Map<*, *>) {
                value = convertKeysToCamelCase(value as Map<String, Any>)
            } else if (value is String && isDateTimeFormat(value)) {
                value = convertToISO8601(value)
            }
            camelCaseMap[camelCaseKey] = value
        }
        return camelCaseMap
    }

    @SneakyThrows
    fun loadJsonFileAsCamelCaseMap(filePath: String): List<ActivityLog> {
        // Read JSON file into a Map
        val originalMaps: List<Map<String, Any>> = objectMapper.readValue<List<Map<String, Any>>>(
            File(filePath)
        )

        // Convert keys to camelCase
        return originalMaps.stream()
            .map { obj: Map<String, Any> -> convertKeysToCamelCase(obj) }
            .map { map: Map<String, Any> ->
                objectMapper.convertValue(map, ActivityLog::class.java)
            }
            .collect(Collectors.toList())
    }


    private fun isDateTimeFormat(value: String): Boolean {
        return value.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d+ \\+\\d{2}:\\d{2}".toRegex())
    }

    private fun convertToISO8601(dateTimeString: String): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS XXX")
        val dateTime = OffsetDateTime.parse(dateTimeString, formatter)
        return dateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }

    private fun toCamelCase(key: String): String {
        val parts = key.split("_".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray() // Assuming snake_case input
        if (parts.size == 1) {
            return key // Already in camelCase
        }
        val camelCaseKey = StringBuilder(parts[0]) // Keep first part lowercase
        for (i in 1 until parts.size) {
            camelCaseKey.append(parts[i][0].uppercaseChar())
                .append(parts[i].substring(1))
        }
        return camelCaseKey.toString()
    }
}
