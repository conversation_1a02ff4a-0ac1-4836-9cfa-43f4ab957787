package com.carsaver.partner.service.crm

import com.carsaver.magellan.client.LeadClient
import com.carsaver.magellan.client.ProgramClient
import com.carsaver.magellan.model.ProgramView
import com.carsaver.magellan.model.UserView
import com.carsaver.magellan.model.campaign.CampaignView
import com.carsaver.magellan.model.foundation.ProductView
import com.carsaver.partner.generate
import com.carsaver.partner.mock
import com.carsaver.partner.model.UserModel
import com.carsaver.partner.service.UserAdfService
import com.carsaver.partner.service.split_io.SplitFeatureFlags
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import java.util.*

class CrmServiceTest {

    private val splitFeatureFlags: SplitFeatureFlags = mock()
    private val leadClient: LeadClient = mock()
    private var userAdfService: UserAdfService = mock()
    private var programClient: ProgramClient = mock()
    private val dealerClient: com.carsaver.magellan.client.DealerClient = mock()
    private val usersClient: com.carsaver.magellan.client.UserClient = mock()
    private val crmService: CrmService = CrmService(
        splitFeatureFlags, leadClient, userAdfService,
        programClient, dealerClient, usersClient
    )


    @Test
    fun `test with upgrade` () {
        whenever(dealerClient.findById("dealerId")).thenReturn(generate())
        val user: UserView = mock()
        val campaign: CampaignView = mock()
        whenever(campaign.programId).thenReturn("programId")
        val program: com.carsaver.magellan.model.foundation.ProgramView = mock()
        val product: ProductView = mock()
        whenever(product.id).thenReturn(UPGRADE_PRODUCT_ID)
        whenever(program.product).thenReturn(product)
        whenever(programClient.findById("programId")).thenReturn(Optional.of(program))
        whenever(user.campaign).thenReturn(campaign)
        whenever(usersClient.findById("userId")).thenReturn(user)

        crmService.sendToCrm("dealerId", "userId", null)
        verify(userAdfService, times(1)).createdQualifiedLead(any(), any(), any())
    }


    @Test
    fun `test not upgrade` () {
        whenever(dealerClient.findById("dealerId")).thenReturn(generate())
        val user: UserView = mock()
        val campaign: CampaignView = mock()
        whenever(campaign.programId).thenReturn("programId")
        val program: com.carsaver.magellan.model.foundation.ProgramView = mock()
        val product: ProductView = mock()
        whenever(product.id).thenReturn(100)
        whenever(program.product).thenReturn(product)
        whenever(programClient.findById("programId")).thenReturn(Optional.of(program))
        whenever(user.campaign).thenReturn(campaign)
        whenever(usersClient.findById("userId")).thenReturn(user)

        crmService.sendToCrm("dealerId", "userId", null)
        verify(leadClient, times(1)).sendToCrm(anyOrNull(), any(), any())
    }
}
