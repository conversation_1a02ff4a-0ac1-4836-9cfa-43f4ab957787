package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.DealerChatConfigClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerChatConfig;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.ToggleConfigRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpSession;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ChatFeatureToggleHandlerTest {

    @Mock
    private DealerChatConfigClient dealerChatConfigClient;

    @Mock
    private HttpSession session;

    @InjectMocks
    private ChatFeatureToggleHandler chatFeatureToggleHandler;

    private ToggleConfigRequest toggleConfigRequest;
    private final String dealerId = "testDealer";
    private final String programId = "testProgram";

    @BeforeEach
    public void setup() {
        toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setIsEnabled(true);
    }

    @Test
    void testSupports_WhenConfigTypeIsChat_ShouldReturnTrue() {
        assertTrue(chatFeatureToggleHandler.supports("chat"));
    }

    @Test
    void testSupports_WhenConfigTypeIsNotChat_ShouldReturnFalse() {
        assertFalse(chatFeatureToggleHandler.supports("other"));
    }

    @Test
    void testHandleFeatureToggle_WhenSuccessful_ShouldReturnDealerProgram() {
        DealerChatConfig savedChatConfig = DealerChatConfig.builder()
            .dealerId(dealerId)
            .programId(programId)
            .chatEnabled(true)
            .build();

        when(dealerChatConfigClient.upsertDealerChatConfig(any(DealerChatConfig.class))).thenReturn(savedChatConfig);

        DealerProgram result = chatFeatureToggleHandler.handleFeatureToggle(dealerId, programId, toggleConfigRequest);

        assertNotNull(result);
        assertTrue(result.getIsChatEnabled());
        assertEquals(savedChatConfig, result.getDealerChatConfig());
        verify(dealerChatConfigClient, times(1)).upsertDealerChatConfig(any(DealerChatConfig.class));
    }

    @Test
    void testHandleFeatureToggle_WhenConfigIsNull_ShouldThrowInternalServerError() {
        when(dealerChatConfigClient.upsertDealerChatConfig(any(DealerChatConfig.class))).thenReturn(null);

        assertThrows(InternalServerError.class, () -> {
            chatFeatureToggleHandler.handleFeatureToggle(dealerId, programId, toggleConfigRequest);
        });

        verify(dealerChatConfigClient, times(1)).upsertDealerChatConfig(any(DealerChatConfig.class));
    }

    @Test
    void testHandleFeatureToggle_WhenChatEnabledIsNull_ShouldThrowInternalServerError() {
        DealerChatConfig savedChatConfig = DealerChatConfig.builder()
            .dealerId(dealerId)
            .programId(programId)
            .chatEnabled(null)
            .build();

        when(dealerChatConfigClient.upsertDealerChatConfig(any(DealerChatConfig.class))).thenReturn(savedChatConfig);

        assertThrows(InternalServerError.class, () -> {
            chatFeatureToggleHandler.handleFeatureToggle(dealerId, programId, toggleConfigRequest);
        });

        verify(dealerChatConfigClient, times(1)).upsertDealerChatConfig(any(DealerChatConfig.class));
    }
}
