package com.carsaver.partner.service.user;

import com.carsaver.partner.client.dealer.DealerClient;
import com.carsaver.partner.model.user.CreditProfile;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreditProfileServiceTest {

    @Mock
    private DealerClient dealerClient;

    @InjectMocks
    private CreditProfileService creditProfileService;

    @Test
    void getCreditProfile_shouldReturnNull_whenNoCreditProfileExists() {
        when(dealerClient.getCreditProfile("user123")).thenReturn(null);
        assertNull(creditProfileService.getCreditProfile("user123"));
    }

    @Test
    void getCreditProfile_shouldReturnProcessedCreditProfile_whenProfileExists() {
        ZonedDateTime scoreDate = ZonedDateTime.now().minusDays(3);
        CreditProfile creditProfile = new CreditProfile();
        creditProfile.setScoreDate(scoreDate);
        creditProfile.setCreditEvaluator(CreditProfile.CreditEvaluator.BUREAU);
        when(dealerClient.getCreditProfile("user123")).thenReturn(creditProfile);

        CreditProfile result = creditProfileService.getCreditProfile("user123");

        assertNotNull(result);
        assertTrue(result.isPreQualified());
        assertNotNull(result.getPreQualifiedExpirationDate());
        assertEquals(scoreDate.plusDays(7), result.getPreQualifiedExpirationDate());
        assertFalse(result.isPreQualifiedExpired());
    }

    @Test
    void getCreditProfile_shouldMarkExpired_whenPreQualificationExpired() {
        ZonedDateTime scoreDate = ZonedDateTime.now().minusDays(10);
        CreditProfile creditProfile = new CreditProfile();
        creditProfile.setScoreDate(scoreDate);
        creditProfile.setCreditEvaluator(CreditProfile.CreditEvaluator.BUREAU);
        when(dealerClient.getCreditProfile("user123")).thenReturn(creditProfile);

        CreditProfile result = creditProfileService.getCreditProfile("user123");

        assertNotNull(result);
        assertTrue(result.isPreQualified());
        assertTrue(result.isPreQualifiedExpired());
    }

    @Test
    void getCreditProfile_shouldNotPreQualify_whenEvaluatorIsNotBureau() {
        ZonedDateTime scoreDate = ZonedDateTime.now();
        CreditProfile creditProfile = new CreditProfile();
        creditProfile.setScoreDate(scoreDate);
        creditProfile.setCreditEvaluator(CreditProfile.CreditEvaluator.CUSTOMER);
        when(dealerClient.getCreditProfile("user123")).thenReturn(creditProfile);

        CreditProfile result = creditProfileService.getCreditProfile("user123");

        assertNotNull(result);
        assertFalse(result.isPreQualified());
        assertNull(result.getPreQualifiedExpirationDate());
        assertFalse(result.isPreQualifiedExpired());
    }

    @Test
    void calculate_isPreQualified() {
        assertFalse(CreditProfileService.calculateIsPreQualified(null));
        assertFalse(CreditProfileService.calculateIsPreQualified(CreditProfile.CreditEvaluator.CUSTOMER));
        assertFalse(CreditProfileService.calculateIsPreQualified(CreditProfile.CreditEvaluator.FINANCIER));
        assertTrue(CreditProfileService.calculateIsPreQualified(CreditProfile.CreditEvaluator.BUREAU));
    }

    @Test
    void calculate_preQualifiedExpirationDate() {
        assertNull(CreditProfileService.calculatePreQualifiedExpirationDate(false, ZonedDateTime.now()));
        assertNull(CreditProfileService.calculatePreQualifiedExpirationDate(false, ZonedDateTime.now().minusDays(10)));

        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime sevenDaysFromToday = now.plusDays(7);
        assertEquals(sevenDaysFromToday, CreditProfileService.calculatePreQualifiedExpirationDate(true, now));
    }

    @Test
    void calculate_isPreQualifiedExpired() {
        ZonedDateTime expired = ZonedDateTime.now().minusDays(1);
        assertFalse(CreditProfileService.calculateIsPreQualifiedExpired(false, expired), "Only Prequalified score can expire");
        assertTrue(CreditProfileService.calculateIsPreQualifiedExpired(true, expired));

        ZonedDateTime notExpiredSameDate = ZonedDateTime.now();
        assertFalse(CreditProfileService.calculateIsPreQualifiedExpired(false, notExpiredSameDate), "Only Prequalified score can expire");
        assertFalse(CreditProfileService.calculateIsPreQualifiedExpired(true, notExpiredSameDate));

        ZonedDateTime notExpiredBy1Day = ZonedDateTime.now().plusDays(1);
        assertFalse(CreditProfileService.calculateIsPreQualifiedExpired(false, notExpiredBy1Day), "Only Prequalified score can expire");
        assertFalse(CreditProfileService.calculateIsPreQualifiedExpired(true, notExpiredBy1Day));
    }

}
