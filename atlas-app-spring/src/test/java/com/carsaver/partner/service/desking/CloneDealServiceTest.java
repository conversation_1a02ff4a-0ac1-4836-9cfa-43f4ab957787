package com.carsaver.partner.service.desking;

import com.carsaver.core.StockType;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.QuoteView;
import com.carsaver.magellan.model.TaxesView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.FinanceConfig;
import com.carsaver.magellan.model.chrome.ModelView;
import com.carsaver.magellan.model.chrome.StyleView;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.deal.DealSheet.DealerFeeItem;
import com.carsaver.magellan.model.deal.DealSheet.RebateLineItem;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.magellan.model.payoff.PayoffSource;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.exception.DealNotFoundException;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.ClonedDealResponse;
import com.carsaver.partner.service.DealerFeesService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * CloneDealServiceTest
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CloneDealServiceTest {

    private static final String INVENTORYSERVICEURL = "http://localhost:8080/inventory";
    public static final String ADVANCE_EDITING = "Advanced";

    public static final Long ORIGINAL_CERTIFICATE_ID = 12456L;
    @Mock
    private NissanWebClient client;

    @Mock
    private DealerFeesService dealerFeesService;

    @Mock
    private FinancierClient financierClient;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @Mock
    private CloneDealClient cloneDealClient;

    @Mock
    private Environment environment;

    @InjectMocks
    private CloneDealService cloneDealService;

    @BeforeEach
    void init(){
        ReflectionTestUtils.setField(cloneDealService, "inventoryServiceUrl", INVENTORYSERVICEURL);
    }


    @Test
    void upsertDealTest() throws JsonProcessingException {

        CloneDealRequest cloneDealRequest =  CloneDealRequest.builder()
            .id(123L)
            .dealerId("123")
            .userId("123")
            .build();
        ClonedDealResponse dealResponse = ClonedDealResponse
            .builder()
            .id(123L)
            .dealerId("123")
            .userId("123")
            .build();
        when(client.post(anyString(), any() ,any(),anyString()))
            .thenReturn(dealResponse);
        ClonedDealResponse response = cloneDealService.upsertDeal(cloneDealRequest);
        assertNotNull(response);
    }

    @Test
    void upsertDeal_dealNotfoundTest() throws JsonProcessingException {

        CloneDealRequest cloneDealRequest =  CloneDealRequest.builder().id(123L).dealerId("123").userId("123").build();
        when(client.post(anyString(), any() ,any(),anyString()))
            .thenThrow(new DealNotFoundException("Deal not find for given data!"));
        Assert
            .assertThrows(DealNotFoundException.class, ()-> cloneDealService.upsertDeal(cloneDealRequest));
    }

    @Test
    void retrieveClonedDealById_SuccessTest() {
        ClonedDealResponse response = buildCloneDealResponse();
        when(client.get(anyString(), any(), anyString())).thenReturn(response);
        ClonedDealResponse result = cloneDealService.retrieveClonedDealById(1001L);
        assertEquals(result, response);
    }

    @Test
    void retrieveClonedDealById_DealNotFoundExceptionTest() {
        when(client.get(anyString(), any(), anyString())).thenThrow(new DealNotFoundException("Deal Not Found"));
        assertThrows(DealNotFoundException.class, () -> cloneDealService.retrieveClonedDealById(1591L));
    }

    @Test
    void retrieveClonedDealByCertificateId_SuccessTest() {
        ClonedDealResponse response = buildCloneDealResponse();
        when(client.get(anyString(), any(), anyString())).thenReturn(response);
        ClonedDealResponse result = cloneDealService.retrieveClonedDealByCertificateId(1001);
        assertEquals(result, response);
    }

    ClonedDealResponse buildCloneDealResponse() {
        ClonedDealResponse result = ClonedDealResponse.builder()
            .capCostReduction(new ClonedDealResponse.CapCostReduction())
            .id(1001L)
            .clonedBy("Test")
            .dealerId(UUID.randomUUID().toString())
            .certificateId(10000)
            .dueAtSigning(new ClonedDealResponse.DueAtSigning())
            .dueAtSigningTotal(100.0d)
            .financeDetails(new ClonedDealResponse.FinanceDetails())
            .capCostReduction(new ClonedDealResponse.CapCostReduction())
            .capCostReductionTotal(100.0)
            .customerRebates(new ClonedDealResponse.CustomerRebates())
            .rebatesTotal(100.0)
            .inceptions(new ClonedDealResponse.Inceptions())
            .inceptionsTotal(100.0)
            .grossCapitalCost(new ClonedDealResponse.GrossCapitalCost())
            .grossCapitalCostTotal(100.0)
            .monthlyPayment(new ClonedDealResponse.MonthlyPayment())
            .monthlyPaymentTotal(100.0)
            .tradeAllowance(new ClonedDealResponse.TradeAllowance())
            .tradeAllowanceTotal(100.0)
            .build();
        return result;
    }

    @Test
    void createCloneDealRequestNulls() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);
        Long originalDealId = null;
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, originalDealId, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getCapCostReduction());
        assertNotNull(request.getAddons());
        assertNotNull(request.getDueAtSigning());
        assertNotNull(request.getVehicleDetail());
        assertNotNull(request.getFinanceDetails());
        assertNotNull(request.getGrossCapitalCost());
        assertNotNull(request.getInceptions());
        assertNotNull(request.getMonthlyPayment());
        assertNotNull(request.getRebates());
        assertNotNull(request.getTradeAllowance());
        assertNotNull(request.getTaxesAndFees());
    }

    @Test
    void createCloneDealCapCostReduction() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);


        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getCapCostReduction());
        assertNotNull(request.getCapCostReduction().getLineItems());
    }

    @Test
    void createCloneDealAddOns() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getAddons());
        assertNotNull(request.getAddons().getLineItems());
        assertTrue(request.getAddons().getLineItems().isEmpty());
    }

    @Test
    void createCloneDealRequestDueAtSigning() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);

        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(dealSheet.getCashDownPayment()).thenReturn(10.00);
        when(dealSheet.getTradeValue()).thenReturn(25.00);
        when(dealSheet.getTradePayoff()).thenReturn(25.00);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getDueAtSigning());
        assertEquals("10.0", request.getDueAtSigning().getConsumerCash());
        assertNotNull(request.getDueAtSigning().getLineItems());
        assertTrue(request.getDueAtSigning().getLineItems().isEmpty());
        assertEquals(50.00, request.getDueAtSigning().getPositiveTradeEquity(), 0);
    }

    @Test
    void createCloneDealRequestVehicleDetail() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);
        VehicleView vehicleView = mock(VehicleView.class);

        when(deal.getVehicle()).thenReturn(vehicleView);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(vehicleView.getInvoicePrice()).thenReturn(1000);
        when(vehicleView.getMsrp()).thenReturn(2000);
        when(dealSheet.getSalePriceByProgram()).thenReturn(3000.0);
        when(vehicleView.getStockNumber()).thenReturn("stock");
        when(vehicleView.getStockType()).thenReturn(StockType.NEW);
        when(vehicleView.getVin()).thenReturn("vin");
        when(vehicleView.getInteriorColor()).thenReturn("black");
        when(vehicleView.getExteriorColor()).thenReturn("white");
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getVehicleDetail());
        assertEquals(1000, request.getVehicleDetail().getInvoice(), 0);
        assertEquals(2000, request.getVehicleDetail().getMsrp(), 0);
        assertEquals(3000.0, request.getVehicleDetail().getSellingPrice(), 0);
        assertEquals("stock", request.getVehicleDetail().getStockNumber());
        assertEquals("NEW", request.getVehicleDetail().getVehicleType());
        assertEquals("vin", request.getVehicleDetail().getVin());
        assertEquals("black", request.getVehicleDetail().getInteriorColor());
        assertEquals("white", request.getVehicleDetail().getExteriorColor());
    }

    @Test
    void createCloneDealRequestFinanceDetails() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);
        QuoteView quote = mock(QuoteView.class);
        FinancierView financierView = mock(FinancierView.class);
        CampaignView campaignView = mock(CampaignView.class);
        FinanceConfig financeConfig = mock(FinanceConfig.class);

        when(deal.getQuote()).thenReturn(quote);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(financierClient.findById(anyInt())).thenReturn(Optional.of(financierView));
        when(financierView.getName()).thenReturn("financier");
        when(quote.getAcquisitionFee()).thenReturn(25.0);
        when(quote.getFinancierId()).thenReturn(1);
        when(quote.getMoneyFactor()).thenReturn(10.0);
        when(quote.getMileageAllowed()).thenReturn(1000);
        when(quote.getMoneyFactor()).thenReturn(2.0);
        when(quote.getResidual()).thenReturn(BigDecimal.TEN);
        when(quote.getTerm()).thenReturn(1);
        when(quote.getBeacon()).thenReturn(25);
        when(quote.getInterestRate()).thenReturn(.1);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getFinanceDetails());
        assertEquals(2.0, request.getFinanceDetails().getBaseMoneyFactor(), 0);
        assertEquals("financier", request.getFinanceDetails().getFinanceCompany());
        assertNull(request.getFinanceDetails().getLeaseTerminationFee());
        assertEquals(1000, request.getFinanceDetails().getMilesPerYear());
        assertEquals("2.0", request.getFinanceDetails().getMoneyFactoryWithBps());
        assertEquals(10.0, request.getFinanceDetails().getResidualAmount(), 0);
        assertNull(request.getFinanceDetails().getResidualPercentage());
        assertEquals(1, request.getFinanceDetails().getTerm());
        assertEquals("25", request.getFinanceDetails().getTier());
        assertEquals(0.1, request.getFinanceDetails().getApr(), 0);
    }

    @Test
    void createCloneDealRequestGrossCapitalCost() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);
        QuoteView quote = mock(QuoteView.class);
        DealerFeeView dealerFee = new DealerFeeView();
        dealerFee.setName("dealerFee");
        dealerFee.setAmount(75.00);

        when(deal.getQuote()).thenReturn(quote);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(quote.getAcquisitionFee()).thenReturn(25.00);
        when(dealerFeesService.getNotInceptionsFeesList(any(), any())).thenReturn(List.of(dealerFee));
        when(dealSheet.getSalePriceByProgram()).thenReturn(3000.0);
        when(dealSheet.getTradeValue()).thenReturn(-45.0);
        when(dealSheet.getTradePayoff()).thenReturn(-45.0);
        when(splitFeatureFlags.isPaasIntegrationEnabled(any(), any(), any())).thenReturn(true);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getGrossCapitalCost());
        assertEquals("25.0", request.getGrossCapitalCost().getAcquisitionFee());
        assertNotNull(request.getGrossCapitalCost().getLineItems());
        assertFalse(request.getGrossCapitalCost().getLineItems().isEmpty());
        assertEquals("dealerFee", request.getGrossCapitalCost().getLineItems().get(0).getName());
        assertEquals(75.00, request.getGrossCapitalCost().getLineItems().get(0).getAmount(), 0);
        assertEquals(3000.0, request.getGrossCapitalCost().getSellingPrice(), 0);
        assertEquals("-90.0", request.getGrossCapitalCost().getTradeBalance());
    }

    @Test
    void createCloneDealRequestInceptions() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);
        Long originalDealId = null;

        DealSheet dealSheet = mock(DealSheet.class);
        QuoteView quote = mock(QuoteView.class);
        DealerFeeView dealerFee = new DealerFeeView();
        dealerFee.setName("dealerFee");
        dealerFee.setAmount(75.00);

        when(deal.getQuote()).thenReturn(quote);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(dealerFeesService.getInceptionsFeesList(any(), any())).thenReturn(List.of(dealerFee));
        when(quote.getLicenseRegistration()).thenReturn(45.0);
        when(quote.getMonthlyPayment()).thenReturn(99.0);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, originalDealId, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getInceptions());
        assertEquals(99.0, request.getInceptions().getFirstMonthlyPayment(), 0);
        assertNotNull(request.getInceptions().getLineItems());
        assertFalse(request.getInceptions().getLineItems().isEmpty());
        assertEquals("dealerFee", request.getInceptions().getLineItems().get(0).getName());
        assertEquals(75.00, request.getInceptions().getLineItems().get(0).getAmount(), 0);
        assertEquals(45.0, request.getInceptions().getTitleAndRegistration(), 0);
    }

    @Test
    void createCloneDealMonthlyPayment() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);
        QuoteView quote = mock(QuoteView.class);
        QuoteView baseQuote = mock(QuoteView.class);
        TaxesView taxes = mock(TaxesView.class);

        when(deal.getQuote()).thenReturn(quote);
        when(deal.getBaseQuote()).thenReturn(baseQuote);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(baseQuote.getMonthlyPayment()).thenReturn(99.0);
        when(quote.getTaxes()).thenReturn(taxes);
        when(taxes.getSalesTaxRate()).thenReturn(0.1);
        when(quote.isLease()).thenReturn(true);
        when(splitFeatureFlags.isPaasIntegrationEnabled(any(), any(), any())).thenReturn(false);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getMonthlyPayment());
        assertEquals(99.0, request.getMonthlyPayment().getFirstMonthlyPayment(), 0);
        assertEquals(0.099, request.getMonthlyPayment().getMonthlyTax(), 0);
    }

    @Test
    void createCloneDealRebates() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);
        QuoteView quote = mock(QuoteView.class);
        RebateLineItem rebate = new RebateLineItem();
        rebate.setName("rebate");
        rebate.setAmount(75.00);

        when(deal.getQuote()).thenReturn(quote);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(dealSheet.getCustomerRebates()).thenReturn(List.of(rebate));
        when(quote.getLicenseRegistration()).thenReturn(45.0);
        when(quote.getMonthlyPayment()).thenReturn(99.0);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);


        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getRebates());
        assertNotNull(request.getRebates().getLineItems());
        assertFalse(request.getRebates().getLineItems().isEmpty());
        assertEquals("rebate", request.getRebates().getLineItems().get(0).getName());
        assertEquals(75.00, request.getRebates().getLineItems().get(0).getAmount(), 0);
    }

    @Test
    void createCloneDealTradeAllowance() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);
        QuoteView quote = mock(QuoteView.class);
        UserVehicleView vehicle = mock(UserVehicleView.class);
        StyleView style = mock(StyleView.class);
        ModelView model = mock(ModelView.class);

        when(deal.getQuote()).thenReturn(quote);
        when(deal.getTradeVehicle()).thenReturn(vehicle);
        when(vehicle.getStyle()).thenReturn(style);
        when(vehicle.getPayoffSource()).thenReturn(PayoffSource.MANUAL);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(dealSheet.getTradeValue()).thenReturn(45.0);
        when(dealSheet.getTradePayoff()).thenReturn(35.0);
        when(style.getModel()).thenReturn(model);
        when(style.getTrim()).thenReturn("trim");
        when(model.getName()).thenReturn("model");
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getTradeAllowance());
        assertEquals("45.0", request.getTradeAllowance().getAllowance());
        assertEquals("manual", request.getTradeAllowance().getAppraisalType());
        assertEquals("model", request.getTradeAllowance().getModel());
        assertEquals(10.0, request.getTradeAllowance().getNetTrade(), 0);
        assertEquals(35.0, request.getTradeAllowance().getPayoff(), 0);
        assertEquals("trim", request.getTradeAllowance().getTrim());
    }

    @Test
    void createCloneDealRequestTaxesAndFees() {
        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);

        DealSheet dealSheet = mock(DealSheet.class);
        QuoteView quote = mock(QuoteView.class);
        DealerFeeItem dealerFeeItem = new DealerFeeItem();
        dealerFeeItem.setName("dealerFee");
        dealerFeeItem.setAmount(75.00);

        when(deal.getQuote()).thenReturn(quote);
        when(deal.getDealSheet()).thenReturn(dealSheet);
        when(dealSheet.getDealerFees()).thenReturn(List.of(dealerFeeItem));
        when(quote.getLicenseRegistration()).thenReturn(45.0);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest request = cloneDealService.createCloneDealRequest(dealer, deal, ORIGINAL_CERTIFICATE_ID, ADVANCE_EDITING);

        assertNotNull(request);
        assertNotNull(request.getTaxesAndFees());
        assertNotNull(request.getTaxesAndFees().getLineItems());
        assertFalse(request.getTaxesAndFees().getLineItems().isEmpty());
        assertEquals(45.00, request.getTaxesAndFees().getTitleAndRegistration(), 0);
        assertEquals("dealerFee", request.getTaxesAndFees().getLineItems().get(0).getName());
        assertEquals(75.00, request.getTaxesAndFees().getLineItems().get(0).getAmount(), 0);
    }

    @Test
    void saveDealTest() throws JsonProcessingException {

        CloneDealRequest cloneDealRequest =  CloneDealRequest.builder()
            .id(123L)
            .dealerId("123")
            .userId("123")
            .build();
        ClonedDealResponse dealResponse = ClonedDealResponse
            .builder()
            .id(123L)
            .dealerId("123")
            .userId("123")
            .build();
        when(client.post(anyString(), any() ,any(),anyString()))
            .thenReturn(dealResponse);
        ClonedDealResponse response = cloneDealService.saveCloneDeal(cloneDealRequest);
        assertNotNull(response);
    }

    @Test
    void cloneDealTest() throws JsonProcessingException {

        DealerView dealer = mock(DealerView.class);
        CertificateView deal = mock(CertificateView.class);
        when(cloneDealClient.getDealAccessories(any())).thenReturn(Optional.empty());
        when(cloneDealClient.getProtectionProduct(any(), any(), any(), any())).thenReturn(null);
        when(environment.acceptsProfiles(Profiles.of("local", "beta"))).thenReturn(true);

        CloneDealRequest cloneDealRequest =  CloneDealRequest.builder()
            .id(123L)
            .dealerId("123")
            .userId("123")
            .build();
        ClonedDealResponse dealResponse = ClonedDealResponse
            .builder()
            .id(123L)
            .dealerId("123")
            .userId("123")
            .build();
        when(client.post(anyString(), any() ,any(),anyString()))
            .thenReturn(dealResponse);
        ClonedDealResponse response = cloneDealService.cloneDeal(dealer,deal,123, ADVANCE_EDITING);
        assertNotNull(response);
    }

    @Test
    void retrieveVehicle_SuccessTest() {
        VehicleView response = new VehicleView();
        response.setVin("VIN");
        when(client.get(anyString(), any(), anyString())).thenReturn(response);
        VehicleView vehicleView = cloneDealService.retrieveVehicle("1001", "1001", "1001");
        assertEquals(vehicleView, response);
    }

}
