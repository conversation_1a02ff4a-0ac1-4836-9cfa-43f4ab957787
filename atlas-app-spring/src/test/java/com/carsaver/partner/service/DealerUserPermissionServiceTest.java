package com.carsaver.partner.service;

import com.carsaver.magellan.model.security.DealerPermissionView;
import com.carsaver.partner.model.DealerUserPermissionGroupNode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class DealerUserPermissionServiceTest {

    @Test
    public void testSpin() {
        List<DealerPermissionView> dealerPermissionViews = initDealerPermissionViews();

        List<DealerUserPermissionGroupNode> permissionGroupNodes = DealerUserPermissionService.convertToDealerPermissionModels(dealerPermissionViews);

        assertDepth("Inventory", 0, permissionGroupNodes);
        assertDepth("Inventory/New", 1, permissionGroupNodes);
        assertDepth("Inventory/New/Read New Inventory Pricing", 2, permissionGroupNodes);

        assertDepth("Customer", 0, permissionGroupNodes);
        assertDepth("Customer/Customer Read Basic Info", 1, permissionGroupNodes);
    }

    private void assertDepth(String groupName, int expectedDepth, List<DealerUserPermissionGroupNode> nodes) {
        int actualDepth = findDepth(groupName.split("/"), nodes, 0);
        Assertions.assertEquals(expectedDepth, actualDepth);
    }

    private int findDepth(String[] dirs, List<DealerUserPermissionGroupNode> nodes, int depth) {
        for(DealerUserPermissionGroupNode node : nodes) {
             if(node.getDescription().equals(dirs[depth])) {
                 return depth == dirs.length - 1 ? depth : findDepth(dirs, node.getChildren(), ++depth);
             }
        }
        return depth;
    }

    private static List<DealerPermissionView> initDealerPermissionViews() {
        List<DealerPermissionView> dealerPermissionViews = new ArrayList<>();

        DealerPermissionView readNewInventoryPricing = new DealerPermissionView();
        readNewInventoryPricing.setDescription("Read New Inventory Pricing");
        readNewInventoryPricing.setGroupName("Inventory/New");
        readNewInventoryPricing.setId(11);
        readNewInventoryPricing.setName("inventory:new-pricing:read");
        dealerPermissionViews.add(readNewInventoryPricing);

        DealerPermissionView editNewInventoryPricing = new DealerPermissionView();
        editNewInventoryPricing.setDescription("Edit New Inventory Pricing");
        editNewInventoryPricing.setGroupName("Inventory/New");
        editNewInventoryPricing.setId(12);
        editNewInventoryPricing.setImpliedPermissions(Arrays.asList(11));
        editNewInventoryPricing.setName("inventory:new-pricing:edit");
        dealerPermissionViews.add(editNewInventoryPricing);

        DealerPermissionView customerReadBasicInfo = new DealerPermissionView();
        customerReadBasicInfo.setDescription("Customer Read Basic Info");
        customerReadBasicInfo.setGroupName("Customer");
        customerReadBasicInfo.setId(15);
        customerReadBasicInfo.setName("customer:read");
        dealerPermissionViews.add(customerReadBasicInfo);

        return dealerPermissionViews;
    }
}
