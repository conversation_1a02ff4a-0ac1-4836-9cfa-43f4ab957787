package com.carsaver.partner.service;

import com.carsaver.accessories.api.DealAccessories;
import com.carsaver.accessories.api.DealAccessory;
import com.carsaver.accessories.client.AccessoriesServiceClient;
import com.carsaver.partner.exception.MissingRequiredFieldException;
import com.carsaver.partner.model.deal.AccessoriesModel;
import com.carsaver.partner.model.deal.AccessoryItemModel;
import com.carsaver.partner.model.mapper.AccessoryItemModelMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AccessoriesServiceTest {

    public static final Integer CERTIFICATE_ID = 100369;
    public static final int ZERO_EXPECTED = 0;
    private AccessoryItemModelMapper accessoryItemModelMapper = mock(AccessoryItemModelMapper.class);
    private AccessoriesServiceClient accessoriesServiceClient = mock(AccessoriesServiceClient.class);
    Double TOTAL_PARTS_AMOUNT = 180D;
    Double TOTAL_LABOR_AMOUNT = 93D;
    Double TOTAL = 273D;

    @InjectMocks
    @Spy
    AccessoriesService accessoriesService;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(accessoriesService, "featureToggleAccessories", true);
    }

    @Test
    void testNullCertificateId(){
        assertThrows(MissingRequiredFieldException.class, () -> accessoriesService.getDealAccessories(null));
    }

    @Test
    void testNullResponseFromAccessoryService(){
        when(accessoriesServiceClient.getDealAccessoriesByCertificateId(any())).thenReturn(null);
        Optional<AccessoriesModel> accessoriesModel = accessoriesService.getDealAccessories(CERTIFICATE_ID);
        assertTrue(accessoriesModel.isEmpty());
    }

    @Test
    void testFailMappingAccessoryService(){
        when(accessoriesServiceClient.getDealAccessoriesByCertificateId(any())).thenReturn(this.generateDealAccessoryList());
        Optional<AccessoriesModel> accessoriesModel = accessoriesService.getDealAccessories(CERTIFICATE_ID);
        assertFalse(accessoriesModel.isEmpty());
        assertEquals(ZERO_EXPECTED,accessoriesModel.get().getTotal());
        assertEquals(ZERO_EXPECTED,accessoriesModel.get().getTotalPartsPrice());
        assertEquals(ZERO_EXPECTED,accessoriesModel.get().getTotalLaborAmount());
    }

    @Test
    void testNullAccessoryItemsList(){
        AccessoriesModel accessoriesModel = new AccessoriesModel();
        assertEquals(ZERO_EXPECTED,accessoriesModel.getTotal());
        assertEquals(ZERO_EXPECTED,accessoriesModel.getTotalPartsPrice());
        assertEquals(ZERO_EXPECTED,accessoriesModel.getTotalLaborAmount());
    }

    @Test
    void testEmptyAccessoryItemsList(){
        AccessoriesModel accessoriesModel = new AccessoriesModel();
        accessoriesModel.setAccessoriesItems(new ArrayList<>());

        assertEquals(ZERO_EXPECTED,accessoriesModel.getTotal());
        assertEquals(ZERO_EXPECTED,accessoriesModel.getTotalPartsPrice());
        assertEquals(ZERO_EXPECTED,accessoriesModel.getTotalLaborAmount());
    }

    @Test
    void getDealAccessories() {
        var dealAccessoryList = this.generateDealAccessoryList();
        when(accessoriesServiceClient.getDealAccessoriesByCertificateId(any())).thenReturn(dealAccessoryList);
        this.setMockForAccessoryModelMapping(dealAccessoryList.getAccessories());

        Optional<AccessoriesModel> accessoriesModel = accessoriesService.getDealAccessories(CERTIFICATE_ID);
        assertFalse(accessoriesModel.isEmpty());
        assertEquals(TOTAL,accessoriesModel.get().getTotal());
        assertEquals(TOTAL_PARTS_AMOUNT,accessoriesModel.get().getTotalPartsPrice());
        assertEquals(TOTAL_LABOR_AMOUNT,accessoriesModel.get().getTotalLaborAmount());

    }

    private DealAccessories generateDealAccessoryList() {
        List<DealAccessory> itemsList = new ArrayList<>();
        for(int i =0; i < 3; i++){
            var dealAccessory = DealAccessory.builder()
                .id(String.valueOf(i))
                .name("item name "+i)
                .description("hello world")
                .disclaimers("test disclaimer")
                .partsPrice(BigDecimal.valueOf(i*50D+10))
                .laborPrice(BigDecimal.valueOf(i*20D+11))
                .monthlyPmt(BigDecimal.valueOf(i*10D+12))
                .build();

            itemsList.add(dealAccessory);
        }
        DealAccessories dealAccessories = new DealAccessories();
        dealAccessories.setAccessories(itemsList);
        return dealAccessories;
    }

    private void setMockForAccessoryModelMapping(List<DealAccessory> dealAccessory) {
        List<DealAccessory> itemsList = new ArrayList<>();

        for(int i =0; i < 3; i++){
            var accessoryItems = AccessoryItemModel.builder() .name("item name "+i)
                .description("hello world")
                .disclaimers("test disclaimer")
                .partsPrice(i*50D+10)
                .laborPrice(i*20D+11)
                .monthlyPayment(i*10D+12)
                .build();

            when(accessoryItemModelMapper.toAccessoryItemModel(dealAccessory.get(i))).thenReturn(accessoryItems);
        }

    }
}
