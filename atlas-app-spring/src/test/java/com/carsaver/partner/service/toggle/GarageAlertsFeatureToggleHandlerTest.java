package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.servlet.http.HttpSession;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GarageAlertsFeatureToggleHandlerTest {

    @Mock
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Mock
    private SplitFeatureFlags splitFeatureFlags;

    @Mock
    private HttpSession session;

    @InjectMocks
    private GarageAlertsFeatureToggleHandler toggleHandler;



    @Test
    void shouldHandleFeatureToggleWhenRequestIsSuccessful() {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setConfigType("garage_alerts");
        toggleConfigRequest.setIsEnabled(true);

        FeatureSubscriptionResponse response = new FeatureSubscriptionResponse();
        response.setActive(true);

        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class)))
                .thenReturn(response);

        when(splitFeatureFlags.isGarageAlertsFeatureEnabled("dealer123", "program123"))
                .thenReturn(true);

        DealerProgram result = toggleHandler.handleFeatureToggle("dealer123", "program123", toggleConfigRequest);

        assertNotNull(result);
        assertTrue(result.getIsGarageAlertsEnabled());
        assertTrue(result.getIsGarageAlertsFeatureEnabled());

        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
        verify(splitFeatureFlags, times(1)).isGarageAlertsFeatureEnabled("dealer123", "program123");
    }

    @Test
    void shouldThrowInternalServerErrorWhenResponseIsNull() {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setConfigType("garage_alerts");
        toggleConfigRequest.setIsEnabled(false);

        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class)))
                .thenReturn(null);

       assertThrows(InternalServerError.class, () ->
                toggleHandler.handleFeatureToggle("dealer123", "program123", toggleConfigRequest));


        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void shouldThrowInternalServerErrorWhenActiveIsNull() {
        ToggleConfigRequest toggleConfigRequest = new ToggleConfigRequest();
        toggleConfigRequest.setConfigType("garage_alerts");
        toggleConfigRequest.setIsEnabled(false); // Disable the feature

        FeatureSubscriptionResponse response = new FeatureSubscriptionResponse();
        response.setActive(null);

        when(featureSubscriptionsClient.saveFeatureSubscription(any(FeatureSubscriptionRequest.class)))
                .thenReturn(response);

        assertThrows(InternalServerError.class, () ->
                toggleHandler.handleFeatureToggle("dealer123", "program123", toggleConfigRequest));


        verify(featureSubscriptionsClient, times(1)).saveFeatureSubscription(any(FeatureSubscriptionRequest.class));
    }

    @Test
    void shouldReturnFalseOnSupportsForWrongConfigType() {
        assertFalse(toggleHandler.supports("others"));
    }

    @Test
    void shouldReturnTrueOnSupportsForCorrectConfigType() {
        assertTrue(toggleHandler.supports("garage_alerts"));
    }
}
