package com.carsaver.partner.service;

import com.carsaver.magellan.client.BasicUserAssociationClient
import com.carsaver.magellan.client.FollowUpManagerClient
import com.carsaver.magellan.client.ProgramManagerClient
import com.carsaver.magellan.client.SalesManagerClient
import com.carsaver.magellan.client.UserClient
import com.carsaver.magellan.model.UserView
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation
import com.carsaver.magellan.model.user.ProgramManagerRoleAssociation
import com.carsaver.partner.client.UserServiceClient
import com.carsaver.partner.service.split_io.SplitFeatureFlags
import com.carsaver.partner.web.form.DealerPermissionsForm
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.util.*


class DealerUserServiceTest {

    private val userClient: UserClient = mock()
    private val basicUserAssociationClient: BasicUserAssociationClient = mock()
    private val programManagerClient: ProgramManagerClient = mock()
    private val salesManagerClient: SalesManagerClient = mock()
    private val followUpManagerClient: FollowUpManagerClient = mock()
    private val userServiceClient: UserServiceClient = mock()
    private val splitFeatureFlags: SplitFeatureFlags = mock()

    private val dealerUserService = DealerUserService(
        userClient,
        basicUserAssociationClient,
        programManagerClient,
        salesManagerClient,
        followUpManagerClient,
        userServiceClient,
        splitFeatureFlags,
    )

    @BeforeEach
    fun setUp() {
        // Initialize mocks or any other setup needed before each test
        whenever(splitFeatureFlags.isDealerSalesPersonEnabled(anyString(), any())).thenReturn(true)
    }

    @Test
    fun testAssociateUserToDealer() {
        // Setup
        val dealerId = "dealer123"
        val userId = "user456"
        val permissions = listOf(1, 2, 3)

        val form = DealerPermissionsForm()
        form.programManager = true
        form.pmStockType = "NEW"
        form.followupManager = true
        form.fmStockType = "CONFIGURED" // Should convert to null
        form.salesManager = true
        form.smStockType = "USED"
        form.contactType = "PRIMARY"
        form.salesPerson = true

        // Mock existing program manager association
        val pmAssociation = ProgramManagerRoleAssociation()
        pmAssociation.id = 123L
        whenever(programManagerClient.getProgramManager(dealerId, userId)).thenReturn(Optional.of(pmAssociation))

        // Mock no existing followup manager association
        whenever(followUpManagerClient.getFollowUpManager(dealerId, userId)).thenReturn(Optional.empty())

        // Mock no existing sales manager association
        whenever(salesManagerClient.getProgramManager(dealerId, userId)).thenReturn(Optional.empty())

        // Mock existing basic user association
        val basicAssociation = BasicDealerUserRoleAssociation()
        basicAssociation.id = 123L
        whenever(basicUserAssociationClient.getBasicUserAssociation(dealerId, userId)).thenReturn(Optional.of(basicAssociation))

        // Execute
        dealerUserService.associateUserToDealer(form, dealerId, userId, permissions, Optional.of(UserView()))

        // Verify
        verify(programManagerClient).updateProgramManagerStockType(eq(123L), any())
        verify(programManagerClient, never()).addProgramManager(any())

        verify(followUpManagerClient, never()).removeFollowUpManager(any())
        verify(followUpManagerClient, times(1)).addFollowUpManager(any())

        verify(salesManagerClient).addProgramManager(any())

        verify(basicUserAssociationClient).replaceUserPermissions(eq(123L), any())
        verify(basicUserAssociationClient, never()).addUserPermissions(any())

        verify(userServiceClient, never()).unassignSalesPerson(eq("user456"), eq("dealer123"))
        verify(userServiceClient, times(1)).assignSalesPerson(eq("user456"), eq("dealer123"))
    }

    @Test
    fun testAssociateSalesPersonUserToDealer() {
        // Setup
        val dealerId = "dealer123"
        val userId = "user456"
        val permissions = listOf(1, 2, 3)

        val form = DealerPermissionsForm()
        form.programManager = false
        form.followupManager = false
        form.salesManager = false
        form.salesPerson = false

        // Mock existing program manager association
        val pmAssociation = ProgramManagerRoleAssociation()
        pmAssociation.id = 123L
        whenever(programManagerClient.getProgramManager(dealerId, userId)).thenReturn(Optional.of(pmAssociation))

        // Mock no existing followup manager association
        whenever(followUpManagerClient.getFollowUpManager(dealerId, userId)).thenReturn(Optional.empty())

        // Mock no existing sales manager association
        whenever(salesManagerClient.getProgramManager(dealerId, userId)).thenReturn(Optional.empty())

        // Mock existing basic user association
        val basicAssociation = BasicDealerUserRoleAssociation()
        basicAssociation.id = 123L
        whenever(basicUserAssociationClient.getBasicUserAssociation(dealerId, userId)).thenReturn(Optional.of(basicAssociation))

        // Execute
        dealerUserService.associateUserToDealer(form, dealerId, userId, permissions, Optional.of(UserView()))

        // Verify
        verify(programManagerClient, never()).updateProgramManagerStockType(eq(123L), any())
        verify(programManagerClient, never()).addProgramManager(any())

        verify(followUpManagerClient, never()).removeFollowUpManager(any())
        verify(followUpManagerClient, never()).addFollowUpManager(any())

        verify(salesManagerClient, never()).addProgramManager(any())

        verify(basicUserAssociationClient).replaceUserPermissions(eq(123L), any())
        verify(basicUserAssociationClient, never()).addUserPermissions(any())

        verify(userServiceClient, times(1)).unassignSalesPerson(eq("user456"), eq("dealer123"))
        verify(userServiceClient, never()).assignSalesPerson(eq("user456"), eq("dealer123"))
    }
}
