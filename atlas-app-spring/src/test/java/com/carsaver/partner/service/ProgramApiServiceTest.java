package com.carsaver.partner.service;

import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.prospect.UpgradeCampaignClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.partner.customer.CustomerDetailsLinkService;
import com.carsaver.partner.helper.SingleScanResponseIterable;
import com.carsaver.partner.model.DealProgramAndDomain;
import com.carsaver.partner.model.ProspectDTO;
import com.carsaver.partner.model.subscription.Product;
import com.carsaver.partner.repository.DynamoDbRepository;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.ScanRequest;
import software.amazon.awssdk.services.dynamodb.model.ScanResponse;
import software.amazon.awssdk.services.dynamodb.paginators.ScanIterable;
import uk.co.jemos.podam.api.PodamFactory;
import uk.co.jemos.podam.api.PodamFactoryImpl;

import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ProgramApiServiceTest {

    public static final PodamFactory FACTORY = new PodamFactoryImpl();
    public static final String DEALER_ID = "dealer id";
    public static final String DEALER_NAME = "dealer!@' name";
    private final String USER_ID = "USER_ID";


    private final ProgramClient programClient = mock(ProgramClient.class);
    private final CampaignClient campaignClient = mock(CampaignClient.class);
    private final DealerClient dealerClient = mock(DealerClient.class);
    private final DynamoDbRepository dynamoDbRepository = mock(DynamoDbRepository.class);
    private final SplitFeatureFlags splitFeatureFlags = mock(SplitFeatureFlags.class);
    private final UserClient userClient = mock(UserClient.class);
    private final ProspectService prospectService = mock(ProspectService.class);
    private final UpgradeCampaignClient upgradeCampaignClient = mock(UpgradeCampaignClient.class);
    private final com.carsaver.partner.client.dealer.DealerClient localDealerClient = mock(com.carsaver.partner.client.dealer.DealerClient.class);
    private final CustomerDetailsLinkService customerDetailsLinkService = mock(CustomerDetailsLinkService.class);
    private final ProgramApiService programApiService = new ProgramApiService(
        programClient,
        campaignClient,
        dealerClient,
        localDealerClient,
        dynamoDbRepository,
        splitFeatureFlags,
        userClient,
        "digitalRetailCampaignId",
        prospectService,
        upgradeCampaignClient,
        customerDetailsLinkService
    );
    private static MockedStatic<AuthUtils> utilities;


    @Value("${dynamoDB.dealerTable}")
    private String dealerTable;

    @Test
    void testGetDealProgramAndDomain() {
        ScanResponse scanResponse = createScanResponse(DEALER_ID, DEALER_NAME);
        ScanIterable scanIterable = new SingleScanResponseIterable(scanResponse);

        when(dynamoDbRepository.getScanResponses(any(ScanRequest.class))).thenReturn(scanIterable);
        final DealerView dealer = FACTORY.manufacturePojo(DealerView.class);
        dealer.setId(DEALER_ID);
        dealer.setName(DEALER_NAME);
        when(dealerClient.findById(eq(DEALER_ID))).thenReturn(dealer);

        final CampaignView campaign = FACTORY.manufacturePojo(CampaignView.class);
        campaign.setId("campaignId");
        campaign.setDomain("domain");
        campaign.setProgramId("ProgramId");
        when(campaignClient.findByIdNoCache(anyString())).thenReturn(campaign);
        when(campaignClient.findById(anyString())).thenReturn(campaign);

        final Optional<ProgramView> program = Optional.of(FACTORY.manufacturePojo(ProgramView.class));
        program.get().setId("programId");
        program.get().setName("program Name");
        ProductView product = new ProductView();
        product.setId(103);
        program.get().setProduct(product);
        when(programClient.findById(anyString())).thenReturn(program);

        when(splitFeatureFlags.isDealerizedProspectFeatureEnabled(anyString())).thenReturn(true);
        // SUCCESS
        String expected = DEALER_NAME.replaceAll("[^a-zA-Z0-9]", "").toLowerCase().concat(".").concat(campaign.getDomain());
        DealProgramAndDomain response = programApiService.getDealProgramAndDomain(USER_ID, campaign.getId(), dealer.getId(), dealerTable);

        assertEquals(expected, response.getDomain());

        // When Dealer ID is not found in Dynamo DB
        campaign.setDomain("domain");
        scanIterable = new SingleScanResponseIterable(null);

        when(dynamoDbRepository.getScanResponses(any(ScanRequest.class))).thenReturn(scanIterable);

        expected = campaign.getDomain();
        response = programApiService.getDealProgramAndDomain(USER_ID, campaign.getId(), dealer.getId(), dealerTable);

        assertEquals(expected, response.getDomain());

        // When Dealer is not found in our own DB
        campaign.setDomain("domain");
        scanIterable = new SingleScanResponseIterable(scanResponse);

        when(dynamoDbRepository.getScanResponses(any(ScanRequest.class))).thenReturn(scanIterable);
        when(dealerClient.findById(DEALER_ID)).thenReturn(null);

        expected = campaign.getDomain();
        response = programApiService.getDealProgramAndDomain(USER_ID, campaign.getId(), dealer.getId(), dealerTable);

        assertEquals(expected, response.getDomain());

        // SUCCESS with different regex
        String dealerName = "dealer-name";
        campaign.setDomain("domain");

        when(dealerClient.findById(DEALER_ID)).thenReturn(dealer);

        expected = dealerName.replaceAll("[^a-zA-Z0-9]", "").toLowerCase().concat(".").concat(campaign.getDomain());
        response = programApiService.getDealProgramAndDomain(USER_ID, campaign.getId(), dealer.getId(), dealerTable);

        assertEquals(expected, response.getDomain());


        //Split OFF
        dealerName = "dealer-name";
        campaign.setDomain("domain");

        when(dealerClient.findById(DEALER_ID)).thenReturn(dealer);
        when(splitFeatureFlags.isDealerizedProspectFeatureEnabled(anyString())).thenReturn(false);
        expected = campaign.getDomain();
        response = programApiService.getDealProgramAndDomain(USER_ID, campaign.getId(), dealer.getId(), dealerTable);

        assertEquals(expected, response.getDomain());

    }



    private ScanResponse createScanResponse(String dealerId, String dealerName) {
        return ScanResponse
            .builder()
            .items(
                Map.of(
                    "dealerId", AttributeValue.builder().s(dealerId).build(),
                    "dealerName", AttributeValue.builder().s(dealerName).build()
                ))
            .build();
    }


    @Test
    void testGetDealProgramAndDomainForUpgradeCampaign() {
        try (MockedStatic<AuthUtils> mockedStatic = mockStatic(AuthUtils.class)) {
            mockedStatic.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of(USER_ID));

            com.carsaver.partner.model.user.UserView userView = mock(com.carsaver.partner.model.user.UserView.class);
            CampaignView campaignView = mock(CampaignView.class);
            ProgramView programView = mock(ProgramView.class);
            ProductView productView =mock(ProductView.class);
            programView.setProduct(productView);

            final DealerView dealer = FACTORY.manufacturePojo(DealerView.class);

            when(campaignView.getDomain()).thenReturn("bmwofschererville-upgrade.beta.carsaver.com");

            when(productView.getName()).thenReturn("BMW");
            when(productView.getId()).thenReturn(Product.UPGRADE);
            when(programView.getProduct()).thenReturn(productView);
            when(localDealerClient.getUserById(eq(USER_ID))).thenReturn(userView);
            when(userView.getCampaignId()).thenReturn(Optional.of("CAMPAIGN_ID"));
            when(campaignClient.findById(eq("CAMPAIGN_ID"))).thenReturn(campaignView);
            when(programClient.findById(eq(campaignView.getProgramId()))).thenReturn(Optional.of(programView));
            when(splitFeatureFlags.isDealerizedProspectFeatureEnabled(USER_ID)).thenReturn(true);
            when(dealerClient.findById(eq(DEALER_ID))).thenReturn(dealer);
            when(dynamoDbRepository.getScanResponses(any())).thenReturn(new SingleScanResponseIterable(createScanResponse(DEALER_ID, DEALER_NAME)));

            DealProgramAndDomain result = programApiService.getDealProgramAndDomain(USER_ID, DEALER_ID, dealerTable);
            String expectedDomain = dealer.getName().replaceAll("[^a-zA-Z0-9]", "").toLowerCase().concat(".").concat(campaignView.getDomain());
            assertEquals(expectedDomain, result.getDomain());
        }
    }

    @Test
    void testGetDealProgramAndDomainForDigitalRetailV2() {
        try (MockedStatic<AuthUtils> mockedStatic = mockStatic(AuthUtils.class)) {
            ReflectionTestUtils.setField(programApiService, "nissanDigitalRetailCampaignDomain", "digitalretail-v2.beta.carsaver.com");
            mockedStatic.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of(USER_ID));

            com.carsaver.partner.model.user.UserView userView = new com.carsaver.partner.model.user.UserView();
            userView.setSource(com.carsaver.partner.model.source.Source.builder().campaignId("CAMPAIGN_ID").build());

            CampaignView campaignView = mock(CampaignView.class);
            ProductView productView = mock(ProductView.class);
            when(productView.getName()).thenReturn("DigitalRetailV2");
            when(productView.getId()).thenReturn(Product.ECOMMERCE);

            ProgramView programView = mock(ProgramView.class);
            programView.setProduct(productView);

            final DealerView dealer = FACTORY.manufacturePojo(DealerView.class);

            when(campaignView.getId()).thenReturn("CAMPAIGN_ID");
            when(campaignView.getDomain()).thenReturn("digitalretail-v2.beta.carsaver.com");
            when(campaignView.getProgramId()).thenReturn("PROGRAM_ID");
            when(programView.getProduct()).thenReturn(productView);
            when(localDealerClient.getUserById(eq(USER_ID))).thenReturn(userView);
            when(campaignClient.findById(eq("CAMPAIGN_ID"))).thenReturn(campaignView);
            when(programClient.findById(eq("PROGRAM_ID"))).thenReturn(Optional.of(programView));
            when(splitFeatureFlags.isDigitalRetailEnabledForDealer(anyString(), any())).thenReturn(true);
            when(dealerClient.findById(DEALER_ID)).thenReturn(dealer);
            when(dynamoDbRepository.getScanResponses(any())).thenReturn(new SingleScanResponseIterable(createScanResponse(DEALER_ID, DEALER_NAME)));
            when(customerDetailsLinkService.getCampaignDomain(campaignView)).thenReturn("digitalretail-v2.beta.carsaver.com");

            DealProgramAndDomain result = programApiService.getDealProgramAndDomain(USER_ID, DEALER_ID, dealerTable);


            assertEquals("digitalretail-v2.beta.carsaver.com", result.getDomain());
        }
    }

    @Test
    void testGetDealProgramAndDomainForUsersDRV1() {
        try (MockedStatic<AuthUtils> mockedStatic = mockStatic(AuthUtils.class)) {
            mockedStatic.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of(USER_ID));

            com.carsaver.partner.model.user.UserView userView = mock(com.carsaver.partner.model.user.UserView.class);
            CampaignView campaignView = spy(CampaignView.class);
            CampaignView drCampaign = mock(CampaignView.class);
            ProgramView programView = mock(ProgramView.class);
            ProductView productView = mock(ProductView.class);


            com.carsaver.partner.model.source.Source source = new com.carsaver.partner.model.source.Source();
            source.setCampaignId(null);
            source.setHostname("shopathome.carsaver.com");
            when(userView.getSource()).thenReturn(source);
            when(programClient.findById(any())).thenReturn(Optional.of(programView));

            when(drCampaign.getId()).thenReturn("drCampaign");
            when(drCampaign.getDomain()).thenReturn("nissanathome.carsaver.com");
            when(drCampaign.getProgramId()).thenReturn("drProgramv2");

            when(productView.getName()).thenReturn("Digital Retail");
            when(productView.getId()).thenReturn(Product.ECOMMERCE);
            when(programView.getProduct()).thenReturn(productView);

            when(localDealerClient.getUserById(USER_ID)).thenReturn(userView);

            when(userView.getCampaignId()).thenReturn(Optional.of("drCampaign"));
            when(campaignView.getProgramId()).thenReturn("testProgramId");

            when(programClient.findById("drProgram")).thenReturn(Optional.of(programView));
            when(splitFeatureFlags.isDigitalRetailEnabledForDealer(anyString(), any())).thenReturn(true);

            final DealerView dealer = FACTORY.manufacturePojo(DealerView.class);
            dealer.setNnaDealerId("dealerId");
            when(dealerClient.findById(DEALER_ID)).thenReturn(dealer);
            when(dynamoDbRepository.getScanResponses(any())).thenReturn(new SingleScanResponseIterable(createScanResponse(DEALER_ID, "dealer-name")));

            when(campaignClient.findById(anyString())).thenReturn(drCampaign);
            when(campaignView.getDomain()).thenReturn("nissanathome.carsaver.com");
            when(customerDetailsLinkService.getCampaignDomain(drCampaign)).thenReturn("nissanathome.carsaver.com");

            DealProgramAndDomain result = programApiService.getDealProgramAndDomain(USER_ID, DEALER_ID, dealerTable);

            String expectedDomain = "nissanathome.carsaver.com";

            assertEquals(expectedDomain, result.getDomain());
        }
    }

    @Test
    void testGetDealProgramAndDomainForUsersWithoutCampaignId_NotFoundException() {
        try (MockedStatic<AuthUtils> mockedStatic = mockStatic(AuthUtils.class)) {
            mockedStatic.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of(USER_ID));

            UserView userView = mock(UserView.class);

            when(userClient.findById(USER_ID)).thenReturn(userView);
            when(userView.getCampaign()).thenReturn(null);

            assertThrows(NotFoundException.class, () -> {
                programApiService.getDealProgramAndDomain(USER_ID, DEALER_ID, dealerTable);
            });
        }
    }

    @Test
    void testGetDealProgramAndDomainFor_Prospect_DigitalRetail() {
        try (MockedStatic<AuthUtils> mockedStatic = mockStatic(AuthUtils.class)) {
            ReflectionTestUtils.setField(programApiService, "nissanDigitalRetailCampaignDomain", "digitalretail-v2.beta.carsaver.com");
            mockedStatic.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of(USER_ID));

            CampaignView campaignView = mock(CampaignView.class);
            when(campaignView.getId()).thenReturn("CAMPAIGN_ID");
            when(campaignView.getDomain()).thenReturn("digitalretail-v2.beta.carsaver.com");

            ProductView productView = mock(ProductView.class);

            ProgramView programView = mock(ProgramView.class);
            when(productView.getName()).thenReturn("DigitalRetailV2");
            when(productView.getId()).thenReturn(Product.ECOMMERCE);
            when(programView.getProduct()).thenReturn(productView);

            final DealerView dealer = FACTORY.manufacturePojo(DealerView.class);
            final ProspectDTO upgradeProspectDTO = mock(ProspectDTO.class);
            when(upgradeProspectDTO.getCampaignId()).thenReturn(Optional.of("CAMPAIGN_ID"));


            when(localDealerClient.getUserById(eq(USER_ID))).thenReturn(null);
            when(prospectService.findUpgradeProspectById(eq(USER_ID))).thenReturn(Optional.of(upgradeProspectDTO));
            when(campaignClient.findById(anyString())).thenReturn(campaignView);
            when(programClient.findById(campaignView.getProgramId())).thenReturn(Optional.of(programView));
            when(splitFeatureFlags.isDigitalRetailEnabledForDealer(anyString(), any())).thenReturn(false);
            when(dealerClient.findById(DEALER_ID)).thenReturn(dealer);
            when(customerDetailsLinkService.getCampaignDomain(campaignView)).thenReturn("digitalretail-v2.beta.carsaver.com");

            DealProgramAndDomain result = programApiService.getDealProgramAndDomain(USER_ID, DEALER_ID, dealerTable);


            assertEquals("digitalretail-v2.beta.carsaver.com", result.getDomain());
        }
    }


    @Test
    void testGetDealProgramAndDomainFor_WhispProspect_DigitalRetail() {
        try (MockedStatic<AuthUtils> mockedStatic = mockStatic(AuthUtils.class)) {
            ReflectionTestUtils.setField(programApiService, "nissanDigitalRetailCampaignDomain", "digitalretail-v2.beta.carsaver.com");
            mockedStatic.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of(USER_ID));

            CampaignView campaignView = mock(CampaignView.class);
            ProgramView programView = mock(ProgramView.class);
            ProductView productView = mock(ProductView.class);

            final DealerView dealer = FACTORY.manufacturePojo(DealerView.class);

            when(campaignView.getId()).thenReturn("CAMPAIGN_ID");
            when(campaignView.getDomain()).thenReturn("digitalretail-v2.beta.carsaver.com");
            when(campaignView.getProgramId()).thenReturn("PROGRAM_ID");

            when(productView.getName()).thenReturn("DigitalRetailV2");
            when(productView.getId()).thenReturn(Product.ECOMMERCE);
            when(programView.getProduct()).thenReturn(productView);
            when(userClient.findById(USER_ID)).thenReturn(null);
            ProspectDTO whispProspectView = mock(ProspectDTO.class);
            when(whispProspectView.getCampaignId()).thenReturn(Optional.of("CAMPAIGN_ID"));
            when(upgradeCampaignClient.findProspectById(anyString())).thenReturn(Optional.empty());
            when(prospectService.findWhipsProspectById(anyString())).thenReturn(Optional.of(whispProspectView));

            when(campaignClient.findById(anyString())).thenReturn(campaignView);
            when(programClient.findById("PROGRAM_ID")).thenReturn(Optional.of(programView));
            when(splitFeatureFlags.isDigitalRetailEnabledForDealer(anyString(), any())).thenReturn(false);
            when(dealerClient.findById(DEALER_ID)).thenReturn(dealer);
            when(customerDetailsLinkService.getCampaignDomain(campaignView)).thenReturn("digitalretail-v2.beta.carsaver.com");

            DealProgramAndDomain result = programApiService.getDealProgramAndDomain(USER_ID, DEALER_ID, dealerTable);


            assertEquals("digitalretail-v2.beta.carsaver.com", result.getDomain());
        }
    }

}

