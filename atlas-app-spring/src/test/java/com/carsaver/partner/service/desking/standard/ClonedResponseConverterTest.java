package com.carsaver.partner.service.desking.standard;

import com.carsaver.magellan.NotFoundException;
import com.carsaver.magellan.model.QuoteView;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.ClonedDealResponse;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse;
import com.carsaver.partner.model.desking.standard.LeaseStepBreakDownDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class ClonedResponseConverterTest {


    @InjectMocks
    private ClonedResponseConverter service;

    @Test
    void leaseQuoteResponseQuoteToCloneResponse_nullQuote() {
        CloneDealRequest request = buildCloneDealRequest();
        assertThrows(NotFoundException.class, () -> service.leaseQuoteResponseQuoteToCloneResponse(null, request), "");
    }

    @Test
    void leaseQuoteResponseQuoteToCloneResponse_nullBreakDownValues() {
        CloneDealRequest request = buildCloneDealRequest();
        QuoteView quoteView = QuoteView.builder().build();
        LeaseQuoteResponse.Quote quote = LeaseQuoteResponse.Quote.builder().quoteView(quoteView).build();
        assertThrows(RuntimeException.class, () -> service.leaseQuoteResponseQuoteToCloneResponse(quote, request), "");
    }

    @Test
    void leaseQuoteResponseQuoteToCloneResponse_nullValuesInsideBreakDownValues() {
        CloneDealRequest request = buildCloneDealRequest();
        LeaseStepBreakDownDTO breakDown = LeaseStepBreakDownDTO.builder().build();
        QuoteView quoteView = QuoteView.builder().build();
        LeaseQuoteResponse.Quote quote = LeaseQuoteResponse.Quote.builder().quoteView(quoteView).stepCalculatedValues(breakDown).build();;

        ClonedDealResponse response = service.leaseQuoteResponseQuoteToCloneResponse(quote, request);
        assertNull(response.getDealerId());
    }


    private CloneDealRequest buildCloneDealRequest() {

        CloneDealRequest cloneDealRequest = CloneDealRequest.builder()
            .financeDetails(CloneDealRequest.FinanceDetails.builder().build())
            .monthlyPayment(CloneDealRequest.MonthlyPayment.builder().build())
            .rebates(CloneDealRequest.Rebates.builder().build())
            .vehicleDetail(CloneDealRequest.VehicleDetail.builder().build())
            .tradeAllowance(CloneDealRequest.TradeAllowance.builder().build())
            .dueAtSigning(CloneDealRequest.DueAtSigning.builder().build())
            .taxesAndFees(CloneDealRequest.TaxesAndFees.builder().build())
            .addons(CloneDealRequest.AddOns.builder().build())
            .inceptions(CloneDealRequest.Inceptions.builder().build())
            .grossCapitalCost(CloneDealRequest.GrossCapitalCost.builder().build())
            .capCostReduction(CloneDealRequest.CapCostReduction.builder().build())
            .build();

        return cloneDealRequest;
    }
}
