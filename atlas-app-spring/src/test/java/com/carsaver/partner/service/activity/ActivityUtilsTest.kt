package com.carsaver.partner.service.activity

import com.carsaver.partner.service.activity.ActivityUtils.Companion.extractDomain
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class ActivityUtilsTest {

    @Test
    fun extractDomainTest() {
        assertNull(extractDomain(null))
        assertNull(extractDomain(""))
        assertEquals("google.com", extractDomain("http://www.google.com"))
        assertEquals("google.com", extractDomain("https://www.google.com"))
        assertEquals("google.com", extractDomain("https://www.google.com/"))
        assertEquals("google.com", extractDomain("www.google.com/"))
    }
}
