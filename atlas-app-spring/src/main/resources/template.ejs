<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="UTF-8">
    <title>CarSaver | Atlas</title>
    <% for (var i = 0; i < htmlWebpackPlugin.files.css.length; i++) { %>
        <link rel="stylesheet" href="<%= htmlWebpackPlugin.files.css[i] %>">
    <% } %>

    <% for (var i = 0; i < htmlWebpackPlugin.files.css.length; i++) { %>
    <link href="<%= htmlWebpackPlugin.files.css[i] %>" rel=preload as=style>
    <% } %>
    <% for (var i = 0; i < htmlWebpackPlugin.files.js.length; i++) { %>
    <link href="<%= htmlWebpackPlugin.files.js[i] %>" rel=preload as=script>
    <% } %>
    <link rel="icon" type="image/png" th:href="@{'/images/favicons/carsaver-favicon.png'}">

    <!-- Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@latest/css/materialdesignicons.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons"/>
    <link rel="preload" href="https://nissan-brand-fonts.carsaver.com/nissan-brand-regular.otf" as="font" type="font/otf" crossorigin="anonymous">
    <link rel="preload" href="https://nissan-brand-fonts.carsaver.com/nissan-brand-light.otf" as="font" type="font/otf" crossorigin="anonymous">
    <link rel="preload" href="https://nissan-brand-fonts.carsaver.com/nissan-brand-bold.otf" as="font" type="font/otf" crossorigin="anonymous">
    <link rel="preload" href="https://nissan-brand-fonts.carsaver.com/nissan-brand-italic.otf" as="font" type="font/otf" crossorigin="anonymous">
    <link rel="preload" href="https://nissan-brand-fonts.carsaver.com/nissan-brand-bold-italic.otf" as="font" type="font/otf" crossorigin="anonymous">
</head>
<body>

<noscript>
    <strong>We're sorry but this site doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
</noscript>
<div class="wrapper" id="app"></div>

<script type="application/javascript" th:inline="javascript">
    window._APP_CONFIG = {
        env: <%- "[[${@environment.getActiveProfiles()}]]" %>,
        theme: <%= "[[${#session.getAttribute('theme')}]]" %>
    };
</script>

<script type='text/javascript' th:if="<%- "${session?.dealerSurveyEnabled}" %>">
    (function(){var g=function(e,h,f,g){
        this.get=function(a){for(var a=a+"=",c=document.cookie.split(";"),b=0,e=c.length;b<e;b++){for(var d=c[b];" "==d.charAt(0);)d=d.substring(1,d.length);if(0==d.indexOf(a))return d.substring(a.length,d.length)}return null};
        this.set=function(a,c){var b="",b=new Date;b.setTime(b.getTime()+6048E5);b="; expires="+b.toGMTString();document.cookie=a+"="+c+b+"; path=/; "};
        this.check=function(){var a=this.get(f);if(a)a=a.split(":");else if(100!=e)"v"==h&&(e=Math.random()>=e/100?0:100),a=[h,e,0],this.set(f,a.join(":"));else return!0;var c=a[1];if(100==c)return!0;switch(a[0]){case "v":return!1;case "r":return c=a[2]%Math.floor(100/c),a[2]++,this.set(f,a.join(":")),!c}return!0};
        this.go=function(){if(this.check()){var a=document.createElement("script");a.type="text/javascript";a.src=g;document.body&&document.body.appendChild(a)}};
        this.start=function(){var t=this;"complete"!==document.readyState?window.addEventListener?window.addEventListener("load",function(){t.go()},!1):window.attachEvent&&window.attachEvent("onload",function(){t.go()}):t.go()};};
        try{(new g(100,"r","QSI_S_ZN_5irKkfubwzRLH6e","https://zn5irkkfubwzrlh6e-nissanbuyathome.siteintercept.qualtrics.com/SIE/?Q_ZID=ZN_5irKkfubwzRLH6e")).start()}catch(i){}})();
</script><div id='ZN_5irKkfubwzRLH6e'><!--DO NOT REMOVE-CONTENTS PLACED HERE--></div>

<script type="application/javascript" th:inline="javascript" sec:authorize="isAuthenticated()">
    window._CS_CONTEXT = <%= "[[${#session.getAttribute('atlasContext')}]]" %>;
    window._CS_AUTHORIZATIONS = <%- "[[${#authentication.authorities}]]" %>;
    window._CS_DEALER_PERMISSIONS = <%- "[[${#authentication?.principal?.dealerPermissions}]]" %>;
    window._CS_USER_DEALER_ACCESS_LIST = <%= "[[${#session.getAttribute('userDealerAccessListWithBoostCheck')}]]" %>;
    window._CS_USER_PROGRAM_ACCESS_LIST = <%= "[[${#session.getAttribute('userProgramAccessList')}]]" %>;
    window._CS_USER_PROGRAM_DEALER_ACCESS_LIST = <%= "[[${#session.getAttribute('userProgramDealerAccessList')}]]" %>;
    window._CS_PROGRAM_PERMISSIONS = <%- "[[${#authentication?.principal?.programPermissions}]]" %>;
    window._CS_FEATURE_FLAGS = <%= "[[${#session.getAttribute('featureFlags')}]]" %>;
    window._CS_NESNA_FEATURE_SUBSCRIPTION_ENABLED = <%= "[[${#session.getAttribute('nesnaFeatureSubscriptionEnabled')}]]" %>;
    window._CS_ATLAS_DEALER_TRACK_PHASE_2_ENABLED = <%= "[[${#session.getAttribute('atlasDealerTrackPhase2Enabled')}]]" %>;
</script>

<script type="application/javascript" th:inline="javascript" sec:authorize="!isAuthenticated()">
    window._CS_FEATURE_FLAGS = <%= "[[${#session.getAttribute('featureFlags')}]]" %>;
</script>

<% for (var i = 0; i < htmlWebpackPlugin.files.js.length; i++) { %>
<script type="application/javascript" src="<%= htmlWebpackPlugin.files.js[i] %>"></script>
<% } %>

</body>
</html>
