spring:
  security:
    oauth2:
      client:
        registration:
          carsaver:
            client-id: PORTAL
            client-secret: 5YLk%T-gGq5~qn=V
  session:
    redis:
      namespace: spring:atlas:prod:session
routeone:
  base-uri: https://www.routeone.net
  partner-id: F00CSV
  sso:
    password: csv$%*HBNF3ed+
mixpanel:
  project-token: 123664357eb8c621fb04881c9d88b17a
program:
  nissan-buy-at-home-id: 68d2083f-8569-49a0-99b7-558400e71306
  liberty-mutual-feature-id: 023f5087-01bb-4755-961d-89b0ea8e15c5
  garage-alerts-feature-id: e3850360-96cd-4046-bd0c-0802ba9078e7
  sms_alerts-feature-id: f4ef0ad8-6cdc-44b0-87f4-c5f5d4c0386c
  in-app-alerts-feature-id: d0baa546-9c38-4951-a0ee-c3f7c89079a2
  email-alerts-feature-id: 09546078-30f4-4a21-b270-64a2ec93659a
  spanish-translation-feature-id: 3e9ea5f7-6b01-4419-aefc-3ccd97034ea4
  lms-feature-id: 6f26e483-e1a6-4082-98b3-fa8fb50a4eab
features-toggle:
  exclude-test-users: true
  routeone-prefix-enable: true
features-subscription:
  rootUri: https://api.carsaver.com/meridian/features-subscription
  liberty-mutual-feature-id: d297cca0-0299-48bd-80b2-a9a86b56bb17
  sell-at-home-feature-id: 11bcd5c0-2292-4089-9265-e95d1852bfd4
  nesna-f-and-i-feature-id: 0e25385a-a54b-4b08-8a06-1eae3d6e7d37
  email-alerts-feature-id: 09546078-30f4-4a21-b270-64a2ec93659a
  garage-alerts-feature-id: e3850360-96cd-4046-bd0c-0802ba9078e7
  sms_alerts-feature-id: f4ef0ad8-6cdc-44b0-87f4-c5f5d4c0386c
  in-app-alerts-feature-id: d0baa546-9c38-4951-a0ee-c3f7c89079a2
  spanish-translation-feature-id: 3e9ea5f7-6b01-4419-aefc-3ccd97034ea4
  lms-feature-id: 6f26e483-e1a6-4082-98b3-fa8fb50a4eab
dealer-service:
  api-uri: https://api.carsaver.com/dealer
finance-service:
  root-uri: https://api.carsaver.com/finance
user-vehicle-service:
  api-uri: https://api.carsaver.com/user-vehicles
nissan:
  wiretap-enabled: false
deal-desking:
  inventory-service:
    api-uri: https://api.carsaver.com/inventory
quote-service:
  endpoint-root: https://api.carsaver.com/quote
carsaver:
  cloud:
    kinesis:
      api-log-stream: api-log-stream-prod
splits:
  key: he1hg9hhnkggticspttmrm1tc4bp1i3k28vh
  defaults:
    preQualifiedCtaFeature: off
    dealEditingCash: off
    offerAdjustmentFeature: off
    tradeInAdjustmentMultipleDealersFeature: off
    sellAtHomeFeature: off
    enableStageTilesForDealers: off
    EnableStageTilesForAdmins: off
    LMSPreferenceFeature: off
    nesnaFAndIFeature: off
    offerAdjustmentFeatureV2: off
    AtlasLoginSanitizationEnable: off
    DomoEmbedAtlas: off
    AtlasDealerTrackPhase2Enabled: off
    SendToCrmEnhancement: off
    AtlasNewCustomerPage: off
    AtlasActionBarEnabled: off
    AtlasRequestScreenShare: off
    atlasCustomerLeadsDisplayAdfEnabled: off
    enableElasticNestedFields: off
    AtlasNewCustomerPageOnlineNow: off
    AtlasNewCustomerPageInShowroom: off
    AtlasStickyFiltersEnabled: off
    EnableGarageAlerts: off
    EnableINAppGarageAlerts: off
    EnableEmailGarageAlerts: off
    EnableSMSGarageAlerts: off
    CustomerTagsEnhancementsToggle: off
    DrawerSettingsEnabled: off
    QrCodeSettingsEnabled: off
    customerInfoCardProgramUserSupportToggle: off
    programUserSupport: off
    AtlasWalmartInventoryEnabled: off
    AtlasEnableLeadFilter: off
    AtlasInAppNotificationEnabled: off
    EnableCarsaverFandI: off
insurance-service:
  insurance-host: https://api.carsaver.com/insurance
  route-one-uri: /api/routeone/deal-jacket
  route-one-secret-key-name: prod/protection-products/api-keys
  insurance-callback-url: https://shopathome.carsaver.com/routeone/thank-you.html
  default-route-one-dealer-id:
service-api-uri: https://api.carsaver.com
lead-service:
  api-uri: https://api.carsaver.com/lead
dynamoDB:
  dealerTable: upgrade_prospect_etl_dealer_prod
  lenderDesk-table: financier-lender-desk-mapping-table-prod
  session-heartbeat-table: digital-retail-session-heartbeat-prod
  prospect-leads-current-table: prospect-leads-current-prod
  prequal-history-table: prequal-transaction-history-prod

digital-retail:
  campaign-id: 9838eebe-4569-4a65-9188-1c1b039ef1e7
  opt-api-uri: https://api.carsaver.com/digital-retail/logins/dealer-otps
client:
  api-host: https://api.carsaver.com
  apple-sso:
    secrets-name: apple-sso/prod
  url-shortener:
    host: https://prod.csvr.co
  oauth:
    configurations:
      carsaver:
        url: ${client.api-host}/uaa/oauth/token
        grant-type: PASSWORD
        auth-username: WEB
        auth-password: wk<7MyC)g$X6Mt.~
        token-username: <EMAIL>
        token-password: tps4QyhT6G7wCH
domo:
  client-id: 21e18b92-3d4c-42b4-8d43-4325a11cac63
  client-secret: 9bc9f629161c2992806945db5c1c3b7ebc663b98b87d1788b7456bf30257118b
  grant-type: client_credentials
  scopes: data,workflow,user,dashboard
  embed-id: l5rWr
  api-host: https://api.domo.com

export:
  customer:
    page-size: 100
in-showroom:
  minutes-threshold: 240
  lead-hour-threshold: 24

app:
  secrets: services/atlas-app/prod
activity-service:
  api-uri: https://api.carsaver.com/activity

vehicle-searches:
  table-name: ga4-eecu.analytics_433663821.events_*
