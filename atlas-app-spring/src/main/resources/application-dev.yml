application:
  domain: https://atlas.dev.carsaver.com
spring:
  security:
    oauth2:
      client:
        registration:
          carsaver:
            client-id: PORTAL
            client-secret: -Ku2Q_FCN2
  redis:
    host: carsaver-atlas-dev.wgjae7.0001.use1.cache.amazonaws.com
  session:
    redis:
      namespace: spring:atlas:dev:session
carsaver:
  cloud:
    kinesis:
      api-log-stream: api-log-stream-dev
magellen-service-api-uri: https://api-dev.carsaver.com
warranty-service:
  nwan-client:
    rootUri: https://qvcarsaver.naenwan.com/eBusinessApi
    username: TCS
    password: zcxUUvLJPj7QdycsQ2hGfTMcEh9N6r
  providerCode: TCS
  e-remittance-portal:
    url: https://qvcarsaver.naenwan.com/Portals/eBusiness/eRemittancePortal.aspx?oauth_token=
    role: f5b2da92-1811-4b0a-9f82-5e0b507b5077
  supply-order-portal:
    url: https://qvcarsaver.naenwan.com/Portals/Organizations/OrderSupplies.aspx?oauth_token=
    role: 5d0b33ad-6a7b-4943-8331-15be54d61a7a
twilio:
  accountId: **********************************
  authToken: 8cf701badf19f0aed13451a3fc92afdb
  number: +***********
routeone:
  base-uri: https://testint.r1dev.com
  partner-id: F00CSV
  sso:
    password: csv$%*HBNF3ed+
portal:
  authorizationUri: https://portal-dev.carsaver.com/oauth2/authorize
atlas:
  features: dealer-user-vehicle-quote
mixpanel:
  project-token: ff3fc6d685c6e1ced5e11391b5282fd7
program:
  nissan-buy-at-home-id: 5e922fe4-e1e9-468c-b100-5b8f7cffcef3
features-toggle:
  pre-approval-enable: true
  accessories-enable: true
  ariya-reservation-enable: true
  routeone-prefix-enable: true
  buy-at-home-program-enable: true
nissan-client:
  apigee:
    baseUri: https://dev.api.na.nissancloud.com
    token-api: #used by payoff,incentives,etc., for oauth2 client_credentials grant types
      username: qFNzhiBI20ng4kmyLADhnhCbIuGebAtp
      password: aeVrUOUQkVZqQb4x
    dealer-inventory:
      rootUri: https://dev.api.na.nissancloud.com
  accessories:
    rootUri: https://dev.api.na.nissancloud.com
features-subscription:
  rootUri: https://api-dev.carsaver.com/meridian/features-subscription
  liberty-mutual-feature-id: 023f5087-01bb-4755-961d-89b0ea8e15c5
  sell-at-home-feature-id: 11bcd5c0-2292-4089-9265-e95d1852bfd4
  nesna-f-and-i-feature-id: 76c3c23b-2b1e-4822-bd26-9ef735820641
  garage-alerts-feature-id: e3850360-96cd-4046-bd0c-0802ba9078e7
  sms_alerts-feature-id: f4ef0ad8-6cdc-44b0-87f4-c5f5d4c0386c
  in-app-alerts-feature-id: d0baa546-9c38-4951-a0ee-c3f7c89079a2
  email-alerts-feature-id: 09546078-30f4-4a21-b270-64a2ec93659a
  spanish-translation-feature-id: 3e9ea5f7-6b01-4419-aefc-3ccd97034ea4
dealer-service:
  api-uri: https://api-dev.carsaver.com/dealer
lead-service:
  api-uri: https://api-dev.carsaver.com/lead
nissan:
  wiretap-enabled: false
deal-desking:
  inventory-service:
    api-uri: https://api-dev.carsaver.com/inventory
quote-service:
  endpoint-root: https://api-dev.carsaver.com/quote
user-vehicle-service:
  api-uri: https://api-dev.carsaver.com/user-vehicles
insurance-service:
  insurance-host: https://api-dev.carsaver.com/insurance
  route-one-uri: /api/routeone/deal-jacket
  route-one-secret-key-name: dev/protection-products/api-keys
  insurance-callback-url: https://nissan-ecommerce.dev.carsaver.com/routeone/thank-you.html
dynamoDB:
  dealerTable: upgrade_prospect_etl_dealer_dev
  lenderDesk-table: financier-lender-desk-mapping-table-dev
  session-heartbeat-table: digital-retail-session-heartbeat-staging
domo:
  client-id: 21e18b92-3d4c-42b4-8d43-4325a11cac63
  client-secret: 9bc9f629161c2992806945db5c1c3b7ebc663b98b87d1788b7456bf30257118b
  grant-type: client_credentials
  scopes: data,workflow,user,dashboard
  embed-id: l5rWr
  api-host: https://api.domo.com
