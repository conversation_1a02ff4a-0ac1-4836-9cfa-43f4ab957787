{"page": "CTA Buttons", "title": "CTA Buttons", "sections": [{"title": "Customize Plugins", "description": "This determines where CTAs appear on your website and how they are displayed.", "vdp_view": {"font-family": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-family", "value": null}, "font-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-size", "value": null}, "font-weight": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/font-weight", "value": null}, "align-content": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/font/align-content", "value": null}, "width": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/width", "value": null}, "height": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/height", "value": null}, "padding": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/padding", "value": null}, "margin": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/margin", "value": null}, "radius": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/formattingOptions/radius", "value": null}, "cta_1": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/primaryButton/image", "value": null}}, "cta_2": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/secondButton/image", "value": null}}, "cta_3": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/thirdButton/image", "value": null}}, "cta_4": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/fourthButton/image", "value": null}}}, "vlp_view": {"font-family": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-family", "value": null}, "font-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-size", "value": null}, "font-weight": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/font-weight", "value": null}, "align-content": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/font/align-content", "value": null}, "width": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/width", "value": null}, "height": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/height", "value": null}, "padding": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/padding", "value": null}, "margin": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/margin", "value": null}, "radius": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/formattingOptions/radius", "value": null}, "cta_1": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/primaryButton/image", "value": null}}, "cta_2": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/secondButton/image", "value": null}}, "cta_3": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/thirdButton/image", "value": null}}, "cta_4": {"display": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/display", "value": null}, "button-text": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/button-text", "value": null}, "link-destination": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/link-destination", "value": null}, "text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/text-color", "value": null}, "background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/background-color", "value": null}, "hover-text-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/hover-text-color", "value": null}, "hover-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/hover-background-color", "value": null}, "enable-image-cta": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/enable-image-cta", "value": null}, "image-display-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/image-display-name", "value": null}, "image-name": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/image-name", "value": null}, "image-size": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/image-size", "value": null}, "image-background-color": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/image-background-color", "value": null}, "image": {"key": "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/fourthButton/image", "value": null}}}, "order": null, "security": null, "metadata": null}]}