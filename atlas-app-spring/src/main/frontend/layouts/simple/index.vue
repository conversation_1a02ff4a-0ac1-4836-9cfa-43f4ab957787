<template>
    <v-app>
        <default-bar />

        <default-view />

        <default-footer />
    </v-app>
</template>

<script>
export default {
    name: "DefaultLayout",

    components: {
        DefaultBar: () =>
            import(
                /* webpackChunkName: "simple-app-bar" */
                "./AppBar"
            ),
        DefaultFooter: () =>
            import(
                /* webpackChunkName: "default-footer" */
                "../default/Footer"
            ),
        DefaultView: () =>
            import(
                /* webpackChunkName: "simple-view" */
                "./View"
            ),
    },
};
</script>
