<template>
    <v-app>
        <program-drawer v-if="isProgramTypeUser" />
        <default-drawer v-else />

        <default-bar />

        <default-view />
    </v-app>
</template>

<script>
export default {
    name: "UserSelfServiceLayout",

    components: {
        DefaultBar: () =>
            import(
                /* webpackChunkName: "default-app-bar" */
                "../sansdealercontext/AppBar"
            ),
        DefaultDrawer: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "../default/Drawer"
            ),
        ProgramDrawer: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "../default/DrawerProgram"
            ),
        DefaultView: () =>
            import(
                /* webpackChunkName: "default-view" */
                "../default/View"
            ),
    },
    computed: {
        isProgramTypeUser() {
            return this.$store.getters["loggedInUser/isProgramTypeUser"];
        },
    },
};
</script>
