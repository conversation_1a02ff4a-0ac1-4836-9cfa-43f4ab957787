<template>
    <v-navigation-drawer v-model="open" :mini-variant.sync="mini" mini-variant-width="80" width="260" app>
        <template #prepend>
            <v-list-item to="/userSelfService" two-line>
                <v-list-item-avatar>
                    <img :src="imageUrl" />
                </v-list-item-avatar>

                <v-list-item-content>
                    <v-list-item-title>{{ name }}</v-list-item-title>
                    <v-list-item-subtitle>Logged In</v-list-item-subtitle>
                </v-list-item-content>
            </v-list-item>
        </template>

        <v-list dense nav expand>
            <!-- v-if dealer in context -->
            <v-list-item :to="`/qaTools/inventory`">
                <v-list-item-action>
                    <v-icon>mdi-monitor-dashboard</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Inventory</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item :to="`/qaTools/pre-approval`">
                <v-list-item-action>
                    <v-icon>mdi-cash</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Pre-Approval</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
            <v-list-item :to="`/qaTools/cunexus`">
                <v-list-item-action>
                    <v-icon>mdi-cash</v-icon>
                </v-list-item-action>
                <v-list-item-content>
                    <v-list-item-title>Cunexus Redirect</v-list-item-title>
                </v-list-item-content>
            </v-list-item>
        </v-list>
    </v-navigation-drawer>
</template>

<script>
import { sync, get } from "vuex-pathify";
import CryptoJS from "crypto-js";

export default {
    name: "Drawer",
    computed: {
        dealerId() {
            return this.$route.params.dealerId;
        },
        ...sync("drawer", ["open", "mini"]),
        ...get("loggedInUser", ["name", "email"]),
        imageUrl() {
            const hash = CryptoJS.MD5(this.email);
            return `https://www.gravatar.com/avatar/${hash}?d=mp`;
        },
        hasWarrantyAccess() {
            return this.$acl.hasDealerPermission(this.dealerId, "warranty:create");
        },
        hasPricingAccess() {
            return (
                this.$acl.hasDealerPermission(this.dealerId, "inventory:new-pricing:edit") ||
                this.$acl.hasDealerPermission(this.dealerId, "inventory:used-pricing:edit")
            );
        },
        hasCustomerAccess() {
            return this.$acl.hasDealerPermission(this.dealerId, "customer:read");
        },
    },
};
</script>
