<template>
    <v-app-bar app color="secondary" dark>
        <v-app-bar-nav-icon class="hidden-md-and-up" @click="open = !open" />
        <default-drawer-toggle class="hidden-sm-and-down" />

        <v-toolbar-title>Atlas</v-toolbar-title>

        <v-spacer />

        <action-menu />
    </v-app-bar>
</template>
<script>
import ActionMenu from "@/modules/Navigation/components/ActionMenu";
import { sync } from "vuex-pathify";
import DefaultDrawerToggle from "@/layouts/default/DrawerToggle";

export default {
    name: "DefaultAppBar",
    components: { DefaultDrawerToggle, ActionMenu },

    computed: {
        open: sync("drawer/open"),
    },
};
</script>
