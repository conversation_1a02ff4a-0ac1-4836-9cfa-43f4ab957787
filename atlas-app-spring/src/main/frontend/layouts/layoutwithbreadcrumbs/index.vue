<template>
    <v-app>
        <template v-if="menuVersionSelected === 'v1'">
            <program-drawer v-if="isProgramTypeUser" />
            <default-drawer v-else new-config />
        </template>
        <template v-else>
            <drawer-v2 />
        </template>

        <default-bar />

        <view-with-breadcrumbs />

        <default-footer />
    </v-app>
</template>

<script>
import { get, sync } from "vuex-pathify";

export default {
    name: "Layoutwithbreadcrumbs",

    components: {
        DefaultBar: () =>
            import(
                /* webpackChunkName: "default-app-bar" */
                "@/layouts/default/AppBar"
            ),
        DefaultDrawer: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "@/layouts/default/Drawer"
            ),
        ProgramDrawer: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "@/layouts/default/DrawerProgram"
            ),
        DrawerV2: () =>
            import(
                /* webpackChunkName: "default-drawer" */
                "@/layouts/default/DrawerV2"
            ),
        DefaultFooter: () =>
            import(
                /* webpackChunkName: "default-footer" */
                "@/layouts/default/Footer"
            ),
        ViewWithBreadcrumbs: () =>
            import(
                /* webpackChunkName: "default-view" */
                "./ViewWithBreadcrumbs"
            ),
    },
    computed: {
        isProgramTypeUser: get("loggedInUser/isProgramTypeUser"),

        // temporary for testing New Config Manager
        newConfigManagerEnabled: get("loggedInUser/featureFlags@NEW_CONFIG_MANAGER"),
        menuVersionSelected: sync("pageConfigs/menuVersionSelected"),
    },
};
</script>
