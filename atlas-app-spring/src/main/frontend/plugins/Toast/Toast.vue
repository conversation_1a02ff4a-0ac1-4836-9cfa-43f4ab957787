<template>
    <v-snackbar
        v-model="active"
        :timeout="timeout"
        :color="color"
        :multi-line="multiLine"
        :vertical="vertical"
        class="v-application vts"
        :class="classes"
        role="alert"
        @click="dismiss"
    >
        <v-icon v-if="!!icon" dark left class="vts__icon" :color="iconColor">
            {{ icon }}
        </v-icon>

        <div class="vts__message" :class="{ 'vts__message--padded': showClose && !closeText }">
            <!-- eslint-disable-next-line vue/no-v-html -->
            <div v-html="message"></div>
            <slot></slot>
        </div>

        <v-btn
            v-if="showClose"
            :icon="!closeText"
            :text="!!closeText"
            class="vts__close"
            :class="{ 'vts__close--icon': !closeText }"
            :color="closeColor"
            @click="close"
        >
            <v-icon v-if="!closeText">{{ closeIcon }}</v-icon>
            <span v-if="!!closeText">{{ closeText }}</span>
        </v-btn>
    </v-snackbar>
</template>

<script>
export default {
    props: {
        color: {
            type: String,
            default: "",
        },
        icon: {
            type: String,
            default: "",
        },
        iconColor: {
            type: String,
            default: "",
        },
        classes: {
            type: [String, Object, Array],
            default: "",
        },
        message: {
            type: String,
            default: "",
        },
        timeout: {
            type: Number,
            default: 3000,
        },
        dismissable: {
            type: Boolean,
            default: true,
        },
        multiLine: {
            type: Boolean,
            default: false,
        },
        vertical: {
            type: Boolean,
            default: false,
        },
        showClose: {
            type: Boolean,
            default: false,
        },
        closeText: {
            type: String,
            default: "",
        },
        closeIcon: {
            type: String,
            default: "close",
        },
        closeColor: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            active: false,
        };
    },
    watch: {
        active(isActive, wasActive) {
            this.$emit("statusChange", isActive, wasActive);
        },
    },
    mounted() {
        this.$nextTick(() => this.show());
    },
    methods: {
        show() {
            this.active = true;
        },
        close() {
            this.active = false;
        },
        dismiss() {
            if (this.dismissable) {
                this.close();
            }
        },
    },
};
</script>
