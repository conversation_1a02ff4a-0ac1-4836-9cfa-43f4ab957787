import Vue from "vue";
import Vuetify from "vuetify/lib/framework";
import themes from "./themes";
import _ from "lodash";
import MaterialIcon from "Components/MaterialIcon";

function missingMaterialIcons(ids) {
    const icons = {};
    for (const id of ids) {
        for (const suffix of ["fill"]) {
            const name = `${id}_${suffix}`;
            icons[name] = {
                component: MaterialIcon,
                props: {
                    name,
                },
            };
        }
    }
    return icons;
}

Vue.use(Vuetify, {
    icons: {
        values: {
            ...missingMaterialIcons(["paid"]),
        },
    },
});

let selectedTheme = _.get(window, "_APP_CONFIG.theme");
const localStorageTheme = window.localStorage.getItem("theme");

if (_.isNil(selectedTheme) || selectedTheme === "") {
    selectedTheme = localStorageTheme;
}

const theme = themes.getTheme(selectedTheme);

if (theme.name !== localStorageTheme) {
    window.localStorage.setItem("theme", theme.name);
}

const opts = {
    breakpoint: { mobileBreakpoint: 960 },
    theme: {
        options: {
            customProperties: true,
        },
        themes: _.omit(theme, ["name"]),
    },
};

export default new Vuetify(opts);
