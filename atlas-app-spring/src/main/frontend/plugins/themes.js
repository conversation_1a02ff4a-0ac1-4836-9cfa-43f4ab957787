import _ from "lodash";

const themes = {
    carsaver: {
        name: "carsaver",
        light: {
            primary: "#007DC6",
            secondary: "#004c91",
            accent: "#F47321",
        },
    },
    nissan: {
        name: "nissan",
        light: {
            primary: "#C3002F",
            secondary: "#000000",
            accent: "#D3D3D3",
        },
    },
};

const getTheme = (themeName) => {
    if (_.isNil(themeName) || themeName === "") {
        return themes.carsaver;
    }
    return _.get(themes, themeName, themes.carsaver) || themes.carsaver;
};

export default {
    getTheme,
};
