/**
 * Validates whether a string represents a valid hexadecimal color code.
 * @param {string} hex - The hexadecimal color code to be validated.
 * @returns {boolean} True if the input string represents a valid hexadecimal color code, false otherwise.
 */
function validateHex(hex) {
    // Regular expression to match hex color code
    const hexRegex = /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
    return hexRegex.test(hex);
}

/**
 * Checks if a number falls within a specified range.
 * @param {number} number - The number to be validated.
 * @param {number} [min=Number.MIN_VALUE] - The minimum value of the range. Defaults to Number.MIN_VALUE if not provided.
 * @param {number} [max=Number.MAX_VALUE] - The maximum value of the range. Defaults to Number.MAX_VALUE if not provided.
 * @returns {boolean} True if the number is within the specified range, false otherwise.
 */
function isNumberInRange(number, min = Number.MIN_VALUE, max = Number.MAX_VALUE) {
    return number >= (min || Number.MIN_VALUE) && number <= (max || Number.MAX_VALUE);
}

export { validateHex, isNumberInRange };
