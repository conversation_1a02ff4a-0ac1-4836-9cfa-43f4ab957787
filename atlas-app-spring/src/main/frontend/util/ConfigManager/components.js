const COMPONENT_MAPPING = Object.freeze({
    TOGGLE: "ToggleBtn",
    RADIO: "RadioBtn",
    INPUT: "InputField",
    INPUT_PERCENTAGE: "InputNumber",
    INPUT_MONEY: "InputNumber",
    DROPDOWN: "Dropdown",
    CHECKBOX: "CheckboxGroup",
    INPUT_WITH_MULTIPLIER: "InputWithMultiplier",
    INPUT_COLOR: "InputColor",
    ENABLED_GROUP: "EnabledGroup",
    GROUPED_TOGGLE: "GroupedToggle",
    LABEL: "Label",
    INPUT_RANGE: "InputRange",
    TIME_DURATION: "TimeDurationInput",
    TABLE: "ActionableTable",
    // INPUT_MONEY: "InputMoney",
    GROUPED_TERM: "GroupedTerm",
    RATE_SHEET_CONFIGURATOR: "RateSheetConfigurator",
});

const PREVIEW_COMPONENT_MAPPING = Object.freeze({
    "Hamburger Menu": "HamburgerMenu",
    "CTA Buttons": "CTAButtons",
    "Smart Links QR Codes": "SmartLinksQrCodes",
    "Overlay Settings": "OverlaySettings",
    "Graphics And Images": "GraphicsAndImages",
});

const COMPONENTS_UTILITY = Object.freeze({
    ACTIONABLE_TABLE: {
        ACTIONS: {
            ADD: "ADD",
            DELETE: "DELETE",
            EDIT: "EDIT",
        },
    },
});

const FORMAT_COMPONENT_DATA = (componentData) => {
    return {
        title: {
            value: componentData?.title?.value || "",
            tooltip: componentData?.title?.tooltip || "",
        },
    };
};

export { COMPONENT_MAPPING, PREVIEW_COMPONENT_MAPPING, COMPONENTS_UTILITY, FORMAT_COMPONENT_DATA };
