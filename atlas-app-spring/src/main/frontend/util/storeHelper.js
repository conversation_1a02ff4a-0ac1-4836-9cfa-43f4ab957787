import pathify from "vuex-pathify";
import _ from "lodash";
import createPersistedState from "vuex-persistedstate";
import URL from "url-parse";

const debug = process.env.NODE_ENV !== "production";
const storageUsed = window.localStorage;

// options
pathify.options.strict = debug;

const getPlugins = (storeName) => {
    const keysToExclude = ["programIds", "programs", "dealerIds", "utm_source"];

    return [
        pathify.plugin,
        createPersistedState({
            key: `${storeName}Filters`,
            paths: [`${storeName}.filters`],
            storage: storageUsed,
            getState(key, storage) {
                const url = new URL(window.location.href, true);
                if (!_.isNil(_.get(url, "query.reset"))) {
                    return undefined;
                }

                const value = storage.getItem(key);

                if (typeof value === "undefined") {
                    return undefined;
                }
                try {
                    const parsedValue = JSON.parse(value);

                    if (parsedValue) {
                        keysToExclude.forEach((excludeKey) => {
                            const firstKey = Object.keys(parsedValue)[0];
                            if (
                                parsedValue[firstKey] &&
                                parsedValue[firstKey].filters &&
                                excludeKey in parsedValue[firstKey].filters
                            ) {
                                delete parsedValue[firstKey].filters[excludeKey];
                            }
                        });
                    }

                    return parsedValue;
                } catch (error) {
                    console.error("Error parsing persisted state:", error);
                    return undefined;
                }
            },
        }),
        createPersistedState({
            key: `${storeName}SearchMethods`,
            paths: [`${storeName}.searchMethods`],
            storage: storageUsed,
        }),
        createPersistedState({
            key: storeName,
            paths: [`${storeName}.displayFields`],
        }),
        createPersistedState({
            key: `${storeName}FilterNames`,
            paths: [`${storeName}.filterNames`],
            storage: storageUsed,
        }),
    ];
};

export default {
    plugins: getPlugins,
};
