import _ from "lodash";

const convertErrorToObserverErrors = (error) => {
    const fieldErrors = _.get(error.response, "data.errors", []);
    if (!_.isEmpty(fieldErrors)) {
        return fieldErrors.reduce((obj, item) => ((obj[item.field] = [item.message]), obj), {});
    }

    return [];
};

const setErrors = (error, observer) => {
    const errorMessages = convertErrorToObserverErrors(error);
    _.forOwn(observer, function (value, key) {
        const errorMessage = _.get(errorMessages, key, null);
        console.log(`${key} = ${errorMessage}`);
        _.set(observer, key, errorMessage);
    });
};

export default {
    convertErrorToObserverErrors,
    setErrors,
};
