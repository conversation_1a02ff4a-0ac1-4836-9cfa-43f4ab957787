export const timeDateFormatter = (isoTimeDate) => {
    // example date in  ISO 8601 format: "2023-02-01T15:30:00"
    const date = new Date(isoTimeDate);
    const options = {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        timeZoneName: "short",
    };
    const formatter = new Intl.DateTimeFormat("default", options);
    const result = formatter.format(date);

    return result;
};

export default timeDateFormatter;
