import axios from "axios";
import lodashGet from "lodash/get";

export class ApiClient {
    constructor() {
        this.basePath = "/api";
        this.httpClient = axios.create({ withCredentials: true });

        this.httpClient.interceptors.response.use(
            (response) => response,
            (error) => {
                return Promise.reject(error);
            }
        );
    }

    get(path, params) {
        return this.httpClient.get(this.basePath + path, { params });
    }

    put(path, data, config) {
        return this.httpClient.put(this.basePath + path, data, config);
    }

    post(path, data, config) {
        return this.httpClient.post(this.basePath + path, data, config);
    }

    patch(path, data) {
        return this.httpClient.patch(this.basePath + path, data);
    }

    delete(path, data) {
        return this.httpClient.delete(this.basePath + path, data);
    }
}

export default new ApiClient();
