import _ from "lodash";

const intersectFieldsForDisplay = (allAvailableFields, fieldsForDisplay) => {
    return _.intersectionWith(allAvailableFields, fieldsForDisplay, function (arrVal, othVal) {
        if (_.isString(arrVal)) {
            return arrVal === othVal;
        } else if (_.isObject(arrVal)) {
            return othVal === _.get(arrVal, "value");
        }

        return false;
    });
};

export default {
    intersectFieldsForDisplay,
};
