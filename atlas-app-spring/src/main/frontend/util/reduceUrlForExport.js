const columnMap = {
    "program.name": "pname",
    firstName: "fn",
    lastName: "ln",
    stage: "st",
    email: "em",
    clientAdvisor: "ca",
    lastLoginAt: "lla",
    websiteVisits: "wvs",
    logins: "lg",
    websiteVisitsBeforeSignup: "wvbs",
    "traits.tradeInsCount": "tic",
    tradeYear: "ty",
    tradeMake: "tm",
    tradeModel: "tmo",
    tradeValue: "tv",
    tradeMileage: "tmil",
    tradeEquity: "te",
    tradePurchaseType: "tpt",
    tradePurchaseDate: "tpd",
    tradePayment: "tp",
    lenderName: "lnm",
    tradePaymentType: "tptp",
    remainingPayments: "rp",
    tradeTerm: "tt",
    maturityDate: "md",
    tier: "t",
    preApprovalCount: "pac",
    "traits.preQualificationsCount": "pq",
    "traits.leadsCount": "lc",
    "traits.financeAppsSubmittedCount": "fasc",
    "traits.salesCount": "sc",
    phoneNumber: "pn",
    smsEnabled: "sms",
    "vehicleOfInterest.stockType": "voist",
    "vehicleOfInterest.inTransit": "voiit",
    "vehicleOfInterest.certified": "voic",
    "vehicleOfInterest.year": "voiy",
    "vehicleOfInterest.make": "voim",
    "vehicleOfInterest.model": "voimo",
    "vehicleOfInterest.stockNumber": "voisn",
    timeZone: "tz",
    address: "addr",
    "address.street": "addrst",
    "address.city": "addrc",
    "address.stateCode": "addrs",
    "address.zipCode": "addrz",
    "address.dma.name": "dmn",
    "address.dma.rank": "dmr",
    "address.dma.code": "dmc",
    "traits.dealerUserTraitsList.activeVehiclesInGarageCount": "avigc",
    "traits.garageSavedVehicleCount": "gsvc",
    "traits.garageViewedVehicleCount": "gvc",
    "traits.dealerUserTraitsList.vehiclesInGarageCount": "vigc",
    "traits.dealerUserTraitsList.new": "dun",
    "traits.dealerUserTraitsList.used": "duu",
    createdDate: "cd",
    customerStatus: "cs",
    "programSubscriptionStages.lastModifiedDate": "stdt",
    assignedDealer: "ad",
    userLeads: "lt",
    locale: "lcl",
};

const labelMap = {
    Program: "pname",
    "First%20Name": "fn",
    "Last%20Name": "ln",
    Stage: "st",
    Email: "em",
    "Originating%20Salesperson": "ca",
    "Last%20Login": "lla",
    "Website%20Visits": "wvs",
    Logins: "lg",
    "PIN%20Visits": "wvbs",
    "Trade-ins": "tic",
    "Trade%20Year": "ty",
    "Trade%20Make": "tm",
    "Trade%20Model": "tmo",
    "Trade%20Offer": "tv",
    "Trade%20Mileage": "tmil",
    "Est.%20Trade%20Equity": "te",
    "Trade%20Purchase%20Type": "tpt",
    "Trade%20Purchase%20Date": "tpd",
    "Current%20Payment": "tp",
    Lender: "lnm",
    "Trade%20Type": "tptp",
    "Remaining%20Payments": "rp",
    "Financing%20Term": "tt",
    "Financing%20Maturity%20Date": "md",
    "Credit%20Tier": "t",
    "Pre-approvals": "pac",
    "Pre-quals": "pq",
    Leads: "lc",
    Applications: "fasc",
    Sales: "sc",
    "Phone%20Number": "pn",
    Sms: "sms",
    "VOI%20StockType": "voist",
    "VOI%20In%20Transit": "voiit",
    "VOI%20Certified": "voic",
    "VOI%20Year": "voiy",
    "VOI%20Make": "voim",
    "VOI%20Model": "voimo",
    "Time%20Zone": "tz",
    Address: "addr",
    Street: "addrst",
    City: "addrc",
    State: "addrs",
    Zip: "addrz",
    "DMA%20Name": "dmn",
    "DMA%20Rank": "dmr",
    "DMA%20Code": "dmc",
    "Active%20Vehicles%20in%20Garage": "avigc",
    "Saved%20Vehicles": "gsvc",
    "Recently%20Viewed%20Vehicles": "gvc",
    "Garage%20Vehicles": "vigc",
    "Garage%3A%20New": "dun",
    "Garage%3A%20Used": "duu",
    "Sign%20Up%20Date": "cd",
    "Customer%20Status": "cs",
    "Stage%20Transition": "stdt",
    "VOI%20Stock%20Number": "voisn",
    "Assigned%20Dealers": "ad",
    "Lead%20Type": "lt",
    Language: "lcl",
};

export const compressUrl = (url) => {
    let shortenedUrl = url;

    // Shorten columns
    const columnsMatch = url.match(/columns=([^&]*)/);
    if (columnsMatch) {
        const columns = columnsMatch[1].split("%2C");
        const shortColumns = columns.map((col) => columnMap[col] || col).join("%2C");
        shortenedUrl = shortenedUrl.replace(columnsMatch[1], shortColumns);
    }

    // Shorten labels
    const labelsMatch = url.match(/labels=([^&]*)/);
    if (labelsMatch) {
        const labels = labelsMatch[1].split("%2C");
        const shortLabels = labels.map((label) => labelMap[label] || label).join("%2C");
        shortenedUrl = shortenedUrl.replace(labelsMatch[1], shortLabels);
    }

    return shortenedUrl;
};
