import forEach from "lodash/forEach";
import capitalize from "lodash/capitalize";

const lookupTable = Object.freeze({
    "long term follow up": "Inactive",
    "post sale": "Sold",
});

/**
 * Renames display values in the given response based on a lookup table.
 *
 * @param {string|Object|Array} response - The response to be renamed. Can be a string, object, or array.
 * @returns {string|Object|Array} - The renamed response.
 */
export const renameDisplayValue = (response) => {
    if (typeof response === "string") {
        return sanitizeValue(response);
    }

    let sanitizedResponse = {};
    if (Array.isArray(response)) {
        sanitizedResponse = [];
    }

    // Function to sanitize individual values
    function sanitizeValue(value) {
        if (typeof value === "string") {
            let normalizedValue = value.trim().toLowerCase();
            return lookupTable.hasOwnProperty(normalizedValue) ? lookupTable[normalizedValue] : value;
        } else if (Array.isArray(value)) {
            return value.map((item) => sanitizeValue(item));
        }
        return value;
    }

    for (let key in response) {
        // Normalize the key
        let normalizedKey = key.trim().toLowerCase();
        let newKey = lookupTable.hasOwnProperty(normalizedKey) ? lookupTable[normalizedKey] : key;

        // Sanitize the value
        let sanitizedValue = sanitizeValue(response[key]);

        sanitizedResponse[newKey] = sanitizedValue;
    }

    return sanitizedResponse;
};

export function getSelectedProgramStage(stage, selectedDealerId, programSubscriptionStages, program) {
    // set selected program as global stage
    let stageName = stage;
    //use global stage if no dealer is selected
    if (selectedDealerId === null || programSubscriptionStages.length === 0) {
        return renameDisplayValue(stageName);
    }

    forEach(programSubscriptionStages, (p) => {
        let dealerMatch = selectedDealerId === p.dealerId;
        // case for Express users. No program associated as there is no campaign defined on the source
        let noProgramFound = program === null;
        let programMatch = program?.id === p.programId;

        // if selectedDealer id equals programDealer id && customers program id matches program id override global stage
        if (dealerMatch && (noProgramFound || programMatch)) {
            stageName = p.stageName;
        }
    });

    return renameDisplayValue(stageName);
}
