import DOMPurify from "dompurify";

/**
 * Recursively sanitize an object's string values using DOMPurify.
 * @param {Object} obj - The object to sanitize.
 * @param {Object} options - Optional configuration object.
 * @param {Object} [options.config={}] - Optional configuration object for DOMPurify.
 * @param {boolean} [options.deep=false] - Flag to indicate if deep sanitization is needed.
 * @param {Array<string>} [options.exclude=[]] - Keys to exclude from sanitization.
 * @returns {Object} - Sanitized object.
 */
function sanitizeObject(obj, { config = {}, deep = false, exclude = [] } = {}) {
    const sanitizedObj = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            let value = obj[key];

            if (exclude.includes(key)) {
                // Exclude this key from sanitization
                sanitizedObj[key] = value;
            } else if (typeof value === "string") {
                // Sanitize string value using DOMPurify
                value = DOMPurify.sanitize(value, config);
                sanitizedObj[key] = value;
            } else if (deep && typeof value === "object" && value !== null) {
                // Recursively sanitize nested objects
                value = sanitizeObject(value, { config, deep, exclude });
                sanitizedObj[key] = value;
            } else {
                // Assign the value as is if not a string or excluded key
                sanitizedObj[key] = value;
            }
        }
    }

    return sanitizedObj;
}

/**
 * Sanitize input using DOMPurify.
 * @param {string|Object} input - The input to sanitize.
 * @param {Object} options - Optional configuration object.
 * @param {Object} [options.config={}] - Optional configuration object for DOMPurify.
 * @param {boolean} [options.deep=false] - Flag to indicate if deep sanitization is needed.
 * @param {Array<string>} [options.exclude=[]] - Keys to exclude from sanitization.
 * @returns {string|Object} - Sanitized input.
 */
export function sanitize(input, { config = {}, deep = false, exclude = [] } = {}) {
    if (typeof input === "string") {
        // Sanitize a single string
        return DOMPurify.sanitize(input, config);
    } else if (typeof input === "object" && input !== null) {
        // Sanitize object's string values (and deep if specified)
        return sanitizeObject(input, { config, deep, exclude });
    } else {
        // Return input as-is for other types (number, boolean, etc.)
        return input;
    }
}
