import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/Sales/router";
import CarSaverFormatters from "@carsaver/filters";

// Import Stores used by this module
import { createStore } from "@/store/common";
import saleSearch from "Modules/Sales/store/saleSearch";

import "@/directives";
import VueCurrencyFilter from "vue-currency-filter";

Vue.use(CarSaverFormatters);
Vue.use(Vuex);
Vue.use(atlas);
Vue.use(VueCurrencyFilter, {
    symbol: "$",
    thousandsSeparator: ",",
    fractionCount: 0,
    fractionSeparator: ".",
    symbolPosition: "front",
    symbolSpacing: false,
});

const plugins = storeHelper.plugins("saleSearch");

const store = createStore(
    {
        saleSearch,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
