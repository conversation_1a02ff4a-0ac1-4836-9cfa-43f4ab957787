import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/Settings/router";
import CarSaverFormatters from "@carsaver/filters";
import VueMoment from "vue-moment";
import VueMask from "v-mask";

// Import Stores used by this module
import { createStore } from "@/store/common";
import userSearch from "Modules/Customers/store/userSearch";
import userDetails from "@/modules/Customers/store/userDetails";
import userDeal from "@/modules/Customers/store/userDeal";
import dealerHours from "@/modules/Customers/store/dealerHours";
import addDeal from "@/modules/Customers/store/addDeal";

import "@/directives";

const debug = process.env.NODE_ENV !== "production";

Vue.use(CarSaverFormatters);
Vue.use(Vuex);
Vue.use(atlas);
Vue.use(VueMoment);
Vue.use(VueMask);

const plugins = storeHelper.plugins("userSearch");

const store = createStore(
    {
        userSearch,
        userDetails,
        userDeal,
        dealerHours,
        addDeal,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
