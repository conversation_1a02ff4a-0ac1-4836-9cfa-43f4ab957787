import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/Training/router";
import VueCurrencyFilter from "vue-currency-filter";
import CarSaverFormatters from "@carsaver/filters";

// Import Stores used by this module
import { createStore } from "@/store/common";
import trainingStore from "Modules/Training/store/Training";

import "@/directives";

Vue.use(Vuex);
Vue.use(atlas);
Vue.use(CarSaverFormatters);
Vue.use(VueCurrencyFilter, {
    symbol: "$",
    thousandsSeparator: ",",
    fractionCount: 0,
    fractionSeparator: ".",
    symbolPosition: "front",
    symbolSpacing: false,
});

const plugins = storeHelper.plugins("trainingStore");

const store = createStore(
    {
        trainingStore,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
