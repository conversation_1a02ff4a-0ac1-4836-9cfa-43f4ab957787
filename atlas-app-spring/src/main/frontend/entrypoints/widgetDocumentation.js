import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import router from "Modules/WidgetDocumentation/router";
import CarSaverFormatters from "@carsaver/filters";
import VuePrism from "vue-prism";

import "prismjs/themes/prism.css";

// Import Stores used by this module
import { createStore } from "@/store/common";

import "@/directives";

Vue.use(Vuex);
Vue.use(atlas);
Vue.use(CarSaverFormatters);
Vue.use(VuePrism);

const store = createStore();

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
