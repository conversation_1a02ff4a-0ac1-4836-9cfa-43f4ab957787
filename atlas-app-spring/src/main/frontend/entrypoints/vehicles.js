import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/Inventory/router";
import VueCurrencyFilter from "vue-currency-filter";

// Import Stores used by this module
import { createStore } from "@/store/common";
import inventorySearch from "Modules/Inventory/store/inventorySearch";

import "@/directives";

Vue.use(Vuex);
Vue.use(atlas);
Vue.use(VueCurrencyFilter, {
    symbol: "$",
    thousandsSeparator: ",",
    fractionCount: 0,
    fractionSeparator: ".",
    symbolPosition: "front",
    symbolSpacing: false,
});

const plugins = storeHelper.plugins("inventorySearch");

const store = createStore(
    {
        inventorySearch,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
