import Vue from "vue";
import Vuex from "vuex";
import vuetify from "@/plugins/vuetify";
import atlas from "@/plugins/atlas";
import "es6-promise/auto";
import App from "@/App";
import storeHelper from "@/util/storeHelper";
import router from "Modules/ImportLogs/router";
import CarSaverFormatters from "@carsaver/filters";

// Import Stores used by this module
import { createStore } from "@/store/common";
import importLogsSearch from "Modules/ImportLogs/store/importLogsSearch";

import "@/directives";

Vue.use(CarSaverFormatters);
Vue.use(Vuex);
Vue.use(atlas);

const plugins = storeHelper.plugins("importLogsSearch");

const store = createStore(
    {
        importLogsSearch,
    },
    plugins
);

/* eslint-disable no-new */
new Vue({
    el: "#app",
    store,
    router,
    vuetify,
    render: (h) => h(App),
});
