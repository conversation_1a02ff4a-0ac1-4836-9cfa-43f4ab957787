import Vue from "vue";
import VueRouter from "vue-router";
import { routerOptions, layout, route, configureRouter } from "Util/routerHelper";
import SmsVerification from "./views/SmsVerification";
import RouteOneSsoFailure from "./views/RouteOneSsoFailure";
import NotFound from "./views/NotFound";
import AccessDenied from "./views/AccessDenied";

Vue.use(VueRouter);

const routes = [
    layout("Simple", [route("Home", "Home", null, "/"), route("Home", "Home", null, "/login-success")]),
    {
        path: "/sms-verification/:status",
        component: SmsVerification,
        props: true,
    },
    {
        path: "/routeone/sso-failure",
        component: RouteOneSsoFailure,
        props: true,
    },
    {
        path: "/not-found",
        component: NotFound,
        props: true,
    },
    {
        path: "/access-denied",
        component: AccessDenied,
        props: true,
    },
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
