<template>
    <div>
        <div v-if="userProgramAccessList.length === 0">
            <v-divider>inset</v-divider>
            <v-container>
                <v-card :class="`elevation-10`">
                    <v-card-text>
                        <p class="mt-4 text-center">
                            It doesn’t appear that you have access to any dealers at this time. Please contact CarSaver
                            Support:
                        </p>
                        <Program :program="supportContact" class="lg2"></Program>
                    </v-card-text>
                </v-card>
            </v-container>
        </div>
        <v-simple-table v-else fixed-header>
            <template #default>
                <thead>
                    <tr>
                        <th scope="col">Name</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(program, index) in userProgramAccessList" :key="index">
                        <td>
                            <a href="#" @click="goToProgram(program.id)">
                                {{ program.name }}
                            </a>
                        </td>
                    </tr>
                </tbody>
            </template>
        </v-simple-table>
    </div>
</template>
<script>
import { get, sync } from "vuex-pathify";
import api from "Util/api";
import Program from "@/components/ProgramSupportCard/components/Program";

export default {
    name: "ProgramAccessList",
    components: { Program },
    data() {
        return {
            supportContact: {
                name: "CarSaver Support",
                supportEmail: "<EMAIL>",
                supportPhone: "************",
            },
        };
    },
    computed: {
        userProgramAccessList: get("loggedInUser/userProgramAccessList"),
        userProgramDealerAccessList: sync("loggedInUser/userProgramDealerAccessList"),
    },
    methods: {
        setSelectedProgram(programId) {
            return api
                .post("/users/selectedProgram", { programId: programId })
                .then((response) => {
                    this.userProgramDealerAccessList = response.data;
                })
                .catch((error) => console.error(error));
        },
        goToProgram(programId) {
            this.setSelectedProgram(programId).finally(() =>
                this.$router.push({
                    path: `/customers`,
                    query: { programIds: programId },
                })
            );
        },
    },
};
</script>
