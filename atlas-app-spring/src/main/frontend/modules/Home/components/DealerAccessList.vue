<template>
    <div>
        <div v-if="userDealerAccessList.length === 0">
            <v-divider>inset</v-divider>
            <v-container>
                <v-card :class="`elevation-10`">
                    <v-card-text>
                        <p class="mt-4 text-center">
                            It doesn’t appear that you have access to any dealers at this time. Please contact CarSaver
                            Support:
                        </p>
                        <Program :program="supportContact" class="lg2"></Program>
                    </v-card-text>
                </v-card>
            </v-container>
        </div>
        <v-simple-table v-else fixed-header>
            <template #default>
                <thead>
                    <tr>
                        <th scope="col">Name</th>
                        <th scope="col">Phone Number</th>
                        <th scope="col">Address</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(dealer, index) in userDealerAccessList" :key="index">
                        <td>
                            <a :href="`/dealer/details?dealerIds=${dealer.id}`">
                                {{ dealer.name }}
                            </a>
                        </td>
                        <td>
                            <a :href="`tel:${dealer.phoneNumber}`">
                                {{ dealer.phoneNumber | phoneFormatter }}
                            </a>
                        </td>
                        <td>{{ dealer.fullAddress }}</td>
                    </tr>
                </tbody>
            </template>
        </v-simple-table>
    </div>
</template>
<script>
import { get } from "vuex-pathify";
import api from "Util/api";
import Program from "@/components/ProgramSupportCard/components/Program";

export default {
    name: "DealerAccessList",
    components: { Program },
    data() {
        return {
            // TODO: Hardcoded.
            supportContact: {
                name: "CarSaver Support",
                supportEmail: "<EMAIL>",
                supportPhone: "************",
            },
        };
    },
    computed: {
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
    },
};
</script>
