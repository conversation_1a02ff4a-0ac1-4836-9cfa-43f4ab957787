<template>
    <v-card :loading="isLoading" class="fill-height mb-4 d-flex flex-column">
        <v-card-title>
            <span>Training Courses</span>
            <v-spacer></v-spacer>
            <v-switch v-if="coursesNotEmpty()" v-model="displayByCourse" :label="`${displaySwitchLabel}`"></v-switch>
        </v-card-title>
        <v-card-text>
            <p v-if="!coursesNotEmpty() && !isLoading">There are not training courses at the moment.</p>
            <course-row v-if="displayByCourse" :courses="courses"></course-row>
            <user-row v-else :users="users"></user-row>
        </v-card-text>
    </v-card>
</template>

<script>
import api from "@/util/api.js";
import CourseRow from "@/modules/Dealer/components/TrainingCourses/ByCourse/CourseRow";
import UserRow from "@/modules/Dealer/components/TrainingCourses/ByUser/UserRow";
export default {
    name: "TrainingCourses",
    components: { CourseRow, UserRow },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            isLoading: false,
            courses: [],
            users: [],
            displayByCourse: true,
        };
    },
    computed: {
        displaySwitchLabel() {
            return this.displayByCourse ? "By Courses" : "By Users";
        },
    },
    mounted() {
        this.fetchCourses();
    },
    methods: {
        coursesNotEmpty() {
            return this.courses != null && !_.isEmpty(this.courses);
        },
        fetchCourses() {
            this.isLoading = true;
            api.get(`/training/dealers/${this.dealerId}/courses`)
                .then((response) => {
                    this.courses = response.data.byCourse;
                    this.users = response.data.byUser;
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>
