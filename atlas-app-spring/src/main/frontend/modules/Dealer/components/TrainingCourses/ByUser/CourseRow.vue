<template>
    <v-expansion-panels accordion focusable class="rounded-0">
        <v-expansion-panel v-for="(course, i) in courses" :key="i">
            <v-expansion-panel-header>
                <span>
                    <span class="font-weight-bold"> Course: </span> {{ course.title }}
                    <completed :is-completed="isCompleted(course)" />
                </span>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
                <video-row :videos="course.videos" />
            </v-expansion-panel-content>
        </v-expansion-panel>
    </v-expansion-panels>
</template>

<script>
import VideoRow from "@/modules/Dealer/components/TrainingCourses/ByUser/VideoRow";
import Completed from "@/modules/Dealer/components/TrainingCourses/components/Completed";
export default {
    name: "CourseRow",
    components: { Completed, VideoRow },
    props: {
        courses: {
            type: Array,
            required: true,
        },
    },
    methods: {
        isCompleted(course) {
            let completed = true;
            course.videos.forEach((course) => {
                if (_.isNil(course.completedAt)) {
                    completed = false;
                    return;
                }
            });
            return completed;
        },
    },
};
</script>
