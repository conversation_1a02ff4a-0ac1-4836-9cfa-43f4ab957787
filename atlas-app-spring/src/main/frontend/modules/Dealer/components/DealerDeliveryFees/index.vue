<template>
    <v-card :loading="isLoading">
        <v-card-text class="text--primary">
            <v-data-table :headers="headers" :items="fees">
                <template #top>
                    <v-toolbar flat>
                        <v-toolbar-title class="card-title">
                            Dealer Delivery Fees
                            <info-tooltip :max-width="250">
                                Delivery Fees are wholly established and managed by
                                <span> {{ dealerName }}. </span>
                                <span>
                                    {{ disclaimerCompanyName }}
                                </span>
                                does not determine the mileage or delivery fees charged by the dealer or any associated
                                policies or rules the dealer may set relating to deliver. To the extent any consumer has
                                questions, concerns or complaints relating to a dealer’s delivery of a vehicle, the
                                consumer should contact the dealer directly.
                            </info-tooltip>
                        </v-toolbar-title>
                        <v-spacer></v-spacer>

                        <add-delivery-fee
                            :dealer-id="dealerId"
                            :show-dialog="showDialog"
                            :fee="selectedFee"
                            @saveDeliveryFee="createOrUpdateDeliveryFee"
                            @reset="resetShowDialog"
                        />
                    </v-toolbar>
                </template>
                <template #item.range="{ item }"> {{ item.min }} - {{ item.max }} </template>
                <template #item.amount="{ item }"> ${{ item.amount }} </template>

                <template #item.actions="{ item }">
                    <v-icon v-if="userCanModifyFees" small class="mr-2" @click="editFee(item)"> mdi-pencil </v-icon>
                    <v-icon v-if="userCanModifyFees" small @click="deleteFee(item.id)"> mdi-delete </v-icon>
                </template>

                <template #no-data> No delivery fees have been added for {{ dealerName }} </template>
            </v-data-table>
        </v-card-text>
    </v-card>
</template>

<script>
import { get } from "vuex-pathify";
import api from "@/util/api.js";
import AddDeliveryFee from "Modules/Dealer/components/DealerDeliveryFees/AddDeliveryFee.vue";
import lodashGet from "lodash/get";
import isEmpty from "lodash/isEmpty";
import InfoTooltip from "Components/InfoTooltip.vue";

export default {
    name: "DealerDeliveryFees",
    components: { InfoTooltip, AddDeliveryFee },
    props: {
        dealerId: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            isLoading: true,
            fees: [],
            showDialog: false,
            selectedFee: {
                id: null,
                min: null,
                max: null,
                amount: null,
            },
        };
    },
    computed: {
        dealerName: get("dealerStore/<EMAIL>"),
        isNissanDealer: get("loggedInUser/isNnaDealer"),
        headers() {
            const tableHeaders = [
                {
                    text: "Mileage Range",
                    sortable: false,
                    value: "range",
                },
                {
                    text: "Fee Amount",
                    sortable: false,
                    value: "amount",
                },
            ];

            if (this.userCanModifyFees) {
                tableHeaders.push({
                    text: "Actions",
                    sortable: false,
                    value: "actions",
                    align: "end",
                });
            }

            return tableHeaders;
        },
        userCanModifyFees() {
            const userCanEditDealer =
                this.$acl.hasAuthority("edit:dealer") || this.$acl.hasDealerPermission(this.dealerId, "dealer:edit");

            const userCanEditNewPricing = this.$acl.hasDealerPermission(this.dealerId, "inventory:new-pricing:edit");

            const userCanEditUsedPricing = this.$acl.hasDealerPermission(this.dealerId, "inventory:used-pricing:edit");

            return userCanEditDealer || userCanEditNewPricing || userCanEditUsedPricing;
        },
        disclaimerCompanyName() {
            let companyName = "CarSaver";

            if (this.isNissanDealer) {
                companyName = "Nissan North America, Inc. (“NNA”)";
            }

            return companyName;
        },
    },
    mounted() {
        this.fetchDealerDeliveryFees(this.dealerId);
    },
    methods: {
        fetchDealerDeliveryFees() {
            const result = api
                .get(`/dealer/${this.dealerId}/dealer-delivery-fees`)
                .then((response) => {
                    this.fees = lodashGet(response, "data", []);
                    this.isLoading = false;

                    if (!isEmpty(this.fees)) {
                        this.$emit("paidDeliveryEnabled");
                    }
                })
                .catch((error) => {
                    console.error(error);
                });

            return result;
        },
        createOrUpdateDeliveryFee(value) {
            this.isLoading = true;
            const result = api
                .post(`/dealer/${this.dealerId}/dealer-delivery-fee`, value)
                .then((response) => {
                    this.fees = lodashGet(response, "data.deliveryFees", []);
                    this.isLoading = false;
                })
                .catch((error) => {
                    const errors = lodashGet(error, "response.data.errors", []);
                    this.handleErrors(errors);
                    this.isLoading = false;
                });

            return result;
        },
        deleteDeliveryFee(id) {
            this.isLoading = true;
            let path = `/dealer/${this.dealerId}/dealer-delivery-fee/${id}`;
            const result = api
                .delete(path)
                .then((response) => {
                    this.fees = lodashGet(response, "data.deliveryFees", []);
                    this.isLoading = false;
                })
                .catch((error) => {
                    console.error(error);
                    this.isLoading = false;
                });

            return result;
        },
        editFee(item) {
            this.selectedFee = Object.assign({}, item);
            this.showDialog = true;
        },
        resetShowDialog() {
            this.showDialog = false;
            this.selectedFee = {
                id: null,
                min: null,
                max: null,
                amount: null,
            };
        },
        deleteFee(id) {
            this.deleteDeliveryFee(id);
        },
        handleErrors(errors) {
            if (isEmpty(errors)) {
                return;
            }

            errors.forEach((error) => {
                let message = "Error: ";

                if (error.message === "rangeOverlap") {
                    message += "Please set a range that doesn't overlap any current min-max delivery ranges";
                } else if (error.message) {
                    message += error.message;
                } else {
                    return;
                }

                this.$toast.error(message);
            });
        },
    },
};
</script>
