<template>
    <v-card :loading="isLoading" class="fill-height mb-4 d-flex flex-column">
        <v-card-text class="text--primary">
            <v-data-table
                :headers="tableHeaders"
                :items="users"
                :single-expand="singleExpand"
                :expanded.sync="expanded"
                item-key="id"
                show-expand
            >
                <template #top>
                    <v-toolbar flat>
                        <v-toolbar-title class="card-title"> Users </v-toolbar-title>
                        <v-spacer></v-spacer>
                        <!-- fee editor -->
                        <add-user-dialog v-if="hasManagerUser" :dealer-id="dealerId" @done="onEditComplete" />
                    </v-toolbar>
                </template>
                <template #item.name="{ item }">
                    <div class="d-flex flex-column align-center text-center my-2">
                        <span class="mb-1">{{ item.fullName }}</span>
                        <v-chip
                            v-if="item.contactType"
                            x-small
                            :color="item.contactType.toLowerCase() === 'primary' ? 'primary' : 'secondary'"
                        >
                            {{ item.contactType }}
                        </v-chip>
                    </div>
                </template>

                <template #item.phoneNumber="{ item }">
                    <a :href="`tel:${item.phoneNumber}`">{{ item.phoneNumber | phoneFormatter }}</a>
                </template>

                <template #item.email="{ item }">
                    <a :href="`mailto:${item.email}`">
                        {{ item.email }}
                    </a>
                </template>

                <template #item.roles="{ item }">
                    <div class="d-flex flex-column">
                        <v-tooltip v-if="item.programManager" left max-width="300px">
                            <template #activator="{ on, attrs }">
                                <v-chip v-bind="attrs" small class="my-1 d-flex justify-center align-center" v-on="on">
                                    PM
                                </v-chip>
                            </template>
                            <div>Program Manger for {{ item.pmStockType }}</div>
                        </v-tooltip>
                        <v-tooltip v-if="item.followupManager" left max-width="300px">
                            <template #activator="{ on, attrs }">
                                <v-chip v-bind="attrs" small class="mb-1 d-flex justify-center align-center" v-on="on">
                                    FM
                                </v-chip>
                            </template>
                            <div>Follow Up Manager for {{ item.fmStockType }}</div>
                        </v-tooltip>
                        <v-tooltip v-if="item.salesManager" left max-width="300px">
                            <template #activator="{ on, attrs }">
                                <v-chip v-bind="attrs" small class="mb-1 d-flex justify-center align-center" v-on="on">
                                    SM
                                </v-chip>
                            </template>
                            <div>Sales Manager for {{ item.smStockType }}</div>
                        </v-tooltip>
                    </div>
                </template>

                <template #item.permissions="{ item }">
                    <v-tooltip left max-width="300px">
                        <template #activator="{ on, attrs }">
                            <v-chip class="ma-2" v-bind="attrs" v-on="on">
                                {{ item.permissions.length }}
                            </v-chip>
                        </template>
                        <div v-for="permission in item.permissions" :key="permission.id">
                            <v-icon dark> mdi-lock-open </v-icon>
                            {{ permission.description }}
                        </div>
                    </v-tooltip>
                </template>

                <template #item.smsVerified="{ item }">
                    <div class="d-flex flex-column align-center">
                        <v-tooltip top>
                            <template #activator="{ on, attrs }">
                                <boolean-indicator
                                    small
                                    :value="item.smsVerified"
                                    v-bind="attrs"
                                    v-on="item.smsVerified ? !on : on"
                                />
                            </template>
                            <span>
                                <span v-if="phoneNumberIsLandline(item.phoneNumberInfo)" class="flex-wrap">
                                    Landline number, unable to send SMS.
                                </span>
                                <span v-else-if="!item.smsEnabled">Disabled</span>
                                <span v-else>Needs verification</span>
                            </span>
                        </v-tooltip>
                    </div>
                </template>
                <template #item.actions="{ item }">
                    <edit-user-dialog
                        v-if="hasManagerUser"
                        :dealer-user="item"
                        :dealer-id="dealerId"
                        @done="onEditComplete"
                    />
                    <delete-user-dialog
                        v-if="hasManagerUser"
                        :dealer-user="item"
                        :dealer-id="dealerId"
                        @done="onDeleteComplete"
                    />
                    <v-menu>
                        <template #activator="{ on, attrs }">
                            <v-btn icon v-bind="attrs" v-on="on">
                                <v-icon>mdi-dots-vertical</v-icon>
                            </v-btn>
                        </template>

                        <v-list nav dense>
                            <v-list-item
                                v-if="
                                    item.smsEnabled && item.smsVerified && !phoneNumberIsLandline(item.phoneNumberInfo)
                                "
                                link
                                @click="sendSmsTest(item)"
                            >
                                <v-list-item-title>Send Test SMS</v-list-item-title>
                            </v-list-item>
                            <v-list-item
                                v-if="
                                    item.smsEnabled && !item.smsVerified && !phoneNumberIsLandline(item.phoneNumberInfo)
                                "
                                link
                                @click="sendSmsVerification(item)"
                            >
                                <v-list-item-title>Verify SMS</v-list-item-title>
                            </v-list-item>
                            <v-list-item
                                v-if="
                                    !(
                                        item.smsEnabled &&
                                        item.smsVerified &&
                                        !phoneNumberIsLandline(item.phoneNumberInfo)
                                    ) &&
                                    !(
                                        item.smsEnabled &&
                                        !item.smsVerified &&
                                        !phoneNumberIsLandline(item.phoneNumberInfo)
                                    )
                                "
                            >
                                <v-list-item-title>No Actions Available</v-list-item-title>
                            </v-list-item>
                        </v-list>
                    </v-menu>
                </template>

                <template #expanded-item="{ headers, item }">
                    <td :colspan="headers.length">
                        <v-card class="my-4" color="#eee" elevation="0">
                            <v-container fluid>
                                <v-row>
                                    <v-col cols="12" sm="3">
                                        <v-list-item dense>
                                            <v-list-item-content class="d-flex flex-column align-center">
                                                <v-list-item-title> SMS Enabled </v-list-item-title>
                                                <v-list-item-subtitle>
                                                    <boolean-indicator :value="item.smsEnabled || false" />
                                                </v-list-item-subtitle>
                                            </v-list-item-content>
                                        </v-list-item>
                                    </v-col>
                                    <v-col cols="12" sm="3">
                                        <v-list-item dense>
                                            <v-list-item-content class="d-flex flex-column align-center">
                                                <v-list-item-title> Spanish Speaking </v-list-item-title>
                                                <v-list-item-subtitle>
                                                    <boolean-indicator :value="item.spanishSpeaking || false" />
                                                </v-list-item-subtitle>
                                            </v-list-item-content>
                                        </v-list-item>
                                    </v-col>
                                    <v-col v-if="item.crmId" cols="12" sm="3">
                                        <v-list-item dense>
                                            <v-list-item-content>
                                                <v-list-item-title> CRM Id </v-list-item-title>
                                                <v-list-item-subtitle>
                                                    {{ item.crmId }}
                                                </v-list-item-subtitle>
                                            </v-list-item-content>
                                        </v-list-item>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </v-card>
                    </td>
                </template>
            </v-data-table>
        </v-card-text>
    </v-card>
</template>

<script>
import api from "Util/api";
import BooleanIndicator from "Components/BooleanIndicator";
import AddUserDialog from "@/modules/Dealer/components/Users/<USER>";
import EditUserDialog from "@/modules/Dealer/components/Users/<USER>";
import _ from "lodash";
import DeleteUserDialog from "@/modules/Dealer/components/Users/<USER>";

export default {
    name: "Users",
    components: { DeleteUserDialog, EditUserDialog, AddUserDialog, BooleanIndicator },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            users: [],
            tableHeaders: [
                {
                    text: "Name",
                    sortable: true,
                    value: "name",
                    align: "center",
                },
                {
                    text: "Phone",
                    sortable: true,
                    value: "phoneNumber",
                },
                {
                    text: "Email",
                    sortable: true,
                    value: "email",
                },
                {
                    text: "Roles",
                    sortable: false,
                    value: "roles",
                },
                {
                    text: "Job Title",
                    sortable: true,
                    value: "jobTitle",
                },
                {
                    text: "Permissions",
                    sortable: true,
                    value: "permissions",
                },
                {
                    text: "Sms Verified",
                    sortable: true,
                    value: "smsVerified",
                    align: "center",
                },
                {
                    text: "Actions",
                    value: "actions",
                    sortable: false,
                    align: "center",
                },
            ],
            expanded: [],
            singleExpand: true,
            isLoading: false,
        };
    },
    computed: {
        hasManagerUser() {
            return this.$acl.hasDealerPermission(this.dealerId, "dealer-user:edit");
        },
    },
    mounted() {
        this.fetchUsers(this.dealerId);
    },
    methods: {
        onEditComplete(editedUser) {
            const exists = _.find(this.users, ["id", editedUser.id]);
            // if exists just update the item inline otherwise add to the list
            if (exists) {
                this.users = _.map(this.users, (dealerUser) => {
                    return dealerUser.id === editedUser.id ? editedUser : dealerUser;
                });
                this.$toast.success("User Updated!");
            } else {
                this.users.push(editedUser);
                this.$toast.success("User Added!");
            }
        },
        onDeleteComplete(deletedUser) {
            this.users = _.filter(this.users, function (u) {
                return u.id !== deletedUser.id;
            });
            this.$toast.success("User Removed!");
        },
        fetchUsers(dealerId) {
            this.isLoading = true;
            api.get(`/dealer/${dealerId}/users`)
                .then((response) => {
                    this.users = response.data;
                })
                .catch((error) => console.error(error))
                .finally(() => {
                    this.isLoading = false;
                });
        },
        phoneNumberIsLandline(phoneNumberInfo) {
            return phoneNumberInfo && phoneNumberInfo.carrier.type === "landline";
        },
        sendSmsVerification(user) {
            api.post(`/users/${user.id}/sendVerificationText`)
                .then(() => {
                    this.$toast.success(`Verification SMS sent to ${user.firstName} ${user.lastName}`);
                })
                .catch((error) => {
                    const errorMsg = _.get(error, "response.data.message", "Unknown Error");
                    this.$toast.error(`Error sending verification SMS! Error = ${errorMsg}`);
                });
        },
        sendSmsTest(user) {
            api.get(`/users/${user.id}/sms-test`)
                .then(() => {
                    this.$toast.success(`Test SMS sent to ${user.firstName} ${user.lastName}`);
                })
                .catch(() => {
                    this.$toast.error(`Error sending verification SMS to ${user.firstName} ${user.lastName}.`);
                });
        },
    },
};
</script>

<style lang="scss">
.v-data-table-header th {
    white-space: nowrap !important;
}
</style>
