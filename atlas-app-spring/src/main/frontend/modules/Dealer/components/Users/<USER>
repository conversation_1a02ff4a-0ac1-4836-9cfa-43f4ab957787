<template>
    <v-dialog v-model="showUserDialog" max-width="700px">
        <!-- activator slot contains the button or whatever is launching the dialog -->
        <template #activator="{ on, attrs }">
            <v-btn color="primary" dark class="mb-2" v-bind="attrs" v-on="on" @click="showUserDialog = true">
                Add User
            </v-btn>
        </template>
        <template #default>
            <v-stepper v-model="wizardStep" vertical>
                <v-stepper-step :complete="wizardStep > 1" step="1"> Find User </v-stepper-step>
                <v-stepper-content step="1">
                    <v-card>
                        <v-card-text>
                            <v-form ref="userEmailSearchForm" lazy-validation @submit.prevent="searchEmail">
                                <v-container>
                                    <v-row>
                                        <v-col cols="12">
                                            <v-text-field
                                                ref="email"
                                                v-model="form.email"
                                                label="Email"
                                                outlined
                                                dense
                                                required
                                                :error-messages="errorMessages.email"
                                                :rules="validationRules.email"
                                            />
                                        </v-col>
                                    </v-row>
                                </v-container>
                                <v-btn :loading="isWorking" @click="searchEmail"> Search </v-btn>
                            </v-form>
                        </v-card-text>
                    </v-card>
                </v-stepper-content>

                <v-stepper-step :complete="wizardStep > 2" step="2">
                    Create Account <small>Optional</small>
                </v-stepper-step>
                <v-stepper-content step="2">
                    <v-card>
                        <v-card-text>
                            <v-form ref="userAddForm" lazy-validation @submit.prevent="createAccount">
                                <v-container>
                                    <v-row>
                                        <v-col cols="12">
                                            <v-text-field v-model="form.email" label="Email" outlined dense disabled />
                                        </v-col>
                                    </v-row>
                                    <v-row>
                                        <v-col>
                                            <v-text-field
                                                ref="firstName"
                                                v-model="form.firstName"
                                                label="First Name"
                                                outlined
                                                dense
                                                required
                                                :error-messages="errorMessages.firstName"
                                                :rules="validationRules.name"
                                            />
                                        </v-col>
                                        <v-col>
                                            <v-text-field
                                                ref="lastName"
                                                v-model="form.lastName"
                                                label="Last Name"
                                                outlined
                                                dense
                                                required
                                                :error-messages="errorMessages.lastName"
                                                :rules="validationRules.name"
                                            />
                                        </v-col>
                                    </v-row>
                                    <v-row>
                                        <v-col>
                                            <v-select
                                                v-model="form.jobTitleId"
                                                :items="availableJobTitles"
                                                label="Job Title"
                                                outlined
                                                dense
                                                item-text="title"
                                                item-value="id"
                                            />
                                        </v-col>
                                    </v-row>
                                    <v-row>
                                        <v-col>
                                            <v-text-field
                                                ref="phoneNumber"
                                                v-model="form.phoneNumber"
                                                label="Phone Number"
                                                outlined
                                                dense
                                                required
                                                :error-messages="errorMessages.phoneNumber"
                                                :rules="validationRules.phoneNumber"
                                            />
                                        </v-col>
                                    </v-row>
                                </v-container>
                                <v-btn :loading="isWorking" @click="createAccount"> Create </v-btn>
                            </v-form>
                        </v-card-text>
                    </v-card>
                </v-stepper-content>

                <v-stepper-step :complete="wizardStep > 3" step="3"> Permissions </v-stepper-step>
                <v-stepper-content step="3">
                    <v-card>
                        <v-card-text>
                            <dealer-permissions-form
                                v-if="dealerUser"
                                :dealer-id="dealerId"
                                :dealer-user="dealerUser"
                                @done="emitDone"
                            />
                        </v-card-text>
                    </v-card>
                </v-stepper-content>
            </v-stepper>
        </template>
    </v-dialog>
</template>

<script>
import api from "Util/api";
import veeValidateUtils from "Util/veeValidateUtils";
import DealerPermissionsForm from "./DealerPermissionsForm";
import _ from "lodash";

export default {
    name: "AddUserDialog",
    components: { DealerPermissionsForm },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            wizardStep: 1,
            dealerUser: null,
            isWorking: false,
            showUserDialog: false,
            availableJobTitles: [],
            errorMessages: {
                email: null,
                firstName: null,
                lastName: null,
                phoneNumber: null,
                jobTitleId: null,
            },
            form: {
                email: null,
                firstName: null,
                lastName: null,
                phoneNumber: null,
            },
            validationRules: {
                email: [(v) => !!v || "Email is required", (v) => !v || v.length <= 50 || "Maximum of 50 characters"],
                name: [(v) => !!v || "Name is required", (v) => !v || v.length <= 50 || "Maximum of 50 characters"],
                phoneNumber: [
                    (v) => !!v || "Phone is required",
                    (v) => !v || v.length <= 10 || "Maximum of 10 characters",
                ],
            },
        };
    },
    watch: {
        showUserDialog: function (val) {
            if (val) {
                // first time component loads this ref doesn't exist yet when this is fired
                const userSearch = this.$refs["userEmailSearchForm"];
                if (userSearch) {
                    userSearch.reset();
                }

                const userAddForm = this.$refs["userAddForm"];
                if (userAddForm) {
                    userAddForm.reset();
                }

                this.wizardStep = 1;
                this.isWorking = false;
                this.dealerUser = null;

                this.fetchFormData();
            }
        },
    },
    methods: {
        emitDone(dealerUser) {
            this.showUserDialog = false;
            this.$emit("done", dealerUser);
        },
        fetchFormData() {
            api.get(`/dealer/${this.dealerId}/users/form-data`)
                .then((response) => {
                    this.availableJobTitles = _.get(response, "data.jobTitles", null);
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        searchEmail() {
            this.isWorking = true;
            api.get(`/dealer/${this.dealerId}/users?email=${this.form.email}`)
                .then((response) => {
                    this.dealerUser = response.data;
                    this.wizardStep = 3;
                })
                .catch((error) => {
                    if (error.response.status === 404) {
                        this.wizardStep = 2;
                    } else if (error.response.status === 400) {
                        veeValidateUtils.setErrors(error, this.errorMessages);
                    }
                })
                .finally(() => {
                    this.isWorking = false;
                });
        },
        createAccount() {
            this.isWorking = true;
            api.post(`/dealer/${this.dealerId}/users`, { ...this.form })
                .then((response) => {
                    this.dealerUser = response.data;
                    this.wizardStep = 3;
                })
                .catch((error) => {
                    veeValidateUtils.setErrors(error, this.errorMessages);
                })
                .finally(() => {
                    this.isWorking = false;
                });
        },
    },
};
</script>

<style scoped></style>
