<template>
    <v-card :loading="isLoading">
        <v-card-text class="text--primary">
            <v-data-table :headers="headers" :items="fees">
                <template #top>
                    <v-toolbar flat>
                        <v-toolbar-title class="card-title"> Dealer Fees </v-toolbar-title>
                        <v-spacer></v-spacer>
                        <!-- fee editor -->
                        <v-dialog v-if="!isLoading" v-model="feeEditorShowing" max-width="500px">
                            <!-- activator slot contains the button or whatever is launching the dialog -->
                            <template #activator="{ on, attrs }">
                                <v-btn
                                    v-if="userCanModifyFees"
                                    color="primary"
                                    dark
                                    class="mb-2"
                                    v-bind="attrs"
                                    v-on="on"
                                    @click="showNewEditor()"
                                >
                                    Add Fee
                                </v-btn>
                            </template>
                            <template #default>
                                <v-card>
                                    <v-card-title>
                                        <span class="headline">
                                            {{ feeEditorTitle }}
                                        </span>
                                    </v-card-title>
                                    <v-card-text>
                                        <v-form ref="feeEditorForm" v-model="feeEditorValid" lazy-validation>
                                            <v-container>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-text-field
                                                            v-model="dealerFee.name"
                                                            label="Name"
                                                            outlined
                                                            dense
                                                            required
                                                            counter
                                                            :rules="validationRules.name"
                                                        />
                                                    </v-col>
                                                </v-row>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-select
                                                            v-model="dealerFee.feeType"
                                                            :items="feeTypes"
                                                            dense
                                                            label="Fee Type"
                                                            outlined
                                                            persistent-hint
                                                            hint="Used for determining how fees are taxed."
                                                            required
                                                            :rules="validationRules.feeType"
                                                        />
                                                    </v-col>
                                                </v-row>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-select
                                                            v-model="dealerFee.stockType"
                                                            :items="stockTypes"
                                                            dense
                                                            label="Stock Type"
                                                            outlined
                                                            persistent-hint
                                                            :hint="stockTypeHint"
                                                            required
                                                            :rules="validationRules.stockType"
                                                        />
                                                    </v-col>
                                                </v-row>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-text-field
                                                            v-model="dealerFee.amount"
                                                            label="Amount"
                                                            prepend-inner-icon="mdi-currency-usd"
                                                            outlined
                                                            dense
                                                            required
                                                            :rules="validationRules.amount"
                                                        />
                                                    </v-col>
                                                </v-row>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-select
                                                            v-model="dealerFee.dealType"
                                                            :items="dealTypes"
                                                            dense
                                                            label="Deal Type"
                                                            outlined
                                                            persistent-hint
                                                            required
                                                            :rules="validationRules.dealType"
                                                        />
                                                    </v-col>
                                                </v-row>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-select
                                                            v-model="dealerFee.isInception"
                                                            :items="isInception"
                                                            dense
                                                            label="Lease | Cap Cost / Inception"
                                                            outlined
                                                            persistent-hint
                                                            required
                                                            :rules="validationRules.inceptionRule"
                                                        />
                                                    </v-col>
                                                </v-row>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-select
                                                            v-model="dealerFee.isTaxable"
                                                            :items="isTaxable"
                                                            dense
                                                            label="Taxable | Yes / No"
                                                            outlined
                                                            persistent-hint
                                                            required
                                                            :rules="validationRules.isTaxable"
                                                        />
                                                    </v-col>
                                                </v-row>
                                                <v-row>
                                                    <v-col cols="12">
                                                        <v-textarea
                                                            v-model="dealerFee.description"
                                                            label="Description"
                                                            outlined
                                                            dense
                                                            required
                                                            persistent-hint
                                                            hint="Brief description of the fee (less than 500 characters).  May be used in tooltips."
                                                            counter
                                                            :rules="validationRules.description"
                                                        />
                                                    </v-col>
                                                </v-row>
                                            </v-container>
                                        </v-form>
                                    </v-card-text>
                                    <v-card-actions>
                                        <v-spacer></v-spacer>
                                        <v-btn color="blue darken-1" text @click="hideEditor"> Cancel </v-btn>
                                        <v-btn color="blue darken-1" text @click="saveFee(dealerFee)"> Save </v-btn>
                                    </v-card-actions>
                                </v-card>
                            </template>
                        </v-dialog>
                        <v-dialog v-model="deleteWarningShowing" max-width="500px">
                            <v-card>
                                <v-card-title> Confirm Delete </v-card-title>
                                <v-card-text>
                                    Are you sure you want to delete "{{ feeToDelete.name }}" fee?
                                </v-card-text>
                                <v-card-actions>
                                    <v-spacer></v-spacer>
                                    <v-btn color="blue darken-1" text @click="cancelDeletion"> Cancel </v-btn>
                                    <v-btn color="blue darken-1" text @click="deletionConfirmed"> OK </v-btn>
                                    <v-spacer></v-spacer>
                                </v-card-actions>
                            </v-card>
                        </v-dialog>
                    </v-toolbar>
                </template>
                <template #item.name="{ item }">
                    {{ item.name | truncate(30) }}
                </template>
                <template #item.description="{ item }">
                    {{ item.description | truncate(30) }}
                </template>
                <template #item.amount="{ item }"> ${{ item.amount }} </template>
                <template #item.isInception="{ item }">
                    {{ inceptionOrCapCost(item.isInception) }}
                </template>
                <template #item.isTaxable="{ item }">
                    {{ yesOrNo(item.isTaxable) }}
                </template>
                <template #item.stockType="{ item }">
                    {{ item.stockType || "ALL" }}
                </template>
                <template #item.actions="{ item }">
                    <v-icon v-if="userCanModifyFees" small class="mr-2" @click="editFee(item)"> mdi-pencil </v-icon>
                    <v-icon v-if="userCanModifyFees" small @click="deleteFee(item)"> mdi-delete </v-icon>
                </template>
                <template #no-data> No fees have been added for {{ dealerName }} </template>
            </v-data-table>
        </v-card-text>
    </v-card>
</template>

<script>
import { get } from "vuex-pathify";
import _ from "lodash";
import api from "@/util/api.js";
import { sanitize } from "Util/sanitize";

export default {
    name: "DealerFees",
    filters: {
        truncate: function (val, size) {
            return val && val.length > size ? val.substring(0, size) + "..." : val;
        },
    },
    props: {
        dealerId: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            isLoading: true,
            fees: [],
            feeEditorShowing: false,
            deleteWarningShowing: false,
            dealerFee: {},
            feeToDelete: {},
            feeEditorValid: true,
            feeEditorTitle: "",
            feeTypes: [
                { text: "Documentation Fee", value: "DOC_FEE" },
                { text: "State Fee", value: "STATE_FEE" },
                { text: "County Fee", value: "COUNTY_FEE" },
                { text: "City Fee", value: "CITY_FEE" },
                { text: "Finance Charge", value: "FINANCE_CHARGE" },
                { text: "Other", value: "OTHER" },
            ],
            stockTypes: [
                { text: "All", value: null },
                { text: "New", value: "NEW" },
                { text: "Used", value: "USED" },
            ],
            dealTypes: [
                { text: "Apply To All Deal Types", value: "ALL_DEALS" },
                { text: "Apply To Finance Deals", value: "FINANCE" },
                { text: "Apply To Lease Deals", value: "LEASE" },
                { text: "Apply To Cash Deals", value: "CASH" },
            ],
            isInception: [
                { text: "Apply to Inceptions", value: "true" },
                { text: "Apply to Capital Cost", value: "false" },
                { text: "Not Applicable", value: null },
            ],
            isTaxable: [
                { text: "Yes", value: "true" },
                { text: "No", value: "false" },
            ],
            validationRules: {
                name: [(v) => !!v || "Name is required", (v) => v.length <= 50 || "Maximum of 50 characters"],
                feeType: [(v) => !!v || "Fee Type is required"],
                stockType: [],
                dealType: [(v) => !!v || "Deal Type is required"],
                isTaxable: [(v) => !!v || "Taxable Type is required"],
                amount: [
                    (v) => !!v || "Amount is required",
                    (v) =>
                        new RegExp(/^(\d{1,4}\.\d{1,2}|\d{1,4})$/).test(v) ||
                        "Amount must be numeric (no $) and less than $9999",
                    (v) => (v > 0 && v <= 9999) || "Amount must be between $0 and $9999",
                ],
                description: [
                    (v) => v.length <= 500 || "Maximum of 500 characters",
                    (v) => !!v || "Description is required",
                ],
                inceptionRule: [
                    (v) => {
                        if (v === null) {
                            if (this.dealerFee.dealType === "ALL_DEALS" || this.dealerFee.dealType === "LEASE") {
                                return "You must select an Inception method to proceed";
                            }
                        }
                    },
                ],
            },
        };
    },
    computed: {
        dealerName: get("dealerStore/<EMAIL>"),
        headers() {
            const tableHeaders = [
                {
                    text: "Fee Type",
                    sortable: true,
                    value: "feeType",
                },
                {
                    text: "Stock Type",
                    sortable: true,
                    value: "stockType",
                },
                {
                    text: "Name",
                    sortable: true,
                    value: "name",
                },
                {
                    text: "Amount",
                    sortable: true,
                    value: "amount",
                },
                {
                    text: "Deal Type",
                    sortable: true,
                    value: "dealType",
                },
                {
                    text: "Lease | Cap Cost / Inception",
                    sortable: true,
                    value: "isInception",
                },
                {
                    text: "Taxable",
                    sortable: true,
                    value: "isTaxable",
                },
                {
                    text: "Description",
                    sortable: true,
                    value: "description",
                },
            ];

            if (this.userCanModifyFees) {
                tableHeaders.push({
                    text: "Actions",
                    sortable: false,
                    value: "actions",
                });
            }

            return tableHeaders;
        },
        userCanModifyFees() {
            const userCanEditDealer =
                this.$acl.hasAuthority("edit:dealer") || this.$acl.hasDealerPermission(this.dealerId, "dealer:edit");

            const userCanEditNewPricing = this.$acl.hasDealerPermission(this.dealerId, "inventory:new-pricing:edit");

            const userCanEditUsedPricing = this.$acl.hasDealerPermission(this.dealerId, "inventory:used-pricing:edit");

            return userCanEditDealer || userCanEditNewPricing || userCanEditUsedPricing;
        },

        stockTypeHint() {
            const selectedStockType = this.dealerFee.stockType;

            const stockTypeText = _.chain(this.stockTypes).filter({ value: selectedStockType }).head().value().text;

            return `This fee will be applied to ${stockTypeText} stock types.`;
        },
    },
    mounted() {
        this.loadFees(this.dealerId);
    },
    methods: {
        loadFees() {
            api.get(`/dealer/${this.dealerId}/dealer-fees`)
                .then((response) => {
                    this.fees = response.data;
                    this.isLoading = false;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        showNewEditor() {
            this.dealerFee = {
                name: "",
                feeType: null,
                stockType: null,
                amount: null,
                dealType: null,
                isInception: null,
                isTaxable: null,
                description: "",
            };
            this.feeEditorTitle = "Add A New Fee";
            this.showEditor();
        },

        editFee(fee) {
            this.dealerFee = { ...fee };
            let isInception = this.dealerFee.isInception;
            this.dealerFee.isInception = isInception != null ? String(isInception) : null;
            this.dealerFee.isTaxable = String(this.dealerFee.isTaxable);
            this.feeEditorTitle = `Edit "${fee.name}" Fee`;
            this.showEditor(this.dealerFee);
        },

        showEditor() {
            const feeEditorFormRef = this.$refs.feeEditorForm;
            if (feeEditorFormRef) {
                feeEditorFormRef.resetValidation();
            }
            this.feeEditorShowing = true;
        },
        hideEditor() {
            this.feeEditorShowing = false;
        },
        persistFee(fee) {
            const feeIsNew = _.isNil(fee.id);
            const savePath = `/dealer/${this.dealerId}/dealer-fees`;

            return api
                .post(savePath, fee)
                .then((response) => {
                    const persistedFee = response.data;
                    /*
                    If a new fee is being added, append. Otherwise update the fee in place.
                    This keeps the UI from jumping around.
                     */
                    if (feeIsNew) {
                        this.fees.push(persistedFee);
                    } else {
                        this.fees = _.map(this.fees, (fee) => {
                            return fee.id === persistedFee.id ? persistedFee : fee;
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        saveFee(fee) {
            const valid = this.$refs.feeEditorForm.validate();
            if (valid) {
                // Sanitization
                this.persistFee(sanitize(fee)).then(() => {
                    this.hideEditor();
                    this.$toast.success("Saved!");
                });
            }
        },

        deleteFee(fee) {
            this.feeToDelete = fee;
            this.openDeleteWarning();
        },

        cancelDeletion() {
            this.resetDeleteFee();
            this.closeDeleteWarning();
        },

        deletionConfirmed() {
            this.closeDeleteWarning();
            this.removeFee(this.feeToDelete).then(() => {
                this.$toast.success(`"${this.feeToDelete.name}" fee removed `);
                this.resetDeleteFee();
            });
        },

        resetDeleteFee() {
            this.feeToDelete = {};
        },

        openDeleteWarning() {
            this.deleteWarningShowing = true;
        },

        closeDeleteWarning() {
            this.deleteWarningShowing = false;
        },

        removeFee(fee) {
            const deletePath = `/dealer/${this.dealerId}/dealer-fees/${fee.id}`;

            return api
                .delete(deletePath)
                .then(() => {
                    this.fees = this.fees.filter((n) => {
                        return n.id !== fee.id;
                    });
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        yesOrNo(value) {
            let result = "No";

            if (value) {
                result = "Yes";
            }

            return result;
        },
        inceptionOrCapCost(value) {
            let result = "Cap Cost";

            if (value === null) {
                result = "Not Applicable";
            }

            if (value) {
                result = "Inception Fee";
            }

            return result;
        },
    },
};
</script>

<style scoped></style>
