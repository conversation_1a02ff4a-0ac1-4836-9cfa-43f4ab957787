<template>
    <v-card :loading="dealerLoader.isLoading" class="fill-height mb-4 d-flex flex-column">
        <v-card-title>{{ title }}</v-card-title>
        <v-card-text class="text--primary">
            <v-simple-table>
                <template #default>
                    <tbody>
                        <tr>
                            <th>Day</th>
                            <th>Open</th>
                            <th>Close</th>
                        </tr>
                        <tr v-for="operatingPeriod in dealerHours" :key="operatingPeriod.day">
                            <td>{{ operatingPeriod.day | normalize }}</td>
                            <td>{{ operatingPeriod.open }}</td>
                            <td>{{ operatingPeriod.close }}</td>
                        </tr>
                    </tbody>
                </template>
            </v-simple-table>
        </v-card-text>
    </v-card>
</template>

<script>
import { get } from "vuex-pathify";
import capitalize from "lodash/capitalize";

export default {
    name: "HoursOfOperation",
    components: {},
    filters: {
        normalize(day) {
            return capitalize(day);
        },
    },
    computed: {
        dealerLoader: get("dealerStore/selectedDealer@loader"),
        dealerTimeZone: get("dealerStore/<EMAIL>"),
        dealerHours: get("dealerStore/<EMAIL>"),
        title() {
            return `Hours of Operation (${this.dealerTimeZone})`;
        },
    },

    methods: {},
};
</script>
