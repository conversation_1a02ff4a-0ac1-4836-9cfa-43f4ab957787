<template>
    <v-card :loading="dealerLoader.isLoading" class="fill-height mb-4 d-flex flex-column">
        <v-card-title>CRM Emails</v-card-title>
        <v-card-text v-if="emailAddressesLoaded" class="text--primary">
            <v-data-table :headers="tableHeaders" :items="emailAddresses">
                <template #item.status="{ item }">
                    <email-status-icon :status="item.sendGridStatus" />
                </template>
                <template #item.actions="{ item }">
                    <v-btn v-if="hasBounce(item.bounceReason)" small @click="clearBounce(item)"> Clear Bounce </v-btn>
                    <span v-else> -- </span>
                </template>
                <template #no-data> No email addresses have been set up for this dealer. </template>
            </v-data-table>
        </v-card-text>
    </v-card>
</template>

<script>
import { get } from "vuex-pathify";
import api from "Util/api";
import _ from "lodash";
import EmailStatusIcon from "./components/EmailStatusIcon";

export default {
    name: "DealerEmailAddresses",
    components: { EmailStatusIcon },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            emailAddresses: [],
            emailAddressesLoaded: false,
            tableHeaders: [
                {
                    text: "Email Address",
                    sortable: true,
                    value: "email",
                },
                {
                    text: "Content Type",
                    sortable: true,
                    value: "contentType",
                },
                {
                    text: "Status",
                    sortable: true,
                    value: "status",
                },
                {
                    text: "Actions",
                    sortable: false,
                    value: "actions",
                },
            ],
        };
    },
    computed: {
        dealerLoader: get("dealerStore/selectedDealer@loader"),
    },
    mounted() {
        this.fetchDealerEmailAddresses(this.dealerId);
    },
    methods: {
        fetchDealerEmailAddresses(dealerId) {
            api.get(`/dealer/${dealerId}/email-addresses`)
                .then((response) => {
                    this.emailAddresses = response.data;
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.emailAddressesLoaded = true;
                });
        },

        hasBounce(bounceReason) {
            return !_.isNil(bounceReason);
        },

        clearBounce(item) {
            const email = item.email;

            api.delete(`/dealer/${this.dealerId}/emails/${email}/bounce`)
                .then(() => {
                    this.$toast.success(`${email} removed from bounce list`);
                })
                .catch((error) => {
                    console.log(error);
                    this.$toast.error(`Server error for ${email}`);
                });
        },
    },
};
</script>
