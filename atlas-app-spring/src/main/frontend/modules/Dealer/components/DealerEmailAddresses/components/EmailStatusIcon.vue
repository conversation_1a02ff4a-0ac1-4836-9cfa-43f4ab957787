<template>
    <v-tooltip bottom>
        <template #activator="{ on, attrs }">
            <v-icon v-bind="attrs" v-on="on">
                {{ getStatusIndicator().icon }}
            </v-icon>
        </template>
        <span>{{ getStatusIndicator().toolTip }}</span>
    </v-tooltip>
</template>

<script>
import _ from "lodash";

export default {
    name: "EmailStatusIcon",
    props: {
        status: {
            type: String,
            required: false,
            default: null,
        },
    },
    methods: {
        goodEmail(status) {
            switch (status) {
                case "delivered":
                    return true;
                case "open":
                    return true;
                case "click":
                    return true;
                default:
                    return false;
            }
        },

        getStatusIndicator(status) {
            let toolTip = "";
            let icon = "";

            if (this.goodEmail(status)) {
                toolTip = "Good email";
                icon = "mdi-check-circle";
            } else if (_.isNil(status)) {
                toolTip = "No emails sent yet";
                icon = "mdi-help-circle";
            } else {
                toolTip = status;
                icon = "warning";
            }

            return {
                toolTip,
                icon,
            };
        },
    },
};
</script>

<style scoped></style>
