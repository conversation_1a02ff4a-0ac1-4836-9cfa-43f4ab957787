<template>
    <v-form @submit.prevent="doSearch">
        <v-card>
            <v-container>
                <v-row>
                    <v-col cols="12" md="3">
                        <v-text-field
                            v-model="name"
                            label="Name / City / DealerId"
                            outlined
                            dense
                            hide-details
                            append-icon="mdi-account"
                        />
                    </v-col>
                    <v-col cols="12" md="3">
                        <v-text-field
                            v-model="zipCode"
                            label="Zip Code"
                            outlined
                            dense
                            hide-details
                            append-icon="mdi-pound-box-outline"
                        />
                    </v-col>
                    <v-col cols="12" md="3">
                        <v-text-field
                            v-model="distance"
                            label="Distance"
                            outlined
                            dense
                            hide-details
                            type="number"
                            min="0"
                            append-icon="mdi-map-marker"
                        />
                    </v-col>

                    <v-col cols="12" md="3" class="d-flex justify-center align-center">
                        <div class="d-flex flex-row align-center justify-center">
                            <v-btn size="sm" type="submit" color="primary" :disabled="isLoading" class="mr-2">
                                Search
                            </v-btn>
                            <v-btn text size="sm" :disabled="isLoading" @click="clearFilters"> Reset </v-btn>
                        </div>
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
    </v-form>
</template>

<script>
import { sync, call, get } from "vuex-pathify";

export default {
    name: "DealerSearchForm",

    computed: {
        isLoading: get("dealerSearch/<EMAIL>"),
        name: sync("dealerSearch/filters@query"),
        zipCode: sync("dealerSearch/filters@zipCode"),
        distance: sync("dealerSearch/filters@distance"),
    },

    methods: {
        doSearch: call("dealerSearch/doSearch"),
        clearFilters: call("dealerSearch/clearFilters"),
    },
};
</script>
