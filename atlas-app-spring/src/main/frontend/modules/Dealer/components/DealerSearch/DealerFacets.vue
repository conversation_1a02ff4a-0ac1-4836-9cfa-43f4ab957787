<template>
    <div>
        <facet-list-group facet-group-label="Dealership Details" facet-icon="mdi-car-hatchback">
            <facet-checkbox
                facet-label="Dealer Group"
                store="dealerSearch"
                facet-name="dealerGroups"
                filter-name="dealerGroupIds"
            />
            <facet-checkbox facet-label="Brands" store="dealerSearch" facet-name="makes" filter-name="makes" />
            <facet-checkbox
                facet-label="% New Inventory Priced"
                store="dealerSearch"
                facet-name="percentageNewPriced"
                filter-name="percentageNewPriced"
            />
            <facet-checkbox
                facet-label="% Used Inventory Priced"
                store="dealerSearch"
                facet-name="percentageUsedPriced"
                filter-name="percentageUsedPriced"
            />
        </facet-list-group>
        <facet-list-group facet-group-label="Location" facet-icon="mdi-map-marker">
            <facet-checkbox facet-label="DMA" store="dealerSearch" facet-name="dmas" filter-name="dmaCodes" />
            <facet-checkbox facet-label="States" store="dealerSearch" facet-name="states" filter-name="states" />
        </facet-list-group>
        <v-divider></v-divider>
    </div>
</template>

<script>
import FacetListGroup from "Components/Facets/FacetListGroup";
import FacetCheckbox from "Components/Facets/FacetCheckbox";
export default {
    name: "DealerFacets",
    components: { FacetCheckbox, FacetListGroup },
};
</script>
