<template>
    <div class="link-info-container">
        <div class="link-name w-100 d-flex justify-space-between align-center">
            <div>
                <span class="mr-1">{{ link.name }}</span>
                <InfoTooltip size="16"> {{ tooltipText }} </InfoTooltip>
            </div>
            <v-btn x-small outlined color="primary" @click="handleCopy">
                <v-icon left style="font-size: 14px">mdi-content-copy</v-icon>
                Copy Link
            </v-btn>
        </div>

        <a :href="link.url" target="_blank" rel="noopener noreferrer">
            {{ link.url }}
        </a>
    </div>
</template>

<script lang="ts">
import Vue, { PropType } from "vue";
import InfoTooltip from "Components/InfoTooltip.vue";

interface Link {
    name: string;
    url: string;
}

export default Vue.extend({
    name: "DealerLinkInfo",
    components: { InfoTooltip },
    props: {
        link: {
            type: Object as PropType<Link>,
            required: true,
            validator: (value: Link) => {
                return value.hasOwnProperty("name") && value.hasOwnProperty("url");
            },
        },
    },
    computed: {
        tooltipText(): string {
            switch (this.link?.name.toLowerCase()) {
                case "home page":
                    return "This is your live home page for customers.";
                case "pre-qual standalone":
                    return "This is your pre-qualification landing page for customers.";
                case "srp":
                    return "This is your vehicle listings page.";
                case "sell standalone":
                    return "This is your sell landing page for customers.";
                case "trade-in standalone":
                    return "This is your trade-in landing page for customers.";
                case "retention":
                    return "This is your retention workflow page.";
                default:
                    return `This is your ${this.link.name} page for customers.`;
            }
        },
    },
    methods: {
        handleCopy() {
            navigator.clipboard.writeText(this.link?.url);
            (this as any).$toast.success(`Link Copied.`);
        },
    },
});
</script>

<style scoped lang="scss">
.link-info-container {
    padding: 10px 0 10px 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.link-name {
    color: var(--grey-grey-darken-4, #212121);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    letter-spacing: 0.035px;
}

a:link {
    text-decoration: none;
    font-size: 14px;
}
</style>
