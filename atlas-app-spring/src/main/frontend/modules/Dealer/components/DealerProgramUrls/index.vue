<template>
    <v-card :loading="dealerEnrolledProgramLoader.isLoading">
        <v-card-title>Program URLs</v-card-title>

        <div v-if="validPrograms?.length" class="program-card">
            <ProgramCard
                v-for="(program, i) in validPrograms"
                :key="i"
                :program="program"
                :is-last-program="isLastProgram(i)"
            />
        </div>

        <div v-else-if="!dealerEnrolledProgramLoader.isLoading" class="no-program__card">
            Program data is not available at this time.
        </div>
    </v-card>
</template>

<script>
import Vue from "vue";
import { get, call } from "vuex-pathify";
import ProgramCard from "./components/ProgramCard.vue";

export default Vue.extend({
    name: "DealerProgramUrls",
    components: { ProgramCard },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    computed: {
        dealerEnrolledProgramLoader: get("dealerStore/programUrls@loader"),
        featureFlags: get("loggedInUser/featureFlags"),
        dealerEnrolledProgram: get("dealerStore/programUrls@data"),
        validPrograms() {
            // Additional safety check to filter out any null or invalid programs
            return Array.isArray(this.dealerEnrolledProgram)
                ? this.dealerEnrolledProgram.filter(
                      (program) =>
                          program &&
                          typeof program === "object" &&
                          program.programName &&
                          Array.isArray(program.resources)
                  )
                : [];
        },
    },
    mounted() {
        this.fetchDealerProgramURLs(this.dealerId);
    },
    methods: {
        fetchDealerProgramURLs: call("dealerStore/fetchDealerProgramURLs"),
        isLastProgram(index) {
            return index === this.validPrograms.length - 1;
        },
    },
});
</script>

<style scoped lang="scss">
.program-card {
    display: flex;
    flex-direction: column;
    padding: 0 16px 22px;
    gap: 12px;
}

.no-program__card {
    border-radius: 8px;
    display: flex;
    padding: 16px 24px;
    align-self: stretch;
    justify-content: center;
    font-size: 16px;
    color: var(--opacity-text--disabled, rgba(0, 0, 0, 0.37));
}
</style>
