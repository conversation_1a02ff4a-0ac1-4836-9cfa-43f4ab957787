<template>
    <div>
        <div class="program-name">{{ program.programName }} URLs</div>
        <LinkInfo v-for="(link, i) in program.resources" :key="i" :link="link" />
        <v-divider v-if="!isLastProgram" />
    </div>
</template>

<script lang="ts">
import Vue, { PropType } from "vue";
import LinkInfo from "./LinkInfo.vue";

interface ProgramResource {
    name: string;
    url: string;
}

interface Program {
    programName: string;
    resources: ProgramResource[];
}

export default Vue.extend({
    name: "ProgramCard",
    components: { LinkInfo },
    props: {
        program: {
            type: Object as PropType<Program>,
            required: true,
            validator: (value: Program) => {
                return value.hasOwnProperty("programName") && value.hasOwnProperty("resources");
            },
        },
        isLastProgram: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
});
</script>

<style scoped lang="scss">
.program-name {
    color: var(--grey-grey-darken-3, #424242);
    font-size: 16px;
    font-weight: 600;
    line-height: 1.5;
}
</style>
