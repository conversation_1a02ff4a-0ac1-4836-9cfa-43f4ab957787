<template>
    <v-card :is-loading="isLoading">
        <v-card-title>Lenders</v-card-title>
        <v-card-text>
            <v-data-table :headers="tableHeaders" :items="items" item-key="id" class="pt-2 rounded-0">
                <template #item.enabled="{ item }">
                    <boolean-indicator :value="item.enabled" />
                </template>
            </v-data-table>
        </v-card-text>
    </v-card>
</template>

<script>
import api from "Util/api";
import BooleanIndicator from "Components/BooleanIndicator";

export default {
    name: "DealerLenders",
    components: { BooleanIndicator },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            items: [],
            isLoading: false,
            tableHeaders: [
                {
                    text: "Name",
                    sortable: true,
                    value: "name",
                    align: "left",
                },
                {
                    text: "Enabled",
                    sortable: true,
                    value: "enabled",
                    align: "left",
                },
                {
                    text: "Route One Id",
                    sortable: true,
                    value: "routeOneId",
                },
                {
                    text: "New Max Lender Fee",
                    sortable: true,
                    value: "newMaxLenderFee",
                    align: "left",
                },
                {
                    text: "Used Max Lender Fee",
                    sortable: true,
                    value: "usedMaxLenderFee",
                },
            ],
        };
    },
    created() {},
    mounted() {
        this.fetchLenders();
    },
    methods: {
        fetchLenders() {
            this.isLoading = true;
            api.get(`/dealer/${this.dealerId}/dealer-lenders`)
                .then((response) => {
                    this.items = response.data;
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>

<style scoped></style>
