<template>
    <search-page :key="programIds">
        <dealer-search-form slot="searchForm" />
        <dealers-list slot="searchList" :store="store" />
        <dealer-facets slot="searchFacets" />
    </search-page>
</template>
<script>
import { call, sync } from "vuex-pathify";
import DealerSearchForm from "../components/DealerSearch/DealerSearchForm";
import DealersList from "../components/DealerSearch/DealersList";
import SearchPage from "Components/Search";
import DealerFacets from "@/modules/Dealer/components/DealerSearch/DealerFacets";

export default {
    components: {
        DealerFacets,
        SearchPage,
        DealersList,
        DealerSearchForm,
    },
    data: () => ({
        store: "dealerSearch",
    }),

    computed: {
        programIds() {
            return this.$route.query.programIds === "" ? null : this.$route.query.programIds;
        },
        filterProgramIds: sync("dealerSearch/filters@programIds"),
        filterSearchMethods: sync("dealerSearch/searchMethods"),
        dealerIds: sync("dealerSearch/filters@dealerIds"),
    },
    watch: {
        programIds(value) {
            this.filterSearchMethods = { ...this.filterSearchMethods, programIds: "POSITIVE" };
            this.filterProgramIds = value;
        },
    },
    created() {
        this.dealerIds = null;
        this.setSearchUri(`/dealers`);
    },
    methods: {
        setSearchUri: call("dealerSearch/setSearchUri"),
    },
};
</script>
