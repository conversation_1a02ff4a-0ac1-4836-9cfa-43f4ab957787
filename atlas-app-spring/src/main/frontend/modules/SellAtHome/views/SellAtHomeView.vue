<template>
    <v-form ref="allCardsForm" lazy-validation class="container sell-page-container container--fluid">
        <v-card class="fill-height" style="max-width: 850px">
            <v-card-title>Sell Your Car Snapshot</v-card-title>
            <div class="ml-6 pb-3 mt-3 numberOfVehiclesText">
                <div class="mb-2">
                    # of Sell Your Car Vehicles:
                    <div style="margin-left: 25px" class="d-inline-flex">{{ numberOfSellAtHomeVehicles }}</div>
                </div>
                <v-divider class="hr"></v-divider>
                <div class="mb-2 mt-2">
                    Offers Expiring Soon:
                    <div style="margin-left: 60px" class="d-inline-flex">{{ offersExpiringSoon }}</div>
                </div>
            </div>
        </v-card>
        <v-card class="mt-3">
            <v-card-title>
                <span class="mr-2">Sell Your Car Vehicles</span>
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" medium class="close-rate-icon ml-2" v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    These are vehicles the customer is interested in selling.
                </v-tooltip>
            </v-card-title>
            <v-data-table
                :loading="loading"
                :items-per-page="5"
                :headers="headers"
                :sort-by="sortBy"
                :sort-desc="sortDesc"
                :footer-props="{
                    'items-per-page-options': [5, 10, 20, 30, 40, 50],
                }"
                :items="vehicleInfo"
                no-data-text="No trades found for this dealer"
                class="elevation-1"
            >
                <template #item.actions="{ item }">
                    <span class="text-no-wrap">
                        <v-btn outlined color="primary" x-small @click="viewTradeDetails(item.userVehicleId)"
                            >View</v-btn
                        >
                    </span>
                </template>
                <template #item.status="{ item }">
                    <v-chip v-if="item.status === 'approved'" class="white--text" small color="green">Approved</v-chip>
                    <v-chip v-if="item.status === 'expired'" class="white--text" small color="red">Expired</v-chip>
                    <v-chip v-if="item.status === 'pending'" class="white--text" small color="orange">Pending</v-chip>
                </template>
                <template #item.customer="{ item }">
                    <a @click="goToUserDetails(item.userId, item.dealerId)">
                        {{ item.fullName }}
                    </a>
                </template>
                <template #item.condition="{ item }">
                    <v-chip v-if="item.condition" small grey>{{ item.condition }}</v-chip>
                </template>
                <template #item.payment="{ item }">
                    {{ item.payment | numeral("$0,00.00") }}
                </template>
                <template #item.vehicleInfo="{ item }">
                    {{ item.vehicleInfo.year }} {{ item.vehicleInfo.make }} {{ item.vehicleInfo.model }}
                </template>
                <template #item.expirationDate="{ item }">
                    <v-chip v-if="item.expired === true && item.expirationDate !== null" small color="red" dark>
                        Expired on {{ item.expirationDate | formatDate }}
                    </v-chip>
                    <v-chip v-if="item.expired !== true && item.expirationDate !== null" small color="green" dark>
                        Expires on {{ item.expirationDate | formatDate }}
                    </v-chip>
                </template>
            </v-data-table>
        </v-card>
        <v-divider></v-divider>
        <v-card class="mt-3">
            <v-card-text class="grey--text italic font-weight-bold">
                By activating Sell Your Car, {{ dealerName }} assumes full responsibility for any Cash Offers that are
                extended to customers. CarSaver will not assume any responsibility for the Cash Offer. Cash Offers shall
                be finalized after a physical inspection of the vehicle. {{ dealerName }} shall honor any final Cash
                Offers made following such inspection.
            </v-card-text>
        </v-card>

        <v-navigation-drawer
            v-model="tradeDrawerShowing"
            fixed
            temporary
            right
            :width="$vuetify.breakpoint.xs ? '80%' : '60%'"
        >
            <template #prepend>
                <div class="float-right py-4 pr-4">
                    <v-btn icon outlined small @click.stop="closeTradeDrawer">
                        <v-icon>mdi-chevron-right</v-icon>
                    </v-btn>
                </div>
            </template>
            <trade-preview
                v-if="userVehicleId"
                :is-sell-at-home="true"
                :trade-id="userVehicleId"
                :dealer-id="dealerIds"
            />
        </v-navigation-drawer>
    </v-form>
</template>
<script>
import { get } from "vuex-pathify";
import api from "Util/api";
import _ from "lodash";
import TradePreview from "@/modules/Customers/components/UserDetails/TradePreview";
import moment from "moment";

export default {
    name: "SellAtHomeView",
    components: {
        TradePreview,
    },
    filters: {
        formatDate(val) {
            const formattedDate = val ? moment(val).format("MM/DD/YYYY") : "";
            return formattedDate;
        },
    },
    data() {
        return {
            programId: null,
            userVehicleId: "",
            numberOfSellAtHomeVehicles: 32,
            offersExpiringSoon: 2,
            dealerLink: "",
            loading: false,
            tradeDrawerShowing: false,
            copyConfirmationMessage: "Website has been copied to clipboard",
            snackbar: false,
            itemsPerPage: "",
            timeout: 2500,
            vehicleInfo: [],
            headers: [
                { text: "Actions", value: "actions", sortable: false },
                {
                    text: "Customers",
                    align: "start",
                    value: "customer",
                    width: 200,
                    sortable: false,
                },
                { text: "Vehicle", align: "start", value: "vehicleInfo", width: 200, sortable: false },
                { text: "Status", value: "status", sortable: false },
                { text: "Expiration", align: "center", value: "expirationDate", width: 200, sortable: true },
                { text: "Condition", align: "end", value: "vehicleCondition", sortable: false },
                { text: "Color", align: "end", value: "color", sortable: false },
                { text: "Miles", align: "end", value: "miles", sortable: false },
                // TODO: Most likely there won't be payments or FinanceCo. leaving the code for now.
                // { text: "Payment", align: "end", value: "payment" },
                // { text: "Finance Co.", align: "end", value: "financeCo" },
            ],
            sortBy: "expirationDate", // Default sorting column
            sortDesc: true, // Default sorting order
        };
    },
    computed: {
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        selectedDealer: get("loggedInUser/selectedDealer"),
        dealerName() {
            const locatedDealer = _.find(this.userDealerAccessList, ["id", this.dealerIds]);
            const result = !_.isNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
    },
    watch: {
        tradeDrawerShowing(oldVal, newVal) {
            if (newVal) {
                this.userVehicleId = null;
            }
        },
        selectedDealer(val) {
            if (this.selectedDealer.id !== this.dealerIds) {
                window.location = `/sell-at-home?dealerIds=${val.id}`;
            }
        },
    },
    created() {
        this.fetchUserVehicleCount(this.dealerIds);
        this.fetchSellAtHomeVehicles(this.dealerIds);
    },

    methods: {
        viewTradeDetails(tradeId) {
            this.userVehicleId = tradeId;
            this.tradeDrawerShowing = true;
        },
        closeTradeDrawer() {
            this.tradeDrawerShowing = false;
        },
        fetchSellAtHomeVehicles(dealerId) {
            this.loading = true;
            let params = {};
            if (!_.isNil(dealerId)) {
                params["dealerIds"] = dealerId;
                params.page = "&page=0&size=10&sort=createdDate,ASC";
            }
            api.get(`/dealer/user-vehicle-sell-info/`, params)
                .then((response) => {
                    this.vehicleInfo = _.get(response, "data.content", []);
                    this.programId = _.get(response, "data.programId", null);
                    _.map(this.vehicleInfo, (item) => {
                        item.fullName = item.firstName + " " + item.lastName;
                    });
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        fetchUserVehicleCount(dealerId) {
            let params = {};
            if (!_.isNil(dealerId)) {
                params["dealerIds"] = dealerId;
            }
            api.get(`/dealer/user-vehicle-sell-info/count`, params)
                .then((response) => {
                    this.numberOfSellAtHomeVehicles = response.data.totalCount;
                    this.offersExpiringSoon = response.data.nearingExpirationCount;
                    this.dealerLink = response.data.dealerLink;
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        copyToClipBoard() {
            const textToCopy = this.dealerLink;

            try {
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(textToCopy);
                } else {
                    const textArea = document.createElement("textarea");
                    textArea.value = textToCopy;
                    textArea.style.position = "fixed";
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand("copy");
                    document.body.removeChild(textArea);
                }
            } catch (error) {
                console.error("Copy failed:", error);
                return false;
            } finally {
                this.snackbar = true;
            }
        },
        goToUserDetails(userId, dealerId) {
            this.$router.push({
                name: "customer",
                path: `/customers/${userId}`,
                query: {
                    dealerIds: dealerId,
                    selecteduserProgramId: this.programId,
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.sell-page-container {
    background-color: $gray-200;
}
.v-main__wrap {
    flex: 1 1 auto;
    max-width: 100%;
    position: relative;
    background-color: $gray-200;
}
.hr {
    max-width: 90%;
}
.numberOfVehiclesText {
    color: dimgrey;
}
</style>
