<template>
    <v-card v-if="show" border="top" colored-border type="info" elevation="2" class="mb-0 mt-2">
        <v-card-text>
            <div><strong>How would you like to manage this opportunity?</strong></div>
        </v-card-text>
        <v-card-actions>
            <v-btn color="primary" :href="callCustomerLink" class="ml-12">Call Customer</v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
export default {
    name: "RouteOneActions",
    props: {
        lead: {
            type: Object,
            required: true,
        },
        loading: {
            type: Boolean,
            required: true,
        },
    },
    computed: {
        show() {
            return !this.loading;
        },
        callCustomerLink() {
            return `tel:${this.lead.customer.phoneNumber}`;
        },
    },
};
</script>

<style scoped></style>
