<template>
    <tr v-if="display">
        <td>{{ label }}</td>
        <td>
            <slot></slot>
        </td>
    </tr>
</template>

<script>
export default {
    name: "LeadPreviewRow",
    props: {
        label: {
            type: String,
            required: true,
        },

        display: {
            type: Boolean,
            required: true,
        },
    },
};
</script>

<style scoped></style>
