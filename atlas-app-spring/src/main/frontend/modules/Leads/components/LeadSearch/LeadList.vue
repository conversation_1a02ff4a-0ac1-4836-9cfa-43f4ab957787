<template>
    <v-card>
        <v-toolbar flat>
            <v-toolbar-title class="grey--text">Leads</v-toolbar-title>

            <v-spacer />

            <table-column-selector id="table-column-config" v-model="displayFields" :fields="headers" />
            <export-column-selector
                id="table-column-export"
                v-model="exportFields"
                :display-fields="displayFields"
                :fields="headers"
                @doExport="prepareExport"
            />
        </v-toolbar>

        <v-divider />

        <filter-chips :store="store" />

        <table-search-count-label :page-number="pageNumber" :page-size="pageSize" :total-elements="totalElements" />

        <v-divider />

        <v-data-table
            :loading="isLoading"
            :headers="fieldsForDisplay"
            :items="searchResults"
            :server-items-length="totalElements"
            :sort-by="sortBy"
            :sort-desc="sortDesc"
            hide-default-footer
            @update:options="updateOptions"
        >
            <template #header.status>
                Lead Status
                <info-tooltip size="18" :max-width="250">
                    Leads go through different stages through out the buying process.
                    <br />
                    Hover over the
                    <v-chip x-small>
                        <span class="mr-1">STATUS</span>
                        <v-icon x-small>mdi-information-outline</v-icon>
                    </v-chip>
                    to view the description.
                </info-tooltip>
            </template>
            <template #item.user="{ item }">
                <router-link v-if="item.userDetailsLink" :to="item.userDetailsLink">
                    {{ item.userFullName }}
                </router-link>
            </template>
            <template #item.status="{ item }">
                <status-tooltip :status="item.status" />
            </template>
            <template #item.visitTime="{ item }">
                <span>{{ convertTime(item.visitTime, item.dealer.timeZone) }}</span>
            </template>
            <template #item.vehicle.certified="{ item }">
                <div class="d-flex align-center justify-center" style="width: 100%">
                    <boolean-indicator :value="item.vehicleCertified" />
                </div>
            </template>
            <template #item.vehicle.inTransit="{ item }">
                <boolean-indicator :value="item.vehicleInTransit" />
            </template>
            <template #item.dealerLink.assignee="{ item }">
                {{ item.assigneeFullName }}
            </template>
            <template #item.createdDate="{ item }">
                <span>{{ item.createdDate | formatEpochDate }}</span>
            </template>
            <template #item.actions="{ item }">
                <v-btn
                    :disabled="!hasLeadDetailsAccess(item)"
                    x-small
                    outlined
                    color="primary"
                    :to="{ path: `/leads/${item.id}`, query: { dealerIds: dealerIds } }"
                >
                    View Details
                </v-btn>
            </template>
        </v-data-table>

        <v-container class="pt-2">
            <v-row>
                <v-col cols="12">
                    <v-pagination :value="page" :total-visible="10" :length="totalPages" @input="changePage" />
                </v-col>
            </v-row>
        </v-container>
    </v-card>
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import TableColumnSelector from "Components/TableColumnSelector";
import ExportColumnSelector from "Components/ExportColumnSelector";
import TableSearchCountLabel from "Components/TableSearchCountLabel";
import tableUtils from "@/util/tableUtils";
import FilterChips from "Components/Search/FilterChips";
import BooleanIndicator from "Components/BooleanIndicator";
import StatusTooltip from "@/modules/Leads/components/LeadSearch/StatusTooltip";
import InfoTooltip from "Components/InfoTooltip";
import _ from "lodash";
import moment from "moment-timezone";

export default {
    name: "LeadList",
    components: {
        InfoTooltip,
        StatusTooltip,
        BooleanIndicator,
        FilterChips,
        TableColumnSelector,
        ExportColumnSelector,
        TableSearchCountLabel,
    },
    props: {
        dealerIds: {
            type: String,
            required: true,
        },
        store: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                {
                    value: "user",
                    text: "Name",
                    sortable: false,
                    width: 125,
                },
                {
                    value: "leadType",
                    text: "Lead Type",
                    width: 125,
                },
                {
                    value: "docType",
                    text: "Doc Type",
                    sortable: false,
                    exportable: true,
                    width: 125,
                },
                {
                    value: "source.utmSource",
                    text: "Source",
                    sortable: false,
                },
                {
                    value: "status",
                    text: "Lead Status",
                    width: 125,
                },
                {
                    value: "visitTime",
                    text: "Visit Time",
                    sortable: true,
                    width: 210,
                },
                {
                    value: "certificate.orderId",
                    text: "Order ID",
                    width: 125,
                },
                {
                    value: "certificate.id",
                    text: "Certificate ID",
                    width: 125,
                },
                {
                    value: "vehicle.vin",
                    text: "VIN",
                    width: 125,
                    sortable: false,
                },
                {
                    value: "vehicle.certified",
                    text: "Certified Vehicle",
                    width: 140,
                    sortable: false,
                },
                {
                    value: "vehicle.stockNumber",
                    text: "Stock #",
                    width: 125,
                    sortable: false,
                },
                {
                    value: "vehicle.fullName",
                    text: "Year Make Model",
                    exportable: true,
                    sortable: false,
                    width: 180,
                },
                {
                    value: "vehicle.inTransit",
                    text: "In Transit",
                    width: 140,
                    sortable: false,
                },
                {
                    value: "dealerLink.assignee",
                    text: "Worked By",
                    width: 125,
                    sortable: false,
                },
                {
                    value: "createdDate",
                    text: "Created Date",
                    sortable: true,
                    width: 210,
                },
                {
                    value: "actions",
                    text: "Actions",
                    sortable: false,
                    exportable: false,
                    width: 125,
                },
            ],
        };
    },

    computed: {
        apiSearchResults: get("leadSearch/searchLoader@data"),
        pageNumber: get("leadSearch/pageMetadata@number"),
        pageSize: get("leadSearch/pageMetadata@size"),
        totalPages: get("leadSearch/pageMetadata@totalPages"),
        totalElements: get("leadSearch/pageMetadata@totalElements"),
        isLoading: get("leadSearch/<EMAIL>"),
        page: sync("leadSearch/pageable@page"),
        sort: sync("leadSearch/pageable@sort"),
        displayFields: sync("leadSearch/displayFields"),
        exportFields: sync("leadSearch/exportFields"),
        exportLabels: sync("leadSearch/exportLabels"),
        sortBy: get("leadSearch/getSortBy"),
        sortDesc: get("leadSearch/getSortDesc"),
        filterDealerIds: sync("leadSearch/filters@dealerIds"),
        fieldsForDisplay() {
            return tableUtils.intersectFieldsForDisplay(this.headers, this.displayFields);
        },
        searchResults() {
            return _.map(this.apiSearchResults, (searchResult) => {
                const item = { ...searchResult };
                item.userFullName = _.get(item, "user.firstName", "") + " " + _.get(item, "user.lastName", "");
                const userId = _.get(item, "user.id", null);

                if (this.isNotProspect(item.docType)) {
                    item.userDetailsLink = userId
                        ? { path: `/customers/${userId}`, query: { dealerIds: this.dealerIds } }
                        : null;
                } else {
                    item.userDetailsLink = { path: `/prospects/${item.user.id}`, query: { dealerIds: this.dealerIds } };
                }

                item.vehicleCertified = _.get(item, "vehicle.certified", false);
                item.vehicleInTransit = _.get(item, "vehicle.inTransit", false);
                item.assigneeFullName =
                    _.get(item, "dealerLink.assignee.firstName", "") +
                    " " +
                    _.get(item, "dealerLink.assignee.lastName", "");
                return item;
            });
        },
    },
    mounted() {
        this.filterDealerIds = this.dealerIds;
        this.doPageLoad();
        this.loadFilterInfo();
    },

    methods: {
        doPageLoad: call("leadSearch/doPageLoad"),
        loadFilterInfo: call("leadSearch/loadFilterInfo"),
        doSort: call("leadSearch/doSort"),
        changePage: call("leadSearch/changePage"),
        updateSort: call("leadSearch/updateSort"),
        doExport: call("leadSearch/doExport"),
        prepareExport() {
            this.exportLabels.length = 0;
            for (let field of this.exportFields) {
                let header = _.find(this.headers, function (o) {
                    return _.toString(o.value) === _.toString(field);
                });
                if (header === undefined) {
                    console.error("Error setting label for export, FIELD: ", field);
                    return;
                }
                this.exportLabels = [...this.exportLabels, header.text];
            }
            this.doExport();
        },
        updateOptions(options) {
            const newSortBy = _.get(options, "sortBy[0]") || "";
            const newSortDesc = _.get(options, "sortDesc[0]") || false;

            if (newSortBy === this.sortBy && newSortBy === "") {
                return;
            }

            if (newSortBy !== this.sortBy || newSortDesc !== this.sortDesc) {
                if (newSortBy === "" || newSortDesc === "") {
                    this.updateSort("");
                } else {
                    const direction = newSortDesc ? "desc" : "asc";
                    this.updateSort(`${newSortBy},${direction}`);
                }
            }
        },
        //ATS-175
        hasLeadDetailsAccess(item) {
            return (
                this.isNotProspect(item.docType) &&
                (this.$acl.hasDealerPermission(this.dealerIds, "customer:read") || this.$acl.hasAuthority("read:user"))
            );
        },
        isNotProspect(docType) {
            const typeStr = typeof docType === "string" ? docType.toUpperCase() : null;
            const prospectStr = "PROSPECT";

            if (typeStr === null) {
                console.trace("Error: docType expected type String", docType);
                return true;
            }

            return docType !== prospectStr;
        },
        convertTime(dateTimeString, timeZone) {
            if (!dateTimeString || !timeZone) {
                return "";
            }
            const formattedTime = moment.tz(dateTimeString, timeZone).format("M/D/YYYY hh:mm a z");

            return formattedTime;
        },
    },
};
</script>
