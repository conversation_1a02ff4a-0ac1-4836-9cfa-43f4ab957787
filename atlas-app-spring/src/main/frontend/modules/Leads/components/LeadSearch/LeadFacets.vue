<template>
    <div>
        <facet-list-group facet-group-label="Lead" facet-icon="mdi-email-search-outline">
            <facet-checkbox facet-label="Type" store="leadSearch" facet-name="types" filter-name="types" />
            <facet-checkbox facet-label="Category" store="leadSearch" facet-name="leadTypes" filter-name="leadTypes" />
            <facet-checkbox
                facet-label="Stock Type"
                store="leadSearch"
                facet-name="stockTypes"
                filter-name="stockTypes"
            />
            <facet-radio
                facet-label="Certified Vehicle"
                store="leadSearch"
                facet-name="vehicleCertified"
                filter-name="vehicleCertified"
            />

            <facet-checkbox facet-label="Make" store="leadSearch" facet-name="makes" filter-name="makes" />
        </facet-list-group>
        <v-divider></v-divider>

        <facet-list-group facet-group-label="Customer" facet-icon="mdi-account-box-outline">
            <facet-checkbox facet-label="Cities" store="leadSearch" facet-name="userCities" filter-name="userCities" />
            <facet-checkbox facet-label="States" store="leadSearch" facet-name="userStates" filter-name="userStates" />
            <facet-checkbox facet-label="DMA" store="leadSearch" facet-name="userDmas" filter-name="userDmaCodes" />
        </facet-list-group>
        <v-divider></v-divider>
    </div>
</template>

<script>
import FacetListGroup from "Components/Facets/FacetListGroup";
import FacetRadio from "Components/Facets/FacetRadio";
import FacetCheckbox from "Components/Facets/FacetCheckbox";
export default {
    name: "LeadFacets",
    components: { FacetCheckbox, FacetRadio, FacetListGroup },
};
</script>
