<template>
    <v-container fluid class="lead-claim-container">
        <v-row>
            <v-col cols="12">
                <v-toolbar flat>
                    <v-toolbar-title>You have a new opportunity</v-toolbar-title>
                </v-toolbar>
            </v-col>
        </v-row>
        <action-needed v-if="appliedPreApprovalIsValid"></action-needed>
        <v-row>
            <v-col cols="12" md="6">
                <v-card v-if="customer" class="fill-height mb-4 d-flex flex-column">
                    <v-card-title>{{ customer.name }}</v-card-title>
                    <v-card-text>
                        <v-list>
                            <v-list-item v-for="(item, i) in checklist" :key="i">
                                <v-list-item-icon>
                                    <v-icon :color="item.icon.color" v-text="item.icon.name" />
                                </v-list-item-icon>
                                <v-list-item-content>
                                    <v-list-item-title v-text="item.message"></v-list-item-title>
                                </v-list-item-content>
                            </v-list-item>
                        </v-list>
                    </v-card-text>
                </v-card>
            </v-col>
            <v-col cols="12" md="6">
                <v-card v-if="vehicle" class="fill-height mb-4 d-flex flex-column">
                    <v-card-title>{{ vehicleCardTitle }}</v-card-title>
                    <v-card-text>
                        <v-simple-table>
                            <tbody>
                                <tr>
                                    <td>
                                        <strong>VIN:</strong>
                                    </td>
                                    <td>
                                        {{ vehicle.vin }}
                                    </td>
                                </tr>
                                <tr v-if="vehicle.inTransit">
                                    <td>
                                        <strong>In Transit Vehicle</strong>
                                    </td>
                                    <td>
                                        <v-chip color="green" text-color="white">True</v-chip>
                                    </td>
                                </tr>
                                <tr v-else>
                                    <td>
                                        <strong>Stock #:</strong>
                                    </td>
                                    <td>{{ vehicle.stockNumber }}</td>
                                </tr>
                            </tbody>
                        </v-simple-table>
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
        <v-row v-if="!loading && !isUnAcceptableLead">
            <v-col cols="12" md="6">
                <v-btn
                    v-if="!isClaimed"
                    x-large
                    block
                    dark
                    color="primary"
                    class="mb-2"
                    @click.stop="confirmOffTodayDialog = true"
                >
                    <v-icon left> mdi-clock-time-nine-outline </v-icon>
                    I'm Off Today
                </v-btn>
            </v-col>
            <v-col cols="12" :md="isClaimed ? 12 : 6">
                <v-btn
                    v-if="!isClaimed"
                    block
                    x-large
                    color="success"
                    class="mb-2"
                    :loading="isClaiming"
                    @click="claimLead(leadId)"
                >
                    <v-icon left> mdi-check </v-icon>
                    Accept
                </v-btn>
                <v-alert v-else type="info" show outlined> Claimed by {{ lead.assignee.name }} </v-alert>
            </v-col>
        </v-row>
        <v-row v-if="isUnAcceptableLead">
            <v-col cols="12">
                <v-alert type="error" show outlined> Problem loading deal information </v-alert>
            </v-col>
        </v-row>
        <v-dialog v-if="!loading" v-model="confirmOffTodayDialog" max-width="500px">
            <!-- activator slot contains the button or whatever is launching the dialog -->
            <template #default>
                <v-card>
                    <v-card-title dark class="title secondary text-center white--text">Are You Sure?</v-card-title>
                    <v-card-text class="text--primary mt-2">
                        Clicking "Confirm" will disable Appointment and Connection Alerts for the rest of the day.
                    </v-card-text>
                    <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn color="blue darken-1" text @click.stop="confirmOffTodayDialog = false"> Cancel </v-btn>
                        <v-btn color="blue darken-1" text @click="confirmOffToday"> Confirm </v-btn>
                    </v-card-actions>
                </v-card>
            </template>
        </v-dialog>
    </v-container>
</template>

<script>
import api from "Util/api";
import _ from "lodash";
import moment from "moment";
import ActionNeeded from "@/modules/Leads/components/ActionNeeded";

export default {
    name: "LeadClaimHome",
    components: { ActionNeeded },
    props: {
        leadId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            lead: {},
            loading: true,
            isClaiming: false,
            confirmOffTodayDialog: false,
            checklistItems: [],
        };
    },
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        customer() {
            return _.get(this.lead, "customer", null);
        },
        deal() {
            return _.get(this.lead, "deal", null);
        },
        vehicle() {
            return _.get(this.lead, "vehicle", null);
        },
        creditStatus() {
            return _.get(this.lead, "deal.dealCreditStatus", null);
        },
        appliedPreApprovalIsValid() {
            return _.get(this.lead, "deal.preApproval.valid", false);
        },
        appliedPreApprovalNumber() {
            return _.get(this.lead, "deal.preApproval.approvalNumber", null);
        },
        orderStatus() {
            return _.get(this.lead, "orderStatus", null);
        },
        tradeExists() {
            return _.get(this.lead, "tradeExists", false);
        },
        trade() {
            return _.get(this.lead, "trade", null);
        },
        isClaimed() {
            const assignee = _.get(this.lead, "assignee", null);
            return !_.isNil(assignee);
        },
        tradeVehicleQuoteId() {
            return _.get(this.trade, "vehicleQuoteId", null);
        },
        isLeaseReturn() {
            return _.get(this.trade, "purchaseType") === "LEASE";
        },
        isLeaseInspectionScheduled() {
            return _.get(this.trade, "leaseInspectionScheduled", false);
        },
        signingOption() {
            const signingOption = _.get(this.orderStatus, "signingOption");
            if (signingOption === "SIGN_ONLINE") {
                return "online e-Contracting";
            } else {
                return "at Dealership";
            }
        },
        vehicleCardTitle() {
            const stockType = _.get(this.vehicle, "stockType", "");
            const year = _.get(this.vehicle, "year", "");
            const make = _.get(this.vehicle, "make", "");
            const model = _.get(this.vehicle, "model", "");

            return `${_.capitalize(stockType)} ${year} ${make} ${model}`;
        },
        isUnAcceptableLead() {
            return !this.loading && (!this.vehicle || !this.customer);
        },
        checklist() {
            let checklistItems = [];
            const contractRequested = _.get(this.orderStatus, "contractRequested", false);
            const preApproved = _.get(this.creditStatus, "preApproved", false);
            const preQualified = _.get(this.creditStatus, "preQualified", false);
            const isSelfSelectedCredit = _.get(this.creditStatus, "selfSelectedCredit", false);
            const appliedPreApprovalIsValid = this.appliedPreApprovalIsValid;

            this.checkIfContractRequested(contractRequested, checklistItems);

            this.checkCreditStatus(
                isSelfSelectedCredit,
                checklistItems,
                preApproved,
                appliedPreApprovalIsValid,
                preQualified
            );

            this.checkIfTradeExist(checklistItems);

            return checklistItems;
        },
    },
    watch: {
        dealerIds(value) {
            this.$router.push({
                path: "/leads",
                params: { dealerIds: value },
                query: { dealerIds: value },
            });
        },
    },
    mounted() {
        this.fetchLead(this.leadId);
    },
    methods: {
        fetchLead(leadId) {
            this.loading = true;

            api.get(`/leads/${leadId}`)
                .then((response) => {
                    this.lead = response.data;
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },

        claimLead(leadId) {
            this.isClaiming = true;

            api.post(`/leads/${leadId}/claim`)
                .then(() => {
                    this.$router.push({
                        name: "LeadPreviewHome",
                        params: { leadId: leadId },
                        query: { dealerIds: dealerIds },
                    });
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.isClaiming = false;
                });
        },

        successChecklistItem(message) {
            const icon = "mdi-check-circle";

            return this.createChecklistItem(message, icon, "green");
        },
        actionRequiredChecklistItem(message) {
            const icon = "mdi-close-circle";

            return this.createChecklistItem(message, icon, "red");
        },
        infoChecklistItem(message) {
            const icon = "mdi-information";

            return this.createChecklistItem(message, icon, "blue");
        },
        createChecklistItem(message, iconName, iconColor) {
            return {
                message,
                icon: {
                    name: iconName,
                    color: iconColor,
                },
            };
        },
        confirmOffToday() {
            this.loading = true;
            api.get(`/leads/${this.leadId}/disable-sms-today`)
                .then((response) => {
                    const formatter = moment(response.data.disabledUntilDate);
                    const disabledUntilDate = formatter.format("MMMM Do, YYYY");
                    this.confirmOffTodayDialog = false;
                    this.$router.push({
                        name: "LeadSMSDisabled",
                        params: { disabledUntilDate: disabledUntilDate, leadId: this.leadId },
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        checkIfContractRequested(contractRequested, checklistItems) {
            if (contractRequested) {
                checklistItems.push(this.successChecklistItem(`Requested Contracts - ${this.signingOption}`));
            }
        },
        checkCreditStatus(isSelfSelectedCredit, checklistItems, preApproved, appliedPreApprovalIsValid, preQualified) {
            if (isSelfSelectedCredit) {
                checklistItems.push(this.infoChecklistItem("Customer Not Pre-qualified"));
                checklistItems.push(this.infoChecklistItem("Customer Self Selected Credit"));
            } else {
                this.checkPreApprovedOrPrequalified(
                    preApproved,
                    appliedPreApprovalIsValid,
                    checklistItems,
                    preQualified
                );
            }
        },
        checkPreApprovedOrPrequalified: function (
            preApproved,
            appliedPreApprovalIsValid,
            checklistItems,
            preQualified
        ) {
            if (preApproved || appliedPreApprovalIsValid) {
                checklistItems.push(this.successChecklistItem(`Pre-Approved with ${this.deal.financierName}`));
            } else if (preQualified) {
                checklistItems.push(this.successChecklistItem("Pre-qualified Customer"));
            }

            if (this.appliedPreApprovalNumber != null) {
                checklistItems.push(this.successChecklistItem(`Pre-Approval Number: ${this.appliedPreApprovalNumber}`));
            }
        },
        checkIfTradeExist(checklistItems) {
            if (this.tradeExists) {
                this.isLeaseReturnOrTradeOffer(checklistItems);
            }
        },
        isLeaseReturnOrTradeOffer(checklistItems) {
            if (this.isLeaseReturn) {
                checklistItems.push(this.successChecklistItem("Turning in current lease"));
                this.checkLeaseInspection(checklistItems);
            } else if (this.tradeVehicleQuoteId) {
                checklistItems.push(this.successChecklistItem("Guaranteed Trade Value"));
            } else {
                checklistItems.push(this.successChecklistItem("Trade (Quote not provided)"));
            }
        },
        checkLeaseInspection(checklistItems) {
            if (this.isLeaseInspectionScheduled) {
                checklistItems.push(this.successChecklistItem("Lease inspection scheduled"));
            } else {
                checklistItems.push(this.actionRequiredChecklistItem("Lease inspection needed"));
            }
        },
    },
};
</script>

<style scoped lang="scss">
.lead-claim-container {
    background-color: $gray-200;
}
</style>
