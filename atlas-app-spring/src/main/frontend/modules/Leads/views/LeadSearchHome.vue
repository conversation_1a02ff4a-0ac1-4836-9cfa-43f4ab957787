<template>
    <search-page :key="dealerIds">
        <lead-search-form slot="searchForm" />
        <lead-list :key="dealerIds" slot="searchList" :store="store" :dealer-ids="dealerIds" />
        <lead-facets :key="dealerIds" slot="searchFacets" />
    </search-page>
</template>
<script>
import { call } from "vuex-pathify";
import LeadSearchForm from "Modules/Leads/components/LeadSearch/LeadSearchForm";
import LeadList from "Modules/Leads/components/LeadSearch/LeadList";
import SearchPage from "Components/Search";
import LeadFacets from "@/modules/Leads/components/LeadSearch/LeadFacets";

export default {
    name: "LeadSearchHome",
    components: { LeadFacets, SearchPage, LeadList, LeadSearchForm },
    data: () => ({
        store: "leadSearch",
    }),
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
    },
    created() {
        this.setSearchUri(`/leads`);
    },
    methods: {
        setSearchUri: call("leadSearch/setSearchUri"),
    },
};
</script>
