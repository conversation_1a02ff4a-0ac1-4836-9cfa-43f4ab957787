<template>
    <v-container fluid class="lead-preview-container">
        <v-toolbar flat class="mb-3">
            <v-toolbar-title>Manage Opportunity</v-toolbar-title>
        </v-toolbar>
        <action-needed v-if="appliedPreApproval"></action-needed>
        <v-row>
            <v-col cols="12" md="6">
                <credit-status-actions :dealer-id="dealerIds" :lead="lead" :lead-id="leadId" :loading="loading" />
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="12">
                <lead-info :lead="lead" :loading="loading" />
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="12">
                <notes :lead="lead" :loading="loading" />
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import LeadInfo from "../components/LeadInfo";
import api from "Util/api";
import Notes from "../components/Notes";
import CreditStatusActions from "../components/CreditStatusActions";
import ActionNeeded from "@/modules/Leads/components/ActionNeeded";
import _ from "lodash";

export default {
    name: "LeadPreviewHome",
    components: { ActionNeeded, LeadInfo, Notes, CreditStatusActions },
    props: {
        leadId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            lead: {},
            loading: true,
        };
    },
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        appliedPreApproval() {
            return _.get(this.lead, "deal.preApproval.valid", false);
        },
    },
    watch: {
        dealerIds(value) {
            this.$router.push({
                path: "/leads",
                params: { dealerIds: value },
                query: { dealerIds: value },
            });
        },
    },
    mounted() {
        this.fetchLead(this.leadId);
    },
    methods: {
        fetchLead(leadId) {
            this.loading = true;

            api.get(`/leads/${leadId}`)
                .then((response) => {
                    this.lead = response.data;
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
    },
};
</script>

<style scoped lang="scss">
.lead-preview-container {
    background-color: $gray-200;
}
</style>
