import Vue from "vue";
import VueRouter from "vue-router";
import { configureRouter, layout, route, routerOptions } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/leads";

const routes = [
    layout("Default", [
        route("Leads", "LeadSearchHome", null, PATH_PREFIX),
        route("Leads", "LeadPreviewHome", null, `${PATH_PREFIX}/:leadId`),
        route("Leads", "LeadDetailsHome", null, `${PATH_PREFIX}/:leadId/details`),
        route("Leads", "LeadClaimHome", null, `${PATH_PREFIX}/:leadId/claim`),
        route("Leads", "LeadSMSDisabled", null, `${PATH_PREFIX}/:leadId/smsDisabled`),
    ]),
];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
