<template>
    <v-menu bottom left>
        <template #activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" href="/logout" v-on="on" @click="clearSession()">
                <v-icon>mdi-logout-variant</v-icon>
            </v-btn>
            <dealer-selector v-if="showDealerSelector" :dealer-id="dealerId" />
            <program-selector v-if="showProgramSelector" :program-id="programId" />
            <announce-kit-widget />
        </template>
    </v-menu>
</template>
<script>
import DealerSelector from "Modules/Navigation/components/ActionMenu/DealerSelector";
import ProgramSelector from "Modules/Navigation/components/ActionMenu/ProgramSelector";
import { get } from "vuex-pathify";
import isNil from "lodash/isNil";
import forEach from "lodash/forEach";
import AnnounceKitWidget from "Components/AnnounceKitWidget/index.vue";

export default {
    name: "ActionMenu",
    components: { AnnounceKitWidget, DealerSelector, ProgramSelector },
    props: {
        showDealerSelector: {
            type: Boolean,
            required: false,
            default: true,
        },
        showProgramSelectorProp: {
            type: Boolean,
            required: false,
            default: true,
        },
    },
    computed: {
        selectedProgram: get("loggedInUser/selectedProgram"),
        selectedDealer: get("loggedInUser/selectedDealer"),
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),

        dealerId() {
            let dealerId;

            if (this.$route.name === "DealerSearchHome") {
                dealerId = null;
            } else if (this.selectedDealer != null && this.userDealerAccessList != null) {
                dealerId = this.filterDealerId(this.selectedDealer.id);
            } else if (this.selectedDealer === null && this.userDealerAccessList.length === 1) {
                dealerId = this.userDealerAccessList[0].id || null;
            } else {
                dealerId = isNil(this.$route.query.dealerIds) ? null : this.$route.query.dealerIds;
                dealerId = this.filterDealerId(dealerId);
            }

            if (dealerId != null && dealerId.trim().length === 0) {
                return null;
            }

            return dealerId;
        },
        programId() {
            if (this.selectedProgram != null) {
                return this.selectedProgram.id;
            } else {
                return isNil(this.$route.query.programIds) ? null : this.$route.query.programIds;
            }
        },
        showProgramSelector() {
            if (_.isNil(this.$acl)) {
                return false;
            }

            return !!this.$acl.hasAuthority("ROLE_PROGRAM") && this.showProgramSelectorProp;
        },
    },
    methods: {
        filterDealerId(dealerId) {
            let id = null;
            if (dealerId != null) {
                forEach(this.userDealerAccessList, (item) => {
                    if (item.id === dealerId) {
                        id = dealerId;
                    }
                });
            }

            return id !== undefined ? id : null;
        },
        clearSession() {
            sessionStorage.clear();
        },
    },
};
</script>
