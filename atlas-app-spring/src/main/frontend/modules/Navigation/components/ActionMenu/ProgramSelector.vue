<template>
    <v-col cols="6" sm="3" class="pb-0">
        <v-select
            :value="getProgramId()"
            :items="programAccessList()"
            item-text="text"
            item-value="value"
            placeholder="Select Program"
            single-line
            dense
            @change="setSelectedProgram"
        ></v-select>
    </v-col>
</template>
<script>
import { get, sync } from "vuex-pathify";
import api from "@/util/api";
import forEach from "lodash/forEach";

export default {
    name: "ProgramSelector",
    props: {
        programId: {
            type: String,
            required: false,
            default: null,
        },
    },
    computed: {
        userProgramAccessList: get("loggedInUser/userProgramAccessList"),
        userProgramDealerAccessList: sync("loggedInUser/userProgramDealerAccessList"),
        selectedProgram: sync("loggedInUser/selectedProgram"),
        selectedDealer: sync("loggedInUser/selectedDealer"),
    },
    created() {
        this.setSelectedProgram(this.programId);
    },
    methods: {
        getProgramId() {
            return this.programId === "" ? null : this.programId;
        },
        programAccessList() {
            if (this.userProgramAccessList.length === 1) {
                const item = this.userProgramAccessList[0];
                const newFormattedArray = [{ text: item.name, value: item.id }];
                this.setSelectedProgram(item.id);
                return newFormattedArray;
            } else {
                const newFormattedArray = [{ text: "All Programs", value: null }];
                forEach(this.userProgramAccessList, (item) => {
                    newFormattedArray.push({ text: item.name, value: item.id });
                });
                return newFormattedArray;
            }
        },
        setSelectedProgram(programId) {
            let selectedDealer = this.selectedDealer;

            if (programId === null) {
                this.selectedProgram = null;
            }

            api.post("/users/selectedProgram", { programId: programId })
                .then((response) => {
                    this.userProgramDealerAccessList = response.data;

                    const selectedDealerNoLongerAvailable =
                        selectedDealer && !this.userProgramDealerAccessList.some((e) => e.id === selectedDealer.id);
                    if (selectedDealerNoLongerAvailable) {
                        selectedDealer = null;
                    }
                })
                .catch((error) => console.error(error))
                .finally(() => {
                    const selectedDealerId = selectedDealer ? selectedDealer.id : null;
                    this.$store.commit("userSearch/CLEAR_FILTER", "dealerLinks");
                    this.programFilterQuery(programId, selectedDealerId);
                });

            this.userProgramAccessList.forEach((program) => {
                if (program.id === programId && programId !== null) {
                    this.selectedProgram = program;
                }
            });
        },
        programFilterQuery(programId, dealerIds) {
            if (this.$route.query.programIds === programId && this.$route.query.dealerIds === dealerIds) {
                return;
            }

            this.$router.push({
                query: {
                    ...this.$route.query,
                    programIds: programId,
                    dealerIds: dealerIds,
                },
            });
        },
    },
};
</script>
