<template>
    <div>
        <v-data-table
            :loading="loading"
            :headers="headers"
            :items="trades"
            :items-per-page="5"
            :page="tradesPageNumber"
            no-data-text="No trades found for this user"
            class="elevation-1"
            role="button"
        >
            <template #item.vehicle="{ item }">
                {{ item.vehicle.ymm }}
                <span v-if="item.vehicle.trim">{{ item.vehicle.trim }}</span>
            </template>
            <template #item.paymentType="{ item }">
                {{ item.paymentType }}
            </template>
            <template #item.payment="{ item }">
                {{ item.payment | numeral("$0,00.00") }}
            </template>
            <template #item.finance.name="{ item }">
                <span v-if="item.finance">
                    {{ item.finance.name }}
                </span>
            </template>
            <template #item.payoff="{ item }">
                {{ item.payoff | numeral("$0,00.00") }}
            </template>
            <template #item.offer="{ item }">
                <span v-if="item.offer">
                    {{ item.offer | numeral("$0,00.00") }}
                </span>
            </template>
        </v-data-table>
    </div>
</template>

<script>
export default {
    name: "Trades",
    props: {
        trades: {
            type: Array,
            required: true,
        },
        loading: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            tradesPageNumber: 1,
            headers: [
                {
                    text: "Vehicle",
                    value: "vehicle",
                    sortable: false,
                    width: 200,
                },
                {
                    text: "Type",
                    value: "paymentType",
                    sortable: false,
                },
                {
                    text: "Payment",
                    value: "payment",
                    sortable: false,
                },
                {
                    text: "Finance Co.",
                    value: "finance.name",
                    sortable: false,
                    width: 200,
                },
                {
                    text: "Payoff",
                    value: "payoff",
                    sortable: false,
                },
                {
                    text: "Offer",
                    value: "offer",
                    sortable: false,
                },
            ],
        };
    },
};
</script>
