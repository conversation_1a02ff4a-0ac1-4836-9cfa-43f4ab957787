<template>
    <v-sheet class="action-bar-sheet" rounded>
        <div class="action-bar-right d-flex justify-content-end">
            <quick-link-selector :dealer-id="dealerId" :user-id="userId" class="float-right" />
        </div>
    </v-sheet>
</template>

<script>
import { defineComponent } from "vue";
import QuickLinkSelector from "Modules/Customers/components/CustomerDetails/QuickLinkSelector.vue";

export default defineComponent({
    name: "ActionBar",
    components: { QuickLinkSelector },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: "",
        },
        userId: {
            type: String,
            required: false,
            default: "",
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";

.action-bar-sheet {
    padding: 10px 12px;
    display: flex;
    justify-content: end;
    align-items: center;
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        flex-direction: column;
        gap: 16px;
        align-items: start;
    }

    .w-full {
        width: 100% !important;
    }

    .gap-16px {
        gap: 16px;
    }

    .action-bar-left,
    .action-bar-right {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
    }

    .action-bar-right {
        > * {
            width: fit-content;
            float: right;
        }
    }
}
</style>
