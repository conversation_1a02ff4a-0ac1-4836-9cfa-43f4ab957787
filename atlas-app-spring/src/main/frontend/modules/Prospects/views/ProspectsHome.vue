<template>
    <v-container fluid class="fill-page page-container">
        <v-row>
            <v-col cols="12" md="6" class="pr-md-0 pb-0">
                <prospect-card v-if="!isProspectProfileEmpty" :prospect-profile="prospectProfile" />
            </v-col>
        </v-row>
        <v-card width="100%" class="mb-3 mt-8">
            <v-card-title>
                <span class="mr-2">Trade Vehicles</span>
                <info-tooltip size="20" :max-width="250">
                    These are vehicles a customer is interested in trading in.
                </info-tooltip>
            </v-card-title>
            <trade-offer :trades="trades" :loading="loader.isLoading" />
        </v-card>
        <v-card v-if="isCRMLeadsForProspectEnabled" width="100%" class="mb-3 mt-8">
            <v-card-title>
                <span class="mr-2">Leads</span>
                <info-tooltip size="20" :max-width="250">
                    These are the leads sent from this Upgrade Prospect record to a Dealer CRM
                </info-tooltip>
            </v-card-title>
            <Leads />
        </v-card>
    </v-container>
</template>

<script>
import { call, get } from "vuex-pathify";
import ProspectCard from "../components/ProspectCard";
import lodashGet from "lodash/get";
import lodashIsEmpty from "lodash/isEmpty";
import InfoTooltip from "Components/InfoTooltip";
import TradeOffer from "../components/tradeOffer";
import Leads from "../components/Leads";

export default {
    name: "ProspectsHome",
    components: {
        Leads,
        ProspectCard,
        InfoTooltip,
        TradeOffer,
    },
    props: {
        prospectId: {
            type: String,
            required: false,
            default: null,
        },
        programId: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            trades: [],
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        loader: get("userProspects/prospect@loader"),
        prospectProfile: get("userProspects/prospect@data"),
        dealerId() {
            return this.$route.query.dealerIds;
        },
        isProspectProfileEmpty() {
            return lodashIsEmpty(this.prospectProfile);
        },
        isProspectTrade() {
            return this.prospectProfile && this.isProspectVehicle && this.isTradePayment;
        },
        isProspectVehicle() {
            const prospectProfile = this.prospectProfile;
            return prospectProfile.tradeYear || prospectProfile.tradeMake || prospectProfile.tradeModel;
        },
        isTradePayment() {
            return this.prospectProfile.tradePayment;
        },
        isCRMLeadsForProspectEnabled() {
            return lodashGet(this.featureFlags, "CRM_LEADS_FOR_PROSPECTS", false) || false;
        },
    },
    created() {
        this.setTrades();
        if (this.isCRMLeadsForProspectEnabled) {
            this.fetchLeads({
                prospectId: this.prospectId,
                dealerId: this.dealerId,
            });
        }
    },
    methods: {
        fetchLeads: call("userProspects/fetchLeads"),
        setTrades() {
            if (this.isProspectTrade) {
                this.trades = [
                    {
                        vehicle: {
                            year: this.prospectProfile.tradeYear,
                            make: this.prospectProfile.tradeMake,
                            model: this.prospectProfile.tradeModel,
                            trim: this.prospectProfile.tradeTrim,
                            ymm: `${this.prospectProfile.tradeYear} ${this.prospectProfile.tradeMake} ${this.prospectProfile.tradeModel} `,
                        },
                        paymentType: this.prospectProfile.tradePaymentType,
                        payment: this.prospectProfile.tradePayment,
                        payoff: this.prospectProfile.tradePayoff,
                        offer: this.prospectProfile.tradeValue,
                        finance: null,
                    },
                ];
            }
        },
    },
};
</script>

<style lang="scss">
.page-container {
    background-color: $gray-200;
}

.fill-page {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
}
</style>
