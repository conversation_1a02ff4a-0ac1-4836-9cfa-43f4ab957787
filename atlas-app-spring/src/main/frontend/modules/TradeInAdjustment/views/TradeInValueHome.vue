<template>
    <div>
        <v-card>
            <v-tabs align-tabs="title">
                <v-tabs-slider color="orange"></v-tabs-slider>
                <v-tab color="blue"> TRADE-IN </v-tab>
                <v-tab v-if="isNissanDealer && isOfferAdjustmentFeatureEnabled && isSellAtHomeEnabled">
                    SELL@HOME
                </v-tab>
                <v-tab-item>
                    <trade-in-value data-type="trade" />
                </v-tab-item>
                <v-tab-item>
                    <trade-in-value data-type="sell" />
                </v-tab-item>
            </v-tabs>
        </v-card>
    </div>
</template>

<script>
import TradeInValue from "@/modules/TradeInAdjustment/views/TradeInValue";
import lodashGet from "lodash/get";
import { get } from "vuex-pathify";
import api from "Util/api";
import * as _ from "lodash";
export default {
    name: "TradeInValueHome",
    components: { TradeInValue },
    data() {
        return {
            isSellAtHomeEnabled: false,
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        isNissanDealer: get("loggedInUser/isNnaDealer"),
        isOfferAdjustmentFeatureEnabled() {
            const enabled = lodashGet(this.featureFlags, "OFFER_ADJUSTMENT_FEATURE", false) || false;
            return enabled;
        },
        dealerIds() {
            const result = this.$route.query.dealerIds;
            return result;
        },
    },
    created() {
        this.isSellAtHomeToggleEnabled();
    },
    methods: {
        isSellAtHomeToggleEnabled() {
            api.get(`/feature-subscription/${this.dealerIds}`)
                .then((response) => {
                    this.isSellAtHomeEnabled = _.get(response, "data.enabled", []);
                })
                .catch((error) => {
                    console.log(error);
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.boxSize {
    width: 207px;
}
.superScriptSize {
    font-size: 8px;
}
.superScriptSizeTitle {
    font-size: 10px;
}
.textarea {
    resize: none;
}
.checkBoxWidth {
    width: 170px;
}
</style>
