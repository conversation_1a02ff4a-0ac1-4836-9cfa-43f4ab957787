import { make } from "vuex-pathify";
import loader from "@/util/loader";
import api from "@/util/api";
import * as _ from "lodash";

const initialState = {
    premiumsSearchForm: {
        data: {
            vin: null,
            purchaseDate: null,
            odometer: null,
            carSaverDealerId: null,
        },
        loader: loader.defaultState(),
        submitting: loader.defaultState(),
    },
    premiumsSearchModel: {
        dealerEnrollmentEffectiveDate: null,
        maxPostDatedDaysDate: null,
        maxPostDatedDays: null,
        prospectDealer: null,
        premiumsRequest: {},
    },
    premiums: [],
    existingWarrantyContract: {},
    premiumQuickQuotes: [],
    selectedPremiums: [],
    customer: {
        id: null,
    },
    dealer: {
        id: null,
        dealerId: null,
    },
    enrollCustomerFormModel: {
        warrantyFormStep: 1,
        vehicleConditionCodes: [],
        agreementFormNumbers: [],
        agreementSurcharges: [],
    },
    enrollCustomerForm: {
        surcharges: [],
        buyer: {
            firstName: null,
            middleName: null,
            lastName: null,
            emailAddress: null,
            primaryPhone: null,
            alternatePhone: null,
            streetAddress: null,
            city: null,
            state: null,
            zip: null,
        },
        deal: {
            scheduleId: null,
            customerNumber: null,
            dealerFAndINumber: null,
            dealerContactName: null,
        },
        lienholder: {
            name: null,
            streetAddress: null,
            city: null,
            state: null,
            zip: null,
            phoneNumber: null,
        },
        vehicle: {
            purchasePrice: null,
            conditionCode: null,
            stockNumber: null,
            vin: null,
            year: null,
            make: null,
            model: null,
            trim: null,
            odometer: null,
            engineAspiration: null,
            driveType: null,
            fuelType: null,
        },
        agreement: {
            formNumber: null,
            stockNumber: null,
            effectiveDate: null,
            paymentPlan: null,
            retailPurchasePrice: null,
            dealerBaseCost: null,
            termMonths: null,
            termMiles: null,
            rateClass: null,
            purchaseDate: null,
        },
        loan: {
            instrument: "Purchase",
        },
    },
    confirmationPageModel: {
        data: {
            existingWarrantsContracts: [],
            premiums: [],
            buyer: null,
            vehicle: null,
            purchaseDate: null,
            premiumsRequestParams: null,
            initialRegistration: false,
        },
        loader: loader.defaultState(),
    },
};

const mutations = {
    ...make.mutations(initialState),
};

const actions = {
    loadConfirmationPageModel({ commit, state, dispatch }, { dealerId, userId, vin, initialRegistration = false }) {
        commit("SET_CONFIRMATION_PAGE_MODEL", {
            ...state.confirmationPageModel,
            loader: loader.started(),
        });
        commit("SET_DEALER", { id: dealerId });
        commit("SET_CUSTOMER", { id: userId });

        return api
            .get(`/dealer/warranty/${userId}/confirmations/${vin}`, { dealerIds: dealerId })
            .then((response) => {
                commit("SET_CONFIRMATION_PAGE_MODEL", {
                    ...state.confirmationPageModel,
                    loader: loader.successful(),
                });

                const data = _.get(response, "data", null);
                data.initialRegistration = initialRegistration;
                commit("SET_CONFIRMATION_PAGE_MODEL", {
                    ...state.confirmationPageModel,
                    data,
                });

                return data;
            })
            .catch((error) => {
                commit("SET_CONFIRMATION_PAGE_MODEL", {
                    ...state.confirmationPageModel,
                    loader: loader.error(error),
                });
                return Promise.reject(error);
            });
    },
    initPremiumsSearch({ commit, state, dispatch }, { dealerId, userId, vin }) {
        commit("SET_PREMIUMS_SEARCH_FORM", {
            ...state.premiumsSearchForm,
            loader: loader.started(),
        });
        commit("SET_DEALER", { id: dealerId });
        return dispatch("doSelectCustomer", { dealerId, userId })
            .then(() => dispatch("doInitPremiumsSearch", { dealerId, userId, vin }))
            .then(() => {
                commit("SET_PREMIUMS_SEARCH_FORM", {
                    ...state.premiumsSearchForm,
                    loader: loader.successful(),
                });
                return state.premiumsSearchForm;
            })
            .catch((error) => {
                commit("SET_PREMIUMS_SEARCH_FORM", {
                    ...state.premiumsSearchForm,
                    loader: loader.error(error),
                });
                return Promise.reject(error);
            });
    },
    doSelectCustomer({ commit, state }, { dealerId, userId }) {
        return api
            .get(`/dealer/warranty/${userId}/select-customer`, { dealerIds: dealerId })
            .then((response) => {
                //capture selected user id in store
                commit("SET_CUSTOMER", { id: userId });

                //init enroll form with buyer data
                const buyer = _.get(response, "data", null);
                commit("SET_ENROLL_CUSTOMER_FORM", {
                    ...state.enrollCustomerForm,
                    buyer,
                });

                return buyer;
            })
            .catch((error) => {
                console.log(error);
                return Promise.reject(error);
            });
    },
    doInitPremiumsSearch({ commit, state }, { dealerId, userId, vin }) {
        return api
            .get(`/dealer/warranty/${userId}/search-premiums`, {
                vin: vin,
                dealerIds: dealerId,
            })
            .then((response) => {
                const premiumsSearchModel = _.get(response, "data", null);
                commit("SET_PREMIUMS_SEARCH_MODEL", premiumsSearchModel);

                const premiumsSearchForm = _.get(premiumsSearchModel, "premiumsRequest", null);

                commit("SET_PREMIUMS_SEARCH_FORM", {
                    ...state.premiumsSearchForm,
                    data: premiumsSearchForm,
                });

                return premiumsSearchModel;
            });
    },
    searchPremiums({ commit, state, dispatch }, { dealerId, userId, form }) {
        commit("SET_PREMIUMS_SEARCH_FORM", {
            ...state.premiumsSearchForm,
            submitting: loader.started(),
        });

        return dispatch("doValidateSearchPremiums", { dealerId, userId, form })
            .then(() => dispatch("doSearchPremiums", { dealerId, userId, form }))
            .then((resp) => {
                commit("SET_PREMIUMS_SEARCH_FORM", {
                    ...state.premiumsSearchForm,
                    submitting: loader.successful(),
                });
                return resp;
            })
            .catch((error) => {
                commit("SET_PREMIUMS_SEARCH_FORM", {
                    ...state.premiumsSearchForm,
                    submitting: loader.error(error),
                });
                return Promise.reject(error);
            });
    },

    doValidateSearchPremiums({ commit, state }, { dealerId, userId, form }) {
        form.dealerIds = dealerId;
        return api.post(`/dealer/warranty/${userId}/search-premiums-validation`, form).then((response) => {
            return response.data;
        });
    },

    doSearchPremiums({ commit, state }, { dealerId, userId, form }) {
        form.dealerIds = dealerId;
        return api.post(`/dealer/warranty/${userId}/search-premiums`, form).then((response) => {
            const premiumsModel = _.get(response, "data", null);

            const existingWarrantyContract = _.get(premiumsModel, "existingWarrantyContract", null);
            commit("SET_EXISTING_WARRANTY_CONTRACT", existingWarrantyContract);

            const lienholder = { name: _.get(existingWarrantyContract, "applicantRegistration.lienholder.name") };
            const vehicle = _.get(premiumsModel, "vehicle", null);
            commit("SET_ENROLL_CUSTOMER_FORM", {
                ...state.enrollCustomerForm,
                vehicle,
                lienholder,
            });

            const premiums = _.get(premiumsModel, "premiums", null);
            commit("SET_PREMIUMS", premiums);

            const premiumQuickQuotes = _.get(premiumsModel, "premiumQuickQuotes", null);
            commit("SET_PREMIUM_QUICK_QUOTES", premiumQuickQuotes);

            return premiums;
        });
    },

    selectLifetimeWarranty({ commit, state, getters }) {
        const selectedPremium = { ...getters.selectedPremium };
        const agreement = { ...getters.selectedPremium };
        const deal = { scheduleId: getters.selectedPremium.scheduleId };
        agreement.purchaseDate = state.premiumsSearchForm.data.purchaseDate;
        commit("SET_ENROLL_CUSTOMER_FORM", {
            ...state.enrollCustomerForm,
            agreement,
            deal,
            selectedPremium,
        });
    },

    selectUpgradePremium({ commit, state, getters }) {
        const _vehicle = _.get(state, "confirmationPageModel.data.vehicle", null);

        const vehicle = { ..._vehicle };

        // if they filled out the form lets grab that odometer value,
        // customer might be getting an upgrade and the mileage may have changed significantly
        const updatedOdometer = _.get(state, "premiumsSearchForm.data.odometer", null);
        if (!_.isNil(updatedOdometer)) {
            vehicle.odometer = updatedOdometer;
        }

        const buyer = _.get(state, "confirmationPageModel.data.buyer", null);

        const selectedPremium = { ...getters.selectedPremium };
        const agreement = { ...getters.selectedPremium };

        //determine where we get purchaseDate from, probably off the existing warranty contract
        agreement.purchaseDate = _.get(state, "confirmationPageModel.data.purchaseDate", null);

        const deal = { scheduleId: getters.selectedPremium.scheduleId };

        commit("SET_ENROLL_CUSTOMER_FORM", {
            ...state.enrollCustomerForm,
            vehicle,
            buyer,
            agreement,
            deal,
            selectedPremium,
        });
    },
};

const getters = {
    ...make.getters(initialState),
    selectedPremium: (state) => {
        return _.head(state.selectedPremiums);
    },
    isCustomerSelected: (state) => {
        const customerFirstName = _.get(state, "enrollCustomerForm.buyer.firstName", null);
        return !_.isNil(customerFirstName);
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
