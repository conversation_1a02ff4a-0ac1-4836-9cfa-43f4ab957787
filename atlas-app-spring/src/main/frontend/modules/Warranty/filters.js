import Vue from "vue";
import _ from "lodash";
import numeral from "numeral";

Vue.filter("unlimited", function (val) {
    if (!_.isNil(val) && val === 0) {
        return "Unlimited";
    } else if (!_.isNil(val) && val > 0) {
        return numeral(val).format("0,0");
    }

    return val;
});

Vue.filter("dash", function (val) {
    if (!_.isNil(val) && val === 0) {
        return "-";
    } else if (!_.isNil(val) && val > 0) {
        return numeral(val).format("$0,0");
    }

    return val;
});
