<template>
    <v-container :key="dealerId" class="lifetime-warranty-container fill-height align-start" fluid>
        <v-row>
            <v-col> <warranty-vehicle-details /> </v-col>
        </v-row>
        <v-row class="mt-0">
            <v-col> <lifetime-warranty-list /> </v-col>
        </v-row>
        <v-row class="mt-0">
            <v-col> <premium-quick-quotes-list /> </v-col>
        </v-row>
    </v-container>
</template>

<script>
import LifetimeWarrantyList from "../components/SelectLifetimeWarranty/LifetimeWarrantyList.vue";
import PremiumQuickQuotesList from "../components/SelectLifetimeWarranty/PremiumQuickQuotesList.vue";
import WarrantyVehicleDetails from "@/modules/Warranty/components/SelectLifetimeWarranty/WarrantyVehicleDetails";
import { get } from "vuex-pathify";
export default {
    name: "SelectLifetimeWarranty",
    components: {
        WarrantyVehicleDetails,
        LifetimeWarrantyList,
        PremiumQuickQuotesList,
    },
    props: {
        userId: {
            type: String,
            required: true,
        },
    },
    computed: {
        isCustomerSelected: get("eRating/isCustomerSelected"),
        dealerId() {
            return this.$route.query.dealerIds;
        },
    },
    created() {
        if (!this.userId) {
            this.$router.push({
                path: `/dealer/warranty/customer-search`,
                query: { dealerIds: this.dealerId },
            });
        } else if (!this.isCustomerSelected) {
            this.$router.push({
                path: `/dealer/warranty/${this.userId}/search-premiums`,
                query: { dealerIds: this.dealerId },
            });
        }
    },
};
</script>
<style lang="scss">
.lifetime-warranty-container {
    background-color: $gray-200;
}
</style>
