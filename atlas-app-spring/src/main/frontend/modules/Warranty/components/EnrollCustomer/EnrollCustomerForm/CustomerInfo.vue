<template>
    <BaseForm>
        <v-row>
            <v-col cols="12" md="4">
                <v-text-field
                    v-model="buyerFirstName"
                    dense
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    outlined
                    label="First Name *"
                />
            </v-col>
            <v-col cols="12" md="4">
                <v-text-field
                    v-model="buyerMiddleName"
                    dense
                    :hide-details="hideDetails"
                    outlined
                    label="Middle Name (optional)"
                />
            </v-col>
            <v-col cols="12" md="4">
                <v-text-field
                    v-model="buyerLastName"
                    dense
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    outlined
                    label="Last Name *"
                />
            </v-col>
        </v-row>

        <v-row>
            <v-col cols="12">
                <v-text-field
                    v-model="buyerEmailAddress"
                    dense
                    :rules="[rules.required, rules.email]"
                    :hide-details="hideDetails"
                    outlined
                    label="Email Address *"
                />
            </v-col>
        </v-row>

        <v-row>
            <v-col cols="12" md="auto" lg="4">
                <v-text-field
                    v-model="buyerPrimaryPhone"
                    dense
                    type="tel"
                    :rules="[rules.required, rules.tel]"
                    maxlength="10"
                    :hide-details="hideDetails"
                    outlined
                    label="Home Phone *"
                />
            </v-col>
            <v-col cols="12" md="auto" lg="4">
                <v-text-field
                    v-model="buyerAlternatePhone"
                    dense
                    type="tel"
                    :rules="[rules.tel]"
                    :hide-details="hideDetails"
                    outlined
                    label="Work Phone (optional)"
                />
            </v-col>
        </v-row>

        <v-row>
            <v-col>
                <v-text-field
                    v-model="buyerStreetAddress"
                    dense
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    outlined
                    label="Street Address *"
                />
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="12" sm="auto">
                <v-text-field
                    v-model="buyerCity"
                    label="City *"
                    dense
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    outlined
                />
            </v-col>
            <v-col cols="6" sm="auto" lg="3">
                <v-select
                    v-model="buyerState"
                    :items="states"
                    label="State *"
                    :rules="[rules.required]"
                    :hide-details="hideDetails"
                    dense
                    outlined
                />
            </v-col>
            <v-col cols="6" sm="auto" lg="4">
                <v-text-field
                    v-model="buyerZip"
                    dense
                    :rules="[rules.required, rules.zipCode]"
                    :hide-details="hideDetails"
                    outlined
                    label="Zip Code *"
                />
            </v-col>
        </v-row>

        <v-row>
            <v-col cols="12" md="4">
                <v-text-field
                    v-model="dealCustomerNumber"
                    dense
                    :hide-details="hideDetails"
                    outlined
                    label="Customer Number (optional)"
                />
            </v-col>
        </v-row>
    </BaseForm>
</template>

<script>
import { sync } from "vuex-pathify";
import states from "@/util/states";
import formRules from "Util/formRules";
import BaseForm from "@/modules/Warranty/components/EnrollCustomer/EnrollCustomerForm/BaseForm";

export default {
    name: "CustomerInfo",
    components: { BaseForm },
    data() {
        return {
            valid: true,
            states: states.getStates(),
            rules: formRules,
            hideDetails: "auto",
        };
    },
    computed: {
        buyerFirstName: sync("eRating/<EMAIL>"),
        buyerLastName: sync("eRating/<EMAIL>"),
        buyerMiddleName: sync("eRating/<EMAIL>"),
        buyerEmailAddress: sync("eRating/<EMAIL>"),
        buyerPrimaryPhone: sync("eRating/<EMAIL>"),
        buyerAlternatePhone: sync("eRating/<EMAIL>"),
        buyerStreetAddress: sync("eRating/<EMAIL>"),
        buyerCity: sync("eRating/<EMAIL>"),
        buyerState: sync("eRating/<EMAIL>"),
        buyerZip: sync("eRating/<EMAIL>"),

        step: sync("eRating/enrollCustomerFormModel@warrantyFormStep"),

        dealCustomerNumber: sync("eRating/<EMAIL>"),
    },
};
</script>

<style scoped></style>
