<template>
    <v-dialog v-model="show" width="500">
        <v-card>
            <v-card-title class="headline"> {{ title }} </v-card-title>
            <v-card-text>
                <p>{{ instructions }}</p>
            </v-card-text>
            <v-card-actions>
                <v-spacer />
                <v-btn color="primary" @click="show = false">Close</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import EventBus from "@/util/eventBus";

export default {
    name: "EnrollCustomerErrorModal",
    data() {
        return {
            show: false,
            title: null,
            instructions: null,
        };
    },

    created() {
        EventBus.$on("enroll-customer-error-modal", (evt) => {
            this.show = true;
            this.title = evt.title;
            this.instructions = evt.instructions;
        });
    },
};
</script>
