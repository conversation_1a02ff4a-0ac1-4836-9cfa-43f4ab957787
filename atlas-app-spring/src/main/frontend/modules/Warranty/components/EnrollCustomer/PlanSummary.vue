<template>
    <v-card>
        <v-card-text>
            <v-row>
                <v-col
                    >Plan: <strong>{{ selectedPremium.planName }}</strong></v-col
                >
            </v-row>
            <v-row>
                <v-col>
                    Term:
                    <strong>
                        {{ selectedPremium.termMonths | unlimited }}
                    </strong>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    Plan Deductible:
                    <strong>{{ selectedPremium.planDeductible | numeral("$0,0") }}</strong>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    Expiration Date:
                    <strong>{{ selectedPremium.termMonths | unlimited }}</strong>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    Expiration Miles:
                    <strong>{{ selectedPremium.termMiles | unlimited }}</strong>
                </v-col>
            </v-row>
        </v-card-text>
    </v-card>
</template>

<script>
import { get } from "vuex-pathify";

export default {
    name: "PlanSummary",
    computed: {
        selectedPremium: get("eRating/selectedPremium"),
    },
};
</script>

<style scoped></style>
