<template>
    <v-card>
        <v-card-title class="d-flex">
            <v-icon class="mr-1">mdi-car-multiple</v-icon> <span>Enter Vehicle Information to Search Premiums</span>
        </v-card-title>
        <v-skeleton-loader v-if="isLoading" type="card" />
        <v-card-text v-else>
            <v-form>
                <v-row>
                    <v-col cols="12">
                        <v-dialog
                            ref="dialog"
                            v-model="modal"
                            :return-value.sync="purchaseDate"
                            persistent
                            width="290px"
                        >
                            <template #activator="{ on, attrs }">
                                <v-text-field
                                    ref="purchaseDate"
                                    v-model="purchaseDate"
                                    :rules="[rules.required, rules.purchaseDate]"
                                    label="Purchase Date *"
                                    outlined
                                    dense
                                    v-bind="attrs"
                                    v-on="on"
                                >
                                    <template #prepend-inner>
                                        <info-tooltip size="20" :max-width="250">
                                            The Vehicles purchase date.
                                            <br />
                                            NOTE: Cannot be post-dated more than
                                            {{ premiumsSearchModel.maxPostDatedDays }} days and must be greater than or
                                            equal to the dealer's enrollment date of
                                            {{ premiumsSearchModel.dealerEnrollmentEffectiveDate }}
                                        </info-tooltip>
                                    </template>
                                    <template #append>
                                        <v-icon class="grey--text" v-bind="attrs" v-on="on">mdi-calendar</v-icon>
                                    </template>
                                </v-text-field>
                            </template>
                            <v-date-picker
                                v-model="purchaseDate"
                                :min="premiumsSearchModel.dealerEnrollmentEffectiveDate"
                                :max="premiumsSearchModel.maxPostDatedDaysDate"
                                scrollable
                            >
                                <v-spacer></v-spacer>
                                <v-btn text color="primary" @click="modal = false"> Cancel </v-btn>
                                <v-btn text color="primary" @click="$refs.dialog.save(purchaseDate)"> OK </v-btn>
                            </v-date-picker>
                        </v-dialog>
                    </v-col>
                    <v-col cols="12">
                        <v-text-field
                            ref="vin"
                            v-model="vin"
                            :rules="[rules.required, rules.vin]"
                            outlined
                            dense
                            maxlength="17"
                            label="VIN *"
                        ></v-text-field>
                    </v-col>
                    <v-col cols="12">
                        <v-text-field
                            ref="odometer"
                            v-model="odometer"
                            :rules="[rules.required, rules.odometer]"
                            outlined
                            dense
                            label="Odometer *"
                        ></v-text-field>
                    </v-col>
                    <v-col v-if="isAdminUser" cols="12">
                        <v-checkbox
                            v-model="carSaverTransaction"
                            :disabled="premiumsSearchModel.prospectDealer"
                            label="CarSaver Transaction?"
                            outlined
                            dense
                        />
                    </v-col>
                </v-row>
            </v-form>
        </v-card-text>
        <v-card-actions>
            <v-btn
                color="primary"
                :disabled="isLoading || isSubmitting"
                :loading="isSubmitting"
                @click.prevent="doSearch"
            >
                Search
            </v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import moment from "moment";
import InfoTooltip from "Components/InfoTooltip";

export default {
    name: "PremiumsSearchForm",
    components: { InfoTooltip },
    props: {
        userId: {
            type: String,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            modal: false,
            rules: {
                required: (value) => !!value || "This field is required.",
                vin: (v) => (v && v.length === 17) || "VIN must be 17 characters",
                purchaseDate: (p) => {
                    const isInValidDate = (p) => {
                        return !moment(p).isValid();
                    };

                    const postDatedTooFar = (p) => {
                        const purchaseDate = moment(p);
                        const maxPostDatedDaysDate = moment(this.premiumsSearchModel.maxPostDatedDaysDate);

                        return purchaseDate.isAfter(maxPostDatedDaysDate);
                    };

                    const beforeDealerEnrollmentDate = (p) => {
                        const purchaseDate = moment(p);
                        const dealerEnrollmentEffectiveDate = moment(
                            this.premiumsSearchModel.dealerEnrollmentEffectiveDate
                        );

                        return purchaseDate.isBefore(dealerEnrollmentEffectiveDate);
                    };
                    if (!p) {
                        return false;
                    } else if (isInValidDate(p)) {
                        return "Please provide a valid date";
                    } else if (postDatedTooFar(p)) {
                        return (
                            "Purchase Date CANNOT be post-dated beyond " + this.premiumsSearchModel.maxPostDatedDaysDate
                        );
                    } else if (beforeDealerEnrollmentDate(p)) {
                        return (
                            "Purchase date: can NOT be before Dealer enrollment date of: " +
                            this.premiumsSearchModel.dealerEnrollmentEffectiveDate
                        );
                    } else {
                        return true;
                    }
                },
                odometer: (o) => {
                    if (!o) {
                        return false;
                    } else if (!(o >= 0 && o <= 100000)) {
                        return "Odometer must be a positive number and 100,000 miles or less";
                    } else if (this.serverFieldErrors["odometer"]) {
                        return this.serverFieldErrors["odometer"];
                    } else {
                        return true;
                    }
                },
            },
            formHasErrors: false,
            serverFieldErrors: {
                odometer: null,
            },
        };
    },
    computed: {
        isLoading: get("eRating/<EMAIL>"),
        isSubmitting: get("eRating/<EMAIL>"),
        premiumsSearchModel: get("eRating/premiumsSearchModel"),
        premiumsSearchForm: get("eRating/premiumsSearchForm"),
        existingWarrantyContract: get("eRating/existingWarrantyContract"),
        vin: sync("eRating/<EMAIL>"),
        purchaseDate: sync("eRating/<EMAIL>"),
        odometer: sync("eRating/<EMAIL>"),
        carSaverTransaction: sync("eRating/<EMAIL>"),
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
    },
    created() {
        const premiumsSearch = {
            dealerId: this.dealerId,
            userId: this.userId,
            vin: _.get(this, "$route.query.vin", null),
            carSaverTransaction: _.get(this, "carSaverTransaction", false),
        };
        this.initPremiumsSearch(premiumsSearch);
    },
    methods: {
        initPremiumsSearch: call("eRating/initPremiumsSearch"),
        searchPremiums: call("eRating/searchPremiums"),
        doSearch() {
            const form = {
                ...this.premiumsSearchForm.data,
            };

            const formKeys = Object.keys(form);
            this.formHasErrors = false;

            _.forEach(formKeys, (field) => {
                if (this.$refs[field] && (_.isNil(form[field]) || !this.$refs[field].validate(true))) {
                    this.formHasErrors = true;
                }
            });
            const dealerId = this.dealerId;
            const userId = this.userId;
            if (!this.formHasErrors && _.get(form, "vin.length", 0) === 17) {
                this.searchPremiums({ dealerId, userId, form })
                    .then(() => {
                        if (this.existingWarrantyContract) {
                            const vin = this.existingWarrantyContract.vin;
                            this.$router.push({
                                path: `/dealer/warranty/${userId}/confirmations/${vin}`,
                                query: { skippedToConfirmation: true, dealerIds: dealerId },
                            });
                        } else {
                            this.$router.push({
                                path: `/dealer/warranty/${userId}/select-lifetime-warranty`,
                                query: { dealerIds: dealerId },
                            });
                        }
                    })
                    .catch((err) => {
                        let errMsg;
                        const fieldErrors = _.get(err, "response.data.errors", []);
                        if (!_.isEmpty(fieldErrors)) {
                            //TODO generify this for all fields
                            const odometerViolation = _.find(fieldErrors, ["field", "odometer"]);
                            if (odometerViolation.message) {
                                this.serverFieldErrors["odometer"] = odometerViolation.message;
                                this.$refs.odometer.validate(true);
                                this.serverFieldErrors["odometer"] = null;
                            }
                        } else {
                            errMsg = _.get(err, "response.data.error", "An unknown error occurred");
                            this.$toast.error(errMsg);
                        }
                    });
            }
        },
    },
};
</script>

<style scoped></style>
