<template>
    <v-card>
        <v-toolbar flat>
            <v-toolbar-title class="grey--text">
                <v-icon class="mr-1">mdi-shield-check</v-icon>
                <span>{{ title }}</span>
            </v-toolbar-title>

            <v-spacer></v-spacer>
        </v-toolbar>

        <v-divider></v-divider>

        <v-card-text v-if="noPremiums"> No premiums found </v-card-text>
        <v-data-table v-else :headers="fields" :items="premiums" item-key="idx" hide-default-footer class="elevation-1">
            <template #item.planName="{ item }">
                <a v-if="selectable" @click="onclickHandler(item)">{{ item.planName }}</a>
                <span v-else>{{ item.planName }}</span>
            </template>
            <template #item.termMonths="{ item }">
                {{ item.termMonths | unlimited }}
            </template>
            <template #item.termMiles="{ item }">
                {{ item.termMiles | unlimited }}
            </template>
            <template #item.planDeductible="{ item }">
                {{ item.planDeductible | dash }}
            </template>
            <template #item.retailPrice="{ item }">
                {{ item.retailPrice | dash }}
            </template>
        </v-data-table>
    </v-card>
</template>
<script lang="ts">
import Vue from "vue";
import _ from "lodash";
import { get } from "vuex-pathify";

export default Vue.extend({
    name: "PremiumsList",
    props: {
        title: {
            type: String,
            required: false,
            default: "",
        },
        selectable: {
            type: Boolean,
            required: false,
            default: false,
        },
        premiums: {
            type: Array,
            required: false,
            default: () => [],
        },
        value: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            fields: [
                {
                    value: "planName",
                    text: "Plan Name",
                },
                {
                    value: "planFamily",
                    text: "Plan Family",
                },
                {
                    value: "termMonths",
                    text: "Months",
                },
                {
                    value: "termMiles",
                    text: "Miles",
                },
                {
                    value: "planDeductible",
                    text: "deductible",
                },
                {
                    value: "retailPrice",
                    text: "Retail Price",
                },
            ],
            selectedPremiums: [] as Array<object>,
        };
    },

    computed: {
        dealerId: get("eRating/dealer@id"),
        userId: get("eRating/customer@id"),
        noPremiums(): boolean {
            return _.isEmpty(this.premiums);
        },
        nextDisabled(): boolean {
            return _.isEmpty(this.selectedPremiums);
        },
    },
    watch: {
        selectedPremiums: {
            deep: true,
            handler(newVal) {
                this.$emit("input", newVal);
            },
        },
    },
    methods: {
        onclickHandler(selectedPremium: object) {
            this.selectedPremiums.push(selectedPremium);
            this.$emit("next", this.selectedPremiums);
        },
    },
});
</script>
<style lang="scss">
.v-data-table-header th {
    white-space: nowrap !important;
}
</style>
