<template>
    <v-form ref="form" lazy-validation @submit.stop.prevent="next">
        <v-container>
            <v-row>
                <v-col lg="8">
                    <v-row>
                        <v-col cols="6">
                            <v-text-field
                                v-model="firstName"
                                label="First Name"
                                :rules="[rules.required]"
                                placeholder="Enter first name"
                            />
                        </v-col>
                        <v-col cols="6">
                            <v-text-field
                                v-model="lastName"
                                label="Last Name"
                                :rules="[rules.required]"
                                placeholder="Enter last name"
                            />
                        </v-col>
                    </v-row>

                    <v-row>
                        <v-col cols="12">
                            <v-text-field
                                v-model="phoneNumber"
                                v-mask="'(###) ###-####'"
                                label="Phone Number"
                                :rules="[rules.required]"
                                placeholder="Enter Phone Number"
                                :error-messages="validPhoneNumber ? null : 'Invalid phone number'"
                            />
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col>
                            <vuetify-google-autocomplete
                                id="address"
                                ref="address"
                                label="Address"
                                :rules="[rules.required]"
                                @placechanged="getAddressData"
                            />
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="4">
                            <v-text-field
                                v-model="city"
                                label="City"
                                placeholder="Enter City"
                                :rules="[rules.required]"
                            />
                        </v-col>
                        <v-col cols="4">
                            <v-select v-model="stateCode" :items="states" label="State" :rules="[rules.required]" />
                        </v-col>
                        <v-col cols="4">
                            <v-text-field
                                v-model="zipCode"
                                label="Zip Code"
                                placeholder="Enter Zip Code"
                                :rules="[rules.required]"
                            />
                        </v-col>
                    </v-row>
                </v-col>
                <v-col lg="4">
                    <div class="text-center">
                        <div style="margin-top: 20px">
                            <v-icon style="font-size: 180px" color="grey lighten-3"> mdi-account </v-icon>
                        </div>
                    </div>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-btn color="primary" @click="next">Next</v-btn>
                </v-col>
            </v-row>
        </v-container>
    </v-form>
</template>

<script>
import _ from "lodash";
import api from "@/util/api";
import states from "@/util/states";
export default {
    name: "PersonalStep",
    props: {
        user: {
            type: Object,
            required: false,
            default: null,
        },
    },

    data() {
        return {
            firstName: "",
            lastName: "",
            phoneNumber: "",
            address: "",
            city: "",
            stateCode: "",
            zipCode: "",
            county: "",
            lat: "",
            lng: "",
            validPhoneNumber: true,
            rules: {
                required: (value) => !!value || "Required.",
            },
            states: states.getStates(),
        };
    },

    watch: {
        user: {
            deep: true,
            handler(newUser) {
                if (!_.isNil(newUser)) {
                    this.firstName = _.get(newUser, "firstName");
                    this.lastName = _.get(newUser, "lastName");
                    this.phoneNumber = this.normalizePhoneNumber(_.get(newUser, "phoneNumber"));
                    this.city = _.get(newUser, "city");
                    this.stateCode = _.get(newUser, "stateCode");
                    this.zipCode = _.get(newUser, "zipCode");
                    this.lat = _.get(newUser, "lat");
                    this.lng = _.get(newUser, "lng");

                    this.$refs.address.update(_.get(newUser, "address"));
                }
            },
        },
    },

    // validations: {
    //     firstName: {
    //         required,
    //         minLength: minLength(2),
    //     },
    //     lastName: {
    //         required,
    //         minLength: minLength(2),
    //     },
    //     phoneNumber: {
    //         required,
    //         minLength: minLength(14),
    //         maxLength: maxLength(14),
    //         isValid(maskedNumber) {
    //             const number = maskedNumber.replace(/\D/g, "");
    //
    //             if (_.isNil(number) || number === "") {
    //                 return true;
    //             }
    //
    //             if (number.length !== 10) {
    //                 return false;
    //             }
    //
    //             return api
    //                 .get("/phone/validate", { number })
    //                 .then(() => {
    //                     return true;
    //                 })
    //                 .catch(() => {
    //                     return false;
    //                 });
    //         },
    //     },
    //     address: {
    //         required,
    //         minLength: minLength(5),
    //     },
    //     city: {
    //         required,
    //         minLength: minLength(2),
    //     },
    //     stateCode: {
    //         required,
    //     },
    //     zipCode: {
    //         required,
    //         minLength: minLength(5),
    //         maxLength: maxLength(5),
    //     },
    // },

    // mounted() {
    //     const apiKey = window._CS_GOOGLE_API_KEY;
    //     const that = this;
    //     this.$loadScript(
    //         `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`
    //     ).then(() => {
    //         that.autocomplete = new google.maps.places.Autocomplete(
    //             that.$refs.autocomplete.$el,
    //             { types: ["geocode"] }
    //         );
    //         that.autocomplete.setFields(["address_component", "geometry"]);
    //
    //         that.autocomplete.addListener("place_changed", () => {
    //             const place = that.autocomplete.getPlace();
    //             const lat = place.geometry.location.lat();
    //             const lng = place.geometry.location.lng();
    //             that.lat = lat;
    //             that.lng = lng;
    //
    //             const streetNumber = that.getGoogleMapsAddressField(
    //                 place,
    //                 "street_number"
    //             );
    //             const route = that.getGoogleMapsAddressField(place, "route");
    //             that.address = `${streetNumber} ${route}`;
    //             that.city = that.getGoogleMapsAddressField(place, "locality");
    //             that.stateCode = that.getGoogleMapsAddressField(
    //                 place,
    //                 "administrative_area_level_1"
    //             );
    //             that.zipCode = that.getGoogleMapsAddressField(
    //                 place,
    //                 "postal_code"
    //             );
    //         });
    //     });
    // },

    methods: {
        normalizePhoneNumber(value) {
            if (value && typeof value === "string") {
                return value.replace(/\D/g, "");
            }

            return value;
        },

        next() {
            if (this.$refs.form.validate()) {
                this.validatePhoneNumber().then((success) => {
                    this.validPhoneNumber = success;
                    if (success) {
                        this.$emit("next", {
                            firstName: this.firstName,
                            lastName: this.lastName,
                            phoneNumber: this.normalizePhoneNumber(this.phoneNumber),
                            address: this.address,
                            city: this.city,
                            stateCode: this.stateCode,
                            zipCode: this.zipCode,
                            county: this.county,
                            lat: this.lat,
                            lng: this.lng,
                        });
                    }
                });
            }
        },

        validatePhoneNumber() {
            const number = this.phoneNumber.replace(/\D/g, "");

            if (_.isNil(number) || number === "") {
                return Promise.resolve(true);
            }

            if (number.length !== 10) {
                return Promise.resolve(false);
            }

            return api
                .get("/phone/validate", { number })
                .then(() => {
                    return true;
                })
                .catch(() => {
                    return false;
                });
        },

        getAddressData(data) {
            this.lat = data.latitude;
            this.lng = data.longitude;

            const streetNumber = data.street_number;
            const route = data.route;
            this.address = `${streetNumber} ${route}`;
            this.city = data.locality;
            this.stateCode = data.administrative_area_level_1;
            this.zipCode = data.postal_code;
            this.county = data.administrative_area_level_2;

            this.$refs.address.update(this.address);
        },
    },
};
</script>
