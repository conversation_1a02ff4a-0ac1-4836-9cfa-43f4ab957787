<template>
    <v-form ref="form" v-model="valid" lazy-validation @submit.stop.prevent="next">
        <v-container>
            <h2>Email</h2>
            <v-row>
                <v-col lg="8">
                    <v-row>
                        <v-col cols="6">
                            <v-text-field
                                v-model="email"
                                :disabled="emailExists"
                                :rules="[rules.required, rules.email]"
                                label="Email"
                            />
                        </v-col>
                        <v-col v-if="emailExists" cols="6">
                            <v-text-field
                                v-model="emailCode"
                                :rules="[rules.required]"
                                label="Email Code"
                                placeholder="Enter code from email"
                                :error-messages="validEmailCode ? null : 'Invalid email code'"
                            />
                        </v-col>
                    </v-row>
                    <v-row v-if="emailExists">
                        <v-col cols="6">
                            <p>
                                Email account already exists but customer has no dealer link. Please have the customer
                                check their email and enter the code from their email.
                            </p>
                            <p>
                                <small>
                                    Please have the customer check their junk folder if the email cannot be found.
                                </small>
                            </p>
                            <p>
                                <v-btn color="info" small text @click="resendValidationCode">
                                    Resend Verification Email
                                </v-btn>
                            </p>
                        </v-col>
                    </v-row>
                </v-col>
                <v-col lg="4">
                    <div class="text-center">
                        <div style="margin-top: 20px">
                            <v-icon style="font-size: 180px" color="grey lighten-3"> mdi-email </v-icon>
                        </div>
                    </div>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-btn color="primary" @click="next">Next</v-btn>
                </v-col>
            </v-row>
        </v-container>
    </v-form>
</template>

<script>
import _ from "lodash";
import api from "@/util/api";

export default {
    props: {
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            valid: true,
            email: "",
            emailCode: "",
            validEmailCode: true,
            emailExists: false,
            user: null,
            rules: {
                required: (value) => !!value || "Required.",
                email: (value) => {
                    const pattern =
                        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                    return pattern.test(value) || "Invalid e-mail.";
                },
            },
        };
    },
    methods: {
        resendValidationCode() {
            api.post("/users/validate-email-code", { email: this.email }).then(() => {
                this.$toast.success("Verification email has been resent.");
            });
        },

        next() {
            if (this.$refs.form.validate()) {
                if (this.emailExists) {
                    this.validEmailCode = true;
                    if (_.isNil(this.emailCode) || this.emailCode === "") {
                        this.validEmailCode = false;
                    }

                    if (this.emailCode.length < 4) {
                        this.validEmailCode = false;
                    }

                    return api
                        .get("/users/validate-email-code", {
                            email: this.email,
                            code: this.emailCode,
                        })
                        .then(() => {
                            this.validEmailCode = true;
                            this.$emit("next", {
                                ...this.user,
                                email: this.email,
                            });
                        })
                        .catch(() => {
                            this.validEmailCode = false;
                        });
                } else {
                    api.get(`/dealer/${this.dealerId}/validate-email`, {
                        email: this.email,
                    })
                        .then(this.handleValidateEmail)
                        .catch(() => {
                            this.emailExists = false;
                            this.$emit("next", {
                                ...this.user,
                                email: this.email,
                            });
                        });
                }
            }
        },

        handleValidateEmail(response) {
            this.user = _.get(response, "data.user");
            const dealerLinkExists = _.get(response, "data.dealerLinkExists");

            if (dealerLinkExists) {
                // todo fix this
                // document.location = `/stratus/dealer/${this.dealerId}/warranty/select-customer/${this.user.id}`;
                console.error("Not Implemented: Dealer Link Exists.  See code for todo.");
                return;
            }

            this.emailExists = true;
            api.post("/users/validate-email-code", {
                email: this.email,
            });
        },
    },
};
</script>
