<template>
    <v-container>
        <v-skeleton-loader v-if="isLoading" type="table" />
        <v-card v-else-if="!showUpgradePremiums && existingWarrantyContractsSize > 1">
            <v-card-text>
                <p class="text-success">
                    <strong>*** This user has already purchased an Upgrade/VSC premium. *** </strong>
                </p>
            </v-card-text>
        </v-card>
        <v-card v-else-if="!showUpgradePremiums && existingWarrantyContractsSize === 1">
            <v-card-text>
                <p class="text-danger">
                    <strong> *** Upgrade/VSC premiums are not available at this time. **** </strong>
                </p>
            </v-card-text>
        </v-card>
        <premiums-list
            v-else-if="showUpgradePremiums"
            :premiums="premiumUpgrades"
            :selectable="true"
            title="Premium Upgrades"
            @next="goToUpgradeCustomer"
        />
    </v-container>
</template>
<script>
import { call, get, sync } from "vuex-pathify";
import PremiumsList from "@/modules/Warranty/components/PremiumsList";

export default {
    name: "PremiumUpgrades",
    components: { PremiumsList },
    computed: {
        selectedPremiums: sync("eRating/selectedPremiums"),
        isLoading: get("eRating/<EMAIL>"),
        existingWarrantyContractsSize() {
            return _.get(this.existingWarrantyContracts, "length", 0);
        },
        existingWarrantyContracts: get("eRating/<EMAIL>"),
        premiumUpgrades: get("eRating/<EMAIL>"),
        showUpgradePremiums: get("eRating/<EMAIL>"),
        premiumsRequestParams: get("eRating/<EMAIL>"),
        dealerId: get("eRating/dealer@id"),
        userId: get("eRating/customer@id"),
    },
    methods: {
        selectUpgradePremium: call("eRating/selectUpgradePremium"),
        goToUpgradeCustomer(selected) {
            if (!selected) {
                console.trace("Error: expected a selected of type Array[{}] instead received", selected);
                return;
            }

            this.selectedPremiums = selected;
            this.selectUpgradePremium().then(() => {
                this.$router.push({
                    path: `/dealer/warranty/${this.userId}/register-applicant/lifetime-warranty`,
                    query: { dealerIds: this.dealerId },
                });
            });
        },
    },
};
</script>
<style lang="scss">
.v-data-table-header th {
    white-space: nowrap !important;
}
</style>
