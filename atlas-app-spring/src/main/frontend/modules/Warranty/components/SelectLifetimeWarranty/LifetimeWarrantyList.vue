<template>
    <premiums-list :premiums="premiums" :selectable="true" title="Lifetime Warranty" @next="goToEnrollCustomer" />
</template>
<script>
import { get, sync, call } from "vuex-pathify";
import PremiumsList from "@/modules/Warranty/components/PremiumsList";

export default {
    name: "LifetimeWarrantyList",
    components: { PremiumsList },
    computed: {
        selectedPremiums: sync("eRating/selectedPremiums"),
        premiums: get("eRating/premiums"),
        dealerId: get("eRating/dealer@id"),
        userId: get("eRating/customer@id"),
    },
    methods: {
        selectLifetimeWarranty: call("eRating/selectLifetimeWarranty"),
        goToEnrollCustomer(selected) {
            if (!selected) {
                console.trace("Error: expected a selected of type Array[{}] instead received", selected);
                return;
            }
            this.selectedPremiums = selected;
            this.selectLifetimeWarranty().then(() => {
                this.$router.push({
                    path: `/dealer/warranty/${this.userId}/register-applicant/lifetime-warranty`,
                    query: { dealerIds: this.dealerId },
                });
            });
        },
    },
};
</script>
<style lang="scss">
.v-data-table-header th {
    white-space: nowrap !important;
}
</style>
