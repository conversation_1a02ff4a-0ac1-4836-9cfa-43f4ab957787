<template>
    <v-form
        ref="allCardsForm"
        v-model="isValidForm"
        lazy-validation
        class="container return-policy-page-container container--fluid"
    >
        <!-- Alert dialog for unsaved changes -->
        <v-dialog v-model="showUnsavedChangesDialog" max-width="500px">
            <v-card>
                <v-card-title class="headline">Unsaved Changes</v-card-title>
                <v-card-text> You have unsaved changes. Do you want to proceed without saving? </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="blue darken-1" text @click="cancelProgramChange">Cancel</v-btn>
                    <v-btn color="red darken-1" text @click="confirmProgramChange">Proceed</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Alert dialog for vehicle type selection -->
        <v-dialog v-model="showVehicleTypeAlert" max-width="500px">
            <v-card>
                <v-card-title class="headline">Vehicle Type Selection</v-card-title>
                <v-card-text> At least one vehicle type needs to be selected </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="red darken-1" text @click="showVehicleTypeAlert = false">Close</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>

        <!-- Loading overlay -->
        <v-overlay :value="isLoading || dealerProgramsLoader.isLoading" z-index="8">
            <div class="d-flex flex-column justify-center align-center">
                <v-progress-circular :size="50" color="primary" indeterminate class="mb-2" />
                <h1 class="white--text">Loading</h1>
            </div>
        </v-overlay>

        <!-- Success Message -->
        <v-snackbar v-model="showSuccessMessage" color="success" timeout="3000">
            Return Policy saved and published successfully!
        </v-snackbar>

        <!-- Error Message -->
        <v-snackbar v-model="showErrorMessage" color="error" timeout="3000">
            Failed to save and publish Return Policy. Please try again.
        </v-snackbar>

        <v-card outlined class="mb-3">
            <div class="d-flex flex-column flex-md-row align-center justify-space-between pa-3">
                <div class="d-flex flex-column flex-lg-row justify-center align-center align-md-start">
                    <div class="rp-label mb-3 mb-lg-0 mt-lg-1">Customize Return Policy</div>
                    <div class="ml-0 ml-lg-6 mb-3 mb-lg-0">
                        <v-select
                            v-if="!dealerProgramsLoader.isLoading"
                            v-model="selectedProgram"
                            :items="programOptions"
                            label="Select Program"
                            item-text="text"
                            item-value="value"
                            outlined
                            dense
                            :rules="[programRequiredRule]"
                            :class="{
                                'program-select-50': programTouched && !selectedProgram,
                                'program-select-45': !programTouched || selectedProgram,
                            }"
                            @change="checkForUnsavedChanges($event)"
                            @blur="programTouched = true"
                        ></v-select>
                    </div>
                </div>
                <div>
                    <v-btn
                        :disabled="!hasWebsiteReturnPolicyEditAccess || !isValidForm || !selectedProgram"
                        color="primary"
                        @click="checkBoxesRule"
                    >
                        SAVE AND PUBLISH
                    </v-btn>
                </div>
            </div>
        </v-card>

        <v-container class="pa-0 ma-0" fluid>
            <v-row no-gutters>
                <v-col cols="12" md="6" class="pr-md-3 pb-3 pb-md-0">
                    <v-card class="pa-3">
                        <div class="rp-label mb-4">Preview</div>
                        <v-card outlined class="pa-3">
                            <div class="mb-1">Banner Preview</div>
                            <v-divider class="divider mb-3" />

                            <v-card
                                v-if="formData.isActive && selectedProgram !== null"
                                outlined
                                class="preview-card-policy pa-3 mb-3"
                            >
                                <div class="d-flex justify-center pa-2">
                                    <car-shield-s-v-g class="shield"></car-shield-s-v-g>
                                    <h2 class="text-center">{{ formData.days }} - Day {{ formData.policyName }}</h2>
                                </div>
                                <div class="d-flex cta-card-title font-weight-bold center justify-center">
                                    {{ formData.policySubTitle }}
                                </div>
                                <div class="d-flex cta-card-title justify-center">
                                    Provided by
                                    <div class="font-weight-bold font-italic dealer-name">
                                        {{ dealerName }}
                                    </div>
                                </div>
                                <br />
                                <a class="d-flex cta-card-title justify-center learn-more-link">Learn More</a>
                            </v-card>
                            <v-alert v-else-if="selectedProgram === null" type="info" text>
                                Please select a program to see the preview
                            </v-alert>
                            <v-alert v-else text> Return policy is currently inactive for this program.</v-alert>

                            <div class="mb-1">Modal Preview</div>
                            <v-divider class="divider mb-3" />

                            <v-card
                                v-if="formData.isActive && selectedProgram !== null"
                                outlined
                                class="preview-card-policy pa-3"
                            >
                                <div class="d-flex justify-center pa-2">
                                    <CarIcon class="carIcon"></CarIcon>
                                </div>
                                <div class="d-flex justify-center pa-2">
                                    <car-shield-s-v-g class="shield"></car-shield-s-v-g>
                                    <h2 class="text-center">{{ formData.days }} - Day {{ formData.policyName }}</h2>
                                </div>
                                <div class="d-flex cta-card-title font-weight-bold center justify-center">
                                    {{ formData.policySubTitle }}
                                </div>
                                <div class="d-flex cta-card-title justify-center">
                                    Provided by
                                    <div class="font-weight-bold font-italic dealer-name">
                                        {{ dealerName }}
                                    </div>
                                </div>
                                <br />
                                <div class="d-flex justify-center pa-2">
                                    <hr class="hr" />
                                </div>
                                <div class="d-flex justify-center pa-2">
                                    <v-container fluid>
                                        <v-textarea
                                            v-model="formData.policyDescription"
                                            no-resize
                                            variant="solo"
                                            type="text"
                                            no-input
                                        ></v-textarea>
                                    </v-container>
                                </div>
                                <div class="text-center">
                                    <a
                                        v-if="formData.isActiveDisclosureLink"
                                        :href="formData.disclosureLink"
                                        target="_blank"
                                        class="disclosure-link"
                                        >Read Dealer’s Return Policy</a
                                    >
                                </div>
                            </v-card>
                            <v-alert v-else-if="selectedProgram === null" type="info" text>
                                Please select a program to see the preview
                            </v-alert>
                            <v-alert v-else text> Return policy is currently inactive for this program. </v-alert>
                        </v-card>
                    </v-card>
                </v-col>
                <v-col cols="12" md="6">
                    <v-card outlined class="cta-forms-card pa-3">
                        <div class="mb-3">
                            Use the toggle switch and forms below to activate and customize your Return Policy as shown
                            on Buy@Home.
                        </div>
                        <div class="font-weight-bold mb-3 pa-2 disclaimer">
                            Consult with your legal counsel in the development of your Return Policy Program; the Policy
                            Name, and the Subtitle/tagline.
                        </div>
                        <v-card outlined class="cta-card pa-3 mb-3">
                            <div class="d-flex justify-space-between">
                                <div class="custom-ml-8">
                                    <div class="cta-card-title font-weight-bold">Vehicle Return Policy</div>
                                    <div class="cta-card-subtitle">
                                        Give users peace of mind with the ability to return their vehicle
                                    </div>
                                </div>
                                <div>
                                    <v-switch v-model="formData.isActive" :disabled="!selectedProgram"></v-switch>
                                </div>
                            </div>
                            <v-divider class="mb-3" />

                            <div>
                                <div class="label font-weight-bold">
                                    Select the vehicle types you want to apply the Vehicle Return Policy to
                                </div>
                                <div class="ml-3 d-flex flex-column">
                                    <div class="form-check py-2">
                                        <input
                                            id="flexCheckDefault"
                                            v-model="formData.vehicleType.isNewVehicle"
                                            :disabled="!formData.isActive"
                                            class="form-check-input mr-2"
                                            type="checkbox"
                                            value=""
                                        />
                                        <label class="form-check-label" for="flexCheckDefault"> New Vehicle </label>
                                    </div>
                                    <div class="form-check py-2">
                                        <input
                                            id="flexCheckChecked2"
                                            v-model="formData.vehicleType.isUsedVehicle"
                                            :disabled="!formData.isActive"
                                            class="form-check-input mr-2"
                                            type="checkbox"
                                            value=""
                                            checked
                                        />
                                        <label class="form-check-label" for="flexCheckChecked2"> Used Vehicle </label>
                                    </div>
                                    <div class="form-check py-2">
                                        <input
                                            id="flexCheckChecked3"
                                            v-model="formData.vehicleType.isCPOVehicle"
                                            :disabled="!formData.isActive"
                                            class="form-check-input mr-2"
                                            type="checkbox"
                                            value=""
                                            checked
                                        />
                                        <label class="form-check-label" for="flexCheckChecked3"> CPO Vehicle </label>
                                    </div>
                                </div>
                            </div>
                            <div class="cta-card-title font-weight-bold py-2">Customize your Return Policy</div>

                            <v-row no-gutters>
                                <v-col cols="12" sm="5" class="pa-0 ma-0">
                                    <div class="mb-2 ml-1 mt-sm-2"># of Days for Return Policy</div>
                                </v-col>
                                <v-col cols="12" sm="7" class="pa-0 ma-0">
                                    <div class="d-flex days-input-block">
                                        <div class="required-asterisk">*</div>
                                        <v-text-field
                                            ref="daysField"
                                            v-model="formData.days"
                                            :disabled="!formData.isActive"
                                            :rules="[numberRule]"
                                            clearable
                                            outlined
                                            label="Number of days"
                                            type="number"
                                            hint="Max 90 Days"
                                            persistent-hint
                                            dense
                                        ></v-text-field>
                                    </div>
                                </v-col>
                            </v-row>

                            <v-row no-gutters>
                                <v-col cols="12" sm="5" class="pa-0 ma-0">
                                    <div class="mb-2 ml-1 mt-sm-2">Return Policy Name</div>
                                </v-col>
                                <v-col cols="12" sm="7" class="pa-0 ma-0">
                                    <div class="d-flex">
                                        <div class="required-asterisk">*</div>
                                        <v-text-field
                                            v-model="formData.policyName"
                                            :disabled="!formData.isActive"
                                            :rules="policyNameRule"
                                            counter
                                            max="19"
                                            clearable
                                            outlined
                                            label="Add return policy name"
                                            type="input"
                                            hint="Max 20 characters"
                                            persistent-hint
                                            :maxlength="20"
                                            dense
                                        ></v-text-field>
                                    </div>
                                </v-col>
                            </v-row>

                            <v-row no-gutters>
                                <v-col cols="12" sm="5" class="pa-0 ma-0">
                                    <div class="mb-2 ml-1 mt-sm-2">Sub-Title</div>
                                </v-col>
                                <v-col cols="12" sm="7" class="pa-0 ma-0">
                                    <div class="d-flex">
                                        <div class="required-asterisk">*</div>
                                        <v-text-field
                                            v-model="formData.policySubTitle"
                                            :disabled="!formData.isActive"
                                            :rules="policySubTitleRule"
                                            clearable
                                            counter
                                            outlined
                                            label="Add Return Policy Sub-title"
                                            type="input"
                                            hint="Max 60 characters"
                                            persistent-hint
                                            :maxlength="60"
                                            dense
                                        ></v-text-field>
                                    </div>
                                </v-col>
                            </v-row>

                            <v-row no-gutters>
                                <v-col cols="12" sm="5" class="pa-0 ma-0">
                                    <div class="mb-2 ml-1 mt-sm-2">
                                        Description <br />
                                        This information will appear in a modal after a user selects "Learn More".
                                    </div>
                                </v-col>
                                <v-col cols="12" sm="7" class="pa-0 ma-0">
                                    <div class="d-flex">
                                        <div class="required-asterisk">*</div>
                                        <v-textarea
                                            v-model="formData.policyDescription"
                                            :disabled="!formData.isActive"
                                            :rules="policyDescriptionRule"
                                            counter
                                            :maxlength="500"
                                            clearable
                                            no-resize
                                            rows="1"
                                            height="250px"
                                            outlined
                                            label="Add Return Policy Description"
                                            type="input"
                                            hint="Max 500 characters"
                                            persistent-hint
                                        ></v-textarea>
                                    </div>
                                </v-col>
                            </v-row>

                            <v-divider class="mb-3" />

                            <div class="d-flex justify-space-between align-center">
                                <div class="custom-ml-8">
                                    <div class="cta-card-title font-weight-bold">
                                        Include the URL to your website's Vehicle Return disclosures
                                    </div>
                                </div>
                                <div>
                                    <v-switch
                                        v-model="formData.isActiveDisclosureLink"
                                        :disabled="!selectedProgram"
                                    ></v-switch>
                                </div>
                            </div>

                            <v-row no-gutters>
                                <v-col cols="12" sm="5" class="pa-0 ma-0">
                                    <div class="mb-2 ml-1 mt-sm-2">Disclosure Link</div>
                                </v-col>
                                <v-col cols="12" sm="7" class="pa-0 ma-0">
                                    <div class="d-flex">
                                        <div class="required-asterisk">*</div>
                                        <v-text-field
                                            v-model="formData.disclosureLink"
                                            :disabled="!formData.isActiveDisclosureLink"
                                            :rules="urlValidation"
                                            outlined
                                            clearable
                                            label="Add URL for Disclosure"
                                            type="input"
                                            dense
                                        ></v-text-field>
                                    </div>
                                </v-col>
                            </v-row>
                        </v-card>
                    </v-card>
                </v-col>
            </v-row>
        </v-container>
    </v-form>
</template>

<script>
import { call, get } from "vuex-pathify";
import _ from "lodash";
import CarShieldSVG from "@/modules/ReturnPolicy/views/CarShieldSVG";
import CarIcon from "@/modules/ReturnPolicy/views/CarIcon";
import api from "Util/api";
import isNil from "lodash/isNil";

const MAX_CHARACTER_LENGTH = 57;
const MAX_CHARACTER_LENGTH_RETURN_POLICY_NAME = 19;
const MAX_DESCRIPTION_LENGTH = 800;
const MAX_DAYS = 90;
const MAX_NAME_CHARACTERS = 20;
const MAX_SUBTITLE_CHARACTERS = 60;
const MAX_DESCRIPTION_CHARACTERS = 500;

export default {
    components: { CarShieldSVG, CarIcon },
    data() {
        return {
            formData: {
                dealerId: this.dealerId,
                isActive: false,
                vehicleType: {
                    isNewVehicle: false,
                    isUsedVehicle: false,
                    isCPOVehicle: false,
                },
                days: null,
                policyDescription: null,
                policyName: null,
                policySubTitle: null,
                isActiveDisclosureLink: false,
                disclosureLink: null,
            },
            selectedProgram: this.loggedInSelectedProgram || null,
            originalFormData: null,
            hasUnsavedChanges: false,
            currentProgramId: null,
            showUnsavedChangesDialog: false,
            showVehicleTypeAlert: false,
            pendingProgramId: null,
            numberRule: (v) => {
                if (!v || !String(v).trim()) {
                    return "Number is required";
                }
                if (isNaN(parseFloat(v)) || v < 0 || v > MAX_DAYS) {
                    return "Number has to be between 0 and " + MAX_DAYS;
                }
                return true;
            },
            policyNameRule: [
                (v) => !!v || "A name is required",
                (v) => {
                    const result =
                        (v && v.length <= MAX_NAME_CHARACTERS) ||
                        "The name must be less than " + MAX_NAME_CHARACTERS + " characters";
                    return result;
                },
            ],
            policySubTitleRule: [
                (v) => !!v || "A subtitle is required",
                (v) => {
                    const result =
                        (v && v.length <= MAX_SUBTITLE_CHARACTERS) ||
                        "The policy subtitle must be less or equal than " + MAX_SUBTITLE_CHARACTERS + " characters";
                    return result;
                },
            ],
            policyDescriptionRule: [
                (v) => !!v || "A description is required",
                (v) => {
                    const result =
                        (v && v.length <= MAX_DESCRIPTION_CHARACTERS) ||
                        "The description must be less or equal than " + MAX_DESCRIPTION_CHARACTERS + " characters";
                    return result;
                },
            ],
            urlValidation: [(value) => !!value || "Required.", (value) => this.isURL(value) || "URL is not valid"],
            isLoading: false,
            isValidForm: true,
            types: ["text", "number", "email", "password", "search", "url", "tel", "date", "time", "range", "color"],
            maxChars: 20,
            programTouched: false,
            programRequiredRule: (v) => !!v || "Please select a program to save and publish",
            showSuccessMessage: false,
            showErrorMessage: false,
        };
    },
    computed: {
        dealerPrograms: get("returnPolicy/dealerPrograms@data"),
        dealerProgramsLoader: get("returnPolicy/dealerPrograms@loader"),
        loggedInSelectedProgram: get("loggedInUser/selectedProgram"),
        userDealerAccessList: get("loggedInUser/userDealerAccessList"),
        dealerId() {
            let result = undefined;

            if (this.$route.params.dealerId) {
                result = this.$route.params.dealerId;
            } else if (this.$route.query.dealerIds) {
                result = this.$route.query.dealerIds;
            }

            return result;
        },
        hasWebsiteReturnPolicyEditAccess() {
            const result = this.$acl.hasDealerPermission(this.dealerId, "return-policy:edit");
            return result;
        },
        dealerName() {
            const locatedDealer = _.find(this.userDealerAccessList, ["id", this.dealerId]);
            const result = !_.isNil(locatedDealer) ? locatedDealer.name : "";
            return result;
        },
        programOptions() {
            return [{ text: "Select Program", value: null }, ...this.dealerPrograms];
        },
    },
    watch: {
        formData: {
            deep: true,
            handler(newVal) {
                this.hasUnsavedChanges = !_.isEqual(newVal, this.originalFormData);
            },
        },
    },
    created() {
        this.getDealerPrograms(this.dealerId);
    },
    mounted() {
        this.$nextTick(() => {
            this.$refs.daysField.validate(true);
        });
    },
    methods: {
        fetchPolicyData: call("returnPolicy/fetchReturnPolicyData"),
        doSaveAndPublish: call("returnPolicy/saveAndPublish"),
        getDealerPrograms: call("returnPolicy/getDealerPrograms"),
        saveAndPublish() {
            this.isLoading = true;
            this.formData.dealerId = this.dealerId;
            this.formData.programId = this.selectedProgram;
            this.doSaveAndPublish(this.formData)
                .then(() => {
                    this.hasUnsavedChanges = false;
                    this.originalFormData = _.cloneDeep(this.formData);
                    this.showSuccessMessage = true;
                })
                .catch(() => {
                    this.showErrorMessage = true;
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        checkBoxesRule() {
            const vehicleTypeList = [];
            vehicleTypeList[0] = this.formData.vehicleType.isNewVehicle;
            vehicleTypeList[1] = this.formData.vehicleType.isUsedVehicle;
            vehicleTypeList[2] = this.formData.vehicleType.isCPOVehicle;

            if (vehicleTypeList.includes(true)) {
                this.saveAndPublish();
            } else {
                this.showVehicleTypeAlert = true;
            }
        },
        checkForUnsavedChanges(newProgramId) {
            this.programTouched = true;
            if (this.hasUnsavedChanges) {
                this.pendingProgramId = newProgramId;
                this.showUnsavedChangesDialog = true;
                // Immediately restore the previous program selection in the UI
                this.$nextTick(() => {
                    this.selectedProgram = this.currentProgramId;
                });
            } else {
                this.currentProgramId = newProgramId;
                this.loadReturnPolicy();
            }
        },
        cancelProgramChange() {
            this.showUnsavedChangesDialog = false;
            this.pendingProgramId = null;
        },
        confirmProgramChange() {
            this.showUnsavedChangesDialog = false;
            this.selectedProgram = this.pendingProgramId;
            this.currentProgramId = this.pendingProgramId;
            this.pendingProgramId = null;
            this.loadReturnPolicy();
        },
        loadReturnPolicy() {
            let path = `/return-policy/${this.dealerId}?programId=${this.selectedProgram}`;
            this.isLoading = true;
            const result = api
                .get(path)
                .then((response) => {
                    console.log("Values loaded: " + response);
                    // Initialize with empty return policy data
                    this.formData = {
                        dealerId: this.dealerId,
                        isActive: false,
                        programId: this.selectedProgram, // Make sure programId is set for new policies
                        vehicleType: {
                            isNewVehicle: false,
                            isUsedVehicle: false,
                            isCPOVehicle: false,
                        },
                        days: null,
                        policyDescription: null,
                        policyName: null,
                        policySubTitle: null,
                        isActiveDisclosureLink: false,
                        disclosureLink: null,
                    };

                    // Only populate with response data if it exists
                    if (!isNil(response) && _.size(response.data) > 0) {
                        this.formData = response.data;
                    }

                    // Store original form data to track changes
                    this.originalFormData = _.cloneDeep(this.formData);
                    this.hasUnsavedChanges = false;
                    this.currentProgramId = this.selectedProgram;
                })
                .catch((error) => {
                    console.log(error);
                    // Also initialize with empty return policy data on error
                    this.formData = {
                        dealerId: this.dealerId,
                        isActive: false,
                        programId: this.selectedProgram, // Make sure programId is set for new policies
                        vehicleType: {
                            isNewVehicle: false,
                            isUsedVehicle: false,
                            isCPOVehicle: false,
                        },
                        days: null,
                        policyDescription: null,
                        policyName: null,
                        policySubTitle: null,
                        isActiveDisclosureLink: false,
                        disclosureLink: null,
                    };
                    this.originalFormData = _.cloneDeep(this.formData);
                })
                .finally(() => {
                    this.isLoading = false;
                });

            return result;
        },
        isURL(str) {
            let url;
            try {
                url = new URL(str);
            } catch (_) {
                return false;
            }
            return url.protocol === "http:" || url.protocol === "https:";
        },
        truncateText(value) {
            const input = value;
            if (input.length > this.maxChars) {
                value = input.slice(0, this.maxChars);
                this.formData.policyName = value;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.return-policy-page-container {
    background-color: $gray-200;
}
.shield {
    margin-right: 10px;
    color: darkred;
    height: 40px;
    width: 40px;
}
.carIcon {
    color: darkred;
    height: 130px;
    width: 130px;
}
.hr {
    overflow: visible;
    height: 0;
    width: 450px;
    border-color: red;
}
.preview-card-policy {
    .border-div {
        border: 1px solid #0000001f;
        border-radius: 3px;
    }
}
.disclaimer {
    background-color: $gray-300;
    border-radius: 3px;
}
.text-spacing {
    margin-top: 20px;
    margin-left: 20px;
}
.rp-label {
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
}
.required-asterisk {
    color: red;
    padding: 5px;
}
.dealer-name {
    margin-left: 2px;
}
.learn-more-link {
    color: #c3002f;
}
.disclosure-link {
    color: #c3002f;
}
.days-field-container {
    margin-right: 282px;
}
.program-select-50 {
    height: 50px;
}
.program-select-45 {
    height: 45px;
}
.program-select-45,
.program-select-50 {
    max-width: 340px;
}
.days-input-block {
    max-width: 175px;
}
</style>
