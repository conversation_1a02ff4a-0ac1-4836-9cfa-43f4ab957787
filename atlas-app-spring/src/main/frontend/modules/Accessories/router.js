import Vue from "vue";
import VueRouter from "vue-router";
import { configureRouter, routerOptions, layout, route } from "Util/routerHelper";

Vue.use(VueRouter);

const PATH_PREFIX = "/dealer/supported-accessories";

const routes = [layout("Default", [route("Accessories", "AccessoriesHome", null, PATH_PREFIX)])];

const router = new VueRouter({
    mode: "history",
    routes,
    ...routerOptions,
});

configureRouter(router);

export default router;
