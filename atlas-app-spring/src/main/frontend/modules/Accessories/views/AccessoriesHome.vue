<template>
    <v-container class="dealer-page-container" fluid>
        <h2 class="mt-5 ml-5">Supported Accessories</h2>
        <div class="mx-5 mt-8">
            <accessories-search-form
                @searchVehicle="searchVehicleHandler"
                @fieldValueChange="fieldValueChangeHandler"
            />
        </div>
        <div v-if="showAccessories" class="mx-5 mt-5">
            <accessories-list
                :accessories-data="accessoriesData"
                :model-line-code="modelLineCode"
                :model-year="modelYear"
            />
        </div>
        <div v-if="!checkSearchedAccessoriesResult && !isLoading" class="mx-5 mt-5">
            Sorry, No data available for this search.
        </div>
        <v-skeleton-loader v-if="isLoading" type="table-tbody" class="mx-5 mt-5"></v-skeleton-loader>
    </v-container>
</template>

<script>
import api from "Util/api";
import AccessoriesSearchForm from "@/modules/Accessories/components/AccessoriesSearch/AccessoriesSearchForm";
import AccessoriesList from "@/modules/Accessories/components/AccessoriesSearch/AccessoriesList";
import { call, get } from "vuex-pathify";
export default {
    components: {
        AccessoriesSearchForm,
        AccessoriesList,
    },
    data() {
        return {
            accessoriesData: [],
            checkSearchedAccessoriesResult: true,
            isLoading: false,
            modelYear: null,
            modelLineCode: null,
        };
    },
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        dealer: get("accessoriesDealerStore/selectedDealer@data"),
        dealerNnaId() {
            return _.get(this.dealer, "nnaDealerId", null);
        },
        showAccessories() {
            return this.accessoriesData && this.accessoriesData.length > 0;
        },
    },
    mounted() {
        this.fetchDealerInfo(this.dealerIds);
    },
    methods: {
        fetchDealerInfo: call("accessoriesDealerStore/fetchDealerInfo"),
        searchVehicleHandler(formObject) {
            this.modelYear = formObject.modelYear;
            this.modelLineCode = formObject.modelCode;

            this.isLoading = true;
            api.get(`/dealer/accessories/${this.dealerNnaId}/${formObject.modelCode}/${formObject.modelYear}`)
                .then((response) => {
                    this.isLoading = false;
                    if (response.data && response.data.length > 0) {
                        this.accessoriesData = response.data;
                        this.checkSearchedAccessoriesResult = true;
                    } else {
                        this.checkSearchedAccessoriesResult = false;
                    }
                })
                .catch((error) => {
                    this.isLoading = false;
                    console.error(error);
                });
        },
        fieldValueChangeHandler() {
            this.accessoriesData = [];
            this.checkSearchedAccessoriesResult = true;
        },
    },
};
</script>

<style lang="scss" scoped>
.dealer-page-container {
    background-color: $gray-200;
}
</style>
