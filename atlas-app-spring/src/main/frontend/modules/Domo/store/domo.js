import loader from "@/util/loader";
import api from "@/util/api";
import { make } from "vuex-pathify";

const initialState = {
    domoModel: {
        data: null,
        loader: loader.defaultState(),
    },
};

const actions = {
    ...make.actions(initialState),

    generateDomoToken({ commit }, params) {
        commit("SET_DOMO_TOKEN_LOADER", loader.started());
        return api
            .post("/domo/generate-token", params)
            .then((response) => {
                const domoToken = response.data;

                commit("SET_DOMO_TOKEN", domoToken);

                commit("SET_DOMO_TOKEN_LOADER", loader.successful());
                return _.get(response, "data");
            })
            .catch((error) => {
                console.error("generate domo token error: ", error);
                commit("SET_DOMO_TOKEN_LOADER", loader.error(error));
                return Promise.reject(error);
            });
    },
};

const mutations = {
    ...make.mutations(initialState),

    SET_DOMO_TOKEN: (state, payload) => {
        state.domoModel.data = payload;
    },

    SET_DOMO_TOKEN_LOADER: (state, payload) => {
        state.domoModel.loader = payload;
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
