<template>
    <search-page :key="dealerIds">
        <div slot="pills">
            <pill-container
                v-if="inShowroomEnabled"
                :pills="inShowroomUsers"
                title="In Showroom"
                pill-color="teal"
                pill-type="inShowroom"
                :loading="inShowroomUsersLoading.isLoading"
            ></pill-container>
            <pill-container
                v-if="onlineNowEnabled"
                :pills="onlineUsers"
                title="Online Now"
                pill-color="green"
                :loading="onlineUsersLoading.isLoading"
            ></pill-container>
        </div>
        <user-search-form slot="searchForm" />
        <user-stage-tiles v-if="displayStageTiles" slot="searchTiles" />
        <user-list slot="searchList" :store="store" class="mt-1" />
        <user-facets slot="searchFacets" />
    </search-page>
</template>
<script>
import { call, get, sync } from "vuex-pathify";
import UserList from "../components/UserSearch/UserList";
import UserSearchForm from "../components/UserSearch/UserSearchForm";
import UserStageTiles from "../components/UserStageTiles";
import UserFacets from "@/modules/Customers/components/UserSearch/UserFacets";
import SearchPage from "Components/Search";
import lodashIsNil from "lodash/isNil";
import lodashGet from "lodash/get";
import PillContainer from "../components/PillContainer";

export default {
    components: {
        SearchPage,
        UserFacets,
        UserSearchForm,
        UserStageTiles,
        UserList,
        PillContainer,
    },
    data: () => ({
        store: "userSearch",
    }),
    computed: {
        onlineUsers: get("userSearch/onlineUsers@data"),
        inShowroomUsers: get("userSearch/inShowroomUsers@data"),
        onlineUsersLoading: get("userSearch/onlineUsers@loader"),
        inShowroomUsersLoading: get("userSearch/inShowroomUsers@loader"),
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        isNewCustomerPageEnabled() {
            return lodashGet(this.featureFlags, "ATLAS_NEW_CUSTOMER_PAGE", false) || false;
        },
        isNewCustomerPageOnlineNowEnabled() {
            return lodashGet(this.featureFlags, "ATLAS_NEW_CUSTOMER_PAGE_ONLINE_NOW", false) || false;
        },
        isNewCustomerPageInShowroomEnabled() {
            return lodashGet(this.featureFlags, "ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM", false) || false;
        },
        onlineNowEnabled() {
            return this.isNewCustomerPageEnabled && this.isNewCustomerPageOnlineNowEnabled;
        },
        inShowroomEnabled() {
            return this.isNewCustomerPageEnabled && this.isNewCustomerPageInShowroomEnabled;
        },
        filterDealerIds: sync("userSearch/filters@dealerIds"),
        programIds() {
            return this.$route.query.programIds === "" ? null : this.$route.query.programIds;
        },
        filterProgramIds: sync("userSearch/filters@programIds"),
        facetPills: sync("userSearch/pills"),
        featureFlags: get("loggedInUser/featureFlags"),

        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
        hasStageTilesPermissionAsDealerUser() {
            if (!this.$acl.hasAuthority("ROLE_DEALER")) {
                return false;
            }

            if (lodashIsNil(this.dealerIds)) {
                return false;
            }

            if (this.isEnableStageTilesForDealers || this.isDealerHasStageTilePermission) {
                return true;
            }

            return false;
        },
        hasStageTilesPermissionAsAdminUser() {
            return !!(this.$acl.hasAuthority("ROLE_ADMIN") && this.isEnableStageTilesForAdmin);
        },
        displayStageTiles() {
            return (
                this.isProgramUser ||
                this.hasStageTilesPermissionAsDealerUser ||
                this.hasStageTilesPermissionAsAdminUser
            );
        },
        isEnableStageTilesForDealers() {
            return lodashGet(this.featureFlags, "ENABLE_STAGE_TILES_FOR_DEALERS", false) || false;
        },
        isEnableStageTilesForAdmin() {
            return lodashGet(this.featureFlags, "ENABLE_STAGE_TILES_FOR_ADMINS", false) || false;
        },
        isDealerHasStageTilePermission() {
            return this.$acl.hasDealerPermission(this.dealerIds, "customer:stage-tiles");
        },
    },
    watch: {
        dealerIds(value) {
            this.filterDealerIds = value;
            if (this.onlineNowEnabled) {
                this.fetchOnlineUsers(this.filterDealerIds);
            }
            if (this.inShowroomEnabled) {
                this.fetchInShowroomUsers(this.filterDealerIds);
            }
        },
        programIds(value) {
            this.filterProgramIds = value;
            if (this.onlineNowEnabled) {
                this.fetchOnlineUsers(this.filterDealerIds);
            }
            if (this.inShowroomEnabled) {
                this.fetchInShowroomUsers(this.filterDealerIds);
            }
        },
    },
    created() {
        this.$store.commit("userSearch/SET_PAGE", 1);
        this.$store.commit("userSearch/SET_STAGE_TITLE", null);

        this.setSearchUri(`/users/v2`);

        this.disableProgramFacetPillForUpgradeUser();
        if (this.onlineNowEnabled) {
            this.fetchOnlineUsers(this.filterDealerIds);
        }
        if (this.inShowroomEnabled) {
            this.fetchInShowroomUsers(this.filterDealerIds);
        }
    },
    methods: {
        setSearchUri: call("userSearch/setSearchUri"),
        fetchOnlineUsers: call("userSearch/fetchOnlineUsers"),
        fetchInShowroomUsers: call("userSearch/fetchInShowroomUsers"),
        disableProgramFacetPillForUpgradeUser() {
            this.facetPills["programs"] = { enabled: !this.isProgramUser };
        },
    },
};
</script>
