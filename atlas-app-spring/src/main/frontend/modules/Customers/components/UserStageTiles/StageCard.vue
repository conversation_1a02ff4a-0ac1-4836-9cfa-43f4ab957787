<template>
    <v-card class="stage-card flex-nowrap" v-on="allowClickAction ? { click: executeFilter } : {}">
        <v-container class="card-top" :class="stageClass">
            <span>{{ displayTitle }}</span>
            <user-stage-tiles-tool-tip :stage="stageTitle" />
        </v-container>
        <v-container class="card-bottom">
            <p class="total-text">{{ stageTotal }}</p>
            <span class="percentage-text">{{ stageTotalPercentage }}</span>
        </v-container>
    </v-card>
</template>

<script>
import { call, get } from "vuex-pathify";
import _ from "lodash";
import UserStageTilesToolTip from "./UserStageTilesToolTip";
import { FACET_TYPE } from "Util/searchUtils";
import lodashGet from "lodash/get";
import { renameDisplayValue } from "@/util/renameUtils";

export default {
    name: "StageCard",
    components: { UserStageTilesToolTip },
    props: {
        stage: {
            type: Object,
            required: true,
        },
    },
    computed: {
        displayTitle() {
            return renameDisplayValue(this.stageTitle);
        },
        stageTitle() {
            return _.keys(this.stage)[0];
        },
        stageTotal() {
            return this.stage[this.stageTitle].total;
        },
        stageTotalPercentage() {
            return this.stage[this.stageTitle].percentage;
        },
        stageClass() {
            if (this.stageTitle === "Out Of Process") {
                return "oop";
            } else if (this.stageTitle === "Sold") {
                return "post-sale";
            } else {
                return "default";
            }
        },
        allowClickAction() {
            return this.stageTitle !== "Prospect";
        },
        featureFlags: get("loggedInUser/featureFlags"),
    },
    methods: {
        executeFilter(event) {
            this.addPositiveFilter();
        },
        addPositiveFilter() {
            let stage = {
                filterName: "stages",
                facetId: this.stageTitle,
                facetType: FACET_TYPE.MULTIPLE,
                clear: true,
            };
            this.$store.dispatch(`userSearch/addPositiveFilter`, stage);
        },
        getStageRecords(event) {
            this.$store.commit("userSearch/SET_PAGE", 1);
            this.$store.commit("userSearch/SET_STAGE_TITLE", this.stageTitle);
            this.fetchStageRecords();
        },
        fetchStageRecords: call("userSearch/fetchStageRecords"),
    },
};
</script>

<style lang="scss" scoped>
.stage-card {
    border: 1px solid var(--v-secondary-base);
    .card-top {
        padding: 6px 0px !important;
        justify-content: center;
        color: white;
        text-align: center;
        display: flex;
        &.default {
            background-color: var(--v-primary-base);
        }
        &.oop {
            background-color: var(--v-error-darken1);
        }
        &.post-sale {
            background-color: var(--v-success-base);
        }
        span {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            @media (max-width: 1903px) {
                max-width: 100px;
            }
        }
    }
    .card-bottom {
        text-align: center;
        .total-text {
            font-size: px2rem(24);
            margin-bottom: 0;
            font-weight: 500;
        }
        .percentage-text {
            color: grey;
        }
    }
}
</style>
