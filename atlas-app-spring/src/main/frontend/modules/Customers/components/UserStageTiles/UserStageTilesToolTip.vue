<template>
    <span class="pl-2" style="white-space: nowrap"
        ><info-tooltip color="white" size="20" :max-width="260">
            <p class="body-1 mb-0">{{ getRankedStageMessage(stage) }}</p>
        </info-tooltip>
    </span>
</template>

<script>
import InfoTooltip from "Components/InfoTooltip";

export default {
    name: "UserStageTilesToolTip",
    components: { InfoTooltip },
    props: {
        stage: {
            type: String,
            required: true,
        },
    },
    methods: {
        getRankedStageMessage(stage) {
            let message = "";

            switch (stage) {
                case "Out Of Process":
                    message =
                        "Created a profile, logged in within last 60 days. Also encountered an issue with the pre-qual process, finance application, or password reset process.";
                    break;
                case "Prospect":
                    message = "Imported into the system, but not identified for marketing.";
                    break;
                case "Eligible Prospect":
                    message = "Qualifies for one or more marketing campaigns.";
                    break;
                case "Explorer":
                    message =
                        "Created a profile, logged in within last 60 days, has no Active Vehicles in their Garage.";
                    break;
                case "Shopper":
                    message =
                        "Created a profile, logged in within last 60 days, has at least one Active Vehicle in their Garage.";
                    break;
                case "Intender":
                    message =
                        "Created a profile, logged in within last 60 days, has initiated contact with the dealer.";
                    break;
                case "Buyer":
                    message = "Created a profile, logged in within last 60 days, submitted a Finance Application.";
                    break;
                case "Post Sale":
                    message = "Created a profile, logged in within last 60 days, completed a vehicle purchase.";
                    break;
                case "Long Term Follow Up":
                    message = "Hasn't logged into the platform for 60+ days.";
                    break;
                default:
                    message = "You are currently in the " + stage + " stage";
            }

            return message;
        },
    },
};
</script>

<style scoped></style>
