<template>
    <div>
        <v-data-table
            :loading="isLoading"
            :items="contractRequests"
            :headers="getUpdatedHeaders()"
            no-data-text="No Contract Requests found for this user"
        >
            <template #item.actions="{ item }">
                <v-menu offset-y>
                    <template #activator="{ on, attrs }">
                        <v-icon small class="action-icon" v-bind="attrs" v-on="on"> mdi-dots-vertical </v-icon>
                    </template>
                    <v-list>
                        <v-list-item @click="openDialog(item)">
                            <v-list-item-title>Direct Link</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
            </template>
            <template #item.createdDate="{ item }">
                {{ item.createdDate | formatEpochDate }}
            </template>
            <template #item.vehicleDeliveryMethod="{ item }">
                {{ renderDeliveryTitle(item.vehicleDeliveryMethod) }}
            </template>
            <template #item.stockType="{ item }">
                <v-chip small>{{ item.stockType }}</v-chip>
            </template>
            <template #item.vehicle="{ item }">
                {{ getVehicle(item) }}
            </template>
        </v-data-table>
        <direct-app-link-dialog
            v-if="showLinkDialog"
            :certificate-id="certificateId"
            :contract-id="contractId"
            :campaign-id="campaignId"
            :vin="vin"
            link-type="Contract Request"
            :dealer-id="dealerId"
            @close="closeDialog"
        />
    </div>
</template>

<script>
import { call, get } from "vuex-pathify";
import getX from "lodash/get";
import DirectAppLinkDialog from "Modules/Customers/components/UserDetails/DirectAppLinkDialog.vue";

export default {
    name: "ContractRequests",
    components: { DirectAppLinkDialog },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                {
                    value: "createdDate",
                    text: "Created Date",
                    sortable: true,
                },
                {
                    value: "vehicleDeliveryMethod",
                    text: "Type",
                    sortable: true,
                },
                {
                    value: "deliveryLocation",
                    text: "Location",
                    sortable: true,
                },
                { text: "Stock", value: "stockType", sortable: true },
                {
                    text: "Vehicle",
                    value: "vehicle",
                    sortable: true,
                },
                {
                    value: "vin",
                    text: "VIN",
                    sortable: true,
                },
            ],
            showLinkDialog: false,
            certificateId: {
                type: String,
                required: true,
            },
            campaignId: {
                type: String,
                required: true,
            },
            contractId: {
                type: String,
                required: false,
            },
            vin: {
                type: String,
                required: false,
            },
        };
    },
    computed: {
        contractRequestsData: get("userDetails/contractRequests@data"),
        isLoading: get("userDetails/<EMAIL>"),
        featureFlags: get("loggedInUser/featureFlags"),
        contractRequests() {
            return _.get(this.contractRequestsData, "contractRequests", []);
        },
    },
    created() {
        this.fetchUserContractRequests({
            dealerId: this.dealerId,
            userId: this.userId,
        });
    },
    methods: {
        fetchUserContractRequests: call("userDetails/fetchUserContractRequests"),
        renderDeliveryTitle(deliveryMethod) {
            if (deliveryMethod === "DELIVERY") {
                return "Delivery";
            }
            if (deliveryMethod === "PICKUP_AT_DEALERSHIP") {
                return "Pickup";
            }
        },
        getVehicle(item) {
            return _.defaultTo(item.vehicle, "");
        },
        openDialog(item) {
            this.showLinkDialog = true;
            this.campaignId = item.campaignId;
            this.certificateId = item.certificateId;
            this.contractId = item.contractId;
            this.vin = item.vin;
        },
        closeDialog() {
            this.showLinkDialog = false;
        },
        getUpdatedHeaders() {
            return [
                {
                    text: "Actions",
                    value: "actions",
                    sortable: false,
                },
                ...this.headers,
            ];
            return this.headers;
        },
    },
};
</script>
