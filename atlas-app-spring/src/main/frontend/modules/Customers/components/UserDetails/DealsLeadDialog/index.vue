<template>
    <v-dialog v-model="show" :persistent="persistent" max-width="600" @click:outside="reset">
        <v-card>
            <v-card-title>
                <h2 class="headline">Send Lead</h2>
            </v-card-title>
            <v-card-text>
                <v-row>
                    <v-col v-if="errorMessage" cols="12">
                        <v-alert outlined type="error" prominent dense border="left">
                            {{ errorMessage }}
                        </v-alert>
                    </v-col>
                    <v-col>
                        <p class="description">This will send the following information to the dealer's CRM</p>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12">
                        <v-form>
                            <v-textarea
                                v-model="form.message"
                                name="input-7-1"
                                label="Message to Dealer (optional)"
                                hint="Additional information for the dealer..."
                                clearable
                                outlined
                                dense
                            ></v-textarea>
                        </v-form>
                    </v-col>
                </v-row>
                <v-row>
                    <v-col>
                        <p class="description">Please confirm the following information</p>
                    </v-col>
                </v-row>
                <v-row class="details">
                    <v-col>Customer</v-col>
                    <v-col>{{ customerName }}</v-col>
                </v-row>
                <v-row class="details">
                    <v-col>Dealership</v-col>
                    <v-col>{{ dealContent.dealerName }}</v-col>
                </v-row>
                <v-row class="details">
                    <v-col>Vehicle of Interest</v-col>
                    <v-col>{{ vehicle }}</v-col>
                </v-row>
            </v-card-text>
            <v-card-actions class="flex-column">
                <v-btn color="primary" block :loading="loader.isLoading" @click="sendLead">
                    <span> Send </span>
                </v-btn>
                <v-btn text color="secondary" block :disabled="loader.isLoading" @click="hideModal">
                    <span> Cancel </span>
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import EventBus from "Util/eventBus";
import lodashGet from "lodash/get";
import { call, get } from "vuex-pathify";
export default {
    name: "DealsLeadDialog",
    props: {
        dealContent: {
            type: Object,
            required: true,
        },
        customerName: {
            type: String,
            required: true,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            show: false,
            ctaStates: {
                cancel: {
                    loading: false,
                    disabled: false,
                },
                send: {
                    loading: false,
                    disabled: false,
                },
            },
            form: {
                dealerId: null,
                message: null,
                userId: null,
            },
            persistent: false,
        };
    },
    computed: {
        loader: get("userDeal/sendDeal@loader"),
        errorMessage() {
            const errMessage = "Sorry something went wrong. Please try again.";

            return this.loader.isError ? errMessage : null;
        },
        vehicle() {
            const { year, make, model, trim } = this.dealContent;
            const ymmt = [year, make, model, trim].join(" ");

            return ymmt;
        },
        certificateId() {
            return lodashGet(this.dealContent, "certificateId");
        },
    },
    created() {
        EventBus.$on("showSendLeadModal", this.showModal);
        EventBus.$on("hideSendLeadModal", this.hideModal);
    },
    methods: {
        postLead: call("userDeal/postLead"),
        resetSendLead: call("userDeal/resetSendLead"),
        fetchUserLeads: call("userDetails/fetchUserLeads"),
        showModal() {
            this.show = true;
        },
        closeDialog() {
            this.hideModal();
        },
        hideModal() {
            this.show = false;
            this.reset();
        },
        reset() {
            this.form = { message: null };
            this.resetSendLead();
        },
        sendLead() {
            const { dealerId } = this.dealContent;
            this.form.dealerId = dealerId;
            this.form.userId = this.userId;

            this.postLead({ certificateId: this.certificateId, formData: this.form })
                .then(() => {
                    if (this.loader.isError) {
                        console.error("Sending lead failed", this.loader.error);
                        return;
                    }

                    // refresh user leads
                    this.fetchUserLeads({
                        dealerId,
                    });
                    this.hideModal();
                })
                .catch((error) => {
                    console.error("Sending lead failed", error);
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.description {
    font-weight: bold;
    margin: 0;
    padding: 0;
}
.details {
    margin-top: 0;
}
</style>
