<template>
    <v-dialog v-model="show" persistent width="600" @click:outside="closeDialog">
        <div v-if="isLoading">Saving Deal...</div>
        <div v-else>
            <v-card v-if="displayOriginalCopyDialog">
                <!-- Cash Deal save card -->
                <v-card-title>
                    <span class="text-h5">Give this deal a name</span>
                </v-card-title>
                <v-card-text>
                    <v-form ref="copyDealForm" lazy-validation @submit.prevent>
                        <v-text-field
                            ref="copyDealName"
                            v-model="copyDealName"
                            label="Deal Name"
                            outlined
                            required
                            :rules="validationRules.dealName"
                            @keyup.enter="copyDeal"
                        />
                    </v-form>
                </v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary darken-1" text @click="closeDialog"> Cancel </v-btn>
                    <v-btn color="primary darken-1" text @click="copyDeal"> Confirm </v-btn>
                </v-card-actions>
            </v-card>
            <v-card v-else>
                <!-- Lease or Finance Deal save card -->
                <v-card-title>
                    <span class="text-h5">Save Deal</span>
                </v-card-title>
                <v-card-text>
                    <v-form ref="saveDealForm" lazy-validation @submit.prevent>
                        <v-text-field
                            ref="saveDealName"
                            v-model="saveDealName"
                            label="Deal Name"
                            outlined
                            required
                            :rules="validationRules.dealName"
                        />
                        <v-textarea
                            ref="saveDealNote"
                            v-model="saveDealNote"
                            label="Deal Note"
                            rows="3"
                            hide-details
                            outlined
                        />
                        <div class="d-flex">
                            <v-spacer></v-spacer>
                            <v-checkbox
                                ref="saveDealDisplayNote"
                                v-model="saveDealDisplayNote"
                                class="align-end"
                                hide-details
                                label="Display Deal Notes to Customer"
                            />
                        </div>
                    </v-form>
                </v-card-text>
                <v-card-actions>
                    <v-btn color="primary" block @click="saveDeal"> SAVE AS NEW DEAL </v-btn>
                </v-card-actions>
            </v-card>
        </div>
    </v-dialog>
</template>

<script>
import EventBus from "@/util/eventBus";
import _ from "lodash";
import api from "Util/api";
import getX from "lodash/get";
import { get } from "vuex-pathify";

export default {
    name: "CopyDealDialog",
    props: {
        dealerId: {
            type: String,
            required: true,
        },
        userId: {
            type: String,
            required: true,
        },
        dealContent: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            show: true,
            isLoading: false,
            copyDealName: null,
            saveDealName: null,
            saveDealNote: null,
            saveDealDisplayNote: false,
            validationRules: {
                dealName: [(v) => !!v || "Deal Name is required"],
            },
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        displayOriginalCopyDialog() {
            const display = this.isCashDeal && !this.isDealEditingCashFeatureEnabled;
            return display;
        },
        isDealEditingCashFeatureEnabled() {
            const enabled = getX(this.featureFlags, "DEAL_EDITING_CASH_FEATURE", false) || false;
            return enabled;
        },
        isCashDeal() {
            const dealType = _.get(this.dealContent, "dealType", null);
            const cashType = "CASH";
            let isCashDeal = false;

            if (typeof dealType !== "string") {
                isCashDeal = false;
            } else {
                isCashDeal = dealType.toUpperCase() === cashType;
            }

            return isCashDeal;
        },
    },
    created() {
        EventBus.$on("copy-deal-dialog", () => (this.show = true));
    },
    methods: {
        closeDialog() {
            this.show = false;
            this.$emit("close");
        },
        resetCopied() {
            this.$refs.copyBtnLabel.innerHTML = "Copy link to clipboard";
        },
        async copy(s) {
            await navigator.clipboard.writeText(s);
            this.$refs.copyBtnLabel.innerHTML = "Copied!";
        },
        saveDeal() {
            if (this.$refs.saveDealForm.validate() === false) {
                return;
            }
            this.loading = true;
            let dealId = this.dealContent.certificateId;
            api.post(
                `/dealer/${this.dealerId}/users/${this.userId}/deals/${dealId}/save?newDealName=${this.saveDealName}&newDealNote=${this.saveDealNote}&newDealDisplayNote=${this.saveDealDisplayNote}`
            )
                .then((response) => {
                    this.$emit("unshift", response.data);
                })
                .catch((error) => {
                    console.error(error);
                    this.$toast.error("There was a problem copying this deal", {
                        positionClass: "toast-top-right",
                        timeout: 5000,
                    });
                })
                .finally(() => {
                    this.show = false;
                    this.loading = false;
                    this.saveDealName = null;
                    this.saveDealNote = null;
                    this.saveDealDisplayNote = null;
                });
        },
        copyDeal() {
            if (this.$refs.copyDealForm.validate() === false) {
                return;
            }
            let dealId = this.dealContent.certificateId;
            api.post(
                `/dealer/${this.dealerId}/users/${this.userId}/deals/${dealId}/copy?newDealName=${this.copyDealName}`
            )
                .then((response) => {
                    this.$emit("unshift", response.data);
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.show = false;
                    this.copyDealName = null;
                });
        },
    },
};
</script>
<style lang="scss" scoped>
.program-text {
    .col:first-of-type {
        align-self: center;
    }
}
.copyContainer {
    flex-direction: column;

    button {
        text-transform: none;
        width: 100%;
        margin: 0;
    }
}
.no-spacing {
    padding: 0;
    margin: 0;
}
</style>
