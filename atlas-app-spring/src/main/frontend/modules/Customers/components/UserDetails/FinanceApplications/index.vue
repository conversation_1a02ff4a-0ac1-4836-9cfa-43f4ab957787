<template>
    <div>
        <v-data-table
            :loading="financeApplications.loader.isLoading"
            :headers="headers"
            :items="financeApplications.data"
            :items-per-page="5"
            :page="pageNumber"
            no-data-text="No Finance Applications found for this user"
            class="elevation-1"
            show-expand
        >
            <template #item.overallStatus="{ item }">
                <overall-status :status="item.overallStatus" />
            </template>
            <template #item.vehicle="{ item }">
                {{ item.vehicleDescription }}
            </template>
            <template #item.requestedAmountFinanced="{ item }">
                {{ item.requestedAmountFinanced | numeral("$0,00.00") }}
            </template>
            <template #item.createdDate="{ item }">
                {{ item.createdDate | formatEpochDate }}
            </template>
            <template #item.actions="{ item }">
                <v-menu v-if="dealerId">
                    <template #activator="{ on, attrs }">
                        <v-btn icon v-bind="attrs" v-on="on">
                            <v-icon>mdi-dots-vertical</v-icon>
                        </v-btn>
                    </template>

                    <v-list nav dense>
                        <v-list-item link @click="showRouteOneModal(item.id)">
                            <v-list-item-title>View Application</v-list-item-title>
                        </v-list-item>

                        <v-list-item v-if="hasNoContractId(item)" link @click="openLinkDialog(item, 'Finance')">
                            <v-list-item-title>Direct Link</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
            </template>
            <template #expanded-item="{ item }">
                <td :colspan="12" class="pa-4 ma-4 expanded-section">
                    <v-alert v-if="item.financeDecisions.length === 0" icon="mdi-alert" color="#eee" class="mb-0">
                        No responses found
                    </v-alert>
                    <div v-else>
                        <finance-decision-table :decisions="item.financeDecisions" />
                    </div>
                </td>
            </template>
        </v-data-table>
        <v-dialog v-model="viewAppDialog" max-width="800">
            <v-card elevation="5">
                <v-card-title class="pb-6">View Application</v-card-title>

                <v-card-subtitle>
                    This will allow you to see all lender call-backs associated with this customer
                </v-card-subtitle>

                <v-card-text>
                    <v-img src="./images/RouteOne/CreditAppPreview.png" />
                </v-card-text>

                <v-divider></v-divider>

                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="gotoRouteOne">Continue to RouteOne</v-btn>
                    <v-btn text @click="viewAppDialog = false">Cancel</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
        <div>
            <direct-app-link-dialog
                v-if="showLinkDialog"
                :certificate-id="certificateId"
                :campaign-id="campaignId"
                link-type="Finance Application"
                :dealer-id="dealerId"
                :vin="vin"
                @close="closeLinkDialog"
            />
        </div>
    </div>
</template>

<script>
import loader from "Util/loader";
import api from "Util/api";
import OverallStatus from "./components/OverallStatus";
import FinanceDecisionTable from "./components/FinanceDecisionTable";
import { get } from "vuex-pathify";
import getX from "lodash/get";
import DirectAppLinkDialog from "Modules/Customers/components/UserDetails/DirectAppLinkDialog.vue";

export default {
    name: "FinanceApplications",
    components: { DirectAppLinkDialog, FinanceDecisionTable, OverallStatus },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },

    data() {
        return {
            viewAppDialog: false,
            selectedApp: null,
            pageNumber: 1,
            linkType: null,
            showLinkDialog: false,
            financeApplications: {
                data: [],
                loader: loader.defaultState(),
            },
            headers: [
                { text: "Actions", value: "actions", sortable: false },
                {
                    text: "Status",
                    value: "overallStatus",
                    sortable: false,
                },
                {
                    text: "Vehicle",
                    value: "vehicle",
                    sortable: false,
                },
                { text: "Type", value: "type", sortable: false },
                { text: "Requested Amount", value: "requestedAmountFinanced", sortable: false },
                { text: "Created", value: "createdDate" },
                { text: "", value: "data-table-expand" },
            ],
            certificateId: "",
            campaignId: "",
            vin: "",
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
    },
    created() {
        this.fetchUserFinanceApplications();
    },
    methods: {
        fetchUserFinanceApplications() {
            this.financeApplications.loader = loader.started();
            let params = {};
            if (!_.isNil(this.dealerId)) {
                params["dealerIds"] = this.dealerId;
            }
            api.get(`/users/${this.userId}/finance-applications`, params)
                .then((response) => {
                    this.financeApplications.data = response.data;
                    this.financeApplications.loader = loader.successful();
                })
                .catch((error) => {
                    console.log(error);
                    this.financeApplications.loader = loader.error(error);
                });
        },
        showRouteOneModal(appId) {
            this.selectedApp = appId;
            this.viewAppDialog = true;
        },
        openLinkDialog(dealContent, type) {
            this.certificateId = dealContent?.certificate.id.toString();
            this.campaignId = dealContent?.certificate.source.campaignId;
            this.vin = dealContent?.vin || "";
            this.linkType = type;

            return (this.showLinkDialog = !this.showLinkDialog);
        },
        gotoRouteOne() {
            this.viewAppDialog = false;
            window.open(`/routeone/${this.dealerId}/finance-app/${this.selectedApp}/sso`, "_blank");
        },
        closeCopyDialog() {
            return (this.showCopyDealDialog = false);
        },
        closeLinkDialog() {
            return (this.showLinkDialog = false);
        },
        hasNoContractId(item) {
            const contractExists = Boolean(item?.certificate?.userContractId);
            return !contractExists;
        },
    },
};
</script>
<style lang="scss">
.expanded-section {
    background-color: #fff;
}
</style>
