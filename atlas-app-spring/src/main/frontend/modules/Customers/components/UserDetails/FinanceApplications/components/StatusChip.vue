<template>
    <v-chip small :color="statusChip.color" :dark="statusChip.dark" v-bind="$attrs" v-on="$listeners">
        {{ statusChip.text }}
    </v-chip>
</template>

<script>
export default {
    name: "StatusChip",
    props: {
        status: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            statusChip: {
                color: null,
                text: null,
                dark: false,
            },
        };
    },
    created() {
        switch (this.status) {
            case "Approved": {
                this.statusChip = { color: "green", text: "Approved", dark: true };
                break;
            }
            case "Conditioned": {
                this.statusChip = { color: "warning", text: "Conditioned", dark: true };
                break;
            }
            case "Declined": {
                this.statusChip = { color: "red", text: "Declined", dark: true };
                break;
            }
            case "In Progress": {
                this.statusChip = { color: "gray", text: "In Progress", dark: false };
                break;
            }
            default:
                this.statusChip = { color: "gray", text: "Unknown", dark: false };
        }
    },
};
</script>
<style lang="scss" scoped></style>
