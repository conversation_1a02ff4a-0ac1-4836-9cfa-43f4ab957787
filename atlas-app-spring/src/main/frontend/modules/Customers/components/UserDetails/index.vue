<template>
    <div>
        <v-card width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Pre-approvals</span>
            </v-card-title>
            <pre-approvals :dealer-id="dealerId" :user-id="userId" @viewPreApprovalDetails="openPreApprovalDrawer" />
        </v-card>
        <v-card width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Vehicle Sales</span>
                <info-tooltip size="20"> Vehicle sales that have a status of verified </info-tooltip>
            </v-card-title>
            <vehicle-sales :dealer-id="dealerId" :user-id="userId" />
        </v-card>

        <v-card width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Contract Requests</span>
                <info-tooltip size="20"> Customer's Contract Requests </info-tooltip>
            </v-card-title>
            <contract-requests :dealer-id="dealerId" :user-id="userId" />
        </v-card>

        <v-card width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Leads</span>
                <info-tooltip size="20" :max-width="250">
                    Appointments are made when a customer schedules a test drive, Connections are made when a customer
                    fills out the contact dealer form & Order Request are when a customer purchases a vehicle.
                </info-tooltip>
            </v-card-title>
            <leads :dealer-id="dealerId" @open="openDealDrawer" />
        </v-card>

        <v-card width="100%" class="mb-3">
            <v-card-title class="d-flex justify-space-between">
                <span class="mr-2">Deals</span>
                <add-deal-modal v-if="hasCustomerDealEditAccess" :dealer-id="dealerId" :user-id="userId" />
            </v-card-title>
            <deals :dealer-id="dealerId" :user-id="userId" :program-id="programId" @open="openDealDrawer" />
        </v-card>

        <v-card width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Trade Vehicles</span>
                <info-tooltip size="20" :max-width="250">
                    These are vehicles a customer is interested in trading in.
                </info-tooltip>
            </v-card-title>
            <trades :dealer-id="dealerId" :user-id="userId" @viewTradeDetails="openTradeDrawer" />
        </v-card>

        <v-card v-if="isSellAtHomeEnabled" width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Sell@Home Vehicles</span>
                <info-tooltip size="20" :max-width="250">
                    These are vehicles a customer is interested in trading in.
                </info-tooltip>
            </v-card-title>
            <sell-at-home-list :dealer-id="dealerId" :user-id="userId" />
        </v-card>

        <v-card width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Finance Applications</span>
            </v-card-title>
            <finance-applications :user-id="userId" :dealer-id="dealerId" />
        </v-card>

        <v-card width="100%" class="mb-3">
            <v-card-title>
                <span class="mr-2">Documents</span>
            </v-card-title>
            <my-documents :user-id="userId" :dealer-id="dealerId" />
        </v-card>

        <v-navigation-drawer
            v-model="dealDrawerShowing"
            fixed
            temporary
            right
            :width="$vuetify.breakpoint.xs ? '80%' : '60%'"
        >
            <template #prepend>
                <div class="float-right py-4 pr-4">
                    <v-btn icon outlined small @click.stop="closeDealDrawer">
                        <v-icon>mdi-chevron-right</v-icon>
                    </v-btn>
                </div>
            </template>
            <deal-preview v-if="selectedDeal" :deal-id="selectedDeal" :dealer-id="dealerId" />
        </v-navigation-drawer>

        <v-navigation-drawer
            v-model="tradeDrawerShowing"
            fixed
            temporary
            right
            :width="$vuetify.breakpoint.xs ? '80%' : '60%'"
        >
            <template #prepend>
                <div class="float-right py-4 pr-4">
                    <v-btn icon outlined small @click.stop="closeTradeDrawer">
                        <v-icon>mdi-chevron-right</v-icon>
                    </v-btn>
                </div>
            </template>
            <trade-preview v-if="selectedTradeId" :trade-id="selectedTradeId" :dealer-id="dealerId" />
        </v-navigation-drawer>

        <v-navigation-drawer
            v-model="preApprovalDrawerShowing"
            fixed
            temporary
            right
            :width="$vuetify.breakpoint.xs ? '80%' : '60%'"
        >
            <template #prepend>
                <div class="float-right py-4 pr-4">
                    <v-btn icon outlined small @click.stop="closePreApprovalDrawer">
                        <v-icon>mdi-chevron-right</v-icon>
                    </v-btn>
                </div>
            </template>
            <pre-approval-preview v-if="selectedPreapproval" :selected-preapproval="selectedPreapproval" />
        </v-navigation-drawer>
    </div>
</template>
<script>
import DealPreview from "@/components/Deal/DealPreview";
import VehicleSales from "@/modules/Customers/components/UserDetails/VehicleSales";
import ContractRequests from "@/modules/Customers/components/UserDetails/ContractRequests";
import Leads from "@/modules/Customers/components/UserDetails/Leads";
import InfoTooltip from "Components/InfoTooltip";
import Deals from "@/modules/Customers/components/UserDetails/Deals";
import Trades from "@/modules/Customers/components/UserDetails/Trades";
import SellAtHomeList from "@/modules/Customers/components/UserDetails/SellAtHomeList";
import TradePreview from "@/modules/Customers/components/UserDetails/TradePreview";
const vuetify = require("@/plugins/vuetify");
import FinanceApplications from "@/modules/Customers/components/UserDetails/FinanceApplications";
import PreApprovals from "@/modules/Customers/components/UserDetails/PreApprovals";
import PreApprovalPreview from "@/modules/Customers/components/UserDetails/PreApprovalPreview";
import MyDocuments from "@/modules/Customers/components/UserDetails/MyDocuments";
import AddDealModal from "./AddDealModal/AddDealModal.vue";
import { get } from "vuex-pathify";
import _ from "lodash";
import api from "Util/api";

export default {
    name: "UserVehicles",
    components: {
        AddDealModal,
        PreApprovalPreview,
        PreApprovals,
        TradePreview,
        FinanceApplications,
        Trades,
        SellAtHomeList,
        Deals,
        InfoTooltip,
        Leads,
        VehicleSales,
        ContractRequests,
        DealPreview,
        MyDocuments,
    },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        programId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            isSellAtHomeEnabled: false,
            selectedDeal: null,
            dealDrawerShowing: null,
            selectedTradeId: null,
            tradeDrawerShowing: false,
            selectedPreapproval: null,
            preApprovalDrawerShowing: false,
        };
    },
    computed: {
        hasCustomerDealEditAccess() {
            return (
                this.$acl.hasDealerPermission(this.dealerId, "customer:deal:edit") ||
                this.$acl.hasProgramPermission(this.loggedInUserId, "customer:deal:edit")
            );
        },
        loggedInUserId: get("loggedInUser/userId"),
    },
    watch: {
        dealDrawerShowing(oldVal, newVal) {
            if (newVal) {
                this.selectedDeal = null;
            }
        },
        tradeDrawerShowing(oldVal, newVal) {
            if (newVal) {
                this.selectedTradeId = null;
            }
        },
        preApprovalDrawerShowing(oldVal, newVal) {
            if (newVal) {
                this.selectedPreapproval = null;
            }
        },
    },
    created() {
        this.isSellAtHomeFeatureEnabled(this.dealerId);
    },
    methods: {
        openDealDrawer(dealId) {
            this.selectedDeal = dealId;
            this.dealDrawerShowing = true;
            // this.$router.push(`/customers/${this.userId}/edit?dealerId=${this.dealerId}`);
        },
        closeDealDrawer() {
            this.dealDrawerShowing = false;
        },
        openTradeDrawer(tradeId) {
            this.selectedTradeId = tradeId;
            this.tradeDrawerShowing = true;
        },
        closeTradeDrawer() {
            this.tradeDrawerShowing = false;
        },
        openPreApprovalDrawer(preApproval) {
            this.selectedPreapproval = preApproval;
            this.preApprovalDrawerShowing = true;
        },
        closePreApprovalDrawer() {
            this.preApprovalDrawerShowing = false;
        },
        isSellAtHomeFeatureEnabled(dealerId) {
            this.loading = true;
            api.get(`/feature-subscription/${dealerId}`)
                .then((response) => {
                    this.isSellAtHomeEnabled = _.get(response, "data.enabled", []);
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
    },
};
</script>
