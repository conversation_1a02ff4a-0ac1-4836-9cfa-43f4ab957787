<template>
    <div v-if="!loading" class="ml-5 mr-5">
        <trade-in
            v-if="showTradeWithQuote"
            :user-vehicle-id="tradeVehicle.id"
            :dealer-id="dealerId"
            :is-sell-at-home="isSellAtHome"
            :active="active"
            class="mb-3"
        />

        <trade-in-zero-value
            v-else
            :dealer-id="dealerId"
            :trade-vehicle-id="tradeVehicle.id"
            :initial-discrepancy="tradeVehicle.discrepancy"
            :vehicle="tradeVehicle.vehicle"
            :vin="tradeVehicle.vin"
            :mileage="tradeVehicle.mileage"
        />
    </div>
</template>

<script>
import TradeIn from "Components/Deal/components/TradeIn";
import TradeInZeroValue from "Components/Deal/components/TradeInZeroValue";
import _ from "lodash";
import api from "Util/api";
import lodashGet from "lodash/get";

export default {
    name: "TradePreview",
    components: { TradeIn, TradeInZeroValue },
    props: {
        dealerId: {
            type: String,
            required: true,
        },
        isSellAtHome: {
            type: Boolean,
            required: false,
            default: false,
        },
        tradeId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            loading: true,
            tradeVehicle: null,
        };
    },
    computed: {
        showTradeWithQuote() {
            return !this.loading && !_.isNil(this.tradeVehicle.quoteId);
        },
        active() {
            const result = !this.tradeVehicle.expired;
            return result;
        },
    },
    mounted() {
        this.fetchTrade(this.dealerId, this.tradeId);
    },
    methods: {
        fetchTrade(dealerId, tradeId) {
            let params = {};
            if (!_.isNil(dealerId)) {
                params["dealerIds"] = dealerId;
            }
            const endpoint = `/users/trades/${tradeId}`;
            api.get(endpoint, params)
                .then((response) => {
                    this.tradeVehicle = _.get(response, "data", null);
                })
                .finally(() => (this.loading = false));
        },
    },
};
</script>

<style scoped></style>
