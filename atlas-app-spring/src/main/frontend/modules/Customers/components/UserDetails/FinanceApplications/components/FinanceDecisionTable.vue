<template>
    <div>
        <v-data-table
            :headers="headers"
            :items="decisions"
            class="grey lighten-3"
            disable-pagination
            hide-default-footer
        >
            <template #item.status="{ item }">
                <status-chip :status="item.status" />
            </template>
            <template #item.stipulations="{ item }">
                <span v-if="item.stipulations && item.stipulations.length === 1" class="text-left align-content-start">
                    {{ item.stipulations[0] }}
                </span>
                <span
                    v-else-if="item.stipulations && item.stipulations.length > 1"
                    class="text-left align-content-start"
                >
                    <ul v-for="stip in item.stipulations" :key="stip">
                        <li>{{ stip }}</li>
                    </ul>
                </span>
            </template>
            <template #item.buyRateApr="{ item }">
                <div v-if="item.buyRateApr">{{ item.buyRateApr | numeral("0.00") }}%</div>
            </template>
            <template #item.sellRateApr="{ item }">
                <div v-if="item.sellRateApr">{{ item.sellRateApr | numeral("0.00") }}%</div>
            </template>
            <template #item.monthlyPayment="{ item }">
                <div v-if="item.monthlyPayment">{{ item.monthlyPayment | numeral("$0,00.00") }}</div>
            </template>
            <template #item.viewHistory="{ item }">
                <div class="view-history" @click="openFinanceApplicationHistoryModal(item)">
                    View History
                    <v-icon class="mdiHistoryIcon" color="primary" size="large"> mdi-history </v-icon>
                </div>
            </template>
        </v-data-table>
        <finance-application-history-modal
            v-if="decisionId"
            :application-id="applicationId"
            :decision-id="decisionId"
            :finance-company="financeCompany"
        />
    </div>
</template>

<script>
import StatusChip from "./StatusChip";
import EventBus from "Util/eventBus";
import { get } from "vuex-pathify";
import lodashGet from "lodash/get";
import FinanceApplicationHistoryModal from "./FinanceApplicationHistoryModal";
export default {
    name: "FinanceDecisionTable",
    components: { StatusChip, FinanceApplicationHistoryModal },
    props: {
        decisions: {
            type: Array,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            headers: [
                {
                    text: "Finance Company",
                    value: "financeCompany",
                    sortable: false,
                    width: 125,
                },
                {
                    text: "Status",
                    value: "status",
                    sortable: false,
                    width: 125,
                },
                {
                    text: "Stipulations",
                    value: "stipulations",
                    sortable: false,
                },
                { text: "App Id", value: "applicationId", width: 125 },
                { text: "Tier", value: "tier" },
                { text: "Term", value: "term" },
                { text: "Buy Rate", value: "buyRateApr" },
                { text: "Sell Rate", value: "sellRateApr" },
                { text: "Payment", value: "monthlyPayment" },
                { text: "Action", value: "viewHistory" },
            ],
            applicationId: "",
            decisionId: 0,
            financeCompany: "",
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
    },
    methods: {
        openFinanceApplicationHistoryModal(application) {
            this.decisionId = application.id;
            this.applicationId = application.applicationId;
            this.financeCompany = application.financeCompany;
            this.$nextTick(() => {
                EventBus.$emit("showFinaceApplicationHistoryModal", this.decisionId);
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.row-wrapper {
    width: 100%;
    .row-border {
        border-top: 1px solid #fff;
        border-bottom: 1px solid #fff;
    }
}
.view-history {
    color: var(--v-primary-base) !important;
    cursor: pointer;
}
</style>
