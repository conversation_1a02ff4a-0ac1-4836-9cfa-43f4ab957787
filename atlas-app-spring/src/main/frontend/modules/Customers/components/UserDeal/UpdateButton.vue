<template>
    <div class="d-flex justify-end">
        <v-btn color="primary" :loading="dealLoader.isLoading" @click="recalculate">Update</v-btn>
    </div>
</template>
<script>
import { get } from "vuex-pathify";

export default {
    name: "UpdateButton",
    computed: {
        dealLoader: get("userDeal/dealModel@loader"),
    },
    methods: {
        recalculate() {
            this.$emit("recalculate");
        },
    },
};
</script>
