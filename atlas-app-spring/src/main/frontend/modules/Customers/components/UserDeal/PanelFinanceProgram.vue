<template>
    <v-expansion-panel :key="1" class="mb-2">
        <v-expansion-panel-header class="grey-bg-color">
            <div class="d-flex justify-space-between">
                <span class="font-weight-bold">Finance Program</span>
                <span v-if="isAnyEditModeEnabled" class="font-weight-bold"
                    >{{ localForm.financeDetails.term }} Month {{ dealDetail.dealType }}</span
                >
                <span v-else class="font-weight-bold">{{ financeDetails.term }} Month {{ dealDetail.dealType }}</span>
            </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pt-2 fw-400 fs-14">
            <v-form v-model="localValidFinanceDetails">
                <!-- Both Lease & Finance Fields-->
                <div class="d-flex justify-space-between align-center mb-4">
                    <div>Finance Company</div>
                    <div v-if="isAdvanceEditOnly" class="advance-input-wrapper">
                        <v-text-field
                            v-model="localForm.financeDetails.financeCompany"
                            class="align-text-end"
                            placeholder="finance company"
                            outlined
                            dense
                            item-text="text"
                            item-value="value"
                            required
                            hide-details
                            :rules="[required]"
                        ></v-text-field>
                    </div>
                    <div v-else>
                        {{ financeDetails.financeCompany }}
                    </div>
                </div>
                <div class="d-flex justify-space-between align-center mb-4">
                    <div>Term</div>
                    <div v-if="isAnyEditModeEnabled" class="advance-input-wrapper">
                        <v-text-field
                            v-model="localForm.financeDetails.term"
                            class="align-text-end"
                            placeholder="Term"
                            outlined
                            dense
                            item-text="text"
                            item-value="value"
                            required
                            hide-details
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                    <div v-else>
                        {{ financeDetails.term }}
                    </div>
                </div>
                <!-- Finance Deal Fields-->
                <div v-if="isFinanceDeal" class="d-flex justify-space-between align-center mb-4">
                    <div>APR</div>
                    <div v-if="isAdvanceEditOnly" class="advance-input-wrapper">
                        <v-text-field
                            v-model="localForm.financeDetails.apr"
                            suffix="%"
                            placeholder="APR"
                            outlined
                            dense
                            hide-details
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                    <div v-else>{{ financeDetails.apr | numeral("0.00") }}%</div>
                </div>
                <!-- Lease Deal Fields-->
                <div v-if="!isFinanceDeal" class="d-flex justify-space-between align-center mb-4">
                    <div>Miles Per Year</div>
                    <div v-if="isAnyEditModeEnabled" class="advance-input-wrapper">
                        <v-text-field
                            v-model="localForm.financeDetails.milesPerYear"
                            class="align-text-end"
                            placeholder="Tax"
                            outlined
                            dense
                            hide-details
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                    <div v-else>{{ financeDetails.milesPerYear }}</div>
                </div>
                <div v-if="!isFinanceDeal" class="d-flex justify-space-between align-center mb-4">
                    <div>Residual Amount</div>
                    <div v-if="isAdvanceEditOnly" class="advance-input-wrapper">
                        <v-text-field
                            v-model="localForm.financeDetails.residualAmount"
                            class="align-text-end"
                            prefix="$"
                            placeholder="Tax"
                            outlined
                            dense
                            hide-details
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                    <div v-else>{{ financeDetails.residualAmount | numeral("$0,0.00") }}</div>
                </div>
                <div v-if="!isFinanceDeal" class="d-flex justify-space-between align-center mb-4">
                    <div>Acquisition Fee</div>
                    <div v-if="isAdvanceEditOnly" class="advance-input-wrapper">
                        <v-text-field
                            v-model="localForm.financeDetails.acquisitionFee"
                            class="align-text-end"
                            prefix="$"
                            placeholder="Acquisition Fee"
                            outlined
                            dense
                            hide-details
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                    <div v-else>{{ financeDetails.acquisitionFee | numeral("$0,0.00") }}</div>
                </div>
                <div v-if="!isFinanceDeal" class="d-flex justify-space-between align-center mb-4">
                    <div>Lease Termination Fee</div>
                    <div v-if="isAdvanceEditOnly" class="advance-input-wrapper">
                        <v-text-field
                            v-model="localForm.financeDetails.leaseTerminationFee"
                            class="align-text-end"
                            prefix="$"
                            placeholder="lease termination fee"
                            outlined
                            dense
                            hide-details
                            :rules="[numericOnly]"
                        ></v-text-field>
                    </div>
                    <div v-else>
                        {{ financeDetails.leaseTerminationFee }}
                    </div>
                </div>

                <update-button v-if="isStandardEditOnly" @recalculate="recalculate" />
            </v-form>
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>
<script>
import loadashGet from "lodash/get";
import UpdateButton from "Modules/Customers/components/UserDeal/UpdateButton.vue";
export default {
    name: "PanelFinanceProgram",
    components: { UpdateButton },
    props: {
        isAnyEditModeEnabled: {
            type: Boolean,
            required: true,
        },
        isStandardEditOnly: {
            type: Boolean,
            required: true,
        },
        isAdvanceEditOnly: {
            type: Boolean,
            required: true,
        },
        form: {
            type: Object,
            required: true,
        },
        dealDetail: {
            type: Object,
            required: true,
        },
        validFinanceDetails: {
            type: Boolean,
            required: true,
        },
        isFinanceDeal: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            localForm: {
                financeDetails: {
                    financeCompany: "",
                    term: 0,
                    milesPerYear: 0,
                    tier: "",
                    baseMoneyFactor: 0.0,
                    bps: 0.0,
                    moneyFactoryWithBps: "",
                    residualPercentage: "",
                    residualAmount: 0.0,
                    acquisitionFee: 0.0,
                    leaseTerminationFee: 0.0,
                    apr: 0.0,
                },
            },
            localValidFinanceDetails: null,
            numericOnly: (v) => !isNaN(v) || "Input is not a number",
            required: (v) => !!v || "Input is required",
        };
    },
    computed: {
        financeDetails() {
            const financeDetails = loadashGet(this.dealDetail, "financeDetails", null);

            return financeDetails;
        },
    },
    watch: {
        form(newValue) {
            this.localForm = newValue;
        },
        validFinanceDetails(newValue) {
            this.localValidFinanceDetails = newValue;
        },
    },
    methods: {
        recalculate() {
            this.$emit("recalculate");
        },
    },
};
</script>
