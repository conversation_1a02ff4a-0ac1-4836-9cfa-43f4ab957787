<template>
    <v-expansion-panel :key="5" class="mb-2">
        <v-expansion-panel-header class="grey-bg-color">
            <div class="d-flex justify-space-between">
                <span class="font-weight-bold">Gross Capital Cost</span>
                <span v-if="isAnyEditModeEnabled" class="font-weight-bold">
                    {{ grossCapitalCostEditingTotal | numeral("$0,0.00") }}
                </span>
                <span v-else class="font-weight-bold">
                    {{ grossCapitalCostTotal | numeral("$0,0.00") }}
                </span>
            </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pt-4 fw-400 fs-14">
            <div class="fw-400 fs-12 text-color">
                Add any items that you would like included in the total cap cost here(accessories, etc).
            </div>
            <div class="row mt-2">
                <div class="col-md-6">Selling Price</div>
                <div class="col-md-6 text-end">
                    {{ vehicleDetails.sellingPrice | numeral("$0,0.00") }}
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">Negative Trade Balance</div>
                <div class="col-md-6 text-end">
                    {{ displayNegativeBalance | numeral("$0,0.00") }}
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">Acquisition Fee</div>
                <div v-if="isAnyEditModeEnabled" class="col-md-6 text-end">
                    {{ displayAcquisitionInGrossCap | numeral("$0,0.00") }}
                </div>
                <div v-else class="col-md-6 text-end">
                    {{ grossCapitalCost.acquisitionFee | numeral("$0,0.00") }}
                </div>
            </div>
            <div class="my-3">
                <h4>Accessories</h4>
                <div
                    v-for="(lineItem, index) in grossCapitalCost.accessories"
                    :key="index"
                    class="pl-5 d-flex justify-space-between align-center mb-4"
                >
                    <div>{{ lineItem.name }}</div>
                    <div>
                        {{ lineItem.amount | numeral("$0,0.00") }}
                    </div>
                </div>
            </div>
            <div class="my-3">
                <h4>Protection Products</h4>
                <div
                    v-for="(lineItem, index) in grossCapitalCost.protectionProduct"
                    :key="index"
                    class="pl-5 d-flex justify-space-between align-center mb-4"
                >
                    <div>{{ lineItem.name }}</div>
                    <div>
                        {{ lineItem.amount | numeral("$0,0.00") }}
                    </div>
                </div>
            </div>

            <div class="my-3">
                <div v-if="isAnyEditModeEnabled">
                    <div v-for="(lineItem, index) in localForm.grossCapitalCost.lineItems" :key="index" class="row">
                        <div class="col-md-6">{{ lineItem.name }}</div>
                        <div class="col-6 text-end">
                            <span>{{ lineItem.amount | numeral("$0,0.00") }}</span>

                            <v-btn
                                class="ml-2"
                                x-small
                                text
                                icon
                                color="red"
                                @click="removeLineItem('grossCapitalCost', lineItem)"
                            >
                                <v-icon>mdi-close-circle-outline</v-icon>
                            </v-btn>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div v-for="(lineItem, index) in grossCapitalCost.lineItems" :key="index" class="row">
                        <div class="col-md-6">{{ lineItem.name }}</div>
                        <div class="col-6 text-end">
                            {{ lineItem.amount | numeral("$0,0.00") }}
                        </div>
                    </div>
                </div>
            </div>
            <v-form v-if="isAnyEditModeEnabled" v-model="localValidGrossCapitalCost">
                <div v-if="localNewLineItems.grossCapitalCost.show" class="row">
                    <div class="col-md-6">
                        <v-text-field
                            v-model="localNewLineItems.grossCapitalCost.name"
                            placeholder="Label"
                            outlined
                            dense
                            hide-details
                            :rules="[required]"
                        ></v-text-field>
                    </div>
                    <div class="col-md-6 text-end">
                        <v-text-field
                            v-model="localNewLineItems.grossCapitalCost.amount"
                            class="align-text-end"
                            placeholder="Amount"
                            prefix="$"
                            outlined
                            dense
                            hide-details
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                </div>
            </v-form>
            <div v-if="isAnyEditModeEnabled" class="d-flex justify-end mt-3">
                <v-btn
                    v-if="localNewLineItems.grossCapitalCost.show"
                    text
                    :disabled="!validGrossCapitalCost"
                    @click="saveLineItem('grossCapitalCost')"
                    >Save</v-btn
                >
                <v-btn v-else text @click="addHandler('grossCapitalCost')">+ ADD</v-btn>
            </div>

            <update-button v-if="isStandardEditOnly" class="mt-2" @recalculate="recalculate" />
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>
<script>
import loadashGet from "lodash/get";
import * as _ from "lodash";
import lodashToNumber from "lodash/toNumber";
import UpdateButton from "Modules/Customers/components/UserDeal/UpdateButton.vue";

export default {
    name: "PanelGrossCapitalCost",
    components: { UpdateButton },
    props: {
        isStandardEditOnly: {
            type: Boolean,
            required: true,
        },
        isAnyEditModeEnabled: {
            type: Boolean,
            required: true,
        },
        form: {
            type: Object,
            required: true,
        },
        dealDetail: {
            type: Object,
            required: true,
        },
        validGrossCapitalCost: {
            type: Boolean,
            required: true,
        },
        newLineItems: {
            type: Object,
            required: true,
        },
        tradeAllowanceEditingTotal: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            localForm: {
                financeDetails: {
                    acquisitionFee: 0.0,
                },
                grossCapitalCost: {
                    lineItems: [],
                },
                vehicleDetail: {
                    sellingPrice: 0,
                },
            },
            localValidGrossCapitalCost: null,
            numericOnly: (v) => !isNaN(v) || "Input is not a number",
            required: (v) => !!v || "Input is required",
            localNewLineItems: {
                grossCapitalCost: {
                    name: "",
                    amount: 0,
                    show: false,
                },
            },
        };
    },
    computed: {
        vehicleDetails() {
            const vehicleDetails = loadashGet(this.dealDetail, "vehicleDetail", null);

            return vehicleDetails;
        },
        dealerFeesTotal() {
            const lineItems = this.grossCapitalCost.lineItems;
            let lineItemsTotal = 0;

            _.forEach(lineItems, (lineItem) => {
                lineItemsTotal += lodashToNumber(lineItem.amount);
            });

            return lineItemsTotal;
        },
        dealerEditingFeesTotal() {
            const lineItems = this.localForm.grossCapitalCost.lineItems;
            let lineItemsTotal = 0;

            _.forEach(lineItems, (lineItem) => {
                lineItemsTotal += lodashToNumber(lineItem.amount);
            });

            return lineItemsTotal;
        },
        grossCapitalCostEditingTotal() {
            const sellingPrice = lodashToNumber(this.localForm.vehicleDetail.sellingPrice);
            const lineItems = this.localForm.grossCapitalCost.lineItems;

            const negativeTradeValueOrZero = this.negativeTradeBalance;
            const acquisitionFee = lodashToNumber(this.localForm.financeDetails.acquisitionFee);
            let lineItemsTotal = 0;

            _.forEach(lineItems, (lineItem) => {
                lineItemsTotal += lodashToNumber(lineItem.amount);
            });

            _.forEach(this.grossCapitalCost.accessories, (lineItem) => {
                lineItemsTotal += lodashToNumber(lineItem.amount);
            });

            _.forEach(this.grossCapitalCost.protectionProduct, (lineItem) => {
                lineItemsTotal += lodashToNumber(lineItem.amount);
            });

            const total = sellingPrice + lineItemsTotal + negativeTradeValueOrZero * -1 + acquisitionFee;

            return total;
        },
        grossCapitalCostTotal() {
            const grossCapitalCostTotal = loadashGet(this.dealDetail, "grossCapitalCostTotal", null);

            return grossCapitalCostTotal;
        },
        grossCapitalCost() {
            const grossCapitalCost = loadashGet(this.dealDetail, "grossCapitalCost", null);

            return grossCapitalCost;
        },
        negativeTradeBalance() {
            const calculateTradeAllowance = this.tradeAllowanceEditingTotal;
            const negativeBalance = calculateTradeAllowance < 0 ? calculateTradeAllowance : 0.0;

            return negativeBalance;
        },
        displayNegativeBalance() {
            const negativeBalance = this.negativeTradeBalance;
            const displayAmount = negativeBalance < 0 ? negativeBalance * -1 : 0.0;

            return displayAmount;
        },
        displayAcquisitionInGrossCap() {
            const displayValue = lodashToNumber(this.localForm.grossCapitalCost.acquisitionFee);
            return displayValue;
        },
    },
    watch: {
        form(newValue) {
            this.localForm = newValue;
        },
        validGrossCapitalCost(newValue) {
            this.localValidGrossCapitalCost = newValue;
        },
    },
    methods: {
        addHandler(field) {
            this.localNewLineItems[field].show = true;
        },
        removeLineItem(field, lineItem) {
            const index = this.localForm[field].lineItems.findIndex((item) => item.name === lineItem.name);
            this.localForm[field].lineItems.splice(index, 1);
        },
        saveLineItem(field) {
            const formField = this.localForm[field];
            if (!formField) {
                console.error("form field is not defined", field);
            } else {
                formField.lineItems.push(this.localNewLineItems[field]);
                this.resetLineItem(field);
            }
        },
        resetLineItem(field) {
            this.localNewLineItems[field] = {
                name: "",
                amount: 0,
                show: false,
            };
        },
        recalculate() {
            this.$emit("recalculate");
        },
    },
};
</script>
