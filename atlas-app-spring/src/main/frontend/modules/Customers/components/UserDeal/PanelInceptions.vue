<template>
    <v-expansion-panel :key="6" class="mb-2">
        <v-expansion-panel-header class="grey-bg-color">
            <div class="d-flex justify-space-between">
                <span class="font-weight-bold">Inceptions</span>
                <span v-if="isAnyEditModeEnabled" class="font-weight-bold">
                    {{ inceptionsEditingTotal | numeral("$0,0.00") }}
                </span>
                <span v-else class="font-weight-bold">
                    {{ inceptionsTotal | numeral("$0,0.00") }}
                </span>
            </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pt-2 fw-400 fs-14">
            <div class="fw-400 fs-12 text-color">
                Add any items that you would like included in inceptions here. Items in this section will be treated as
                upfront charges and will not be included in total cap cost.
            </div>
            <div class="row mt-2">
                <div class="col-md-6">First Monthly Payment</div>
                <div v-if="isAnyEditModeEnabled" class="col-md-6 text-end">
                    {{ monthlyPaymentEditingTotal | numeral("$0,0.00") }}
                </div>
                <div v-else class="col-md-6 text-end">
                    {{ monthlyPaymentTotal | numeral("$0,0.00") }}
                </div>
            </div>
            <v-form v-model="localValidTitleAndRegistration">
                <div class="row mt-2">
                    <div class="col-md-6">Title & Registration</div>
                    <div v-if="isAdvanceEditOnly" class="col-6 text-end">
                        <v-text-field
                            v-model="localForm.inceptions.titleAndRegistration"
                            class="align-text-end"
                            prefix="$"
                            placeholder="Gross Cap Cost Dealer Fee"
                            outlined
                            hide-details
                            dense
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                    <div v-else class="col-md-6 text-end">
                        {{ inceptions.titleAndRegistration | numeral("$0,0.00") }}
                    </div>
                </div>
            </v-form>
            <div class="my-5">
                <div
                    v-for="(lineItem, index) in inceptions.staticLineItems"
                    :key="index"
                    class="d-flex justify-space-between align-center mb-4"
                >
                    <div>{{ lineItem.name }}</div>
                    <div class="d-flex">
                        <span>{{ lineItem.amount | numeral("$0,0.00") }}</span>
                    </div>
                </div>
            </div>
            <div class="my-3">
                <div v-if="isAnyEditModeEnabled">
                    <div
                        v-for="(lineItem, index) in localForm.inceptions.lineItems"
                        :key="index"
                        class="d-flex justify-space-between align-center mb-4"
                    >
                        <div>{{ lineItem.name }}</div>
                        <div class="d-flex">
                            <span>{{ lineItem.amount | numeral("$0,0.00") }}</span>

                            <v-btn
                                class="ml-2"
                                x-small
                                text
                                icon
                                color="red"
                                @click="removeLineItem('inceptions', lineItem)"
                            >
                                <v-icon>mdi-close-circle-outline</v-icon>
                            </v-btn>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div
                        v-for="(lineItem, index) in inceptions.lineItems"
                        :key="index"
                        class="d-flex justify-space-between align-center mb-4"
                    >
                        <div>{{ lineItem.name }}</div>
                        <div>
                            {{ lineItem.amount | numeral("$0,0.00") }}
                        </div>
                    </div>
                </div>
            </div>
            <v-form v-if="isAnyEditModeEnabled" v-model="localValidInceptions">
                <div v-if="localNewLineItems.inceptions.show" class="d-flex justify-space-between align-center mb-4">
                    <div>
                        <v-text-field
                            v-model="localNewLineItems.inceptions.name"
                            placeholder="Label"
                            outlined
                            dense
                            hide-details
                            :rules="[required]"
                        ></v-text-field>
                    </div>
                    <div class="advance-input-wrapper">
                        <v-text-field
                            v-model="localNewLineItems.inceptions.amount"
                            class="align-text-end"
                            placeholder="Amount"
                            prefix="$"
                            outlined
                            dense
                            hide-details
                            :rules="[required, numericOnly]"
                        ></v-text-field>
                    </div>
                </div>
            </v-form>
            <div v-if="isAnyEditModeEnabled" class="d-flex justify-end">
                <v-btn
                    v-if="localNewLineItems.inceptions.show"
                    text
                    :disabled="!localValidInceptions"
                    @click="saveLineItem('inceptions')"
                    >Save</v-btn
                >
                <v-btn v-else text @click="addHandler('inceptions')">+ ADD</v-btn>
            </div>

            <update-button v-if="isStandardEditOnly" class="mt-2" @recalculate="recalculate" />
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>
<script>
import loadashGet from "lodash/get";
import * as _ from "lodash";
import lodashToNumber from "lodash/toNumber";
import UpdateButton from "Modules/Customers/components/UserDeal/UpdateButton.vue";
export default {
    name: "PanelInceptions",
    components: { UpdateButton },
    props: {
        isStandardEditOnly: {
            type: Boolean,
            required: true,
        },
        isAdvanceEditOnly: {
            type: Boolean,
            required: true,
        },
        isAnyEditModeEnabled: {
            type: Boolean,
            required: true,
        },
        form: {
            type: Object,
            required: true,
        },
        dealDetail: {
            type: Object,
            required: true,
        },
        validTitleAndRegistration: {
            type: Boolean,
            required: true,
        },
        validInceptions: {
            type: Boolean,
            required: true,
        },
        newLineItems: {
            type: Object,
            required: true,
        },
        monthlyPaymentEditingTotal: {
            type: Number,
            required: true,
        },
        monthlyPaymentTotal: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            localForm: {
                vehicleDetail: {
                    sellingPrice: 0,
                },
                inceptions: {
                    lineItems: [],
                },
                monthlyPayment: {
                    firstMonthlyPayment: "0.0",
                    monthlyTax: "0.0",
                },
                monthlyPaymentTotal: 0,
            },
            localValidTitleAndRegistration: null,
            localValidInceptions: null,
            numericOnly: (v) => !isNaN(v) || "Input is not a number",
            required: (v) => !!v || "Input is required",
            localNewLineItems: {
                inceptions: {
                    name: "",
                    amount: 0,
                    show: false,
                },
            },
        };
    },
    computed: {
        inceptions() {
            const inceptions = loadashGet(this.dealDetail, "inceptions", null);

            return inceptions;
        },
        inceptionsEditingTotal() {
            const lineItems = this.localForm.inceptions.lineItems;
            let firstMonthPayment = lodashToNumber(this.localForm.monthlyPayment.firstMonthlyPayment);
            let monthlyTax = lodashToNumber(this.localForm.monthlyPayment.monthlyTax);
            let titleAndRegistration = lodashToNumber(this.localForm.inceptions.titleAndRegistration);
            let lineItemsTotal = 0;
            let total = 0;

            _.forEach(lineItems, (lineItem) => {
                lineItemsTotal += lodashToNumber(lineItem.amount);
            });
            _.forEach(this.inceptions.staticLineItems, (lineItem) => {
                total += lodashToNumber(lineItem.amount);
            });

            total += lineItemsTotal;
            total += firstMonthPayment;
            total += monthlyTax;
            total += titleAndRegistration;

            return total;
        },
        inceptionsTotal() {
            const inceptionsTotal = loadashGet(this.dealDetail, "inceptionsTotal", null);

            return inceptionsTotal;
        },
    },
    watch: {
        form(newValue) {
            this.localForm = newValue;
        },
        validTitleAndRegistration(newValue) {
            this.localValidTitleAndRegistration = newValue;
        },
        validInceptions(newValue) {
            this.localValidInceptions = newValue;
        },
    },
    methods: {
        addHandler(field) {
            this.localNewLineItems[field].show = true;
        },
        removeLineItem(field, lineItem) {
            const index = this.localForm[field].lineItems.findIndex((item) => item.name === lineItem.name);
            this.localForm[field].lineItems.splice(index, 1);
        },
        saveLineItem(field) {
            const formField = this.localForm[field];
            if (!formField) {
                console.error("form field is not defined", field);
            } else {
                formField.lineItems.push(this.localNewLineItems[field]);
                this.resetLineItem(field);
            }
        },
        resetLineItem(field) {
            this.localNewLineItems[field] = {
                name: "",
                amount: 0,
                show: false,
            };
        },
        recalculate() {
            this.$emit("recalculate");
        },
    },
};
</script>
