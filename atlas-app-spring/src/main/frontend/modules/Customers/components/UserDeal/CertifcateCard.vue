<template>
    <v-card id="certificateCard">
        <div class="d-flex flex-column flex-md-row justify-space-between justify-md-center align-center pa-3">
            <div class="label-box">
                <span class="font-weight-bold mr-2">Certificate Number:</span>
                <span class="fw-400 fs-14">{{ certificateId }}</span>
            </div>
            <div class="label-box">
                <span class="font-weight-bold mr-2">Deal Number:</span>
                <span class="fw-400 fs-14 pl-1">{{ stockNumber }}</span>
            </div>
            <div>
                <span class="font-weight-bold mr-2">Created Date:</span>
                <span class="fw-400 fs-14 pl-1">{{ createdDate | formatEpochDate }}</span>
            </div>
        </div>
    </v-card>
</template>
<script>
export default {
    name: "CertificateCard",
    props: {
        certificateId: {
            type: Number,
            required: false,
            default: null,
        },
        stockNumber: {
            type: String,
            required: false,
            default: null,
        },
        createdDate: {
            type: Date,
            required: false,
            default: null,
        },
    },
};
</script>
<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
#certificateCard {
    .label-box {
        margin-right: 0;

        @media #{map-get($display-breakpoints, 'md-and-up')} {
            margin-right: 100px;
        }
    }
}
</style>
