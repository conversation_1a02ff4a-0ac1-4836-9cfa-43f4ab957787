<template>
    <v-expansion-panel :key="8">
        <v-expansion-panel-header class="grey-bg-color">
            <div class="d-flex justify-space-between">
                <span class="font-weight-bold">Monthly Payment</span>
                <span v-if="isAdvanceEditOnly" class="font-weight-bold">
                    {{ monthlyPaymentEditingTotal | numeral("$0,0.00") }}
                </span>
                <span v-else class="font-weight-bold">
                    {{ monthlyPaymentTotal | numeral("$0,0.00") }}
                </span>
            </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pt-4 fw-400 fs-14">
            <div v-if="isFinanceDeal">
                <v-form v-model="localValidFinanceMonthlyPayment">
                    <div class="d-flex justify-space-between align-center mb-4">
                        <div>Monthly Payment</div>
                        <div v-if="isAdvanceEditOnly">
                            <v-text-field
                                v-model="localForm.monthlyPayment.firstMonthlyPayment"
                                prefix="$"
                                placeholder="Monthly Payment"
                                outlined
                                dense
                                hide-details
                                :rules="[required, numericOnly]"
                            ></v-text-field>
                        </div>
                        <div v-else>
                            {{ monthlyPayment.firstMonthlyPayment | numeral("$0,0.00") }}
                        </div>
                    </div>
                </v-form>
            </div>
            <div v-else>
                <v-form v-model="localValidLeaseMonthlyPayment">
                    <div class="d-flex justify-space-between align-center mb-4">
                        <div>Base Monthly Payment</div>
                        <div v-if="isAdvanceEditOnly">
                            <v-text-field
                                v-model="localForm.monthlyPayment.firstMonthlyPayment"
                                prefix="$"
                                placeholder="Monthly Payment"
                                outlined
                                dense
                                hide-details
                                :rules="[numericOnly, required]"
                            ></v-text-field>
                        </div>
                        <div v-else>
                            {{ monthlyPayment.firstMonthlyPayment | numeral("$0,0.00") }}
                        </div>
                    </div>
                    <div class="d-flex justify-space-between mb-4">
                        <div>Monthly Tax</div>
                        <div v-if="isAdvanceEditOnly">
                            <v-text-field
                                v-model="localForm.monthlyPayment.monthlyTax"
                                prefix="$"
                                placeholder="Monthly Tax"
                                outlined
                                dense
                                hide-details
                                :rules="[numericOnly, required]"
                            ></v-text-field>
                        </div>
                        <div v-else>
                            {{ monthlyPayment.monthlyTax | numeral("$0,0.00") }}
                        </div>
                    </div>
                </v-form>
            </div>
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>
<script>
import loadashGet from "lodash/get";
import lodashToNumber from "lodash/toNumber";

export default {
    name: "PanelMonthlyPayment",
    props: {
        isAdvanceEditOnly: {
            type: Boolean,
            required: true,
        },
        form: {
            type: Object,
            required: true,
        },
        dealDetail: {
            type: Object,
            required: true,
        },
        validFinanceMonthlyPayment: {
            type: Boolean,
            required: true,
        },
        validLeaseMonthlyPayment: {
            type: Boolean,
            required: true,
        },
        isFinanceDeal: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            localForm: {
                monthlyPayment: {
                    firstMonthlyPayment: "0.0",
                    monthlyTax: "0.0",
                },
            },
            localValidFinanceMonthlyPayment: null,
            localValidLeaseMonthlyPayment: null,
            numericOnly: (v) => !isNaN(v) || "Input is not a number",
            required: (v) => !!v || "Input is required",
        };
    },
    computed: {
        financeDetails() {
            const financeDetails = loadashGet(this.dealDetail, "financeDetails", null);

            return financeDetails;
        },
        monthlyPaymentEditingTotal() {
            let basePayment = lodashToNumber(this.localForm.monthlyPayment.firstMonthlyPayment);
            let monthlyTax = lodashToNumber(this.localForm.monthlyPayment.monthlyTax);
            let total;

            if (this.isFinanceDeal) {
                total = basePayment;
            } else {
                total = basePayment + monthlyTax;
            }

            return total;
        },
        monthlyPaymentTotal() {
            let basePayment = loadashGet(this.monthlyPayment, "firstMonthlyPayment", 0.0);
            let monthlyTax = loadashGet(this.monthlyPayment, "monthlyTax", 0.0);
            let total;

            if (this.isFinanceDeal) {
                total = basePayment;
            } else {
                total = basePayment + monthlyTax;
            }
            return total;
        },
        monthlyPayment() {
            const monthlyPayment = loadashGet(this.dealDetail, "monthlyPayment", null);

            return monthlyPayment;
        },
    },
    watch: {
        form(newValue) {
            this.localForm = newValue;
        },
        validFinanceMonthlyPayment(newValue) {
            this.localValidFinanceMonthlyPayment = newValue;
        },
        validLeaseMonthlyPayment(newValue) {
            this.localValidLeaseMonthlyPayment = newValue;
        },
    },
};
</script>
