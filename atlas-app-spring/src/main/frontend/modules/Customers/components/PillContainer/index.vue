<template>
    <v-card v-if="pills?.length || loading" class="main pill-container mb-2" flat>
        <div v-if="loading" class="d-flex gap-12 align-center w-full">
            <div>
                <v-skeleton-loader class="d-flex gap-12" type="chip, chip"></v-skeleton-loader>
            </div>
            <div>
                <v-skeleton-loader class="d-none d-md-flex gap-12" type="chip, chip"></v-skeleton-loader>
            </div>
        </div>
        <div v-else-if="pills?.length" class="d-flex main-container">
            <div v-if="title" class="text-no-wrap title-text d-flex mx-1 my-0 pb-2 pb-sm-0 align-center">
                <span> {{ title }}</span>
                <span class="pill-count"> {{ pills.length }} </span>
                <span class="px-2 d-none d-sm-block"> | </span>
            </div>
            <div
                class="flex-grow-1 overflow-auto position-relative"
                :class="{ 'pe-sm-9': showExpandButton || showCollapseButton }"
            >
                <div
                    :id="`pillRow-${pillType}`"
                    class="d-flex position-relative pill-row"
                    :class="isExpanded ? 'expanded' : 'collapsed'"
                >
                    <div v-if="showExpandButton" class="expand-icon" @click="isExpanded = !isExpanded">
                        <v-icon>mdi-plus</v-icon>
                    </div>
                    <div v-else-if="showCollapseButton" class="expand-icon" @click="isExpanded = !isExpanded">
                        <v-icon>mdi-minus</v-icon>
                    </div>
                    <div v-for="(pill, index) in pills" :key="index" class="d-inline-flex flex-shrink-0 pill-wrapper">
                        <component :is="pillType" :pill="pill" :pill-color="pillColor"></component>
                    </div>
                </div>
            </div>
        </div>
    </v-card>
</template>
<script lang="ts">
import { defineComponent } from "vue";
import { PropType } from "vue";

export default defineComponent({
    name: "PillContainer",
    components: {
        ["userDetail"]: () => import("../PillContainer/components/pillTypes/userDetailPill.vue"),
        ["inShowroom"]: () => import("../PillContainer/components/pillTypes/InShowrromPill.vue"),
    },
    props: {
        pills: {
            type: Array,
            required: true,
        },
        title: {
            type: String,
            required: false,
            default: "",
        },
        pillColor: {
            type: String,
            required: false,
            default: "info",
        },
        pillType: {
            type: String as PropType<"userDetail" | "filter" | "stage" | "inShowroom">,
            default: "userDetail",
            required: false,
        },
        expandable: {
            type: Boolean,
            required: false,
            default: true,
        },
        loading: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            isExpanded: false,
            overflowed: false,
        };
    },
    computed: {
        showExpandButton() {
            return !this.isExpanded && this.expandable && this.overflowed && this.pills.length > 1;
        },
        showCollapseButton() {
            return this.expandable && this.isExpanded;
        },
    },
    mounted() {
        //check overflow
        const pillRow = document.getElementById(`pillRow-${this.pillType}`);
        if (pillRow) {
            this.overflowed = pillRow.scrollWidth > pillRow.clientWidth;
        }
    },
    updated() {
        //check overflow
        const pillRow = document.getElementById(`pillRow-${this.pillType}`);
        if (pillRow) {
            this.overflowed = pillRow.scrollWidth > pillRow.clientWidth;
        }
    },
});
</script>
<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.pill-container {
    padding: 12px;
    max-width: 100%;
    max-height: 500px;
    overflow: auto;
}
.pill-row {
    display: flex;
    gap: 12px;
}

.collapsed {
    flex-wrap: nowrap;
    overflow: auto;
}

.expanded {
    flex-wrap: wrap;
    overflow: auto;
    height: auto;
}

.pill-row::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.pill-row {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

title {
    flex-wrap: nowrap;
}
.fs-16 {
    font-size: px2em(16);
}
.pill-count {
    font-weight: 600;
    font-size: px2em(20);
}

.title-text {
    color: var(--grey-grey-darken-3, #424242);
    height: 32px;
    gap: 4px;
}

.expand-icon {
    position: absolute;
    right: 8px;
    top: 12px;
    z-index: 1;
    font-size: 20px;
    border: 1px solid #bdbdbd;
    min-width: 32px;
    height: 32px;
    text-align: center;
    border-radius: 32px;
    align-content: center;
    justify-content: center;
    align-items: center;
    display: flex;
}

.expand-transition-enter-active,
.expand-transition-leave-active {
    transition: all 6s ease;
}

.gap-12 {
    gap: 12px;
}

@media #{map-get($display-breakpoints, 'xs-only')} {
    .title-text {
        width: 100%;
    }

    .main-container {
        flex-wrap: wrap;
    }

    .expanded {
        overflow-x: hidden;

        .pill-wrapper {
            max-width: 100%;
        }

        :deep(.user-detail-pill__content) {
            display: flex;
            max-width: 100%;
            overflow-wrap: break-word;
            flex-wrap: wrap;
            gap: 0px 4px;
        }

        :deep(.custom-pill.v-chip.v-size--default) {
            height: auto;
            max-width: 100%;
            padding-block: 6px;
            border-radius: 40px;
        }

        :deep(.v-chip .v-chip__content) {
            align-items: start;
            max-width: 100%;
        }
    }
    .expand-icon {
        top: 8px;
    }
}
</style>
