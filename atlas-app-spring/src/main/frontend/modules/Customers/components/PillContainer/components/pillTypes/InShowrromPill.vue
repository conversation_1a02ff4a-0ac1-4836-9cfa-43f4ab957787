<template>
    <Pill :color="pillColor" prepend-icon="mdi-account-supervisor-circle-outline">
        <template #content>
            <div class="pointer user-detail-pill__content" @click="gotoCustomerDetails">
                <span class="align-middle">{{ pill.fullName }}</span>
                <span class="align-middle">({{ pill.minutes }} min)</span>
            </div>
        </template>
    </Pill>
</template>
<script lang="ts">
import Pill from "@/components/Pill.vue";
import { defineComponent } from "vue";
import VueRouter from "vue-router";

export default defineComponent({
    name: "UserDetailPill",
    components: { Pill },
    props: {
        pill: {
            type: Object,
            required: true,
        },
        pillColor: {
            type: String,
            required: false,
            default: "info",
        },
    },
    methods: {
        gotoCustomerDetails() {
            this.$router.push({
                path: `/customers/${this.pill.userId}`,
                query: {
                    dealerIds: this.$route.query.dealerIds,
                    selecteduserProgramId: this.pill.programId,
                },
            });
        },
    },
});
</script>

<style lang="scss">
.pointer {
    cursor: pointer;
}

.user-detail-pill__content {
    display: flex;
    gap: 0px 4px;
}
</style>
