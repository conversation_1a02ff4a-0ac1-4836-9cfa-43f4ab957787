<template>
    <div>
        <facet-list-group v-if="!isProgramUser" facet-group-label="Customer Details" facet-icon="mdi-account-box">
            <facet-checkbox facet-label="Program" store="userSearch" facet-name="programs" filter-name="programs" />
        </facet-list-group>

        <v-divider />

        <facet-list-group v-if="isProgramUser" facet-group-label="Dealerships" facet-icon="mdi-format-list-checks">
            <facet-checkbox
                facet-label="Dealers"
                store="userSearch"
                facet-name="dealerLinks"
                filter-name="dealerLinks"
            />
        </facet-list-group>

        <v-divider />

        <facet-list-group facet-group-label="Vehicles of Interest" facet-icon="mdi-car-hatchback">
            <facet-checkbox
                facet-label="Viewed/Saved"
                store="userSearch"
                facet-name="vehicleOfInterestSavedOrViewed"
                filter-name="vehicleOfInterestSavedOrViewed"
                :use-unique-count="true"
            />
            <facet-checkbox
                facet-label="Stock Type"
                store="userSearch"
                facet-name="vehicleOfInterestStockTypes"
                filter-name="vehicleOfInterestStockTypes"
                :use-unique-count="true"
            />
            <facet-checkbox
                facet-label="Certified"
                store="userSearch"
                facet-name="vehicleOfInterestCertified"
                filter-name="vehicleOfInterestCertified"
                :use-unique-count="true"
            />
            <facet-checkbox
                facet-label="Year"
                store="userSearch"
                facet-name="vehicleOfInterestYears"
                filter-name="vehicleOfInterestYears"
                :use-unique-count="true"
            />
            <facet-checkbox
                facet-label="Make"
                store="userSearch"
                facet-name="vehicleOfInterestMakes"
                filter-name="vehicleOfInterestMakes"
                :use-unique-count="true"
            />
            <facet-checkbox
                facet-label="Model"
                store="userSearch"
                facet-name="vehicleOfInterestModels"
                filter-name="vehicleOfInterestModels"
                :use-unique-count="true"
            />
        </facet-list-group>
        <v-divider />

        <facet-list-group facet-group-label="Location" facet-icon="mdi-map-marker">
            <facet-radio
                v-if="isTopDmaEnabled"
                facet-label="Top DMAs"
                store="userSearch"
                facet-name="topDmas"
                filter-name="topDmas.end"
                :show-count="false"
            />
            <facet-checkbox facet-label="DMA" store="userSearch" facet-name="dmas" filter-name="dmaCodes" />
            <facet-checkbox facet-label="States" store="userSearch" facet-name="states" filter-name="states" />
        </facet-list-group>
        <v-divider></v-divider>

        <facet-checkbox
            v-if="isAtlasEnhancedNoGroupFiltersEnabled"
            facet-icon="mdi-dots-horizontal"
            facet-label="Sales Stages"
            store="userSearch"
            facet-name="stages"
            filter-name="stages"
            no-group
        />

        <template v-if="isAtlasLeadFilterEnabled">
            <v-divider></v-divider>

            <facet-list-group facet-group-label="Lead Types" facet-icon="mdi-email-search-outline">
                <facet-checkbox
                    facet-label="Connection Leads"
                    store="userSearch"
                    facet-name="connectionLeads"
                    filter-name="connectionLeads"
                />
                <facet-checkbox
                    facet-label="Other Leads"
                    store="userSearch"
                    facet-name="otherLeads"
                    filter-name="otherLeads"
                />
            </facet-list-group>
        </template>

        <template v-if="isAtlasStickyFiltersFeatureEnabled">
            <v-divider></v-divider>
            <facet-checkbox
                facet-icon="mdi-account-arrow-right-outline"
                facet-label="Customer Status"
                store="userSearch"
                facet-name="status"
                filter-name="status"
                no-group
            />
        </template>

        <v-divider></v-divider>

        <facet-radio
            facet-label="Account Created"
            store="userSearch"
            facet-name="createdDate"
            filter-name="withinCreatedDate"
            facet-icon="mdi-account-multiple-plus-outline"
            :show-count="false"
            no-group
        />

        <v-divider></v-divider>

        <facet-radio
            v-if="isAtlasSecondPhaseFiltersOn"
            facet-label="Last Active"
            store="userSearch"
            facet-name="lastActives"
            filter-name="withinLastActive"
            facet-icon="mdi-progress-clock"
            :show-count="false"
            no-group
        />

        <v-divider></v-divider>

        <facet-checkbox
            v-if="isAtlasSecondPhaseFiltersOn"
            facet-label="Original Salesperson"
            store="userSearch"
            facet-name="originalSalespersons"
            filter-name="originalSalespersons"
            facet-icon="mdi-account-details"
            no-group
        />

        <v-divider></v-divider>

        <facet-list-group
            v-if="isAtlasSecondPhaseFiltersOn"
            facet-group-label="Trade-In Vehicle"
            facet-icon="mdi-car-outline"
        >
            <facet-radio
                facet-label="Has Trade-In"
                store="userSearch"
                facet-name="tradeInsCounts"
                filter-name="hasTradeIn"
            />
            <facet-checkbox
                facet-label="Trade Make"
                store="userSearch"
                facet-name="tradeMakes"
                filter-name="tradeMakes"
            />
            <facet-checkbox
                facet-label="Trade Model"
                store="userSearch"
                facet-name="tradeModels"
                filter-name="tradeModels"
            />
            <facet-checkbox
                facet-label="Trade Year"
                store="userSearch"
                facet-name="tradeYears"
                filter-name="tradeYears"
            />
            <facet-slider
                v-if="isAtlasRangedSliderFiltersOn"
                facet-label="Trade Mileage"
                store="userSearch"
                facet-name="tradeMileage"
                filter-name="tradeMileages"
            />
            <facet-slider
                v-if="isAtlasRangedSliderFiltersOn"
                facet-label="Trade Offer"
                store="userSearch"
                facet-name="tradeValues"
                filter-name="tradeValues"
                prefix="$"
            />
        </facet-list-group>

        <v-divider></v-divider>

        <facet-list-group
            v-if="isAtlasSecondPhaseFiltersOn"
            facet-group-label="Trade-In Financing"
            facet-icon="mdi-currency-usd"
        >
            <facet-slider
                v-if="isAtlasRangedSliderFiltersOn"
                facet-label="Financing Term"
                store="userSearch"
                facet-name="tradeTerm"
                filter-name="tradeTerms"
                :suffix="'Mo'"
            />
            <facet-radio
                facet-label="Financing Maturity Date"
                store="userSearch"
                facet-name="maturityDate"
                filter-name="withinMaturityDate"
                :show-count="false"
            />
            <facet-checkbox
                facet-label="Trade Type"
                store="userSearch"
                facet-name="tradePaymentTypes"
                filter-name="tradePaymentType"
            />
            <facet-slider
                v-if="isAtlasRangedSliderFiltersOn"
                facet-label="Current Payment"
                store="userSearch"
                facet-name="tradePayment"
                filter-name="tradePayments"
                prefix="$"
            />
            <facet-slider
                v-if="isAtlasRangedSliderFiltersOn"
                facet-label="Remaining Payments"
                store="userSearch"
                facet-name="remainingPayments"
                filter-name="remainingPayments"
                prefix="$"
            />
            <facet-slider
                v-if="isAtlasRangedSliderFiltersOn"
                facet-label="Trade Equity"
                store="userSearch"
                facet-name="tradeEquity"
                filter-name="tradeEquity"
                prefix="$"
            />
        </facet-list-group>

        <template v-if="enabledLanguageSelection">
            <v-divider></v-divider>

            <facet-checkbox
                facet-icon="mdi-earth"
                facet-label="Language"
                store="userSearch"
                facet-name="language"
                filter-name="language"
                no-group
            />
        </template>
    </div>
</template>

<script>
import FacetListGroup from "Components/Facets/FacetListGroup";
import FacetRadio from "Components/Facets/FacetRadio";
import FacetCheckbox from "Components/Facets/FacetCheckbox";
import lodashGet from "lodash/get";
import { get } from "vuex-pathify";
import FacetSlider from "Components/Facets/FacetSlider.vue";

export default {
    name: "UserFacets",
    components: { FacetSlider, FacetCheckbox, FacetRadio, FacetListGroup },
    data() {
        return {
            isTopDmaEnabled: false,
        };
    },
    computed: {
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
        featureFlags: get("loggedInUser/featureFlags"),

        isAtlasEnhancedNoGroupFiltersEnabled() {
            const result = lodashGet(this.featureFlags, "ATLAS_ENHANCED_NO_GROUP_FILTERS", false) || false;
            return result;
        },

        isAtlasStickyFiltersFeatureEnabled() {
            const result = lodashGet(this.featureFlags, "ATLAS_STICKY_FILTERS_ENABLED", false) || false;
            return result;
        },

        isAtlasSecondPhaseFiltersOn() {
            const result = lodashGet(this.featureFlags, "ATLAS_SECOND_PHASE_FILTERS", false) || false;
            return result;
        },
        isAtlasRangedSliderFiltersOn() {
            const result = lodashGet(this.featureFlags, "ATLAS_RANGE_SLIDER_FILTERS", false) || false;
            return result;
        },
        isAtlasLeadFilterEnabled() {
            return lodashGet(this.featureFlags, "ATLAS_ENABLED_LEAD_FILTER", false) || false;
        },
        enabledLanguageSelection() {
            const enabledLanguageSelection = lodashGet(this.featureFlags, "ENABLE_LANGUAGE_SELECTION", false) || false;
            return enabledLanguageSelection;
        },
    },
};
</script>
