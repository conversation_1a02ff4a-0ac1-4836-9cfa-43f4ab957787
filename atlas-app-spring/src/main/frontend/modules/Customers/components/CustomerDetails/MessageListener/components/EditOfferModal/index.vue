<template>
    <base-modal v-model="showModal" title="Edit Offer" :max-width="600" :min-height="359">
        <!-- Vehicle Info -->
        <div class="d-flex align-center justify-center mb-6">
            <img
                v-if="vehicleInfo.imgUrl"
                :src="vehicleInfo.imgUrl"
                alt="Vehicle"
                class="vehicle-image mr-4"
                width="80"
            />
            <div class="d-flex flex-column">
                <h3 class="vehicle-title text-h6">
                    {{ vehicleInfo.year }} {{ vehicleInfo.make }} {{ vehicleInfo.model }}
                </h3>
                <p class="vehicle-trim ma-0">{{ vehicleInfo.trim }}</p>
            </div>
        </div>

        <v-form id="edit-offer-form" ref="form" v-model="isValid">
            <!-- Cash Offer -->
            <v-text-field
                v-model="formattedQuoteAmount"
                label="Cash Offer"
                prefix="$"
                type="text"
                outlined
                :rules="[
                    (v) => !!v || 'Cash offer is required',
                    (v) => {
                        const numericValue = v.replace(/[^0-9.]/g, '');
                        return (
                            !valueTradeAmount ||
                            parseFloat(numericValue) >= parseFloat(valueTradeAmount) ||
                            `Offer must be higher than the CarSaver Guaranteed Offer of $${formatNumberWithCommas(
                                valueTradeAmount
                            )}`
                        );
                    },
                ]"
            />

            <!-- Expiration Date -->
            <v-menu
                v-model="dateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="290px"
            >
                <template #activator="{ on, attrs }">
                    <v-text-field
                        v-model="formattedExpirationDate"
                        label="Expiration Date"
                        readonly
                        outlined
                        v-bind="attrs"
                        :rules="[(v) => !!v || 'Expiration date is required']"
                        hint="MM/DD/YYYY"
                        persistent-hint
                        prepend-inner-icon="mdi-calendar-blank-outline"
                        v-on="on"
                    />
                </template>
                <v-date-picker v-model="expirationDate" :min="minDate" @change="dateMenu = false" />
            </v-menu>
        </v-form>

        <!-- Actions -->
        <template #actions>
            <v-spacer />
            <v-btn outlined class="cta-btn" large @click="closeModal"> CLOSE </v-btn>
            <v-btn
                class="cta-btn"
                color="primary"
                large
                :loading="loader?.isLoading"
                :disabled="!isValid"
                @click="saveOfferHandler"
            >
                SAVE
            </v-btn>
        </template>
    </base-modal>
</template>

<script>
import BaseModal from "@/components/BaseModal.vue";
import { get, sync, call } from "vuex-pathify";
import EventBus from "@/util/eventBus";

export default {
    name: "EditOfferModal",

    components: {
        BaseModal,
    },

    filters: {
        formatDate(value) {
            if (!value) return "";

            const date = new Date(value);
            const month = String(date.getMonth() + 1).padStart(2, "0");
            const day = String(date.getDate()).padStart(2, "0");
            const year = date.getFullYear();

            return `${month}/${day}/${year}`;
        },
    },

    data() {
        return {
            isValid: true,
            isSaving: false,
            dateMenu: false,
        };
    },

    computed: {
        showModal: sync("vehicleOffers/editOfferModal@isOpen"),
        offer: get("vehicleOffers/offer@data"),
        quoteAmount: sync("vehicleOffers/offer@data?.quoteAmount"),
        expirationDate: sync("vehicleOffers/offer@data?.expirationDate"),
        valueTradeAmount: get("vehicleOffers/offer@data?.valueTradeAmount"),
        loader: get("vehicleOffers/offer@loader"),
        formattedQuoteAmount: {
            get() {
                if (!this.quoteAmount) return "";
                return this.formatNumberWithCommas(this.quoteAmount);
            },
            set(value) {
                // Remove all non-numeric characters except decimal point
                const numericValue = value.replace(/[^0-9.]/g, "");
                this.quoteAmount = numericValue ? parseFloat(numericValue) : "";
            },
        },
        minDate() {
            return this.formatDateForInput(new Date());
        },
        vehicleInfo() {
            return (
                this.offer?.vehicle || {
                    id: "",
                    vin: "",
                    year: "",
                    make: "",
                    model: "",
                    trim: "",
                    imgUrl: "",
                }
            );
        },
        formattedExpirationDate: {
            get() {
                if (!this.expirationDate) return "";

                // Parse the date string but keep it in the current time zone
                const dateParts = this.expirationDate.split("T")[0].split("-");
                const year = parseInt(dateParts[0]);
                const month = String(parseInt(dateParts[1])).padStart(2, "0");
                const day = String(parseInt(dateParts[2])).padStart(2, "0");

                return `${month}/${day}/${year}`;
            },
            set(value) {
                // This is a read-only field, so we don't need to implement setting logic
                // The date is set through the date picker, not by typing in the field
            },
        },
    },

    watch: {
        showModal(newVal) {
            // Reset validation when modal is opened
            if (newVal && this.$refs.form) {
                this.$nextTick(() => {
                    this.$refs.form.resetValidation();
                });
            }
        },
    },

    methods: {
        saveOffer: call("vehicleOffers/saveOffer"),
        formatDateForInput(date) {
            return date.toISOString().split("T")[0];
        },
        formatNumberWithCommas(number) {
            if (number === null || number === undefined || number === "") return "";
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        closeEditOfferModal: call("vehicleOffers/closeEditOfferModal"),

        closeModal() {
            this.closeEditOfferModal();
            this.resetForm();
        },

        resetForm() {
            this.quoteAmount = "";
            this.expirationDate = this.formatDateForInput(new Date());
            this.$refs.form?.reset();
        },

        async saveOfferHandler() {
            if (!this.$refs.form?.validate()) return;

            try {
                await this.saveOffer(this.offer);
                this.$toast.success("Offer updated successfully");
                EventBus.$emit("refresh-digital-retail-iframe");
                this.closeEditOfferModal();
            } catch (error) {
                console.error("Failed to save offer:", error);
                this.$toast.error("Failed to update offer. Please try again.");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.cta-btn {
    max-width: 150px !important;
    margin: 0 !important;
    width: calc(50% - 4px) !important;

    &:not(:last-child) {
        margin-right: 10px !important;
    }
}

.vehicle-image {
    object-fit: cover;
}

.vehicle-title {
    font-size: px2rem(16);
    font-weight: 700;
    line-height: 1.5;
    color: #212121;
}

.vehicle-trim {
    font-size: px2rem(14);
    line-height: 1.4;
    color: #616161;
}
</style>

<style lang="scss">
#edit-offer-form {
    .v-text-field {
        & input,
        & .v-text-field__prefix {
            color: #616161 !important;
        }
    }
    .v-messages__message {
        margin-bottom: 8px !important;
    }
}
</style>
