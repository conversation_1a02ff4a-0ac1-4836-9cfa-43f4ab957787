<template>
    <div v-if="loader.isLoading" class="pt-2">
        <v-skeleton-loader class="skeleton-loader" type="list-item-avatar" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
        <v-skeleton-loader class="skeleton-loader" type="list-item" />
    </div>

    <transition v-else name="fade">
        <div class="customer-details-menu">
            <h2 class="menu-header">
                <v-icon class="header-icon">mdi-account-circle-outline</v-icon>
                <span>Customer Details</span>
            </h2>
            <div class="detail-links">
                <button
                    v-for="(link, index) in menuLinks"
                    :key="link.name"
                    :class="{ active: isLinkActive(index) }"
                    class="detail-link-btn mb-1"
                    @click="handleActive(index)"
                >
                    <v-icon class="active-link-icon" :class="{ 'visibility-hidden': !isLinkActive(index) }"
                        >mdi-chevron-left</v-icon
                    >
                    <div class="status-bubble-container" :class="{ active: isLinkActive(index) }">
                        <v-icon v-if="link.status === 'EXPIRED'" class="status-bubble alert"
                            >mdi-alert-circle-outline</v-icon
                        >
                        <v-icon v-else-if="link.status === 'INACTIVE'" class="status-bubble">mdi-circle-outline</v-icon>
                        <v-icon v-else class="status-bubble">mdi-check-circle-outline</v-icon>
                    </div>
                    <div class="link-name">{{ link.name }}</div>
                    <div class="count-bubble" :style="isZeroOrNil(link.count) ? 'border: 2px solid #E0E0E0;' : ''">
                        <span v-if="link.count > 0">{{ link.count }}</span>
                    </div>
                </button>
            </div>
        </div>
    </transition>
</template>

<script>
import { call, get, sync } from "vuex-pathify";
import isNil from "lodash/isNil";

export default {
    name: "CustomerDetailsMenu",
    emits: ["link-clicked"],

    data() {
        return {
            active: true, // for testing only
            intervalId: null,
            refreshTime: 30000, // 30 seconds
        };
    },

    computed: {
        menuLinks: get("customerDetails/customerDetailsLinks@data"),
        loader: get("customerDetails/customerDetailsLinks@loader"),
        activeLink: sync("customerDetails/activeDetailsLink"), // integer 0 - (n-1)
        user: get("userDetails/userModel@data"),
        dealerIds: get("loggedInUser/selectedDealer@id"),
        programId: get("loggedInUser/selectedProgram@id"),
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        clearInterval(this.intervalId);
    },
    methods: {
        fetchCustomerDetailsLinks: call("customerDetails/fetchCustomerDetailsLinks"),
        handleSetActiveDetailsLink: call("customerDetails/handleSetActiveDetailsLink"),
        init() {
            this.initializeLinks(this.setInitialActiveLink);
        },
        initializeLinks(cb) {
            const payload = {
                userId: this.user.id,
                dealerId: this.dealerIds,
                programId: this.programId,
            };

            this.fetchCustomerDetailsLinks(payload).then(() => {
                if (cb) {
                    cb();
                }
            });

            this.intervalId = setInterval(() => {
                this.fetchCustomerDetailsLinks({ ...payload, refresh: true });
            }, this.refreshTime);
        },
        setInitialActiveLink() {
            const saveDeals = this.menuLinks.find((link) => this.nameCompare(link.name, "Saved Deals"));
            const viewedVehicle = this.menuLinks.find((link) => this.nameCompare(link.name, "Viewed Vehicles"));
            // If Saved Deals = 0 then display Viewed Vehicles, If Both = 0 then display Saved Deals
            // 0 is Saved Vehicles, 1 is Viewed Vehicles
            const activeLink = saveDeals?.count > 0 ? 0 : viewedVehicle?.count > 0 ? 1 : 0;

            if (isNil(saveDeals) || isNil(viewedVehicle)) {
                return;
            }

            this.handleSetActiveDetailsLink(activeLink);
        },
        nameCompare(a, b) {
            if (typeof a !== "string" || typeof b !== "string") {
                return false;
            }

            const nameA = a.toUpperCase();
            const nameB = b.toUpperCase();

            return nameA === nameB;
        },
        isLinkActive(index) {
            return index === this.activeLink;
        },
        isZeroOrNil(value) {
            return value === 0 || isNil(value);
        },
        handleActive(index) {
            this.activeLink = index;
            this.$emit("link-clicked", index);
        },
    },
};
</script>

<style scoped lang="scss">
.fade-enter-active {
    transition: opacity 0.3s;
}
.fade-enter {
    opacity: 0;
}
.customer-details-menu {
    padding: 8px 8px 4px 8px;
    width: 100%;

    .visibility-hidden {
        visibility: hidden;
    }

    * {
        font-size: px2rem(14) !important;
    }

    .menu-header {
        padding: 12px 8px;
        margin-bottom: 4px;
        min-height: 45px;
        font-weight: normal;

        .header-icon {
            font-size: px2rem(20) !important;
            margin-right: 12px;
        }
    }
    .detail-links {
        .detail-link-btn {
            display: flex;
            padding: 8px 12px 8px 8px;
            width: 100%;
            align-items: center;
            border-radius: 4px;
            transition: filter 0.3s ease;

            &:hover {
                filter: brightness(85%);
            }

            &:not(.active):hover {
                background-color: rgba(0, 0, 0, 0.05);
            }

            &.active,
            .active > .status-bubble,
            .active-link-icon {
                color: #1976d2 !important;
                background-color: #e1f5fe !important;

                .count-bubble {
                    background-color: #b3e5fc !important;
                    border-color: #b3e5fc !important;
                }
            }

            .active-link-icon,
            .status-bubble,
            .link-name {
                margin-right: 12px;
            }

            .active-link-icon {
                font-size: px2rem(20) !important;
            }
            .status-bubble {
                font-size: px2rem(20) !important;

                &.alert {
                    color: #b00020;
                }
            }
            .link-name {
                text-align: left;
                width: 100%;
            }
            .count-bubble {
                display: flex;
                border: 1px solid #9e9e9e;
                min-width: 32px;
                height: 32px;
                text-align: center;
                border-radius: 32px;
                align-content: center;
                justify-content: center;
                align-items: center;
            }
        }
    }
}
</style>
