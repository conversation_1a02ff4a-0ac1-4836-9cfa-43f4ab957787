<template>
    <v-dialog
        v-model="dialog"
        scrollable
        max-width="363px"
        :fullscreen="isSm"
        :hide-overlay="isSm"
        transition="dialog-bottom-transition"
    >
        <template #activator="{ on, attrs }">
            <button :class="[isMd ? 'customer-details-mobile-menu' : 'hidden']" v-bind="attrs" v-on="on">
                <span class="d-flex">
                    <v-icon class="header-icon">mdi-account-circle-outline</v-icon>
                    <span>Customer Details</span>
                </span>
                <v-icon class="options-icon">mdi-dots-vertical</v-icon>
            </button>
        </template>

        <v-card class="customer-details-modal">
            <button class="close-btn" @click="dialog = false">
                <v-icon class="close-icon">mdi-close</v-icon>
            </button>
            <MainMenu @link-clicked="handleLinkClicked" />
        </v-card>
    </v-dialog>
</template>

<script>
import MainMenu from "./MainMenu.vue";

export default {
    name: "MobileMenu",
    components: { MainMenu },
    data() {
        return {
            dialog: false,
        };
    },
    computed: {
        isSm() {
            return this.$vuetify.breakpoint.smAndDown;
        },
        isMd() {
            return this.$vuetify.breakpoint.mdAndDown;
        },
    },
    methods: {
        handleLinkClicked(index) {
            this.dialog = false;
        },
    },
};
</script>

<style scoped lang="scss">
.close-btn {
    position: absolute;
    top: 20px;
    right: 20px;

    .close-icon {
        font-size: px2rem(20);
    }
}

.customer-details-mobile-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    padding: 12px 8px;
    min-height: 45px;
    font-weight: normal;

    .header-icon,
    options-icon {
        font-size: px2rem(20) !important;
        margin-right: 12px;
    }
}

.hidden {
    display: none;
}
</style>
