<template>
    <Pill :color="pillColor">
        <template #prependIcon>
            <v-icon v-if="isApproved" left class="left-icon"> mdi-checkbox-marked-circle-outline </v-icon>
        </template>
        <template #content>
            {{ pillText }}
        </template>
    </Pill>
</template>

<script>
import { defineComponent } from "vue";
import Pill from "Components/Pill.vue";

export default defineComponent({
    name: "CreditFlagPill",
    components: { Pill },
    props: {
        pillText: {
            type: String,
            required: true,
        },
    },

    computed: {
        isApproved() {
            return this.pillText === "Approved";
        },
        isConditioned() {
            return this.pillText === "Conditioned";
        },
        pillColor() {
            if (this.isConditioned) {
                return "orange";
            }
            return "green";
        },
    },
});
</script>

<style scoped lang="scss">
.left-icon {
    font-size: px2rem(18) !important;
    margin-left: 0 !important;
    margin-right: 4px !important;
}
</style>
