<template>
    <div>
        <div v-if="loading" class="pa-3">
            <v-skeleton-loader type="image"></v-skeleton-loader>
        </div>

        <div v-else flat class="credit-info-wrapper">
            <div class="credit-info-row-wrapper">
                <v-row class="info-row">
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Credit Profile:</span>
                        <span class="value-text text-capitalize"
                            >{{ creditInfo.range | capitalize }} {{ creditRange }}</span
                        >
                    </v-col>
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Credit Rating Source:</span>
                        <span class="value-text">{{ creditInfo.creditRatingSource }}</span>
                    </v-col>
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Current Payment:</span>
                        <span class="value-text"
                            >{{ creditInfo.currentPayment | currency("$", ",") }}
                            {{ creditInfo.purchaseType | capitalize }}
                        </span>
                    </v-col>
                </v-row>
                <v-row class="info-row">
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Max Payment:</span>
                        <span v-if="creditInfo.maxPayment > 0" class="value-text"
                            >est. {{ creditInfo.maxPayment | currency("$", ",") }}</span
                        >
                    </v-col>
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Payment Preference:</span>
                        <span class="value-text">{{ creditInfo.paymentPreference | capitalize }}</span>
                    </v-col>
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Desired Down Payment:</span>
                        <template v-if="hasDownPayment">
                            <span v-if="isDownPaymentFixed" class="value-text">
                                Up to {{ creditInfo.fixedDownPayment | currency("$", ",") }}
                            </span>
                            <span v-else class="value-text">
                                {{ creditInfo.minDownPayment | currency("$", ",") }} -
                                {{ creditInfo.maxDownPayment | currency("$", ",") }}
                            </span>
                        </template>
                    </v-col>
                </v-row>
                <v-row class="info-row">
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Trade:</span>
                        <span class="value-text"
                            >{{ creditInfo?.trade?.year }} {{ creditInfo?.trade?.make }}
                            {{ creditInfo?.trade?.model }}</span
                        >
                    </v-col>
                    <v-col cols="12" md="4" class="pa-0">
                        <span class="mr-1">Trade Equity:</span>
                        <span class="value-text">{{ creditInfo.tradeEquity | currency("$", ",") }}</span>
                    </v-col>
                    <v-col cols="12" md="4" class="pa-0">
                        <span></span>
                        <span></span>
                    </v-col>
                </v-row>
            </div>
        </div>
    </div>
</template>

<script>
import { get } from "vuex-pathify";

export default {
    filters: {
        capitalize(val) {
            if (!val) return "";

            const str = val.toLowerCase();
            return str.charAt(0).toUpperCase() + str.slice(1);
        },
    },
    computed: {
        creditInfo: get("customerDetails/customerCreditInfo@data"),
        loading: get("customerDetails/<EMAIL>"),
        isDownPaymentFixed() {
            return this.creditInfo.fixedDownPayment || this.creditInfo.minDownPayment === null;
        },
        hasDownPayment() {
            return this.creditInfo.minDownPayment !== null && this.creditInfo.maxDownPayment !== null;
        },
        creditRange() {
            switch (this.creditInfo.range) {
                case "WORST":
                    return "(0 - 599)";
                case "POOR":
                    return "(600 - 619)";
                case "FAIR":
                    return "(620 - 659)";
                case "GOOD":
                    return "(660 - 699)";
                case "GREAT":
                    return "(700 - 739)";
                case "EXCELLENT":
                    return "(740 - 850)";
                default:
                    return "";
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.divider {
    max-height: 16px !important;
}

.credit-info-wrapper {
    background-color: #f5f5f5 !important;
    border-radius: 8px !important;
    padding: 16px !important;
}

.value-text {
    color: #424242;
}
.credit-info-row-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px 12px;
}

.info-row {
    margin: 0px !important;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        gap: 8px;
    }
}
</style>
