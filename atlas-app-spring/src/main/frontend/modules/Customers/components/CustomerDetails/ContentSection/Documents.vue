<template>
    <v-card class="document-card-wrapper" flat>
        <v-card-title>Documents</v-card-title>
        <v-divider class="mx-4"></v-divider>
        <v-card-text class="document-content">
            <div class="document-card">
                <v-data-table
                    :loading="loader.isLoading"
                    :headers="headers"
                    :items="items"
                    no-data-text="No Documents found for this user"
                    hide-default-footer
                >
                    <template #item.entityType="{ item }">
                        <div class="document-type">
                            {{ determineDocumentType(item.entityType, item.description) }}
                        </div>
                    </template>

                    <template #item.name="{ item }">
                        <div class="document-name-container">
                            <div class="document-name">
                                {{ item.name }}
                            </div>
                            <span class="document-expiry-date">Expires {{ item.expirationDate | dateMDY }} </span>
                        </div>
                    </template>

                    <template #item.actions="{ item }">
                        <div class="items-action">
                            <v-btn color="primary" small outlined @click="documentViewHandler(item.fileId)">
                                View
                            </v-btn>
                            <v-btn color="primary" small @click="documentDownloadHandler(item.fileId)">
                                Download
                            </v-btn>
                        </div>
                    </template>
                </v-data-table>
                <div class="disclaimer-text">
                    Documents uploaded by customers will be deleted automatically after 30 days. Dealer is responsible
                    for complying with all laws applicable to the use, disclosure, and storage of the documents and the
                    information contained within them.
                </div>
            </div>
        </v-card-text>
    </v-card>
</template>

<script>
import { defineComponent } from "vue";
import { call, get } from "vuex-pathify";
import moment from "moment";

export default defineComponent({
    name: "Documents",
    filters: {
        dateMDY(val) {
            const result = moment(val).format("MM/DD/YY");
            return result;
        },
    },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                { text: "File Type", value: "entityType", sortable: false },
                { text: "Document Name", value: "name", sortable: false },
                { text: "", value: "actions", sortable: false, align: "right" },
            ],
        };
    },
    computed: {
        items: get("userDetails/documents@data"),
        loader: get("userDetails/documents@loader"),
    },

    mounted() {
        this.fetchDocumentsList({ dealerId: this.dealerId, userId: this.userId });
    },

    methods: {
        fetchDocumentsList: call("userDetails/fetchDocumentsList"),
        determineDocumentType(entityType, description) {
            let result = "";

            switch (entityType) {
                case "INSURANCE":
                    result = "Proof of Insurance";
                    break;
                case "DRIVERS_LICENSE":
                    result = "Driver License";
                    break;
                case "OTHER":
                    result = description;
                    break;
                default:
                    result = description;
            }

            return result;
        },
        documentViewHandler(fileId) {
            window.open(`/api/document_upload/view/${fileId}`, "_blank");
        },
        documentDownloadHandler(fileId) {
            window.location = `/api/document_upload/download/${fileId}`;
        },
    },
});
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.document-card-wrapper {
    .document-card {
        .document-type {
            font-size: px2rem(14);
            line-height: 16px;
        }
        .document-name-container {
            display: flex;
            flex-direction: column;
            gap: 0;
            padding-top: 16px;
            padding-bottom: 16px;
            .document-name {
                font-size: px2rem(14);
                line-height: 20px;
                letter-spacing: 0.035px;
                color: $gray-800;
                font-style: normal;
            }
            .document-expiry-date {
                font-size: px2rem(12);
                color: #616161;
                font-style: normal;
                line-height: normal;
                letter-spacing: 0.048px;
            }
        }
        .items-action {
            display: flex;
            justify-content: end;
            gap: 8px;
        }
        .disclaimer-text {
            font-size: px2rem(12);
            line-height: normal;
            color: #666;
            padding-top: 16px;
            padding-bottom: 16px;
            letter-spacing: 0.048px;
        }
    }
}
</style>
