<template>
    <v-card v-if="hasQueryParams" outlined flat class="d-flex w-100 pt-2 pt-md-0">
        <div class="pa-3 w-100">
            <!-- Display days/weeks ago -->
            <span class="searches__days-ago d-flex float-right">
                {{ formattedDaysAgo }}
            </span>

            <!-- Display search filters -->
            <div class="d-flex pill-container flex-column flex-md-row pb-3 pt-3 pt-md-0">
                <Pill v-for="(value, key) in searchData.queryParams" :key="key" class="mw-100 mh-100 pill">
                    <template #content>
                        <div class="pill-data">
                            <span>{{ formatLabel(key) }}</span>
                            <span class="font-weight-bold value">{{ formattedFilterValue(key, value) }}</span>
                        </div>
                    </template>
                </Pill>
            </div>

            <!-- Search count & action buttons -->
            <div class="d-flex flex-column gap-2 flex-md-row">
                <span v-if="searchData.count > 1" class="mb-2 mb-md-1">
                    Total number of searches: <span class="font-weight-bold">{{ searchData.count }}</span>
                </span>
                <div class="items-action ml-auto">
                    <v-btn small outlined @click="showCopyLinkModal(searchData.link)">Copy Link</v-btn>
                    <v-btn color="primary" small @click="showIframeOverlay">View Results</v-btn>
                </div>
            </div>
        </div>
    </v-card>
</template>

<script>
import { defineComponent } from "vue";
import Pill from "Components/Pill.vue";
import { getDaysToDate, formatIntoCurrency } from "Util/helpers";
import { call, get } from "vuex-pathify";

export default defineComponent({
    name: "SearchCard",
    components: { Pill },
    props: {
        searchData: {
            type: Object,
            required: true,
        },
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    computed: {
        otpToken: get("customerDetails/<EMAIL>"),
        programList: get("loggedInUser/userProgramAccessList"),
        menuLinks: get("customerDetails/customerDetailsLinks@data"),

        hasQueryParams() {
            return Object.keys(this.searchData.queryParams).length > 0;
        },

        // Format the time since search as "X weeks ago" or "X days ago"
        formattedDaysAgo() {
            const days = getDaysToDate(this.searchData.latestTimestamp);
            if (days < 7) return `${days} day${days !== 1 ? "s" : ""} ago`;
            const weeks = Math.floor(days / 7);
            return `${weeks} week${weeks !== 1 ? "s" : ""} ago`;
        },

        selectedProgram() {
            let userProgramId = this.$route.query.selecteduserProgramId;
            return this.programList.find((program) => program.id === userProgramId) ?? { name: null };
        },

        searchesBaseURL() {
            const searchesMenu = this.menuLinks.find((menuItem) => menuItem.name === "Searches");
            return searchesMenu.url;
        },

        iframeURL() {
            return this.searchesBaseURL + this.extractPathAndQuery(this.searchData.link);
        },
    },
    methods: {
        openOverlay: call("customerDetails/openOverlay"),
        openShortUrlModal: call("customerDetails/openShortUrlModal"),
        // Format filter labels (camelCase & snake_case to readable text)
        formatReadableText(input) {
            return input
                .replace(/([a-z])([A-Z])/g, "$1 $2") // Convert camelCase to spaced words
                .replace(/_/g, " ") // Replace underscores with spaces
                .toLowerCase()
                .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize each word
        },

        // Convert array of values into a readable comma-separated string
        formattedValues(values) {
            return values.map(this.formatReadableText).join(", ");
        },

        // Format the label based on key
        formatLabel(key) {
            switch (key) {
                case "stockTypes":
                    return "";
                case "passengerCapacity":
                    return "Seats: ";
                case "location":
                    return "Distance: ";
                case "driveTrains":
                    return "Drive Types: ";
                default:
                    return this.formatReadableText(key) + ": ";
            }
        },

        formattedFilterValue(key, value) {
            switch (key) {
                case "price":
                    return this.formattedPrice(value);
                case "miles":
                    return this.formattedMiles(value);
                case "years":
                    return `${value[0]} - ${value[1]}`;
                default:
                    return this.formattedValues(value);
            }
        },

        formattedPrice(range) {
            return `$${formatIntoCurrency(range[0], { includeCurrencySymbol: true })} - 
                    $${formatIntoCurrency(range[1], { includeCurrencySymbol: true })}`;
        },

        formattedMiles(range) {
            return `${formatIntoCurrency(range[0])} - ${formatIntoCurrency(range[1])}`;
        },

        extractPathAndQuery(url) {
            try {
                const parsedUrl = new URL(url);
                return parsedUrl.pathname.replace("/buy", "") + parsedUrl.search;
            } catch (error) {
                return "";
            }
        },

        showIframeOverlay() {
            this.openOverlay({
                iframeUrl: this.iframeURL,
                dealerId: this.dealerId,
                dealerUserId: this.userId,
                otpToken: this.otpToken,
            });
        },

        showCopyLinkModal(longUrl) {
            this.openShortUrlModal({
                longUrl: longUrl.replace("://ar-", "://"),
                overlayUrl: this.searchData.link,
                title: "Copy Link",
                program: this.selectedProgram.name,
                disableOpenPageButton: true,
                enableCloseButton: true,
            });
        },
    },
});
</script>

<style lang="scss" scoped>
.pill-container {
    flex-direction: row;
    flex-wrap: wrap;
    max-width: 90%;
    gap: 4px 12px;
    overflow: hidden;
}

.searches__days-ago {
    font-size: 14px;
    position: absolute;
    right: 12px;
    top: 14px;
    font-weight: 500;
    color: var(--grey-grey-darken-2, #616161);
}

.items-action {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    v-btn {
        width: 121px; // Matching Figma design
    }
}

.pill {
    max-width: 100%;
    padding: 6px 12px;
    height: auto !important;
}

.pill-data {
    display: flex;
    gap: 4px;
}

.pill-data .value {
    white-space: normal;
    word-break: break-word;
}
</style>
