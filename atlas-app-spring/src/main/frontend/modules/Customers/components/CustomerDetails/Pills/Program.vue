<template>
    <Pill color="black">
        <template #content>
            {{ pillText }}
        </template>
    </Pill>
</template>

<script>
import { defineComponent } from "vue";
import Pill from "Components/Pill.vue";

export default defineComponent({
    name: "ProgramPill",
    components: { Pill },
    props: {
        pillText: {
            type: String,
            required: true,
        },
    },

    computed: {},
});
</script>

<style scoped lang="scss"></style>
