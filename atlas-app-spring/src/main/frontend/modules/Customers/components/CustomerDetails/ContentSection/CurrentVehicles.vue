<template>
    <div>
        <v-card class="current-vehicles-card-wrapper" flat>
            <v-card-title>
                <span class="mr-2">Current Vehicles</span>
                <info-tooltip size="20" :max-width="250">
                    These are vehicles a customer is interested in trading in.
                </info-tooltip>
            </v-card-title>
            <v-card-text>
                <v-data-table
                    :loading="loading"
                    :headers="computedHeaders"
                    :items="trades"
                    :items-per-page="5"
                    :page="tradesPageNumber"
                    no-data-text="No trades found for this user"
                    role="button"
                    @click:row="(e, data) => viewTradeDetails(data.item.id)"
                >
                    <template #header.status>
                        <span class="text-no-wrap">
                            Status
                            <info-tooltip size="20" :max-width="250">
                                <div class="d-flex flex-column">
                                    <div v-for="(status, index) in statuses" :key="index + '-status'" class="mb-2">
                                        {{ status }}
                                    </div>
                                </div>
                            </info-tooltip>
                        </span>
                    </template>
                    <template #item.vehicle="{ item }">
                        {{ item.vehicle.ymm }} <span v-if="item.vehicle.trim">{{ item.vehicle.trim }}</span>
                    </template>
                    <template #item.status="{ item }">
                        <div v-if="item.paymentType !== 'LEASE'" key="finance-status">
                            <v-chip v-if="item.provider === 'dealer'" key="dealer-quote" small color="green" dark>
                                Dealer Quote
                            </v-chip>

                            <v-chip
                                v-else-if="item.offer > 40000 || item.vehicle.year >= new Date().getFullYear()"
                                key="no-guarantee"
                                small
                                color="green"
                                dark
                            >
                                No Guarantee
                            </v-chip>

                            <v-chip v-else-if="item.completed" key="completed" small color="green" dark>Valid</v-chip>

                            <v-chip v-else key="incomplete" small color="warning" dark>Incomplete</v-chip>
                        </div>

                        <div v-else key="lease-status">
                            <v-chip small color="warning" dark>Lease Return</v-chip>
                        </div>
                    </template>
                    <template #item.expired="{ item }">
                        <div v-if="item.expired && item.completed">
                            <v-tooltip left max-width="300px">
                                <template #activator="{ on, attrs }">
                                    <v-chip small color="red" dark v-bind="attrs" v-on="on"> Expired </v-chip>
                                </template>
                                <div>Expired on: {{ item.expirationDate | moment("l") }}</div>
                            </v-tooltip>
                        </div>

                        <v-chip v-if="!item.expired && item.completed && item.expirationDate" small color="green" dark>
                            Expires on {{ item.expirationDate | moment("l") }}
                        </v-chip>
                    </template>
                    <template #item.condition="{ item }">
                        <v-chip v-if="item.condition" small>{{ item.condition }}</v-chip>
                    </template>
                    <template #item.mileage="{ item }">
                        {{ item.mileage | numeral("0,00") }}
                    </template>
                    <template #item.paymentType="{ item }">
                        {{ item.paymentType }}
                    </template>
                    <template #item.tradeType="{ item }">
                        {{ item.tradeType | formattedTradeType }}
                    </template>
                    <template #item.payment="{ item }">
                        {{ item.payment | numeral("$0,00.00") }}
                    </template>
                    <template #item.finance.name="{ item }">
                        <span>
                            {{ financeName(item) }}
                        </span>
                    </template>
                    <template #item.payoff="{ item }">
                        <span v-if="shouldDisplayTradeValue(item)">
                            {{ item.payoff | numeral("$0,00.00") }}
                        </span>
                    </template>
                    <template #item.offer="{ item }">
                        <span v-if="shouldDisplayTradeValue(item)">
                            {{ item.offer | numeral("$0,00.00") }}
                        </span>
                    </template>
                    <template #item.equity="{ item }">
                        <span v-if="shouldDisplayTradeValue(item)">
                            {{ item.equity | numeral("$0,00.00") }}
                        </span>
                    </template>
                    <template #item.actions="{ item }">
                        <span class="text-no-wrap">
                            <v-btn outlined color="primary" x-small @click="viewTradeDetails(item.id)">View</v-btn>
                        </span>
                    </template>
                </v-data-table>
            </v-card-text>
        </v-card>
        <v-navigation-drawer
            v-model="drawerShowing"
            fixed
            temporary
            right
            :width="$vuetify.breakpoint.xs ? '80%' : '60%'"
        >
            <template #prepend>
                <div class="float-right py-4 pr-4">
                    <v-btn icon outlined small @click.stop="closeDrawer">
                        <v-icon>mdi-chevron-right</v-icon>
                    </v-btn>
                </div>
            </template>
            <trade-preview v-if="selectedTradeId" :trade-id="selectedTradeId" :dealer-id="dealerId" />
        </v-navigation-drawer>
    </div>
</template>

<script>
import InfoTooltip from "Components/InfoTooltip.vue";
import api from "Util/api";
import EventBus from "Util/eventBus";
import lodashGet from "lodash/get";
import TradePreview from "Modules/Customers/components/UserDetails/TradePreview.vue";

export default {
    name: "CurrentVehicles",
    components: { TradePreview, InfoTooltip },
    filters: {
        formattedTradeType(tradeType) {
            let result = "";
            if (tradeType != null) {
                result = tradeType
                    .toLowerCase()
                    .split("_")
                    .map((word) => word.charAt(0).toUpperCase() + word.substring(1))
                    .join(" ");
            }
            return result === "Lease Turn In" ? "Lease Turn-In " : result;
        },
    },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            tradesPageNumber: 1,
            headers: [
                { text: "Actions", value: "actions", sortable: false },
                {
                    text: "Vehicle",
                    value: "vehicle",
                    sortable: false,
                    width: 200,
                },
                {
                    text: "Status",
                    value: "status",
                    sortable: false,
                },
                { text: "Expiration", value: "expired", sortable: false },
                { text: "Condition", value: "condition", sortable: false },
                { text: "Finance Type", value: "paymentType", sortable: false, width: 120 },
                { text: "Trade Type", value: "tradeType", sortable: false, width: 120 },

                { text: "Color", value: "exteriorColor", sortable: false, width: 200 },
                { text: "Miles", value: "mileage", sortable: false },

                { text: "Payment", value: "payment", sortable: false },
                { text: "Finance Co.", value: "finance.name", sortable: false, width: 200 },
                { text: "Payoff", value: "payoff", sortable: false },
                { text: "Offer", value: "offer", sortable: false },
                { text: "Equity", value: "equity", sortable: false },
            ],
            statuses: [
                "A status of Valid means the User completed the trade process.",
                "A status of Incomplete means the User did not finish the trade process.",
                "A status of Lease Return means the vehicle being traded is a lease.",
                "A status of Dealer Quote means the vehicle being traded has been quoted by the dealer.",
                "A status of No Guarantee means the vehicle is either less than 1 year old or valued at more than $40,000 and therefore does not have a Guaranteed offer.",
            ],

            trades: [],
            loading: false,
            drawerShowing: false,
            selectedTradeId: null,
        };
    },

    computed: {
        isLeasePayOffEnabled() {
            //isLeasePayOffEnabled will remain same for all the items in the list (either true or false)
            return lodashGet(this.trades[0], "isLeasePayoffEnabled", false) || false;
        },

        computedHeaders() {
            if (!this.isLeasePayOffEnabled) {
                return this.headers.filter((item) => item.text !== "Trade Type");
            }
            return this.headers;
        },
    },

    watch: {
        drawerShowing() {
            if (!this.drawerShowing) {
                this.selectedTradeId = null;
            }
        },
    },

    created() {
        this.fetchUserTrades(this.dealerId, this.userId);

        EventBus.$on("refresh-deals", () => this.fetchUserTrades(this.dealerId, this.userId));
    },

    methods: {
        fetchUserTrades(dealerId, userId) {
            this.loading = true;
            let params = {};
            if (!_.isNil(dealerId)) {
                params["dealerIds"] = dealerId;
            }
            api.get(`/users/${userId}/trades`, params)
                .then((response) => {
                    this.trades = _.get(response, "data.trades", []);
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        viewTradeDetails(tradeId) {
            this.selectedTradeId = tradeId;
            this.drawerShowing = true;
        },
        shouldDisplayTradeValue(item) {
            return item.paymentType !== "LEASE" || this.isLeasePayOffEnabled;
        },
        financeName(item) {
            return lodashGet(item, "finance.name", null);
        },
        closeDrawer() {
            this.drawerShowing = false;
            this.selectedTradeId = null;
        },
    },
};
</script>
