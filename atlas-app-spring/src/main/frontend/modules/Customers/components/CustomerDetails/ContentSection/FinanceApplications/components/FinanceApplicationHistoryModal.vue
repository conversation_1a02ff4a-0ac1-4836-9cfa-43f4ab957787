<template>
    <v-dialog v-model="show" max-width="700">
        <v-card>
            <v-card-title class="modal-title">
                <v-icon class="mdiHistoryIcon mr-1"> mdi-history </v-icon>
                Finance Application History
                <div class="close-modal-icon mr-1">
                    <v-btn class="ml-2" text icon @click="closeDialog">
                        <v-icon>mdi-close</v-icon>
                    </v-btn>
                </div>
            </v-card-title>

            <v-card-text class="modal-content">
                <v-row class="mb-2 pl-2">
                    <v-col cols="12" md="6"><strong class="mr-2">Finance Company</strong> {{ financeCompany }}</v-col>
                    <v-col cols="12" md="6"><strong class="mr-2">App Id</strong> {{ partnerAppId }}</v-col>
                    <v-col cols="12" md="6">
                        <strong class="mr-2">Requested Amount</strong>
                        {{ requestedAmountFinanced | numeral("$0,00.00") }}
                    </v-col>
                    <v-col cols="12" md="6"><strong class="mr-2">CarSaver App ID</strong> {{ applicationId }}</v-col>
                    <v-col cols="12" md="6">
                        <strong class="mr-2">Decisions received</strong> {{ oldDecisions.data.length }}
                    </v-col>
                    <v-col cols="12" md="6">
                        <strong class="mr-2">Expiration Date</strong>
                        {{ expirationDate | moment("MM/DD/YYYY") }}
                    </v-col>
                </v-row>

                <v-data-table
                    :headers="headers"
                    :items="oldDecisions.data"
                    :loading="oldDecisions.loader.isLoading"
                    item-key="rev"
                    class="grey lighten-3"
                    no-data-text="No Finance Applications History found"
                    disable-pagination
                    hide-default-footer
                >
                    <template #item.status="{ item }">
                        <status-chip-v2 v-if="isARFinanceAppEnabled" :status="item.status" />
                        <status-chip v-else :status="item.status" />
                    </template>
                    <template #item.buyRate="{ item }">
                        <div v-if="item.buyRate">{{ item.buyRate | numeral("0.00") }}%</div>
                    </template>
                    <template #item.sellRate="{ item }">
                        <div v-if="item.sellRate">{{ item.sellRate | numeral("0.00") }}%</div>
                    </template>
                    <template #item.maxLoanAmount="{ item }">
                        <div v-if="item.maxLoanAmount">{{ item.maxLoanAmount | numeral("$0,00.00") }}</div>
                    </template>
                    <template #item.monthlyPayment="{ item }">
                        <div v-if="item.monthlyPayment">{{ item.monthlyPayment | numeral("$0,00.00") }}</div>
                    </template>
                </v-data-table>
            </v-card-text>

            <v-card-actions class="d-flex flex-row-reverse pb-3">
                <v-btn class="white--text close-btn" color="primary" @click="closeDialog">
                    <span> CLOSE </span>
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import EventBus from "Util/eventBus";
import loader from "Util/loader";
import api from "Util/api";
import StatusChip from "./StatusChip";
import StatusChipV2 from "./StatusChipV2";
import { get } from "vuex-pathify";
import lodashGet from "lodash/get";

export default {
    name: "FinanceApplicationHistoryModal",
    components: { StatusChip, StatusChipV2 },
    props: {
        partnerAppId: {
            type: String,
            required: false,
            default: "",
        },
        applicationId: {
            type: String,
            required: true,
            default: "",
        },
        decisionId: {
            type: Number,
            required: true,
            default: 0,
        },
        financeCompany: {
            type: String,
            required: true,
            default: "",
        },
        requestedAmountFinanced: {
            type: Number,
            required: false,
            default: 0,
        },
        expirationDate: {
            type: String,
            required: false,
            default: "",
        },
    },
    data() {
        return {
            show: false,
            headers: [
                {
                    text: "Status",
                    value: "status",
                    sortable: false,
                    width: 125,
                },
                { text: "Tier", value: "tier", sortable: false },
                { text: "Term", value: "term", sortable: false, width: 100 },
                { text: "Buy Rate", value: "buyRate", sortable: false, width: 85 },
                { text: "Sell Rate", value: "sellRate", sortable: false, width: 85 },
                { text: "Max Loan Amount", value: "maxLoanAmount", sortable: false, width: 90 },
                { text: "Payment", value: "monthlyPayment", sortable: false, width: 80 },
            ],
            oldDecisions: {
                data: [],
                loader: loader.defaultState(),
            },
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        isARFinanceAppEnabled() {
            return lodashGet(this.featureFlags, "ENABLE_AR_FINANCE_APP", false);
        },
    },
    watch: {
        show: {
            handler: function (val) {
                if (val) {
                    this.fetchFinanceDecisionsHistory();
                } else {
                    this.oldDecisions.data = [];
                }
            },
            immediate: true,
        },
    },
    created() {
        EventBus.$on("showFinaceApplicationHistoryModal", this.showModal);
        EventBus.$on("hideFinaceApplicationHistoryModal", this.hideModal);
    },
    methods: {
        showModal(decisionId) {
            if (decisionId === this.decisionId) this.show = true;
        },
        closeDialog() {
            this.hideModal();
        },
        hideModal() {
            this.show = false;
        },
        fetchFinanceDecisionsHistory() {
            this.oldDecisions.loader = loader.started();
            return api
                .get(`/loanresponses/${this.decisionId}/changes`)
                .then((response) => {
                    this.oldDecisions.data = response.data;
                    this.oldDecisions.loader = loader.successful();
                })
                .catch((error) => {
                    console.error(error);
                    this.oldDecisions.loader = loader.error(error);
                });
        },
    },
};
</script>

<style lang="scss" scoped>
.modal-title {
    line-height: 1.6;
}
.modal-content {
    margin-top: 25px;
    color: #212121 !important;
}
.close-modal-icon {
    position: absolute;
    right: 0;
}
.close-btn {
    height: 32px !important;
    padding: 10px 16px !important;
}
</style>
