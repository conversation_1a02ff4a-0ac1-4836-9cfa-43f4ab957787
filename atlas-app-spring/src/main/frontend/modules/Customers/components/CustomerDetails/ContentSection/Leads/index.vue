<template>
    <div>
        <v-card flat>
            <v-card-title>
                <span class="mr-2">Leads</span>
                <info-tooltip size="20" :max-width="250">
                    Appointments are made when a customer schedules a test drive, Connections are made when a customer
                    fills out the contact dealer form & Order Request are when a customer purchases a vehicle.
                </info-tooltip>
            </v-card-title>
            <v-card-text>
                <v-data-table
                    :loading="loader.isLoading"
                    :headers="headers"
                    :items="leads"
                    :items-per-page="5"
                    :page="leadsPageNumber"
                    no-data-text="No leads found for this user"
                    role="button"
                >
                    <template #item.type="{ item }">
                        {{ item.type }}
                        <info-tooltip size="14">{{ item.leadType }}</info-tooltip>
                    </template>
                    <template #item.visitTime="{ item }">
                        <span v-if="item.visitTime"> {{ convertTime(item.visitTime, item.timeZone) }} </span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.stockType="{ item }">
                        <v-chip v-if="item.stockTypeName" small>{{ item.stockTypeName }}</v-chip>
                        <span v-else>- -</span>
                    </template>
                    <template #item.vehicle="{ item }">
                        <span v-if="item.vehicle">{{ item.vehicle }}</span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.inTransit="{ item }">
                        <boolean-indicator :value="item.inTransit" />
                    </template>
                    <template #item.certificate.trim="{ item }">
                        <span v-if="item.certificate.trim">{{ item.certificate.trim }}</span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.certificate.exteriorColor="{ item }">
                        <div v-if="item.certificate.exteriorColorHex || item.certificate.exteriorColor">
                            <color-chip
                                v-if="item.certificate.exteriorColorHex"
                                :color="item.certificate.exteriorColorHex"
                            >
                                {{ item.certificate.exteriorColor }}
                            </color-chip>
                            <color-chip v-else color="FFFFFF">
                                {{ item.certificate.exteriorColor }}
                            </color-chip>
                        </div>
                        <span v-else>- -</span>
                    </template>
                    <template #item.certificate.msrp="{ item }">
                        <span v-if="item.certificate.msrp">{{ item.certificate.msrp | numeral("$0,00.00") }}</span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.certificate.salePrice="{ item }">
                        <span v-if="item.certificate.salePrice">
                            {{ item.certificate.salePrice | numeral("$0,00.00") }}
                        </span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.certificate.dealType="{ item }">
                        {{ item.certificate.dealType != null ? item.certificate.dealType : "- -" }}
                    </template>
                    <template #item.certificate.paymentType="{ item }">
                        <span v-if="item.certificate.dealType !== 'CASH'">
                            {{ item.certificate.paymentType != null ? item.certificate.paymentType : "- -" }}
                        </span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.certificate.payment="{ item }">
                        <span v-if="item.certificate.dealType !== 'CASH' && item.certificate.payment">
                            {{ item.certificate.payment | numeral("$0,00.00") }}
                        </span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.createdDate="{ item }">
                        <span v-if="item.createdDate"> {{ convertTime(item.createdDate, item.timeZone) }} </span>
                        <span v-else>- -</span>
                    </template>
                    <template #item.actions="{ item }">
                        <leadDetails :user-id="userId" :dealer-id="dealerId" :lead="item">
                            <template #activator>
                                <v-btn outlined color="primary" x-small> View </v-btn>
                            </template>
                        </leadDetails>
                    </template>
                </v-data-table>
            </v-card-text>
        </v-card>
    </div>
</template>

<script>
import { call, get } from "vuex-pathify";
import lodashGet from "lodash/get";
import ColorChip from "Components/ColorChip";
import BooleanIndicator from "Components/BooleanIndicator";
import timeDateFormatter from "@/util/formatters";
import InfoTooltip from "Components/InfoTooltip";
import moment from "moment-timezone";
import leadDetails from "./components/LeadDetails.vue";

export default {
    name: "Leads",
    components: { BooleanIndicator, ColorChip, InfoTooltip, leadDetails },
    props: {
        userId: {
            type: String,
            required: true,
        },
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            drawerShowing: false,
            selectedDeal: null,
            leadsPageNumber: 1,
            headers: [
                { text: "Actions", value: "actions", sortable: false },
                { text: "Type", value: "type", sortable: false },
                { text: "Lead Type", value: "leadType", sortable: false },
                { text: "Stock", value: "stockType", sortable: false },
                {
                    text: "Vehicle",
                    value: "vehicle",
                    sortable: false,
                },
                {
                    text: "In Transit",
                    value: "inTransit",
                    sortable: false,
                },
                {
                    text: "Trim",
                    value: "certificate.trim",
                    sortable: false,
                },
                {
                    text: "Appt Date/Time",
                    value: "visitTime",
                    sortable: false,
                },
                {
                    text: "Color",
                    value: "certificate.exteriorColor",
                    sortable: false,
                },
                { text: "MSRP", value: "certificate.msrp", sortable: false },
                {
                    text: "Sale Price",
                    value: "certificate.salePrice",
                    sortable: false,
                },
                {
                    text: "Deal Type",
                    value: "certificate.dealType",
                    sortable: false,
                },
                {
                    text: "Payment Type",
                    value: "certificate.paymentType",
                    sortable: false,
                },
                {
                    text: "Payment",
                    value: "certificate.payment",
                    sortable: false,
                },
                {
                    text: "Date/Time",
                    value: "createdDate",
                    sortable: false,
                },
            ],
        };
    },
    computed: {
        leadsData: get("userDetails/leads@data"),
        loader: get("userDetails/leads@loader"),
        leads() {
            return lodashGet(this.leadsData, "leads", []);
        },
    },

    watch: {
        leadsPageNumber(newVal) {
            this.fetchUserLeads({
                dealerId: this.dealerId,
                page: newVal - 1,
            });
        },
    },

    created() {
        this.fetchUserLeads({
            dealerId: this.dealerId,
        });
    },

    methods: {
        fetchUserLeads: call("userDetails/fetchUserLeads"),
        timeDateFormatter(date) {
            return timeDateFormatter(date);
        },
        convertTime(dateTimeString, timeZone) {
            if (!dateTimeString || !timeZone) {
                return "";
            }
            const formattedTime = moment.tz(dateTimeString, timeZone).format("M/D/YYYY hh:mm a z");

            return formattedTime;
        },
    },
};
</script>
