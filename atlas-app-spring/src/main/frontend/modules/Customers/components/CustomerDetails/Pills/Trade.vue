<template>
    <Pill color="grey">
        <template #prependIcon>
            <v-icon left class="left-icon"> mdi-cash-multiple </v-icon>
        </template>
        <template #content>
            {{ pillText }}
        </template>
    </Pill>
</template>

<script>
import { defineComponent } from "vue";
import Pill from "Components/Pill.vue";

export default defineComponent({
    name: "TradePill",
    components: { Pill },
    props: {
        pillText: {
            type: String,
            required: true,
        },
    },

    computed: {},
});
</script>

<style scoped lang="scss">
.left-icon {
    font-size: px2rem(18) !important;
    margin-left: 0 !important;
    margin-right: 4px !important;
    color: var(--grey-grey-darken-2, #616161) !important;
}
</style>
