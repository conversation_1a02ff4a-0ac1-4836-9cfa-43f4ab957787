<template>
    <v-dialog v-model="dialog" max-width="600" max-height="800">
        <template v-if="$slots.activator" #activator="{ on, attrs }">
            <span v-bind="attrs" v-on="on">
                <slot name="activator" />
            </span>
        </template>

        <v-card class="lead-detail-wrapper h-100">
            <v-card-title>
                Lead Information
                <div class="close-modal-icon mr-1">
                    <v-btn class="ml-2" text icon @click="dialog = false">
                        <v-icon small>mdi-close</v-icon>
                    </v-btn>
                </div>
            </v-card-title>
            <v-card-subtitle>XML sent to dealer</v-card-subtitle>
            <v-card-text class="pt-2 pb-0 h-100">
                <div class="xml-content">
                    <pre v-if="lead.adfPayload">
                        {{ lead.adfPayload }}
                    </pre>
                    <div v-else class="justify-content-center">Failed to load adf content at the moment.</div>
                </div>
            </v-card-text>
            <v-card-actions class="d-flex flex-row px-2 px-md-4 py-2 pb-4 gap-2">
                <v-spacer></v-spacer>
                <send-to-crm-modal
                    :user-id="userId"
                    :dealer-id="dealerId"
                    :selected-dealer-id="dealerId"
                    :name="fullName"
                    :dealership="selectedDealerName"
                    :vehicle="lead?.vehicle"
                    from="customers"
                >
                    <template #activator>
                        <v-btn large outlined class="mr-2 rsc-btn" min-width="150" @click="dialog = false">
                            Re-Send to CRM
                        </v-btn>
                    </template>
                </send-to-crm-modal>
                <v-btn
                    v-if="showDealDetails"
                    large
                    color="primary"
                    min-width="150"
                    class="view-deal"
                    @click="showIframeOverlay"
                >
                    <span> View Deal </span>
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<script>
import { get, call } from "vuex-pathify";
import SendToCrmModal from "./SendToCrmModal";
import lodashGet from "lodash/get";

export default {
    components: { SendToCrmModal },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
        lead: {
            type: Object,
            required: true,
        },
    },
    data: () => {
        return {
            dialog: false,
            parsedData: {},
        };
    },
    computed: {
        user: get("userDetails/userModel@data"),
        selectedDealerName: get("loggedInUser/selectedDealer@name"),
        dealerUserId: get("loggedInUser/userId"),
        otpToken: get("customerDetails/<EMAIL>"),
        menuLinks: get("customerDetails/customerDetailsLinks@data"),
        fullName() {
            return `${this.user.firstName} ${this.user.lastName}`;
        },
        showDealDetails() {
            return this.lead.vehicleActive && !this.lead.certificate.deleted;
        },
        environment() {
            return lodashGet(window, "_APP_CONFIG.env[0]", "local");
        },
        leadsBaseURL() {
            const leadsMenu = this.menuLinks.find((menuItem) => menuItem.name === "Leads");
            return leadsMenu.url;
        },
        iframeUrl() {
            return `${this.leadsBaseURL}/my-deal-page?vin=${this.lead.vin}&certificateId=${this.lead.certificate.id}#Payment`;
        },
    },

    methods: {
        openOverlay: call("customerDetails/openOverlay"),
        showIframeOverlay() {
            // close the modal and then open overlay
            this.dialog = false;
            this.openOverlay({
                iframeUrl: this.iframeUrl,
                dealerId: this.lead.dealer.id,
                dealerUserId: this.dealerUserId,
                otpToken: this.otpToken,
            });
        },
    },
};
</script>

<style scoped lang="scss">
.lead-detail-wrapper {
    background-color: #fff;
    max-height: 800px;
    overflow-y: hidden;
    overflow-x: hidden;
}
.xml-content {
    height: 400px;
    max-width: 600px;
    overflow-y: scroll;
    overflow-x: scroll;
    color: #000;
}

.modal-title {
    line-height: 1.6;
}
.close-modal-icon {
    position: absolute;
    right: 0;
}
.close-btn {
    height: 32px !important;
    padding: 10px 16px !important;
}
.rsc-btn {
    max-width: 150px;
    @media only screen and (min-width: 768px) {
        max-width: unset !important;
    }
}
</style>
