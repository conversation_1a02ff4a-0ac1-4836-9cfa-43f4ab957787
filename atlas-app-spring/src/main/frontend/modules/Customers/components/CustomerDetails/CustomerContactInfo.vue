<template>
    <div class="contact-info-wrapper">
        <v-card-title class="text-capitalize pa-0">
            <div class="mr-auto user-name">
                {{ user.firstName }}
                {{ user.lastName }}
            </div>
        </v-card-title>
        <div class="flex-md-row gap-md-flex flex-column d-flex contact-info-container">
            <div v-if="user.phoneNumber" class="">
                <v-icon class="pr-2 custom-icon"> mdi-phone-outline </v-icon>
                <a :href="`tel: ${user.phoneNumber}`" class="phone-number">{{ user.phoneNumber | phoneFormatter }}</a>
            </div>
            <v-divider v-if="user.phoneNumber" inset vertical class="custom-divider d-none d-md-flex"></v-divider>
            <div>
                <v-icon class="pr-2 custom-icon"> mdi-email-outline </v-icon>
                <a :href="`mailto: ${user.email}`" class="text-primary">{{ user.email }}</a>
            </div>
            <v-divider inset vertical class="custom-divider d-none d-md-flex"></v-divider>
            <div>
                <v-icon class="float-left pr-2 custom-icon"> mdi-map-marker-outline </v-icon>
                <span class="d-block overflow-hidden">
                    {{ addressLine1 }} <br v-if="addressLine1" />
                    {{ addressLine2 }}
                </span>
            </div>
            <template v-if="enableLanguageSelection">
                <v-divider inset vertical class="custom-divider d-none d-md-flex"></v-divider>
                <div>
                    <v-icon class="float-left pr-2 custom-icon"> mdi-earth </v-icon>
                    <span class="d-block overflow-hidden">
                        {{ localeHandler(locale) }}
                    </span>
                </div>
            </template>
            <v-divider inset vertical class="custom-divider d-none d-md-flex"></v-divider>
            <div>
                <v-icon class="float-left pr-2 custom-icon"> mdi-calendar-blank-outline </v-icon>
                <span class="d-block overflow-hidden"
                    >Customer Since: <br class="d-md-block d-none" />
                    {{ user.createdDate | formatDate }}</span
                >
            </div>
        </div>
    </div>
</template>

<script>
import { call, get } from "vuex-pathify";
import lodashGet from "lodash/get";
import isNil from "lodash/isNil";
import moment from "moment";
export default {
    filters: {
        formatDate(val) {
            const formattedDate = val ? moment(val).format("MM/DD/YYYY") : "";
            return formattedDate;
        },
    },

    computed: {
        user: get("userDetails/userModel@data"),
        loader: get("userDetails/userModel@loader"),
        locale: get("userDetails/<EMAIL>"),
        enableLanguageSelection: get("loggedInUser/featureFlags@ENABLE_LANGUAGE_SELECTION"),
        addressLine2() {
            let addr2 = [];
            let addressLine2 = "";
            const city = lodashGet(this.user, "city");
            const stateCode = lodashGet(this.user, "stateCode", "");
            const zip = lodashGet(this.user, "zipCode", "");
            const stateZip = stateCode ? `${stateCode} ${zip}` : zip;
            addr2 = [city, stateZip];
            addressLine2 = addr2
                .filter(function (value) {
                    return !isNil(value);
                })
                .join(", ");
            return addressLine2;
        },
        addressLine1() {
            let add1 = lodashGet(this.user, "address");
            let add2 = lodashGet(this.user, "address2");
            if (add1 === "Unknown") {
                return "";
            }
            if (add2) {
                return add1 + ", " + add2;
            }
            return add1;
        },
    },

    methods: {
        localeHandler(locale) {
            const EN = "English";
            const ES = "Español";
            const defaultLocale = EN;

            if (isNil(locale)) {
                return defaultLocale;
            }

            const localeLowerCase = locale.trim().toLowerCase();

            switch (localeLowerCase) {
                case "en":
                    return EN;
                case "es":
                    return ES;
                default:
                    console.trace("warning: locale not found", locale);
                    return locale;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.gap-md-flex {
    color: var(--grey-grey-darken-3, #424242);
    gap: 4px;
    @media #{map-get($display-breakpoints, 'md-and-up')} {
        gap: 15px;
    }
}

.custom-divider {
    max-height: 16px !important;
    color: #9e9e9e !important;
}

.custom-icon {
    color: var(--grey-grey-darken-3, #424242);
}

.phone-number {
    text-decoration: none;
    color: var(--grey-grey-darken-3, #424242);
}

.user-name {
    font-size: 24px;
    font-weight: 700;
    color: var(--grey-grey-darken-4, #212121);
}

.contact-info-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;
}
</style>
