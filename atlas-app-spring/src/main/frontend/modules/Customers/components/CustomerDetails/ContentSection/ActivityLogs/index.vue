<template>
    <v-card v-if="isARActivityEnabled" class="notes-card-wrapper pb-4" flat>
        <v-card-title class="d-flex align-center mb-1"> Activity ({{ activityCount }}) </v-card-title>

        <v-skeleton-loader v-if="loader.isLoading" type="image" class="ma-4" />

        <div v-else class="activity-logs-content">
            <SearchActivityLogs />

            <v-expansion-panels v-if="activityLogs.length" v-model="openPanels" class="timeline-cards" multiple>
                <ActivityLogCard
                    v-for="(log, index) in paginatedActivityLogs"
                    :key="index"
                    :activity-log="log"
                    :dealer-id="dealerId"
                    :url="activityURL"
                    class="w-100"
                />
            </v-expansion-panels>

            <v-card v-else flat class="no-activity__card">
                <span class="no-activity__info">No activity has been saved</span>
            </v-card>
        </div>

        <Pagination
            v-if="activityLogs.length"
            class="pr-md-5"
            :pagination="paginationData"
            @update:pagination="handlePaginationUpdate"
        ></Pagination>
    </v-card>
</template>

<script>
import { get, call } from "vuex-pathify";
import ActivityLogCard from "./components/ActivityLogCard.vue";
import SearchActivityLogs from "./components/SearchActivityLog.vue";
import Pagination from "Components/Pagination";
import lodashGet from "lodash/get";

export default {
    name: "ActivityLogs",
    components: {
        ActivityLogCard,
        SearchActivityLogs,
        Pagination,
    },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            openPanels: [],
            currentPage: 1,
            itemsPerPage: 10,
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        activityLogs: get("customerDetails/filteredActivityLogs"),
        loader: get("customerDetails/activityLogs@loader"),
        menuLinks: get("customerDetails/customerDetailsLinks@data"),

        paginationData() {
            return {
                page: this.currentPage,
                itemsPerPage: this.itemsPerPage,
                totalItems: this.activityLogs.length,
            };
        },
        paginatedActivityLogs() {
            const startIndex = (this.currentPage - 1) * this.itemsPerPage;
            const endIndex = startIndex + this.itemsPerPage;
            return this.activityLogs.slice(startIndex, endIndex);
        },

        activityCount() {
            const activityMenu = this.menuLinks.find((menuItem) => menuItem.name === "Activity");
            return activityMenu.count;
        },

        isARActivityEnabled() {
            return lodashGet(this.featureFlags, "ENABLE_AR_ACTIVITY", false);
        },

        activityURL() {
            const activityMenu = this.menuLinks.find((menuItem) => menuItem.name === "Activity");
            return activityMenu.url;
        },
    },

    watch: {
        activityLogs: {
            handler(val) {
                this.openPanels = [...Array(this.activityLogs.length).keys()];
            },
            immediate: true,
        },
    },

    mounted() {
        if (this.isARActivityEnabled) {
            this.fetchActivityLogs({ dealerId: this.dealerId, userId: this.userId });
        }
    },

    methods: {
        fetchActivityLogs: call("customerDetails/fetchActivityLogs"),
        handlePaginationUpdate(newPagination) {
            this.currentPage = newPagination.page;
            this.itemsPerPage = newPagination.itemsPerPage;
        },
    },
};
</script>

<style lang="scss" scoped>
.activity-logs-content {
    display: flex;
    flex-direction: column;
    margin: 24px;
    margin-top: 0px;
    gap: 24px;
}

.timeline-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
    > div {
        width: 100% !important;
    }
}

.no-activity__card {
    border-radius: 8px;
    border: 1px solid var(--grey-grey-lighten-1, #bdbdbd);
    display: flex;
    padding: 16px 24px;
    align-self: stretch;
    justify-content: center;
}

.no-activity__info {
    font-size: 16px;
    color: var(--opacity-text--disabled, rgba(0, 0, 0, 0.37));
    text-align: center;
    font-weight: 500;
    display: flex;
}
</style>
