<template>
    <v-card v-if="isARSeachesEnabled" flat class="pb-4">
        <v-card-title>Searches</v-card-title>
        <v-skeleton-loader v-if="loader.isLoading" type="image" class="px-4 pb-4" />
        <div v-else-if="searchHistory.length" class="px-4 d-flex flex-column search-card-container pb-4">
            <searchCard
                v-for="(search, index) in searchHistory"
                :key="index"
                :search-data="search"
                :dealer-id="dealerId"
                :user-id="userId"
            ></searchCard>
        </div>
        <div v-else>
            <v-card flat outlined class="no-search__card mx-4">
                <span class="no-search__info">No search history available</span>
            </v-card>
        </div>
    </v-card>
</template>

<script>
import { defineComponent } from "vue";
import searchCard from "./components/SearchHistoryCard.vue";
import { call, get } from "vuex-pathify";
import lodashGet from "lodash/get";

export default defineComponent({
    name: "SearchHistory",
    components: {
        searchCard,
    },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    computed: {
        searchHistory: get("customerDetails/searchHistory@data"),
        loader: get("customerDetails/searchHistory@loader"),
        featureFlags: get("loggedInUser/featureFlags"),

        isARSeachesEnabled() {
            return lodashGet(this.featureFlags, "ENABLE_AR_SEARCHES", false);
        },
    },

    mounted() {
        if (this.isARSeachesEnabled) {
            this.fetchCustomerSearchHistory({ dealerId: this.dealerId, userId: this.userId });
        }
    },

    methods: {
        fetchCustomerSearchHistory: call("customerDetails/fetchCustomerSearchHistory"),
    },
});
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.search-card-container {
    gap: 16px;
}

.no-search__card {
    border-radius: 8px;
    border: 1px solid var(--grey-grey-lighten-1, #bdbdbd);
    display: flex;
    padding: 16px 24px;
    align-self: stretch;
    justify-content: center;
    margin-bottom: 20px;
}

.no-search__info {
    font-size: 16px;
    color: var(--opacity-text--disabled, rgba(0, 0, 0, 0.37));
    text-align: center;
    font-weight: 500;
    display: flex;
}
</style>
