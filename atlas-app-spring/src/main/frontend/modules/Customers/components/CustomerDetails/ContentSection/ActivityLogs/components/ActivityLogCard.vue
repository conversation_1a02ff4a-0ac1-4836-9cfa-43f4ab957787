<template>
    <SubCollapsible :expand="true">
        <!-- Title Section -->
        <template #title>
            <div class="timeline-card__title">
                <!-- Icon -->
                <v-icon size="18" class="timeline-card__icon">{{ visitIcon }}</v-icon>

                <!-- Visit Information -->
                <div class="d-flex flex-column">
                    <div class="timeline-card__user">
                        <span class="timeline-card__label">{{ formattedVisitType }} Visit:</span>
                        <span class="timeline-card__value">{{ dealerOrWebsiteName }}</span>
                    </div>

                    <div class="timeline-card__description w-100">
                        <span class="timeline-card__date">
                            {{ activityLog.earliestStartTime | formatEpochDate | dateTimeFormat }}
                        </span>
                        <span class="timeline-card__days-ago"
                            >{{ daysAgo }}
                            <span v-if="daysAgo === 1">day ago </span>
                            <span v-else> days ago </span>
                        </span>
                    </div>
                </div>
            </div>
        </template>

        <!-- Body Section -->
        <template #body>
            <div class="timeline-card__content">
                <timeline :dot-size="8" dot-color="#4db6ac" :dense="true" :items="activityLog.activityLogs">
                    <template #content="{ item }">
                        <div class="change-log">
                            <span class="change-log__label">
                                {{ activityType(item.activityType) }}
                                <span v-if="showDescription(item)">:</span>
                            </span>
                            <span v-if="showDescription(item)" class="change-log__value">
                                {{ getActivityDescription(item) }}
                                <span v-if="item.isVehicleActive" class="view-link" @click="showIframeOverlay(item)">
                                    View
                                </span>
                            </span>
                        </div>
                        <span class="change-log__timeStamp">
                            {{ item.eventTime | formatEpochDate | dateTimeFormat }}
                        </span>
                    </template>
                </timeline>
            </div>
        </template>
    </SubCollapsible>
</template>

<script>
import { get, call } from "vuex-pathify";
import timeline from "Components/Timeline";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible";
import { getDaysToDate } from "Util/helpers";

export default {
    name: "ActivityLogCard",
    components: {
        timeline,
        SubCollapsible,
    },
    filters: {
        dateTimeFormat(dateString) {
            if (!dateString) return "";

            const parts = dateString.split(" ");
            if (parts.length < 2) return dateString; // If format is incorrect, return original

            const date = parts[0];
            const time = parts.slice(1).join(" ");

            return `${date} - ${time}`;
        },
    },
    props: {
        activityLog: {
            type: Object,
            required: true,
        },
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        url: {
            type: String,
            required: false,
            default: null,
        },
    },
    computed: {
        dealerUserId: get("loggedInUser/userId"),
        otpToken: get("customerDetails/<EMAIL>"),
        formattedVisitType() {
            return this.formatData(this.activityLog.visitType);
        },
        daysAgo() {
            return getDaysToDate(this.activityLog.earliestStartTime);
        },
        visitIcon() {
            const icon =
                this.activityLog.visitType === "SHOWROOM"
                    ? "mdi-account-supervisor-circle-outline"
                    : "mdi-monitor-account";
            return icon;
        },
        dealerOrWebsiteName() {
            const name =
                this.activityLog.visitType === "SHOWROOM"
                    ? this.activityLog.dealerName
                    : this.activityLog.dealerWebsite;

            return name;
        },
    },
    methods: {
        openOverlay: call("customerDetails/openOverlay"),
        showIframeOverlay(val) {
            this.openOverlay({
                iframeUrl: this.getIframeUrl(val),
                dealerId: this.dealerId,
                dealerUserId: this.dealerUserId,
                otpToken: this.otpToken,
            });
        },
        formatData(value) {
            if (!value) return "";
            return value
                .toLowerCase()
                .replace(/_/g, " ")
                .replace(/\b\w/g, (char) => char.toUpperCase());
        },
        getIframeUrl(val) {
            return `${this.url}/my-deal-page?vin=${val.vin}&certificateId=${val.certificateId}#Payment`;
        },
        activityType(val) {
            switch (val) {
                case "PRE_QUAL_COMPLETED":
                    return "Pre-qual Completed";
                case "CREDIT_APPLICATION_SUBMITTED":
                    return "Finance Application";
                default:
                    return this.formatData(val);
            }
        },
        getActivityDescription(item) {
            switch (item.activityType) {
                case "PRE_QUAL_COMPLETED":
                    return item.preQualStatus || "Success";
                default:
                    return `${item.year} ${item.make} ${item.model}`;
            }
        },

        showDescription(item) {
            return (item.year && item.make && item.model) || item.activityType === "PRE_QUAL_COMPLETED";
        },
    },
};
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";

.timeline-card__title {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding-bottom: 20px;
    }
}

.timeline-card__icon {
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px !important;
    height: 32px !important;
    background-color: var(--teal-teal-lighten-2, #4db6ac);
    border-radius: 50%;
    color: #000;
    padding: 8px;
}

.timeline-card__user {
    display: flex;
    flex-direction: row;
    gap: 8px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        flex-direction: column;
        gap: 0px;
    }
}

.timeline-card__label {
    font-size: 16px;
    font-weight: 600;
    color: var(--grey-grey-darken-4, #212121);
    line-height: 24px;
}

.timeline-card__value {
    font-size: 16px;
    color: var(--grey-grey-darken-3, #424242);
    line-height: 24px;
    font-weight: 500 !important;
}
.timeline-card__description {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}
.timeline-card__date {
    font-size: 14px;
    color: var(--grey-grey-darken-2, #616161);
    font-weight: 500 !important;
}
.timeline-card__days-ago {
    font-size: 14px;
    position: absolute;
    right: 12px;
    bottom: 14px;
    font-weight: 500 !important;
    color: var(--grey-grey-darken-2, #616161);
}
.timeline-card__timeline-item {
    margin-bottom: 12px;
}
.change-log {
    display: flex;
    flex-direction: row;
    gap: 0px 4px;
    color: var(--grey-dark, #212121);
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        flex-direction: column;
    }
}
.change-log__label {
    font-weight: bold;
}
.change-log__value {
    display: inline-flex;
    gap: 4px;
    color: var(--grey-grey-darken-4, #212121);
}

.change-log__timeStamp {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    color: var(--grey-grey-darken-2, #616161);
}

:deep(.custom-timeline__dot) {
    margin-top: 15px;
}

.view-link {
    text-decoration: underline;
    color: var(--standard-theme-primary, #1976d2) !important;
    padding-left: 8px;
    padding-right: 8px;
    cursor: pointer;
}
</style>
