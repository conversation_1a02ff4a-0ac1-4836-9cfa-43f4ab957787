<template>
    <div class="d-flex gap-4 flex-column flex-md-row search-container">
        <v-select
            v-model="activityType"
            label="Activity Type"
            :items="activityOptions"
            item-text="name"
            item-value="value"
            hide-details
            outlined
            dense
            class="w-100 w-md-50"
            @change="filterActivities()"
        >
        </v-select>

        <v-select
            v-model="sortOrder"
            label="Sort By"
            item-text="name"
            item-value="value"
            :items="sortingOptions"
            hide-details
            outlined
            dense
            class="w-100 w-md-50"
            @change="filterActivities()"
        >
        </v-select>
    </div>
</template>

<script>
import { get, call, sync } from "vuex-pathify";

export default {
    name: "SearchActivityLogs",
    data: () => ({
        activityOptions: [
            {
                name: "All",
                value: "",
            },
            {
                name: "Account Created",
                value: "ACCOUNT_CREATED",
            },
            {
                name: "Vehicle Viewed",
                value: "VEHICLE_VIEWED",
            },
            {
                name: "Vehicle Search",
                value: "VEHICLE_SEARCH",
            },
            {
                name: "Lead Sent",
                value: "LEAD_SENT",
            },
            {
                name: "Pre-qual Completed",
                value: "PRE_QUAL_COMPLETED",
            },
            {
                name: "Finance Application Submitted",
                value: "CREDIT_APPLICATION_SUBMITTED",
            },
        ],
        sortingOptions: [
            {
                name: "Newest to Oldest",
                value: "desc",
            },
            {
                name: "Oldest to Newest",
                value: "asc",
            },
        ],
        activityType: "",
        sortOrder: "desc",
    }),
    computed: {
        activityLogs: get("customerDetails/activityLogs@data"),
        filteredActivityLogs: sync("customerDetails/filteredActivityLogs"),
    },

    methods: {
        filterActivities() {
            const logs = this.activityType
                ? this.activityLogs
                      .map((entry) => ({
                          ...entry,
                          activityLogs: entry.activityLogs.filter((log) => log.activityType === this.activityType),
                      }))
                      .filter((entry) => entry.activityLogs.length > 0) // Remove entries without matching activities
                : this.activityLogs;

            this.filteredActivityLogs = this.sortLogs([...logs]);
        },

        // Sorting Helper Function
        sortLogs(logs) {
            return logs.sort((a, b) => {
                const dateA = new Date(a.earliestStartTime);
                const dateB = new Date(b.earliestStartTime);
                return this.sortOrder === "asc" ? dateA - dateB : dateB - dateA;
            });
        },
    },
};
</script>

<style>
.search-container {
    justify-content: flex-end;
    gap: 16px 24px;
}
</style>
