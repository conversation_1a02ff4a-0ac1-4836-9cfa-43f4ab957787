<template>
    <div>
        <v-card class="pre-qualifications-card-wrapper" flat>
            <v-card-title>Pre-qualifications</v-card-title>
            <v-divider class="mx-4"></v-divider>
            <v-card-text class="pre-qualifications-content">
                <v-data-table
                    :loading="loader.isLoading"
                    :headers="headers"
                    :items="creditPreQualHistoryData"
                    :items-per-page="5"
                    no-data-text="No Pre-Qualifications found for this user"
                >
                    <template #item.actions="{ item }">
                        <span class="text-no-wrap">
                            <v-btn outlined color="primary" x-small @click="emitOpenPreQualHistoryEvent(item)"
                                >View</v-btn
                            >
                        </span>
                    </template>
                    <template #item.maxPayment="{ item }">
                        {{ getMaxPaymentOrNA(item) }}
                    </template>
                    <template #item.requestDate="{ item }">
                        {{ item.requestDate | dateMDY }}
                    </template>
                    <template #item.expirationDate="{ item }">
                        {{ item.expirationDate | dateMDY }}
                    </template>
                    <template #item.creditTier="{ item }">
                        {{ item.creditTier }} {{ item.creditTier | range }}
                    </template>
                </v-data-table>
            </v-card-text>
        </v-card>
    </div>
</template>

<script>
import { call, get } from "vuex-pathify";
import moment from "moment";
import lodashGet from "lodash/get";

export default {
    name: "PreQualifications",
    filters: {
        dateMDY(val) {
            if (!val) return "N/A";
            // Strip microseconds if present
            const cleanedVal = val.replace(/\.\d+/, "");
            return moment.parseZone(cleanedVal).format("MM/DD/YY");
        },
        range(val) {
            switch (val) {
                case "WORST":
                    return "(0 - 599)";
                case "POOR":
                    return "(600 - 619)";
                case "FAIR":
                    return "(620 - 659)";
                case "GOOD":
                    return "(660 - 699)";
                case "GREAT":
                    return "(700 - 739)";
                case "EXCELLENT":
                    return "(740 - 850)";
                default:
                    return "";
            }
        },
    },
    props: {
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
        userId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            headers: [
                { text: "Pre-Qual Date", value: "requestDate", sortable: false },
                { text: "Status", value: "responseStatus", sortable: false },
                { text: "Max Monthly Payment", value: "maxPayment", sortable: false },
                { text: "Submission Source", value: "source", sortable: false },
                { text: "Bureau", value: "bureau", sortable: false },
                { text: "Finance Program", value: "financeProgram", sortable: false },
                { text: "Credit Tier", value: "creditTier", sortable: false },
                { text: "Expiration", value: "expirationDate", sortable: false },
            ],
            drawerShowing: false,
            selectedPreQualHistoryRecord: null,
        };
    },
    computed: {
        creditPreQualHistory: get("customerDetails/creditPreQualHistory@data"),
        loader: get("customerDetails/creditPreQualHistory@loader"),
        featureFlags: get("loggedInUser/featureFlags"),
        creditPreQualHistoryData() {
            return this.creditPreQualHistory || [];
        },
        isCustomerDetailsPreQualificationsEnabled() {
            return lodashGet(this.featureFlags, "CUSTOMER_DETAILS_PRE_QUALIFICATIONS", false);
        },
    },

    mounted() {
        if (this.isCustomerDetailsPreQualificationsEnabled) {
            this.fetchPreQualHistory({ userId: this.userId });
        }
    },

    methods: {
        fetchPreQualHistory: call("customerDetails/fetchPreQualHistory"),
        emitOpenPreQualHistoryEvent(preQualHistoryRecord) {
            this.selectedPreQualHistoryRecord = preQualHistoryRecord;
            this.drawerShowing = true;
        },
        closeDrawer() {
            this.drawerShowing = false;
        },
        getMaxPaymentOrNA(preQualRecord) {
            if (preQualRecord.maxPayment) {
                return preQualRecord.maxPayment.toLocaleString("en-US", {
                    style: "currency",
                    currency: "USD",
                    minimumFractionDigits: 0,
                });
            }
            return "N/A";
        },
    },
};
</script>
