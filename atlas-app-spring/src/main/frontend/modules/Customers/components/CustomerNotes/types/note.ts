export interface Note {
    id: number;
    createdBy: string;
    createdDate: string;
    lastModifiedDate: string;
    lastModifiedBy: string;
    content: string;
    dealerVisible: boolean;
    entityId: string;
    dealerId: string;
    userId: string;
    noteType: string;
    ownerName: string;
    jobTitle: string;
}

export interface NotePayload {
    id?: number;
    content: string;
    dealerVisible: boolean;
    entityId: string;
    dealerId: string;
    userId: string;
    noteType: string;
}
