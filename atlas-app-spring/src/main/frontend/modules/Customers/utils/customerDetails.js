// Todo: This is a temporary solution add featureflag support for CurrentVehicles section
const featureFlags = window._CS_FEATURE_FLAGS || {};
const flaggedComponents = {};
const isNewCurrentVehiclesSectionEnabled = featureFlags["ATLAS_NEW_CUSTOMER_DETAILS_PAGE_CURRENT_VEHICLES"];

if (!isNewCurrentVehiclesSectionEnabled) {
    flaggedComponents.CURRENT_VEHICLES = "CurrentVehicles"; // This activates the old CurrentVehicles section
}
//////////////////////Above is the temporary solution//////////////////////
///////////////////Remove when feature flag is removed/////////////////////

const COMPONENT_MAPPING = Object.freeze({
    // OVERVIEW: "Overview",
    // SAVED_DEALS: "SavedDeals",
    // VIEWED_VEHICLES: "ViewedVehicles",
    // CURRENT_VEHICLES: "CurrentVehicles",
    ...flaggedComponents,
    ACTIVITYLOGS: "ActivityLogs",
    PRE_QUALIFICATIONS: "PreQualifications",
    PRE_APPROVALS: "PreApprovals",
    LEADS: "Leads",
    FINANCE_APPLICATIONS: "FinanceApplications",
    DOCUMENTS: "Documents",
    NOTES: "Notes",
    VEHICLE_SALES: "VehicleSales",
    CONTRACT_REQUESTS: "ContractRequests",
    SEARCHES: "Searches",

    DEFAULT: "Default",
});

const NAME_COMPONENT_MAPPING = Object.freeze({
    Overview: COMPONENT_MAPPING.OVERVIEW,
    Activity: COMPONENT_MAPPING.ACTIVITYLOGS,
    "Saved Deals": COMPONENT_MAPPING.SAVED_DEALS,
    "Viewed Vehicles": COMPONENT_MAPPING.VIEWED_VEHICLES,
    Searches: COMPONENT_MAPPING.SEARCHES,
    "Current Vehicles": COMPONENT_MAPPING.CURRENT_VEHICLES,
    "Pre-Qualifications": COMPONENT_MAPPING.PRE_QUALIFICATIONS,
    "Pre-Approvals": COMPONENT_MAPPING.PRE_APPROVALS,
    Leads: COMPONENT_MAPPING.LEADS,
    "Finance Applications": COMPONENT_MAPPING.FINANCE_APPLICATIONS,
    Documents: COMPONENT_MAPPING.DOCUMENTS,
    Notes: COMPONENT_MAPPING.NOTES,
    "Vehicle Sales": COMPONENT_MAPPING.VEHICLE_SALES,
    "Contract Requests": COMPONENT_MAPPING.CONTRACT_REQUESTS,
    Default: COMPONENT_MAPPING.DEFAULT,
});

export { COMPONENT_MAPPING, NAME_COMPONENT_MAPPING };
