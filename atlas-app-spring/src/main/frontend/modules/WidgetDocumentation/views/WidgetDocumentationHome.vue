<template>
    <v-row class="doc-row" no-gutters>
        <v-col cols="12">
            <v-tabs class="doc-tabs" vertical>
                <v-tabs-slider color="orange"></v-tabs-slider>

                <v-tab> Usage </v-tab>
                <v-tab> Styling </v-tab>
                <v-tab> Test Mode </v-tab>
                <v-tab> Properties </v-tab>
                <v-tab> Example </v-tab>
                <v-tab> Common Issues </v-tab>
                <v-tab> Nissan eCommerce </v-tab>

                <v-tab-item>
                    <v-card class="px-5 py-2" flat>
                        <usage />
                    </v-card>
                </v-tab-item>
                <v-tab-item>
                    <v-card class="px-5 py-2" flat>
                        <styling />
                    </v-card>
                </v-tab-item>
                <v-tab-item>
                    <v-card class="px-5 py-2" flat>
                        <test-mode />
                    </v-card>
                </v-tab-item>
                <v-tab-item>
                    <v-card class="px-5 py-2" flat>
                        <properties />
                    </v-card>
                </v-tab-item>
                <v-tab-item>
                    <v-card class="px-5 py-2" flat>
                        <example />
                    </v-card>
                </v-tab-item>
                <v-tab-item>
                    <v-card class="px-5 py-2" flat>
                        <common-issues />
                    </v-card>
                </v-tab-item>
                <v-tab-item>
                    <v-card class="px-5 py-2" flat>
                        <nissan-content />
                    </v-card>
                </v-tab-item>
            </v-tabs>
        </v-col>
    </v-row>
</template>

<script>
import Usage from "@/modules/WidgetDocumentation/components/Usage";
import Styling from "@/modules/WidgetDocumentation/components/Styling";
import TestMode from "@/modules/WidgetDocumentation/components/TestMode";
import Properties from "@/modules/WidgetDocumentation/components/Properties";
import CommonIssues from "@/modules/WidgetDocumentation/components/CommonIssues";
import NissanContent from "@/modules/WidgetDocumentation/components/NissanContent";
import Example from "@/modules/WidgetDocumentation/components/Example";

export default {
    name: "WidgetDocumentation",
    components: { Example, NissanContent, CommonIssues, Properties, TestMode, Styling, Usage },
};
</script>
<style lang="scss">
.container {
    height: 100%;
    .doc-row {
        height: 100%;

        .doc-tabs {
            height: 100%;
            .v-tabs-bar {
                background-color: #eeeeee !important;
                height: 100%;
            }
        }
    }
}
code {
    background-color: transparent !important;
}
</style>
