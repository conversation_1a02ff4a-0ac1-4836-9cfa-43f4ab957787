<template>
    <div id="nissan-ecommerce" class="d-flex flex-column">
        <h1 class="mb-3">Nissan eCommerce Cs-Buy-Widget Documentation</h1>

        <nna-dealer-codes />

        <nna-campaign-id />

        <nna-test-mode />

        <nna-example />
    </div>
</template>
<script>
import NnaDealerCodes from "@/modules/WidgetDocumentation/components/NissanContent/NnaDealerCodes";
import NnaCampaignId from "@/modules/WidgetDocumentation/components/NissanContent/NnaCampaignId";
import NnaTestMode from "@/modules/WidgetDocumentation/components/NissanContent/NnaTestMode";
import NnaExample from "@/modules/WidgetDocumentation/components/NissanContent/NnaExample";
export default {
    name: "NissanContent",
    components: { NnaExample, NnaTestMode, NnaCampaignId, NnaDealerCodes },
};
</script>
