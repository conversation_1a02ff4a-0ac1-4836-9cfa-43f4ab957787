<template>
    <div id="styling" class="d-flex flex-column">
        <h1 class="mb-3">Cs-Buy-Widget Styling</h1>

        <div class="mb-1">
            Because of the way the plugin is built, styling using CSS is <strong>not</strong> possible on the widget
            itself.
        </div>
        <div class="mb-1">
            <div class="mb-3">
                For positioning the button, we recommend wrapping the component in a <code>&lt;div&gt;</code> and adding
                a CSS class to the wrapping element and positioning that element with CSS like this:
                <br />
                <pre class="language-markup">
<code>
&lt;div class="position-me"&gt;
    &lt;cs-buy-widget /&gt;
&lt;/div&gt;
</code>
</pre>
                <pre class="language-css">
*Then just target the wrapping element with css like so:
<code>
.position-me {
    // add css properties here
}
</code>
</pre>
            </div>
        </div>
        <div class="mb-1">
            A good way to test out styling positioning is to add the <strong>test</strong> property to the button. This
            will always show the button and disable the click functionality for use while styling and positioning.
        </div>
        <div>
            For all styling options available, see the <strong>Properties</strong> section and for more information on
            the <strong>test</strong> property, see the <strong>Test Mode</strong> section.
        </div>
    </div>
</template>
<script>
export default {
    name: "Styling",
};
</script>
<style lang="scss">
.code-block {
    padding: 8px;
    width: 100%;
    background-color: #eeeeee;
    border-radius: 4px;
}
</style>
