<template>
    <div id="issues" class="d-flex flex-column">
        <h1 class="mb-3">Cs-Buy-Widget Common Issues</h1>

        <h2 class="mb-2">How to fix Flash of Un-styled Content (FOUC) issue</h2>

        <div>
            Sometimes the button can flash on the screen with out any styling properties applied.
            <br />
            Just add a
            <pre class="language-markdown">
<code>style="display: none"</code>
</pre>
            to the component, and it will automatically get removed once the styling has loaded.
        </div>
        <pre class="language-markup">
<code>
&lt;cs-buy-widget
    style="display: none"
    test="true"
    dev="true"
    dealer-id="12345"
    vin="1234567890ABCDEFG"
    campaign-id="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"&gt;
    Buy NOW
&lt;/cs-buy-widget&gt;
</code>
</pre>
    </div>
</template>
<script>
export default {
    name: "CommonIssues",
};
</script>
