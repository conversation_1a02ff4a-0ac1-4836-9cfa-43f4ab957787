<template>
    <div id="example" class="d-flex flex-column">
        <h1 class="mb-3">Example of how to use all possible properties</h1>
        <pre class="language-markup">
<code>
&lt;cs-buy-widget
        dealer-id="31405"
        vin="1N4BL4DV9LC214488"
        campaign-id="c7a06436-44b1-4bed-bdb5-108b7a9e4a95"
        provider="nissan"
        source="button-example"
        deal-type="FINANCE"
        width="100%"
        height="44px"
        font-size="24px"
        font="15px bold Arial, sans-serif"
        font-color="#fff"
        text-align="center"
        padding="5px 10px"
        margin="20px"
        dev="true"
        test="true"
        background-color="#43aa8b"
        hover-background-color="#9d0026"
    &gt;
    Buy Now
&lt;/cs-buy-widget&gt;
</code>
</pre>
    </div>
</template>
<script>
export default {
    name: "Example",
};
</script>
