<template>
    <div class="placeholder-container d-flex justify-center">
        <div class="placeholder d-flex flex-column">
            <v-icon class="icon-image" color="white"> mdi-arrow-right-drop-circle-outline </v-icon>
            <p>{{ text }}</p>
        </div>
    </div>
</template>

<script>
export default {
    name: "ProgramPlaceholder",
    props: {
        text: {
            type: String,
            required: true,
        },
    },
};
</script>
<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.placeholder {
    background-color: lightgrey;
    border-radius: 15px;
    padding: 45px;

    font-size: px2rem(24);
    text-align: center;
    color: #444;
    max-width: 500px;

    .icon-image {
        font-size: 76px;
    }

    p {
        font-size: px2rem(18);
        margin: 0px;
    }

    @media #{map-get($display-breakpoints, 'sm-and-up')} {
        background: none;

        p {
            font-size: px2rem(24);
        }

        .icon-image {
            font-size: 136px;
        }
    }
}
</style>
