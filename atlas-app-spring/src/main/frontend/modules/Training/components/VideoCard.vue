<template>
    <v-card>
        <video-player ref="videoPlayer" :value="value"></video-player>
        <v-list-item>
            <v-list-item-avatar size="80" class="poster-wrapper" tile>
                <v-img :src="poster"></v-img>
                <v-icon class="play-btn" @click="playVideo">mdi-play</v-icon>
            </v-list-item-avatar>

            <div class="video-info pa-2">
                <v-list-item-title v-text="value.title"></v-list-item-title>
                <v-list-item-subtitle class="text-center"
                    ><v-chip small dark :color="statusColor">{{ statusText }} </v-chip></v-list-item-subtitle
                >
            </div>
        </v-list-item>
    </v-card>
</template>

<script>
import _ from "lodash";
import VideoPlayer from "@/modules/Training/components/VideoPlayer";
import moment from "moment";

export default {
    name: "VideoCard",
    components: { VideoPlayer },
    props: {
        value: {
            type: Object,
            required: true,
        },
        index: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {};
    },
    computed: {
        poster() {
            if (_.isNil(this.value)) {
                return null;
            }

            return this.value.url + ".png";
        },
        statusColor() {
            return this.value.completedAt === null ? "red" : "green";
        },
        statusText() {
            if (this.value.completedAt !== null) {
                const completedDate = moment(this.value.completedAt);
                return `Completed At ${completedDate.format("MMMM Do, YYYY")}`;
            }
            return "Not Completed";
        },
    },
    methods: {
        playVideo() {
            this.$refs.videoPlayer.playVideo();
        },
    },
};
</script>

<style scoped>
.video-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.poster-wrapper {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.poster-wrapper img {
    width: 100%;
    height: auto;
}

.poster-wrapper .play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    color: white;
    font-size: 4rem;
    padding: 12px 24px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    text-align: center;
}

.poster-wrapper .play-btn:hover {
    background-color: black;
    opacity: 0.5;
}
</style>
