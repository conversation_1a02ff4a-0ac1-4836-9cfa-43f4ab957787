<template>
    <v-treeview
        :active.sync="active"
        :items="items"
        :activatable="true"
        item-text="title"
        item-children="videos"
        item-key="title"
        shaped
        hoverable
        transition
        :return-object="true"
        open-on-click
        :open-all="true"
    >
        <template #prepend="{ item }">
            <v-icon v-if="!item.videos && item.completedAt" color="green"> mdi-check </v-icon>
            <v-icon v-else-if="!item.videos && !item.completedAt"> mdi-arrow-right-drop-circle-outline </v-icon>
        </template>
    </v-treeview>
</template>

<script>
import { sync } from "vuex-pathify";

export default {
    name: "VideoNav",
    props: {
        items: {
            type: Array,
            required: false,
            default: function () {
                return [];
            },
        },
    },
    computed: {
        active: sync("trainingStore/activeVideo"),
    },
};
</script>

<style scoped></style>
