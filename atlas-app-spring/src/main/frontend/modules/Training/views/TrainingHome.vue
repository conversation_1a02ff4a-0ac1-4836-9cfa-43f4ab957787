<template>
    <v-container class="training-container" fluid>
        <v-row>
            <v-col cols="12" class="d-flex flex-column">
                <v-card class="fill-height mb-4 d-flex flex-column">
                    <v-card-text class="text--primary">
                        <h2>Training Videos</h2>
                        <v-select
                            v-model="programId"
                            label="Select Program"
                            placeholder="Select Program"
                            :items="programs"
                            item-text="name"
                            item-value="id"
                            @change="setSelectedProgram"
                        />
                    </v-card-text>
                </v-card>
            </v-col>
        </v-row>
        <v-row v-if="areVideosAvailable">
            <v-col sm="12" lg>
                <program-placeholder v-if="!isVideoActive" text="Please select a video to get started." />
                <video-player v-else></video-player>
            </v-col>
            <v-divider vertical></v-divider>
            <v-col lg="3">
                <video-nav :items="videoSections"></video-nav>
            </v-col>
        </v-row>
        <v-row v-else>
            <v-col>
                <program-placeholder
                    v-if="programId"
                    text="The selected program does not have any video available. Please, select a different program."
                />
                <program-placeholder v-else text="Please select a program above and pick a video to get started." />
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import { call, get } from "vuex-pathify";
import VideoNav from "@/modules/Training/components/VideoNav";
import VideoPlayer from "@/modules/Training/components/VideoPlayer";
import ProgramPlaceholder from "@/modules/Training/components/ProgramPlaceholder";

export default {
    name: "Training",
    components: { VideoPlayer, VideoNav, ProgramPlaceholder },
    data() {
        return {
            programId: null,
        };
    },
    computed: {
        activeVideo: get("trainingStore/activeVideo"),
        isVideoActive() {
            return this.activeVideo.length === 1;
        },
        programs: get("trainingStore/programs"),
        videoSections: get("trainingStore/courses"),
        areVideosAvailable() {
            return this.videoSections.length > 0;
        },
    },
    watch: {
        programs(newValue) {
            if (_.isArray(newValue) && _.size(newValue) === 1) {
                this.programId = newValue[0].id;
                this.setSelectedProgram(this.programId);
            }
        },
    },
    mounted() {
        this.fetchPrograms();
    },
    methods: {
        fetchPrograms: call("trainingStore/fetchPrograms"),
        fetchCourses: call("trainingStore/fetchCourses"),
        setSelectedProgram(programId) {
            this.fetchCourses({ programId });
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.training-container {
    background-color: $gray-200;
    height: 100%;
}
</style>
