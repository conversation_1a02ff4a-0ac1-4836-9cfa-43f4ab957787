import { make } from "vuex-pathify";
import api from "Util/api";

const initialState = {
    programs: [],
    courses: [],
    activeVideo: [],
};

const mutations = {
    ...make.mutations(initialState),
    SET_PROGRAMS(state, payload) {
        state.programs = payload;
    },
    SET_COURSES(state, payload) {
        state.courses = payload;
        state.activeVideo = [];
    },
    MARK_VIDEO_COMPLETED(state, payload) {
        state.courses.forEach((course) => {
            let video = course.videos.find((video) => video.videoId === payload.videoId);
            if (video) {
                video.completedAt = payload.completedAt;
                return;
            }
        });
    },
};

const actions = {
    ...make.actions(initialState),
    fetchPrograms({ commit }) {
        api.get(`/training/programs`)
            .then((response) => {
                commit("SET_PROGRAMS", response.data);
            })
            .catch((error) => {
                console.error(error);
            });
    },
    fetchCourses({ commit }, payload) {
        api.get(`/training/programs/${payload.programId}/courses`)
            .then((response) => {
                commit("SET_COURSES", response.data);
            })
            .catch(() => {
                console.error("Error fetching Training Videos");
            });
    },
    markVideoCompleted({ commit }, payload) {
        api.post(`/training/courses/videos/${payload.videoId}/completed`)
            .then((response) => {
                commit("MARK_VIDEO_COMPLETED", { videoId: payload.videoId, completedAt: response.data.completedAt });
            })
            .catch((error) => {
                console.error("Error marking video as completed: ", error);
            });
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
