<template>
    <v-skeleton-loader v-if="loading" type="card,card,table"></v-skeleton-loader>
    <v-container v-else class="pricing-container" fluid>
        <low-fixed-price-warning-modal />
        <v-row>
            <v-col>
                <search-form :key="dealerIds" :dealer-ids="dealerIds" @search-completed="handleSearchCompleted" />
            </v-col>
        </v-row>
        <v-row v-if="vehicleSearchFailureMessage && vehicleSearchFailureMessage.length > 0" class="my-0">
            <v-col class="pb-0">
                <v-alert type="warning" class="mb-0"> {{ vehicleSearchFailureMessage }}</v-alert></v-col
            >
        </v-row>
        <v-row class="mt-0">
            <v-col cols="12" md="6" class="pr-md-0 pb-0 pb-md-3">
                <stock-type-price-adjustment
                    :key="dealerIds"
                    :enable-lease-pricing="enableLeasePricing"
                    :adjusted-value-options="adjustedValueOptions"
                    :adjustment-type-options="adjustmentTypeOptions"
                />
            </v-col>
            <v-col cols="12" md="6">
                <priced-widget
                    :key="dealerIds"
                    name="new-priced"
                    title="% of New Vehicles Priced"
                    chart-description="Priced"
                    :dealer-id="dealerIds"
                />
            </v-col>
        </v-row>
        <v-row class="mt-0">
            <v-col>
                <v-card>
                    <v-toolbar flat>
                        <v-toolbar-title>New Vehicle Pricing</v-toolbar-title>
                    </v-toolbar>

                    <v-divider></v-divider>
                    <v-simple-table fixed-header height="500px">
                        <new-priced-car-row-header :key="dealerIds" :enable-lease-pricing="enableLeasePricing" />
                        <tbody style="white-space: nowrap; text-align: center">
                            <template v-for="(pricedModel, modelIdx) in renderedPricedModels">
                                <priced-model-row
                                    :key="'pricedModel_' + pricedModel.model.id"
                                    :priced-model="pricedModel"
                                    :model-idx="modelIdx"
                                    :enable-lease-pricing="enableLeasePricing"
                                    :enable-internet-price="internetPriceEnabled"
                                    :adjusted-value-options="adjustedValueOptions"
                                    :adjustment-type-options="adjustmentTypeOptions"
                                    @fetchPricedStyles="fetchPricedStyles"
                                    @change="modelRowChanged"
                                />

                                <template v-for="(pricedStyle, styleIdx) in pricedModel.renderedPricedStyles">
                                    <priced-style-row
                                        v-if="pricedModel.showStyles"
                                        :key="'pricedStyle_' + pricedStyle.style.id"
                                        :priced-model="pricedModel"
                                        :model-idx="modelIdx"
                                        :priced-style="pricedStyle"
                                        :style-idx="styleIdx"
                                        :enable-lease-pricing="enableLeasePricing"
                                        :enable-internet-price="internetPriceEnabled"
                                        :adjusted-value-options="adjustedValueOptions"
                                        :adjustment-type-options="adjustmentTypeOptions"
                                        @fetchPricedCars="fetchPricedCars"
                                        @change="styleRowChanged"
                                    />

                                    <template v-for="pricedCar in pricedStyle.renderedPricedCars">
                                        <new-priced-car-row
                                            v-if="pricedStyle.showCars"
                                            :key="'pricedCar_' + pricedCar.vehicle.id"
                                            :priced-car="pricedCar"
                                            :enable-lease-pricing="enableLeasePricing"
                                            :enable-internet-price="internetPriceEnabled"
                                            :adjusted-value-options="adjustedValueOptions"
                                            :adjustment-type-options="adjustmentTypeOptions"
                                            @change="carRowChanged"
                                        />
                                    </template>
                                </template>
                            </template>
                        </tbody>
                    </v-simple-table>
                </v-card>
            </v-col>
        </v-row>
        <v-fab-transition>
            <v-btn color="warning" fab large fixed right bottom @click.prevent="savePricingAdjustments"> Save </v-btn>
        </v-fab-transition>
    </v-container>
</template>

<script>
import Vue from "vue";
import { call, get, sync } from "vuex-pathify";
import api from "Util/api";
import StockTypePriceAdjustment from "@/modules/VehiclePricing/components/NewVehiclePricing/StockTypePriceAdjustment";
import SearchForm from "@/modules/VehiclePricing/components/SearchForm";
import LowFixedPriceWarningModal from "@/modules/VehiclePricing/components/LowFixedPriceWarningModal";
import NewPricedCarRowHeader from "@/modules/VehiclePricing/components/NewVehiclePricing/PricedCarRowHeader";
import PricedModelRow from "@/modules/VehiclePricing/components/NewVehiclePricing/PricedModelRow";
import PricedStyleRow from "@/modules/VehiclePricing/components/NewVehiclePricing/PricedStyleRow";
import NewPricedCarRow from "@/modules/VehiclePricing/components/NewVehiclePricing/PricedCarRow";
import PricedWidget from "@/modules/VehiclePricing/components/PricedWidget";
import { sanitize } from "Util/sanitize";
import lodashEach from "lodash/each";
import lodashForEach from "lodash/forEach";
import lodashMap from "lodash/map";

export default {
    name: "NewVehiclePricing",
    components: {
        PricedWidget,
        NewPricedCarRow,
        PricedStyleRow,
        PricedModelRow,
        NewPricedCarRowHeader,
        LowFixedPriceWarningModal,
        SearchForm,
        StockTypePriceAdjustment,
    },
    data() {
        return {
            pricedModelRows: [],
            pricedModelMap: {},
            pricedStyleMap: {},
            pricedCarMap: {},
            submitting: false,
            metricsRefreshKey: 0,
            adjustmentTypeOptions: [
                { text: "$", value: "DOLLAR" },
                {
                    text: "%",
                    value: "PERCENTAGE",
                },
            ],
            stockType: "new",
        };
    },

    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
        loading: sync("vehiclePricing/loading"),
        dealer: get("vehiclePricing/dealer"),
        enforcePricingRules: sync("newVehiclePricing/enforcePricingRules"),
        enableLeasePricing: sync("newVehiclePricing/enableLeasePricing"),
        vehicleSearchAttempted: sync("newVehiclePricing/vehicleSearchAttempted"),
        vehicleSearchFailureMessage: sync("newVehiclePricing/vehicleSearchFailureMessage"),
        vehicleSearchFoundVehicle: sync("newVehiclePricing/vehicleSearchFoundVehicle"),
        pricingFormStyleFilter: sync("newVehiclePricing/pricingFormStyleFilter"),
        pricingFormInventoryIdFilter: sync("newVehiclePricing/pricingFormInventoryIdFilter"),
        stockTypePriceAdjustment: sync("newVehiclePricing/stockTypePriceAdjustment"),
        searchForm: get("newVehiclePricing/searchForm"),
        internetPriceEnabled: get("vehiclePricing/<EMAIL>"),
        pricingForm() {
            return {
                searchForm: this.searchForm,
                stockTypePriceAdjustment: this.stockTypePriceAdjustment,
                pricedModelMap: this.pricedModelMap,
                pricedStyleMap: this.pricedStyleMap,
                pricedCarMap: this.pricedCarMap,
                dealerIds: this.dealer.id,
            };
        },

        renderedPricedModels() {
            return lodashMap(this.pricedModelRows, (pricedModel, idx) => {
                return this.renderPricedModel(pricedModel, idx);
            });
        },
        adjustedValueOptions() {
            return [
                { text: "Invoice", value: "INVOICE" },
                {
                    text: "Internet",
                    value: "INTERNET_PRICE",
                    disabled: !this.internetPriceEnabled,
                },
                { text: "Market", value: "MARKET", disabled: true },
                { text: "MSRP", value: "MSRP" },
            ];
        },
    },
    watch: {
        dealerIds(_val) {
            this.onLoad();
        },
    },
    created() {
        this.onLoad();
    },
    methods: {
        onLoad() {
            const payload = {
                dealerId: this.dealerIds,
                stockType: this.stockType,
            };
            this.loadPage(payload);
        },
        loadPage: call("vehiclePricing/loadPage"),
        handlePricingSuccessfullyDeleted() {
            this.loading = true;
            this.submitting = true;
            setTimeout(() => {
                window.location.reload();
            }, 5000);
        },

        fetchPricedCars(pricedModel, modelIdx, pricedStyle, styleIdx) {
            pricedStyle.showCars = !pricedStyle.showCars; // toggle visibility

            if (pricedStyle.fetchedPricedCars) {
                pricedModel.fetchedPricedStyles[styleIdx] = pricedStyle;
                this.$set(this.pricedModelRows, modelIdx, pricedModel);
            } else {
                const query = {
                    styleId: pricedStyle.style.id,
                    active: this.searchForm.active,
                    pricingFormInventoryIdFilter: this.pricingFormInventoryIdFilter,
                    dealerIds: this.dealer.id,
                };
                api.get(`/stratus/vehicle-pricing/new/cars`, query)
                    .then((response) => {
                        pricedStyle.fetchedPricedCars = response.data;
                        lodashEach(response.data, (pricedCar) => {
                            this.$set(this.pricedCarMap, pricedCar.vehicle.id, pricedCar);
                        });
                        pricedModel.fetchedPricedStyles[styleIdx] = pricedStyle;

                        Vue.nextTick(() => {
                            this.$set(this.pricedModelRows, modelIdx, pricedModel);
                        });
                    })
                    .catch((error) => {
                        console.log(error);
                    });
            }
        },
        fetchPricedStyles(pricedModel, idx) {
            pricedModel.showStyles = !pricedModel.showStyles; // toggle visibility

            if (pricedModel.fetchedPricedStyles) {
                this.$set(this.pricedModelRows, idx, pricedModel);
            } else {
                const query = {
                    modelId: pricedModel.model.id,
                    active: this.searchForm.active,
                    pricingFormStyleFilter: this.pricingFormStyleFilter,
                    dealerIds: this.dealer.id,
                };
                api.get(`/stratus/vehicle-pricing/styles`, query)
                    .then((response) => {
                        pricedModel.fetchedPricedStyles = response.data;
                        lodashEach(response.data, (pricedStyle) => {
                            this.$set(this.pricedStyleMap, pricedStyle.style.id, pricedStyle);
                        });
                        this.$set(this.pricedModelRows, idx, pricedModel);
                    })
                    .catch((error) => {
                        console.log(error);
                    });
            }
        },

        renderPricedStyle(pricedModel, pricedStyle) {
            pricedStyle.renderedPricedCars = pricedStyle.showCars
                ? lodashMap(pricedStyle.fetchedPricedCars, (fetchedPricedCar) => {
                      return this.renderPricedCar(pricedModel, pricedStyle, fetchedPricedCar);
                  })
                : [];

            return pricedStyle;
        },

        renderPricedModel(pricedModel, idx) {
            pricedModel.renderedPricedStyles = pricedModel.showStyles
                ? lodashMap(pricedModel.fetchedPricedStyles, (fetchedPricedStyle) => {
                      return this.renderPricedStyle(pricedModel, fetchedPricedStyle);
                  })
                : [];

            this.$set(this.pricedModelRows, idx, pricedModel);

            return pricedModel;
        },

        renderPricedCar(pricedModel, pricedStyle, pricedCar) {
            const { adjustment: carAdjustment, vehicle } = pricedCar;

            // consult car,style,model,stockType adjustments in that order of preference to determine effective adjustment to apply
            if (carAdjustment.enabled) {
                // individual car adjustment enabled
                const fixedPriceEnabledAndValid =
                    carAdjustment.fixedPrice &&
                    carAdjustment.fixedPriceAmount &&
                    !isNaN(carAdjustment.fixedPriceAmount);
                if (fixedPriceEnabledAndValid) {
                    this.addFixedPriceAndAdjustmentAmount(vehicle, carAdjustment);
                } else {
                    this.addVehiclePriceAndEffectiveAdjustmentAmount(vehicle, carAdjustment, carAdjustment, "CAR");
                }
            } else if (pricedStyle.adjustment.enabled) {
                // style adjustment enabled
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    pricedStyle.adjustment,
                    "STYLE"
                );
            } else if (pricedModel.adjustment.enabled) {
                // model adjustment enabled
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    pricedModel.adjustment,
                    "MODEL"
                );
            } else if (this.stockTypePriceAdjustment.adjustment.enabled) {
                // stockType/global adjustment enabled
                this.addVehiclePriceAndEffectiveAdjustmentAmount(
                    vehicle,
                    carAdjustment,
                    this.stockTypePriceAdjustment.adjustment,
                    "STOCK_TYPE"
                );
            } else {
                // NO adjustment enabled
                carAdjustment.amount = null;
                this.addVehiclePriceAndEffectiveAdjustmentAmount(vehicle, carAdjustment, carAdjustment, null);
            }

            return pricedCar;
        },

        addFixedPriceAndAdjustmentAmount(vehicle, carAdjustment) {
            const priceToAdjust =
                carAdjustment.adjustedValue === "INVOICE" ? vehicle.invoicePrice : vehicle.averageMarketPrice;
            vehicle.price = carAdjustment.fixedPriceAmount;
            carAdjustment.amount = carAdjustment.fixedPriceAmount - priceToAdjust; // for display purposes only set this
            this.addActiveStatus(vehicle, carAdjustment, "FIXED_CAR");
        },

        addVehiclePriceAndEffectiveAdjustmentAmount(
            vehicle,
            carAdjustment,
            effectiveAdjustment,
            effectiveAdjustmentType
        ) {
            let priceToAdjust;
            let price;
            let leasePrice;

            switch (effectiveAdjustment.adjustedValue) {
                case "INVOICE":
                    priceToAdjust = vehicle.invoicePrice;
                    break;
                case "MARKET":
                    priceToAdjust = vehicle.averageMarketPrice;
                    break;
                case "INTERNET_PRICE":
                    priceToAdjust = vehicle.internetPrice;
                    break;
                case "MSRP":
                    priceToAdjust = vehicle.msrp;
                    break;
                default:
                    return "";
            }

            carAdjustment.fixedPriceAmount = null;
            carAdjustment.type = effectiveAdjustment.type;
            carAdjustment.amount = effectiveAdjustment.amount;
            carAdjustment.leaseType = effectiveAdjustment.leaseType;
            carAdjustment.leaseAmount = effectiveAdjustment.leaseAmount;
            carAdjustment.adjustedValue = effectiveAdjustment.adjustedValue;

            if (priceToAdjust && !isNaN(priceToAdjust) && carAdjustment.amount != null) {
                price =
                    carAdjustment.type === "PERCENTAGE"
                        ? Math.round(priceToAdjust + priceToAdjust * (carAdjustment.amount / 100))
                        : priceToAdjust + carAdjustment.amount;
            }

            if (priceToAdjust && !isNaN(priceToAdjust) && carAdjustment.leaseAmount != null) {
                leasePrice =
                    carAdjustment.leaseType === "PERCENTAGE"
                        ? Math.round(priceToAdjust + priceToAdjust * (carAdjustment.leaseAmount / 100))
                        : priceToAdjust + carAdjustment.leaseAmount;
            }

            carAdjustment.amount = !price || price < 0 ? null : carAdjustment.amount;
            carAdjustment.leaseAmount = !leasePrice || leasePrice < 0 ? null : carAdjustment.leaseAmount;
            vehicle.price = !price || price < 0 ? null : price;

            this.addActiveStatus(vehicle, carAdjustment, effectiveAdjustmentType);
        },

        addActiveStatus(vehicle, effectiveCarAdjustment, effectiveAdjustmentType) {
            if (this.enforcePricingRules === true) {
                this.addActiveStatusForEnforcePricingRulesIsTrue(
                    vehicle,
                    effectiveCarAdjustment,
                    effectiveAdjustmentType
                );
            } else {
                vehicle.activeStatus = true;
                this.$set(vehicle, "activeStatusDesc", "Enforce Pricing Rules is turned OFF");
            }
        },
        addActiveStatusForEnforcePricingRulesIsTrue(vehicle, effectiveCarAdjustment, effectiveAdjustmentType) {
            let activeStatus, activeStatusDesc;

            // commenting out since Vincentric service is turned off. JIRA issues: POR-2248, POR-2255
            // const hasAverageMarketPrice = vehicle.averageMarketPrice && !isNaN(vehicle.averageMarketPrice);
            const hasAverageMarketPrice = false;
            const hasMsrp = vehicle.msrp && !isNaN(vehicle.msrp);
            // commenting out since Vincentric service is turned off. JIRA issues: POR-2248, POR-2255
            // const hasMarketAndAMP = effectiveAdjustmentType && effectiveCarAdjustment.adjustedValue === 'MARKET' && !hasAverageMarketPrice;
            const hasMarketAndAMP = false;

            if (hasMarketAndAMP) {
                // special case
                activeStatus = false;
                activeStatusDesc =
                    effectiveAdjustmentType +
                    " Adjustment is applied to Market Price but the vehicle has no defined Market Price";
            } else {
                if (!effectiveAdjustmentType || effectiveCarAdjustment.amount == null) {
                    activeStatus = false;
                    activeStatusDesc = "No Adjustment Set";
                } else if (hasAverageMarketPrice) {
                    if (vehicle.price <= vehicle.averageMarketPrice - 100) {
                        activeStatus = true;
                        activeStatusDesc =
                            "Active via " +
                            effectiveAdjustmentType +
                            " adjustment since it brings price at least $100 below Market Price";
                    } else {
                        activeStatus = false;
                        activeStatusDesc =
                            effectiveAdjustmentType + " Adjustment does NOT bring price $100 below Market Price";
                    }
                } else if (hasMsrp) {
                    if (vehicle.price <= vehicle.msrp - 100) {
                        activeStatus = true;
                        activeStatusDesc =
                            "Active via " +
                            effectiveAdjustmentType +
                            " adjustment since it brings price at least $100 below MSRP";
                    } else {
                        activeStatus = false;
                        activeStatusDesc = effectiveAdjustmentType + " Adjustment does NOT bring price $100 below MSRP";
                    }
                } else {
                    activeStatus = false;
                    activeStatusDesc = "Neither MSRP or Average Market Price are valid";
                }
            }

            vehicle.activeStatus = activeStatus;
            this.$set(vehicle, "activeStatusDesc", activeStatusDesc);
        },

        savePricingAdjustments() {
            this.submitting = true;
            api.post(`/stratus/vehicle-pricing/${this.stockType}`, sanitize(this.pricingForm, { deep: true }))
                .then((response) => {
                    this.$toast(
                        "New Vehicle Pricing Rules Updated. Please give us a couple of minutes to recalculate your new vehicle prices."
                    );

                    this.handleSearchCompleted(response.data);
                    this.refreshMetrics();
                })
                .catch((error) => {
                    if (error.response.status === 400 && error.response.data && error.response.data.errors) {
                        const errors = error.response.data.errors;
                        const messages = lodashMap(errors, (e) => {
                            return e.message;
                        });
                        const toastr = lodashForEach(messages, (m) => {
                            return m + ",";
                        });
                        this.$toast.error(toastr);
                    } else {
                        console.error(error.response);
                    }
                    this.submitting = false;
                });
        },

        refreshMetrics() {
            setTimeout(() => {
                this.metricsRefreshKey++;
                this.submitting = false;
            }, 5000);
        },

        handleSearchCompleted(searchResults) {
            this.pricedModelRows = [];

            this.pricedModelMap = {};
            this.pricedStyleMap = {};
            this.pricedCarMap = {};

            this.vehicleSearchAttempted = searchResults.vehicleSearchAttempted;
            this.vehicleSearchFoundVehicle = searchResults.vehicleSearchFoundVehicle;
            this.vehicleSearchFailureMessage = searchResults.vehicleSearchFailureMessage;
            this.enableLeasePricing = searchResults.enableLeasePricing;
            this.enforcePricingRules = searchResults.enforcePricingRules;
            this.pricingFormStyleFilter = searchResults.pricingFormStyleFilter;
            this.pricingFormInventoryIdFilter = searchResults.pricingFormInventoryIdFilter;
            this.stockTypePriceAdjustment = searchResults.stockTypePriceAdjustment;

            lodashEach(searchResults.pricedModelRows, (item) => {
                item.showStyles = false;
                this.pricedModelRows.push(item);
                this.$set(this.pricedModelMap, item.model.id, item);
            });
        },

        modelRowChanged(modelPrice) {
            this.pricedModelMap[modelPrice.model.id] = modelPrice;
        },

        styleRowChanged(stylePrice) {
            this.pricedStyleMap[stylePrice.style.id] = stylePrice;
        },

        carRowChanged(carPrice) {
            this.pricedCarMap[carPrice.vehicle.id] = carPrice;
        },
    },
};
</script>
<style lang="scss">
.pricing-container {
    background-color: $gray-200;
}

.adjustment-type {
    max-width: 58px;
}
</style>
