import isNil from "lodash/isNil";
import lodashIsNaN from "lodash/isNaN";

export default {
    methods: {
        adjustmentLeaseAmountBlur({ adjustment }) {
            const adjustmentAmountInvalid =
                isNil(adjustment.leaseAmount) ||
                lodashIsNaN(adjustment.leaseAmount) ||
                (adjustment.leaseType === "PERCENTAGE" && adjustment.leaseAmount < -100);
            if (adjustmentAmountInvalid) {
                adjustment.leaseAmount = null;
                adjustment.enabled = false;
            }
        },
    },
};
