import { make } from "vuex-pathify";

const state = {
    enforcePricingRules: false,
    stockTypePriceAdjustment: null,
    searchForm: {},
    vehicleSearchAttempted: false,
    vehicleSearchFoundVehicle: false,
    vehicleSearchFailureMessage: null,
    pageMetadata: {
        pageSize: 20,
        totalElements: 0,
        totalPages: 0,
        number: 0,
    },
    pageable: {
        sort: "year,desc",
        page: 1,
    },
};

const getters = {
    getSortBy: (state) => {
        const sortSplit = state.pageable?.sort?.split(",");
        return sortSplit[0];
    },
    getSortDesc: (state) => {
        const sortSplit = state.pageable?.sort?.split(",");
        return sortSplit[1] === "desc";
    },
};

const mutations = {
    ...make.mutations(state),
};

export default {
    namespaced: true,
    state,
    mutations,
    getters,
};
