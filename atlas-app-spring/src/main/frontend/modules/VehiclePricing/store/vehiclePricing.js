import api from "@/util/api";
import _ from "lodash";
import { make } from "vuex-pathify";

const initialState = {
    selectedTab: 0,
    loading: true,
    dealer: null,
    stockType: null,
    ymm: null,
    invalidUsedCount: null,
    invalidNewCount: null,
    duplicateUsedCount: null,
    duplicateNewCount: null,
};

const mutations = {
    ...make.mutations(initialState),
};

const actions = {
    ...make.actions(initialState),
    loadPage({ commit, state, dispatch }, { stockType, dealerId }) {
        commit("SET_LOADING", true);
        return dispatch("setStockType", stockType)
            .then(() => {
                return dispatch("loadDealer", dealerId);
            })
            .then(() => {
                return Promise.all([dispatch("fetchYmm"), dispatch("fetchMetrics")]);
            })
            .finally(() => {
                commit("SET_LOADING", false);
            });
    },

    setStockType({ commit, state }, stockType) {
        if (_.isNil(stockType)) {
            return Promise.resolve();
        }
        return commit("SET_STOCK_TYPE", stockType);
    },

    loadDealer({ commit, state }, dealerId) {
        if (!_.isNil(_.get(state, "dealer.id")) && _.isEqual(_.get(state, "dealer.id"), dealerId)) {
            return Promise.resolve(_.get(state, "dealer"));
        }

        return api
            .get(`/stratus/vehicle-pricing`, { dealerIds: dealerId })
            .then((response) => {
                const dealer = _.get(response, "data");
                return commit("SET_DEALER", dealer);
            })
            .catch((error) => {
                console.log("error =", error);

                return null;
            });
    },

    fetchYmm({ commit, state }) {
        const normalizeOptions = (options) => {
            if (!options) {
                return;
            }

            let resultArray = options.map((option) => {
                return {
                    text: option,
                    value: option,
                };
            });

            resultArray.unshift({ text: "Any", value: null });
            return resultArray;
        };

        return api
            .get(`/stratus/vehicle-pricing/${state.stockType}/ymm`, { dealerIds: state.dealer.id })
            .then((response) => {
                const ymm = _.get(response, "data");
                ymm.years = normalizeOptions(_.get(ymm, "years"));
                ymm.makes = normalizeOptions(_.get(ymm, "makes"));
                ymm.models = normalizeOptions(_.get(ymm, "models"));
                return commit("SET_YMM", ymm);
            });
    },

    fetchMetrics({ commit, state }) {
        api.get(`/stratus/vehicle-pricing/metrics`, { dealerIds: state.dealer.id }).then((response) => {
            commit("SET_INVALID_USED_COUNT", response.data.invalidUsedCount);
            commit("SET_DUPLICATE_USED_COUNT", response.data.duplicateUsedCount);
            commit("SET_INVALID_NEW_COUNT", response.data.invalidNewCount);
            commit("SET_DUPLICATE_NEW_COUNT", response.data.duplicateNewCount);
        });
    },
};

export default {
    namespaced: true,
    state: initialState,
    actions,
    mutations,
};
