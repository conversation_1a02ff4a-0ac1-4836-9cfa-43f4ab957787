<template>
    <tr class="model-row price-adjustment-row">
        <td class="model-name accordion-toggle" style="text-align: left; padding-left: 50px; white-space: normal">
            <a class="model-description" @click.prevent="fetchPricedStyles(pricedModel, modelIdx)">
                <v-icon>
                    {{ pricedModel.showStyles ? "mdi-chevron-down" : "mdi-chevron-right" }}
                </v-icon>
                {{ pricedModel.model.year }} {{ pricedModel.model.name }}
            </a>
        </td>
        <td>{{ pricedModel.inventoryCount | numeral("0,0") }} Vehicles</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>
            <v-checkbox :input-value="pricedModel.adjustment.enabled" @change="(v) => enablePricingChangeEvent(v)" />
        </td>
        <td>
            <adjustment-value
                :adjustment="pricedModel.adjustment"
                :adjusted-value-options="adjustedValueOptions"
                @input="(v) => changeAdjustment(v)"
            />
        </td>
        <td>
            <div class="d-inline-flex">
                <adjustment-type
                    :adjustment="pricedModel.adjustment"
                    :adjustment-type-options="adjustmentTypeOptions"
                    @input="(v) => change('adjustment.type', v)"
                />
                <v-text-field
                    :value="pricedModel.adjustment.amount"
                    :disabled="!pricedModel.adjustment.enabled || pricedModel.adjustment.fixedPrice"
                    type="number"
                    class="adjustment-amount"
                    style="width: 180px"
                    pattern="^-{0,1}(?!0.)\d+$"
                    data-pattern-error="Please enter a whole number"
                    max="999999"
                    :rules="[rules.numericOnly, rules.maxValue(999999)]"
                    @blur="adjustmentAmountBlur(pricedModel)"
                    @input="(v) => change('adjustment.amount', v, 'number')"
                />
            </div>
        </td>
        <td v-if="enableLeasePricing">
            <div class="d-inline-flex">
                <v-select
                    :value="pricedModel.adjustment.leaseType"
                    :items="adjustmentTypeOptions"
                    :disabled="!pricedModel.adjustment.enabled || pricedModel.adjustment.fixedPrice"
                    class="adjustment-type"
                    @input="(v) => change('adjustment.leaseType', v)"
                />
                <v-text-field
                    :value="pricedModel.adjustment.leaseAmount"
                    :disabled="!pricedModel.adjustment.enabled || pricedModel.adjustment.fixedPrice"
                    type="number"
                    class="adjustment-lease-amount"
                    style="width: 180px"
                    pattern="^-{0,1}(?!0.)\d+$"
                    data-pattern-error="Please enter a whole number"
                    max="999999"
                    :rules="[rules.numericOnly, rules.maxValue(999999)]"
                    @input="(v) => change('adjustment.leaseAmount', v, 'number')"
                />
            </div>
        </td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
    </tr>
</template>

<script>
import adjustmentAmountBlur from "@/modules/VehiclePricing/mixins/adjustmentAmountBlur";
import AdjustmentValue from "@/modules/VehiclePricing/components/AdjustmentValue";
import AdjustmentType from "@/modules/VehiclePricing/components/AdjustmentType";
import toNumber from "lodash/toNumber";
import lodashSet from "lodash/set";
import { numericOnly, maxValue } from "Util/formRules";

export default {
    name: "PricedModelRow",
    components: { AdjustmentType, AdjustmentValue },
    mixins: [adjustmentAmountBlur],
    props: {
        pricedModel: {
            type: Object,
            required: true,
        },
        modelIdx: {
            type: Number,
            required: true,
        },
        enableLeasePricing: {
            type: Boolean,
            required: true,
        },
        enableInternetPrice: {
            type: Boolean,
            required: false,
            default: false,
        },
        adjustedValueOptions: {
            type: Array,
            required: true,
        },
        adjustmentTypeOptions: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            rules: { numericOnly, maxValue },
        };
    },

    methods: {
        fetchPricedStyles(...args) {
            this.$emit("fetchPricedStyles", ...args);
        },

        change(path, value, type = null) {
            const newValue = {
                ...this.pricedModel,
            };

            if (type === "number") {
                value = toNumber(value);
            }

            lodashSet(newValue, path, value);

            this.$emit("change", newValue);
        },

        enablePricingChangeEvent(value) {
            this.change("adjustment.enabled", value);

            if (value === true && this.pricedModel.adjustment.adjustedValue === null) {
                this.changeAdjustment("INVOICE");
            }
        },

        changeAdjustment(value) {
            this.change("adjustment.adjustedValue", value);

            if (value && this.pricedModel.adjustment.amount === null) {
                this.change("adjustment.amount", "0");
            }
        },
    },
};
</script>
