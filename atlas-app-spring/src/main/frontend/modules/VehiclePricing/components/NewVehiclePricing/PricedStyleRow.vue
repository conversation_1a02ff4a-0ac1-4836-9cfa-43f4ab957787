<template>
    <tr class="style-row price-adjustment-row">
        <td class="style-name accordion-toggle" style="text-align: left; padding-left: 60px; white-space: normal">
            <a class="style-description" @click.prevent="fetchPricedCars(pricedModel, modelIdx, pricedStyle, styleIdx)">
                <v-icon>
                    {{ pricedStyle.showCars ? "mdi-chevron-down" : "mdi-chevron-right" }}
                </v-icon>
                {{ pricedStyle.style.name }}
            </a>
        </td>
        <td>{{ pricedStyle.inventoryCount | numeral("0,0") }} Vehicles</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>
            <v-checkbox :input-value="pricedStyle.adjustment.enabled" @change="(v) => enablePricingChangeEvent(v)" />
        </td>
        <td>
            <adjustment-value
                :adjustment="pricedStyle.adjustment"
                :adjusted-value-options="adjustedValueOptions"
                @input="(v) => change('adjustment.adjustedValue', v)"
            />
        </td>
        <td>
            <div class="d-inline-flex">
                <adjustment-type
                    :adjustment="pricedStyle.adjustment"
                    :adjustment-type-options="adjustmentTypeOptions"
                    @input="(v) => change('adjustment.type', v)"
                />
                <v-text-field
                    :value="pricedStyle.adjustment.amount"
                    :disabled="!pricedStyle.adjustment.enabled || pricedStyle.adjustment.fixedPrice"
                    type="number"
                    class="adjustment-amount"
                    style="width: 180px"
                    pattern="^-{0,1}(?!0.)\d+$"
                    data-pattern-error="Please enter a whole number"
                    max="999999"
                    :rules="[rules.numericOnly, rules.maxValue(999999)]"
                    @blur="adjustmentAmountBlur(pricedStyle)"
                    @input="(v) => change('adjustment.amount', v, 'number')"
                />
            </div>
        </td>
        <td v-if="enableLeasePricing">
            <div class="d-inline-flex">
                <v-select
                    :value="pricedStyle.adjustment.leaseType"
                    :items="adjustmentTypeOptions"
                    :disabled="!pricedStyle.adjustment.enabled || pricedStyle.adjustment.fixedPrice"
                    class="adjustment-type"
                    @input="(v) => change('adjustment.leaseType', v)"
                />
                <v-text-field
                    :value="pricedStyle.adjustment.leaseAmount"
                    :disabled="!pricedStyle.adjustment.enabled || pricedStyle.adjustment.fixedPrice"
                    type="number"
                    class="adjustment-lease-amount"
                    pattern="^-{0,1}(?!0.)\d+$"
                    data-pattern-error="Please enter a whole number"
                    max="999999"
                    :rules="[rules.numericOnly, rules.maxValue(999999)]"
                    @input="(v) => change('adjustment.leaseAmount', v, 'number')"
                />
            </div>
        </td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
        <td>-</td>
    </tr>
</template>

<script>
import toNumber from "lodash/toNumber";
import lodashSet from "lodash/set";
import adjustmentAmountBlur from "@/modules/VehiclePricing/mixins/adjustmentAmountBlur";
import AdjustmentValue from "@/modules/VehiclePricing/components/AdjustmentValue";
import AdjustmentType from "@/modules/VehiclePricing/components/AdjustmentType";
import { numericOnly, maxValue } from "Util/formRules";

export default {
    name: "PricedStyleRow",
    components: { AdjustmentType, AdjustmentValue },
    mixins: [adjustmentAmountBlur],
    props: {
        pricedModel: {
            type: Object,
            required: true,
        },
        modelIdx: {
            type: Number,
            required: true,
        },
        pricedStyle: {
            type: Object,
            required: true,
        },
        styleIdx: {
            type: Number,
            required: true,
        },
        enableLeasePricing: {
            type: Boolean,
            required: true,
        },
        enableInternetPrice: {
            type: Boolean,
            required: false,
            default: false,
        },
        adjustedValueOptions: {
            type: Array,
            required: true,
        },
        adjustmentTypeOptions: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {
            rules: { numericOnly, maxValue },
        };
    },

    methods: {
        fetchPricedCars(...args) {
            this.$emit("fetchPricedCars", ...args);
        },

        change(path, value, type = null) {
            const newValue = {
                ...this.pricedStyle,
            };

            if (type === "number") {
                value = toNumber(value);
            }

            lodashSet(newValue, path, value);

            this.$emit("change", newValue);
        },

        enablePricingChangeEvent(value) {
            this.change("adjustment.enabled", value);

            if (value === true && this.pricedStyle.adjustment.adjustedValue === null) {
                this.change("adjustment.adjustedValue", "INVOICE");
            }
        },
    },
};
</script>
