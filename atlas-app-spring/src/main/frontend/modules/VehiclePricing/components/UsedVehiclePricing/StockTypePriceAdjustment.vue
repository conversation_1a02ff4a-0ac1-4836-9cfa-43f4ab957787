<template>
    <v-card class="fill-height">
        <v-card-title>Global Pricing</v-card-title>
        <v-divider></v-divider>
        <v-form v-model="valid">
            <v-container>
                <v-row>
                    <v-col cols="12">
                        <v-checkbox
                            v-model="adjustmentEnabled"
                            class="mt-0"
                            :disabled="loading"
                            dense
                            label="Enable Global Pricing"
                            persistent-hint
                            hint="Enable if you want adjustment amounts to apply to ALL 'Used' vehicles"
                        />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12">
                        <v-select
                            v-model="adjustmentType"
                            outlined
                            dense
                            hide-details
                            :disabled="!adjustmentEnabled"
                            :items="adjustmentTypeOptions"
                            label="Adjustment Type"
                        />
                    </v-col>
                </v-row>
                <v-row>
                    <v-col cols="12">
                        <v-text-field
                            v-model.number="adjustmentAmount"
                            outlined
                            dense
                            :prefix="adjustmentAmountInputPrefix"
                            :required="adjustmentEnabled"
                            :disabled="!adjustmentEnabled"
                            label="Adjustment Amount"
                            hint="This adjustment amount will apply to all USED vehicles by default; NOTE: VIN level pricing set below will override this amount"
                            persistent-hint
                            type="number"
                            pattern="^-{0,1}(?!0.)\d+$"
                            max="9999"
                            :rules="[rules.numericOnly, rules.maxValue(9999)]"
                            data-pattern-error="Please enter a valid whole dollar amount"
                            @blur="stockTypeAdjustmentAmountBlur"
                        />
                    </v-col>
                </v-row>
            </v-container>
        </v-form>
    </v-card>
</template>
<script>
import { get, sync } from "vuex-pathify";
import { numericOnly, maxValue } from "Util/formRules";
import isNil from "lodash/isNil";
import lodashIsNaN from "lodash/isNaN";

export default {
    name: "StockTypePriceAdjustment",

    props: {
        enableInternetPrice: {
            type: Boolean,
            required: false,
            default: false,
        },
        adjustmentTypeOptions: {
            type: Array,
            required: true,
        },
    },

    data() {
        return {
            rules: { numericOnly, maxValue },
            valid: true,
        };
    },

    computed: {
        loading: get("vehiclePricing/loading"),
        adjustmentType: sync("usedVehiclePricing/<EMAIL>"),
        adjustmentEnabled: sync("usedVehiclePricing/<EMAIL>"),
        adjustmentAmount: sync("usedVehiclePricing/<EMAIL>"),
        adjustmentAmountInputPrefix() {
            return this.adjustmentType === "PERCENTAGE" ? "%" : "$";
        },
    },

    methods: {
        stockTypeAdjustmentAmountBlur() {
            const adjustmentAmountInvalid =
                isNil(this.adjustmentAmount) ||
                lodashIsNaN(this.adjustmentAmount) ||
                (this.adjustmentType === "PERCENTAGE" && this.adjustmentAmount < -100);
            if (adjustmentAmountInvalid) {
                this.adjustmentAmount = 0;
            }
        },
    },
};
</script>
