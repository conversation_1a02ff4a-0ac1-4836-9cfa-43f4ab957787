<template>
    <v-card class="priced-widget fill-height" :loading="elasticMetrics.isLoading || pricingMetricsFromDb.isLoading">
        <v-card-title>
            <span class="mr-2">{{ title }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text class="text-center">
            <div ref="pricedWidget" />
            <div class="text-center red--text text--lighten-1" aria-label="Total errors logged" style="cursor: pointer">
                <router-link
                    v-if="isUsedPriced() && invalidUsedCount > 0"
                    :to="`/import-logs?stockType=USED&dealerIds=${dealerId}`"
                    class="InvalidVehiclesLink text-center red--text text--lighten-1"
                >
                    {{ invalidUsedCount }} Errors Logged
                </router-link>
                <router-link
                    v-else-if="isNewPriced() && invalidNewCount > 0"
                    :to="`/import-logs?stockType=NEW&dealerIds=${dealerId}`"
                    class="InvalidVehiclesLink text-center red--text text--lighten-1"
                >
                    {{ invalidNewCount }} Errors Logged
                </router-link>
            </div>
        </v-card-text>
    </v-card>
</template>

<script>
import "c3/c3.min.css";
import c3 from "c3";
import _ from "lodash";
import api from "@/util/api";
import { get } from "vuex-pathify";

export default {
    name: "PriceWidget",
    props: {
        name: {
            type: String,
            required: true,
        },
        title: {
            type: String,
            required: true,
        },
        chartDescription: {
            type: String,
            required: false,
            default: () => {
                return this.title;
            },
        },
        colorConfig: {
            type: Object,
            required: false,
            default: () => {
                return {};
            },
        },
        description: {
            type: String,
            required: false,
            default: null,
        },
        alertMessage: {
            type: String,
            required: false,
            default: null,
        },
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
    },

    data() {
        return {
            elasticMetrics: {
                pricedCount: 0,
                total: 0,
                isLoading: true,
            },
            defaultColorConfig: {
                green: 90,
                yellow: 75,
            },
            pricingMetricsFromDb: {
                totalNew: 0,
                totalNewPriced: 0,
                totalUsed: 0,
                totalUsedPriced: 0,
                isLoading: true,
            },
            totalInvalidVehicles: 0,
        };
    },

    computed: {
        invalidUsedCount: get("vehiclePricing/invalidUsedCount"),
        invalidNewCount: get("vehiclePricing/invalidNewCount"),
        colorConfigInUse() {
            return _.merge(this.defaultColorConfig, this.colorConfig);
        },

        columns() {
            const pricedCount = _.get(this.metricsDisplay, "pricedCount", 0);
            if (_.isNaN(pricedCount)) {
                return [[this.title, 0]];
            }

            return [[this.title, pricedCount]];
        },

        max() {
            const total = _.get(this.metricsDisplay, "total", 100);
            if (_.isNaN(total) || total <= 0) {
                return 100;
            }

            return total;
        },

        metricsDisplay() {
            if (
                this.elasticMetrics.isLoading === false &&
                this.pricingMetricsFromDb.isLoading === false &&
                this.elasticMetrics.total === 0
            ) {
                if (this.isUsedPriced()) {
                    return {
                        pricedCount: this.pricingMetricsFromDb.totalUsedPriced,
                        total: this.pricingMetricsFromDb.totalUsed,
                    };
                } else if (this.isNewPriced()) {
                    return {
                        pricedCount: this.pricingMetricsFromDb.totalNewPriced,
                        total: this.pricingMetricsFromDb.totalNew,
                    };
                } else {
                    return {
                        pricedCount: 0,
                        total: 100,
                    };
                }
            } else {
                return this.elasticMetrics;
            }
        },
    },

    created() {
        this.loadData();
    },

    methods: {
        updateChart() {
            c3.generate({
                bindto: this.$refs.pricedWidget,
                data: {
                    columns: this.columns,
                    type: "gauge",
                },
                gauge: {
                    max: this.max,
                    label: {
                        format: function (value, ratio) {
                            return _.round(ratio * 100) + "%";
                        },
                    },
                },
                color: {
                    pattern: ["#dc3545", "#ffb237", "#60B044"],
                    threshold: {
                        values: [
                            (this.colorConfigInUse.yellow / 100) * this.metricsDisplay.total,
                            (this.colorConfigInUse.green / 100) * this.metricsDisplay.total,
                            100,
                        ],
                    },
                },
            });
        },

        loadData() {
            this.elasticMetrics.isLoading = true;
            this.pricingMetricsFromDb.isLoading = true;

            api.post(`/dealers/dashboard/${this.name}`, {
                dealerIds: [this.dealerId],
            })
                .then((response) => {
                    this.elasticMetrics = response.data;
                    this.elasticMetrics.isLoading = false;
                })
                .then(() => {
                    this.updateChart();
                })
                .catch((error) => {
                    console.log(error);
                    this.elasticMetrics.isLoading = false;
                });

            api.get(`/stratus/vehicle-pricing/pricing-metrics`, { dealerIds: this.dealerId })
                .then((response) => {
                    this.pricingMetricsFromDb = response.data;
                    this.pricingMetricsFromDb.isLoading = false;
                    this.updateChart();
                })
                .catch((error) => {
                    console.log(error);
                    this.pricingMetricsFromDb.isLoading = false;
                });
        },
        isUsedPriced() {
            return this.name === "used-priced";
        },
        isNewPriced() {
            return this.name === "new-priced";
        },
    },
};
</script>

<style lang="scss" scoped>
.priced-widget {
    min-width: 400px;

    .percent-number {
        font-size: 18px;
    }
}

.InvalidVehiclesLink {
    text-decoration: none;
}
</style>
