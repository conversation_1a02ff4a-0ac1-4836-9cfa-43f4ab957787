<template>
    <v-container>
        <v-row align="start">
            <v-col cols="6">
                <v-text-field v-model="item.vehicle.vehicleId.vin" label="VIN" name="vin"></v-text-field>
            </v-col>
            <v-col cols="6">
                <v-text-field
                    v-model="item.vehicle.vehicleId.dealerId"
                    name="dealerId"
                    label="Dealer Id (Short)"
                ></v-text-field>
            </v-col>
            <v-col cols="6">
                <h3>Vehicle Action</h3>
                <v-radio-group v-model="item.context.action" name="action" row>
                    <v-radio label="Update" value="Update"></v-radio>
                    <v-radio label="Insert" value="Insert"></v-radio>
                </v-radio-group>
            </v-col>
            <v-col cols="6">
                <h3>Stock Type</h3>
                <v-radio-group v-model="item.vehicle.vehicleCondition" name="vehicleCondition" row>
                    <v-radio label="New" value="New"></v-radio><v-radio label="Used" value="Used"></v-radio>
                </v-radio-group>
            </v-col>
            <v-col cols="12">
                <h3>Vehicle Data</h3>
            </v-col>
            <v-col cols="6">
                <v-text-field v-model="item.vehicle.miles" outlined dense label="Miles" name="miles"></v-text-field>
                <v-text-field v-model="item.vehicle.make" outlined dense label="Make" name="make"></v-text-field>
            </v-col>
            <v-col cols="6">
                <v-text-field v-model="item.vehicle.year" outlined dense label="Year" name="year"></v-text-field>
                <v-text-field v-model="item.vehicle.model" outlined dense label="model" name="model"></v-text-field>
            </v-col>
            <v-col v-if="item.vehicle.chromeStyleData" cols="12">
                <h3>Chrome Style Data</h3>
            </v-col>
            <v-col v-if="item.vehicle.chromeStyleData" cols="6">
                <v-text-field
                    v-model="item.vehicle.chromeStyleData.chromeStyleList"
                    outlined
                    dense
                    label="Chrome Style List"
                    name="chromeStyleList"
                ></v-text-field>
            </v-col>
            <v-col v-if="item.vehicle.chromeStyleData" cols="6">
                <v-text-field
                    v-model="item.vehicle.chromeStyleData.styleDescription"
                    outlined
                    dense
                    label="Style Description"
                    name="styleDescription"
                ></v-text-field>
            </v-col>
            <v-col cols="12">
                <h3>Financial Data</h3>
            </v-col>
            <v-col cols="6">
                <v-text-field v-model="item.vehicle.financialData.msrp" outlined dense label="MSRP"></v-text-field>
                <v-text-field
                    v-model="item.vehicle.financialData.customPrice1"
                    name="customPrice1"
                    outlined
                    dense
                    label="Custom Price 1"
                ></v-text-field>
            </v-col>
            <v-col cols="6">
                <v-text-field
                    v-model="item.vehicle.financialData.customPrice2"
                    name="customPrice2"
                    outlined
                    dense
                    label="Custom Price 2"
                ></v-text-field>
                <v-text-field
                    v-model="item.vehicle.financialData.customPrice3"
                    name="customPrice3"
                    outlined
                    dense
                    label="Custom Price 3"
                ></v-text-field>
            </v-col>
            <v-col cols="6">
                <v-text-field
                    v-model="item.vehicle.financialData.sellingPrice"
                    name="sellingPrice"
                    outlined
                    dense
                    label="Selling Price"
                ></v-text-field>
                <v-text-field
                    v-model="item.vehicle.financialData.internetPrice"
                    name="internetPrice"
                    outlined
                    dense
                    label="Internet Price"
                ></v-text-field>
            </v-col>
            <v-col cols="6">
                <v-text-field
                    v-model="item.vehicle.financialData.invoiceAmount"
                    name="invoiceAmount"
                    outlined
                    dense
                    label="Invoice Amount"
                ></v-text-field>
                <v-text-field
                    v-model="item.vehicle.financialData.defaultBookValue"
                    name="defaultBookValue"
                    outlined
                    dense
                    label="Default Book Value"
                ></v-text-field>
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="12">
                <v-textarea
                    :value="metadataFormatted"
                    name="metadata"
                    outlined
                    dense
                    label="Metadata"
                    @blur="callUpdateMetadata"
                ></v-textarea>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import { call } from "vuex-pathify";

export default {
    name: "ImportVehicleRowForm",
    props: {
        vehicle: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            metadataFormatted: "",
        };
    },
    computed: {
        item() {
            return this.vehicle;
        },
    },
    mounted() {
        this.metadataFormatted = JSON.stringify(this.item.vehicle.metadata, undefined, 4);
    },
    methods: {
        updateMetadata: call("qaToolsStore/updateVehicleMetadata"),
        callUpdateMetadata(event) {
            this.updateMetadata({
                vin: this.vehicle.vehicle.vehicleId.vin,
                newMetadata: JSON.parse(event.target.value),
            });
        },
    },
};
</script>

<style scoped></style>
