<template>
    <div>
        <v-card>
            <v-card-title
                >Request
                <v-btn text color="teal accent-4" @click="revealRequest = !revealRequest">
                    {{ revealRequest ? "Hide" : "Show" }}
                </v-btn></v-card-title
            >
            <v-card-text v-if="revealRequest" class="transition-fast-in-fast-out v-card--reveal">
                <v-textarea
                    v-model="conveyorRequestField"
                    name="conveyorRequestField"
                    label="Paste Conveyor Request"
                    clearable
                    clear-icon="mdi-close-circle"
                    rows="12"
                    @change="textAreaChange"
                ></v-textarea>
            </v-card-text>
        </v-card>
        <v-spacer />
        <v-card>
            <v-card-title
                >Pretty Print
                <v-btn
                    text
                    color="teal accent-4"
                    @click="
                        revealPrettyPrint = !revealPrettyPrint;
                        callPrettyPrint();
                    "
                >
                    {{ revealPrettyPrint ? "Hide" : "Show" }}
                </v-btn></v-card-title
            >
            <v-expand-transition>
                <v-card-text v-if="revealPrettyPrint" class="transition-fast-in-fast-out v-card--reveal">
                    <!--eslint-disable-next-line-->
                    <pre style="overflow-y: scroll" v-html="formattedJSON">mock text</pre>
                </v-card-text>
            </v-expand-transition>
        </v-card>
    </div>
</template>

<script>
import { sync } from "vuex-pathify";
export default {
    name: "ImportRequest",
    data() {
        return {
            isLoading: false,
            revealPrettyPrint: false,
            revealRequest: true,
        };
    },
    computed: {
        conveyorRequestField: sync("qaToolsStore/inventoryImportRequest@rawValue"),
        formattedJSON: sync("qaToolsStore/inventoryImportRequest@formatted"),
        vehicles: sync("qaToolsStore/inventoryImportRequest@vehicles"),
        importAction: sync("qaToolsStore/inventoryImportRequest@importAction"),
    },
    mounted() {
        const text =
            '{"action": "Upsert", "export": "2115073", "history": "89857757", "vehicles": [{"valid": false, "context": {"action": "Update"}, "vehicle": {"body": {"style": "4dr Car", "doorCount": 4, "wheelBase": "112"}, "make": "Hyundai", "trim": "Limited", "year": 2016, "media": {"images": [], "videos": []}, "miles": 49780, "model": "Azera", "engine": {"blockType": "V", "cylinders": 6, "description": "Regular Unleaded V-6 3.3 L/204", "displacement": "3.3 L", "aspirationType": "Gasoline Direct Injection", "displacementCubicInches": "204"}, "kbbData": {}, "options": [{"category": "Interior", "description": "Full Cloth Headliner"}, {"category": "Interior", "description": "2 Seatback Storage Pockets"}, {"category": "Safety", "description": "Dual Stage Driver And Passenger Front Airbags"}, {"category": "Exterior", "description": "LED Brakelights"}, {"category": "Interior", "description": "Perimeter Alarm"}, {"category": "Exterior", "description": "Compact Spare Tire Mounted Inside Under Cargo"}, {"category": "Mechanical", "description": "Gas-Pressurized Shock Absorbers"}, {"category": "Interior", "description": "Trip Computer"}, {"category": "Interior", "description": "Power 1st Row Windows w/Driver And Passenger 1-Touch Up/Down"}, {"category": "Exterior", "description": "Speed Sensitive Variable Intermittent Wipers"}, {"category": "Interior", "description": "Delayed Accessory Power"}, {"category": "Interior", "description": "Illuminated Locking Glove Box"}, {"category": "Mechanical", "description": "Dual Stainless Steel Exhaust w/Chrome Tailpipe Finisher"}, {"category": "Interior", "description": "Systems Monitor"}, {"category": "Safety", "description": "Rear Parking Sensors"}, {"category": "Safety", "description": "Curtain 1st And 2nd Row Airbags"}, {"category": "Interior", "description": "Fade-To-Off Interior Lighting"}, {"category": "Exterior", "description": "Chrome Side Windows Trim and Black Front Windshield Trim"}, {"category": "Safety", "description": "Airbag Occupancy Sensor"}, {"category": "Interior", "description": "Leatherette Door Trim Insert"}, {"category": "Safety", "description": "Outboard Front Lap And Shoulder Safety Belts -inc: Rear Center 3 Point, Height Adjusters and Pretensioners"}, {"category": "Interior", "description": "Compass"}, {"category": "Interior", "description": "Cargo Space Lights"}, {"category": "Exterior", "description": "Clearcoat Paint"}, {"category": "Safety", "description": "Side Impact Beams"}, {"category": "Interior", "description": "Tracker System"}, {"category": "Safety", "description": "Rear Child Safety Locks"}, {"category": "Exterior", "description": "Steel Spare Wheel"}, {"category": "Safety", "description": "Dual Stage Driver And Passenger Seat-Mounted Side Airbags"}, {"category": "Exterior", "description": "Chrome Door Handles"}, {"category": "Mechanical", "description": "Front And Rear Anti-Roll Bars"}, {"category": "Interior", "description": "Outside Temp Gauge"}, {"category": "Interior", "description": "Driver And Passenger Visor Vanity Mirrors w/Driver And Passenger Illumination"}, {"category": "Exterior", "description": "Front Fog Lamps"}, {"category": "Interior", "description": "Power Door Locks w/Autolock Feature"}, {"category": "Mechanical", "description": "Engine Oil Cooler"}, {"category": "Exterior", "description": "Perimeter/Approach Lights"}, {"category": "Interior", "description": "Front Cupholder"}, {"category": "Interior", "description": "Digital/Analog Display"}, {"category": "Interior", "description": "Rear Cupholder"}, {"category": "Interior", "description": "Proximity Key For Doors And Push Button Start"}, {"category": "Interior", "description": "Valet Function"}, {"category": "Interior", "description": "Air Filtration"}, {"category": "Interior", "description": "Front And Rear Map Lights"}, {"category": "Interior", "description": "Cruise Control w/Steering Wheel Controls"}, {"category": "Safety", "description": "ABS And Driveline Traction Control"}, {"category": "Safety", "description": "Low Tire Pressure Warning"}, {"category": "Interior", "description": "Gauges -inc: Speedometer, Odometer, Engine Coolant Temp, Tachometer, Trip Odometer and Trip Computer"}, {"category": "Interior", "description": "Streaming Audio"}, {"category": "Exterior", "description": "Light Tinted Glass"}, {"category": "Interior", "description": "Driver Foot Rest"}, {"category": "Exterior", "description": "Fully Galvanized Steel Panels"}, {"category": "Safety", "description": "Electronic Stability Control (ESC)"}, {"category": "Mechanical", "description": "Strut Front Suspension w/Coil Springs"}, {"category": "Interior", "description": "2 12V DC Power Outlets"}, {"category": "Mechanical", "description": "Front-Wheel Drive"}, {"category": "Exterior", "description": "Body-Colored Rear Bumper"}, {"category": "Mechanical", "description": "Electric Power-Assist Speed-Sensing Steering"}, {"category": "Interior", "description": "Front Center Armrest and Rear Center Armrest"}, {"category": "Interior", "description": "HVAC -inc: Underseat Ducts and Console Ducts"}, {"category": "Interior", "description": "Engine Immobilizer"}, {"category": "Interior", "description": "HomeLink Garage Door Transmitter"}, {"category": "Interior", "description": "Full Carpet Floor Covering"}, {"category": "Mechanical", "description": "Multi-Link Rear Suspension w/Coil Springs"}, {"category": "Interior", "description": "Day-Night Auto-Dimming Rearview Mirror"}, {"category": "Interior", "description": "Dual Zone Front Automatic Air Conditioning"}, {"category": "Exterior", "description": "Front Windshield -inc: Sun Visor Strip"}, {"category": "Interior", "description": "Carpet Floor Trim and Carpet Trunk Lid/Rear Cargo Door Trim"}, {"category": "Interior", "description": "Full Floor Console w/Covered Storage, Mini Overhead Console w/Storage and 2 12V DC Power Outlets"}, {"category": "Interior", "description": "Trunk/Hatch Auto-Latch"}, {"category": "Interior", "description": "Leather seating surfaces"}, {"category": "Exterior", "description": "Chrome Grille"}, {"oemCode": "FK", "category": "Installed", "description": "FIRST AID KIT"}, {"oemCode": "CF", "category": "Installed", "description": "CARPETED FLOOR MATS"}, {"oemCode": "WL-0", "category": "Installed", "description": "WHEEL LOCKS"}, {"category": "Mechanical", "description": "150 Amp Alternator"}, {"category": "Interior", "description": "Manual Anti-Whiplash Adjustable Front Head Restraints and Manual Adjustable Rear Head Restraints"}, {"category": "Mechanical", "description": "18.5 Gal. Fuel Tank"}, {"category": "Mechanical", "description": "2.88 Axle Ratio"}, {"category": "Interior", "description": "Remote Releases -Inc: Power Cargo Access and Power Fuel"}, {"category": "Interior", "description": "Selective Service Internet Access"}, {"oemCode": "CN", "category": "Installed", "description": "CARGO NET"}, {"category": "Exterior", "description": "Chrome Bodyside Insert"}, {"category": "Interior", "description": "Leather Steering Wheel"}, {"category": "Safety", "description": "Lane Departure Warning"}, {"category": "Interior", "description": "Distance Pacing w/Traffic Stop-Go"}, {"category": "Interior", "description": "60-40 Folding Bench Front Facing Heated Fold Forward Seatback Rear Seat"}, {"category": "Interior", "description": "Power Rear Windows and w/Manual Sun Blinds"}, {"category": "Safety", "description": "Restricted Driving Mode"}, {"oemCode": "CT", "category": "Installed", "description": "COMPOSITE CARGO TRAY"}, {"category": "Mechanical", "description": "Engine: 3.3L GDI DOHC 24-Valve V6"}, {"category": "Interior", "description": "Remote Keyless Entry w/Integrated Key Transmitter, 4 Door Curb/Courtesy, Illuminated Entry, Illuminated Ignition Switch and Panic Button"}, {"category": "Exterior", "description": "Body-Colored Power Heated Side Mirrors w/Power Folding and Turn Signal Indicator"}, {"category": "Interior", "description": "Power Tilt/Telescoping Steering Column"}, {"category": "Interior", "description": "Automatic Equalizer"}, {"category": "Exterior", "description": "Power Open And Close Trunk Rear Cargo Access"}, {"category": "Exterior", "description": "Fixed Rear Window w/Defroster and Power Blind"}, {"category": "Interior", "description": "Driver Seat"}, {"category": "Interior", "description": "Passenger Seat"}, {"category": "Safety", "description": "Forward Collision Warning and Rear Collision Warning"}, {"oemCode": "01-0", "category": "Installed", "description": "OPTION GROUP 01"}, {"category": "Exterior", "description": "Body-Colored Front Bumper w/Chrome Rub Strip/Fascia Accent"}, {"category": "Mechanical", "description": "80-Amp/Hr 660CCA Maintenance-Free Battery w/Run Down Protection"}, {"category": "Interior", "description": "Integrated Roof Diversity Antenna"}, {"category": "Mechanical", "description": "4-Wheel Disc Brakes w/4-Wheel ABS, Front Vented Discs, Brake Assist and Electric Parking Brake"}, {"category": "Interior", "description": "FOB Controls -inc: Cargo Access"}, {"category": "Safety", "description": "Blind Spot"}, {"category": "Interior", "description": "Leather Gear Shifter Material"}, {"category": "Interior", "description": "Radio w/Clock, Steering Wheel Controls and Radio Data System"}, {"oemCode": "RY", "category": "Installed", "description": "GRAPHITE BLACK, LEATHER SEATING SURFACES"}, {"oemCode": "WW7", "category": "Installed", "description": "DIAMOND WHITE PEARL"}, {"category": "Interior", "description": "Interior Trim -inc: Simulated Carbon Fiber/Metal-Look Instrument Panel Insert, Simulated Carbon Fiber Door Panel Insert, Metal-Look Console Insert and Chrome Interior Accents"}, {"category": "Exterior", "description": "Fully Automatic Projector Beam High Intensity Low Beam Daytime Running Auto-Leveling Auto High-Beam Headlamps w/Delay-Off"}, {"category": "Safety", "description": "Driver And Passenger Side Airbag Head Extension, Driver Knee Airbag and Rear Side-Impact Airbag"}, {"category": "Interior", "description": "Instrument Panel Covered Bin, Refrigerated/Cooled Box Located In The Glovebox, Driver / Passenger And Rear Door Bins"}, {"category": "Exterior", "description": "Express Open/Close Sliding And Tilting Glass 1st And 2nd Row Sunroof w/Power Sunshade and Wind Deflector"}, {"category": "Exterior", "description": "Tires: P245/40VR19"}, {"category": "Interior", "description": "Power Heated & Ventilated Front Bucket Seats -inc: 10-way power driver\'s seat w/memory and 2-way power lumbar (adds seat cushion extension), and 8-way power passenger seat"}, {"category": "Interior", "description": "Door Mirrors and Steering Wheel"}, {"category": "Interior", "description": "Radio: AM/FM/SiriusXM/CD/MP3 w/Navigation System -inc: 8-inch touchscreen (AVN 4.5), 14-speaker Infinity Logic 7 surround sound audio system (550-watt), album cover art, iPod/USB and auxiliary input jacks, HD Radio w/multicasting, SiriusXM TravelLink (complimentary trial), Blue Link Telematics (Gen 2), and Bluetooth hands-free phone system"}, {"category": "Mechanical", "description": "Transmission: 6-Speed Automatic w/SHIFTRONIC -inc: lock-up torque converter, shift lock and manual shift mode"}, {"category": "Exterior", "description": "Wheels: 19 x 8.0J Aluminum Alloy"}], "evoxData": {}, "fuelData": {"type": "Gasoline Fuel", "cityMilesPerGallon": 20, "highwayMilesPerGallon": 29}, "metadata": {"schemaURI": "https://www.homenetiol.com/CanonicalVehicle/schema", "sourceType": "Used", "dateCreated": "2021-07-24T19:31:28.1330000", "dateInStock": "2021-07-23T00:00:00.0000000", "daysInStock": 3, "stockNumber": "H17319", "dateModified": "2021-07-26T13:55:00.6930000", "schemaVersion": "********", "dealershipInfo": {"url": "stamfordhyundai.com", "city": "Stamford", "name": "Stamford Hyundai", "state": "CT", "address": "85 Magee Ave", "zipCode": "06902", "phoneNumber": "5162387513", "addressLatitude": 41.046897, "addressLongitude": -73.529365}, "cachedVehicleId": 850176988, "masterVehicleId": 1197618477, "isHomePageSpecial": false, "documentCreationDate": "2021-07-26T14:03:33.5352665"}, "nadaData": {}, "vehicleId": {"vin": "KMHFH4JG2GA536650", "dealerId": "39139"}, "decodeData": {"options": "COMPOSITE CARGO TRAY, GRAPHITE BLACK  LEATHER SEATING SURFACES, FIRST AID KIT, CARGO NET, DIAMOND WHITE PEARL, WHEEL LOCKS, CARPETED FLOOR MATS, OPTION GROUP 01, Front Wheel Drive, Power Steering, ABS, 4-Wheel Disc Brakes, Brake Assist, Aluminum Wheels, Tires - Front Performance, Tires - Rear Performance, Temporary Spare Tire, Sun/Moonroof, Generic Sun/Moonroof, Panoramic Roof, Heated Mirrors, Power Mirror(s), Integrated Turn Signal Mirrors, Power Folding Mirrors, Rear Defrost, Intermittent Wipers, Variable Speed Intermittent Wipers, Fog Lamps, Daytime Running Lights, HID headlights, Automatic Headlights, Headlights-Auto-Leveling, AM/FM Stereo, CD Player, Navigation System, Premium Sound System, Satellite Radio, MP3 Player, Bluetooth Connection, Telematics, Back-Up Camera, Auxiliary Audio Input, HD Radio, Smart Device Integration, Requires Subscription, Steering Wheel Audio Controls, Power Driver Seat, Power Passenger Seat, Bucket Seats, Heated Front Seat(s), Driver Adjustable Lumbar, Seat Memory, Cooled Front Seat(s), Pass-Through Rear Seat, Heated Rear Seat(s), Rear Bench Seat, Adjustable Steering Wheel, Trip Computer, Power Windows, Leather Steering Wheel, Keyless Entry, Power Door Locks, Keyless Start, Keyless Entry, Power Door Locks, Remote Trunk Release, Universal Garage Door Opener, Cruise Control, Adaptive Cruise Control, Climate Control, Multi-Zone A/C, A/C, Leather Seats, Auto-Dimming Rearview Mirror, Driver Vanity Mirror, Passenger Vanity Mirror, Driver Illuminated Vanity Mirror, Passenger Illuminated Visor Mirror, Mirror Memory, Keyless Start, Power Windows, Power Door Locks, Trip Computer, Security System, Engine Immobilizer, Traction Control, Stability Control, Traction Control, Front Side Air Bag, Blind Spot Monitor, Rear Parking Aid, Cross-Traffic Alert, Lane Departure Warning, Tire Pressure Monitor, Driver Air Bag, Passenger Air Bag, Front Head Air Bag, Rear Head Air Bag, Passenger Air Bag Sensor, Front Side Air Bag, Rear Side Air Bag, Knee Air Bag, Driver Restriction Features, Child Safety Locks", "factoryCodes": "01-0 CT RY FK CN WW7 WL-0 CF"}, "description": "Excellent Condition, LOW MILES - 49,780! FUEL EFFICIENT 29 MPG Hwy/20 MPG City! Heated/Cooled Leather Seats, Navigation, Moonroof, Back-Up Camera, Premium Sound System, Satellite Radio, Aluminum Wheels, OPTION GROUP 01 CLICK NOW!<br /><br />KEY FEATURES INCLUDE<br />Leather Seats, Navigation, Sunroof, Panoramic Roof, Heated Driver Seat, Heated Rear Seat, Cooled Driver Seat, Back-Up Camera, Premium Sound System, Satellite Radio, iPod/MP3 Input, Onboard Communications System, Aluminum Wheels, Keyless Start, Dual Zone A/C MP3 Player, Keyless Entry, Remote Trunk Release, Steering Wheel Controls, Child Safety Locks. <br /><br />OPTION PACKAGES<br />OPTION GROUP 01. Hyundai Limited with DIAMOND WHITE PEARL exterior and BLACK interior features a V6 Cylinder Engine with 293 HP at 6400 RPM*. Local Trade-In, Financing Available <br /><br />WHY BUY FROM US<br />Stamford Hyundai is a digital, interactive dealership that is the Hyundai Eastern Region #1 service satisfaction Hyundai Dealer in the district two years running. We believe in transparency for our clients and feel that when our customers are educated about their purchase from availability, total brand value and finance options it is easy to say Yes to Hyundai. Combine this with our exceptional customer service and convenient online purchase process; it is easy to say Yes to Stamford Hyundai! <br /><br />Mileage may be higher due to test drives or if vehicle is/was a loaner vehicle. Horsepower calculations based on trim engine configuration. Fuel economy calculations based on original manufacturer data for trim engine configuration. Please confirm the accuracy of the included equipment by calling us prior to purchase. <br />", "marketClass": "4-door Large Passenger Car", "modelNumber": "73442F65", "transmission": {"name": "Automatic", "driveType": "FWD", "description": "6-Speed Automatic w/OD", "numberOfSpeeds": 6}, "certification": {"dealerCertified": false, "manufacturerCertified": "False"}, "financialData": {"msrp": 18815, "customPrice1": 0, "customPrice2": 0, "customPrice3": 18815, "sellingPrice": 18815, "internetPrice": 0, "invoiceAmount": 0, "defaultBookValue": 18815, "extendedCustomPrices": {}}, "toyotaOemData": {}, "visualDetails": {"exteriorColor": {"code": "WW7", "hexCode": "E7E6E4", "description": "Diamond White Pearl", "genericColor": "White"}, "interiorColor": {"code": "RY", "description": "Graphite Black", "genericColor": "Black"}}, "dealerComments": {"customCommentFields": {}}, "chromeStyleData": {"chromeStyleList": "375483", "styleDescription": "4dr Sdn Limited", "chromeStyleMatchCount": 1}, "standardizedData": {"epaClassification": "Large Cars"}, "vehicleCondition": "Used", "passengerCapacity": 5, "customBooleanFields": {}, "mercedesBenzVistaData": {}}, "adaptable": false}]}';
        this.conveyorRequestField = text;
        this.textAreaChange(text);
    },
    methods: {
        textAreaChange(text) {
            if (text == null || !text) {
                this.formattedJSON = "";
                this.vehicles = [];
                this.importAction = null;
                this.isLoading = false;
                return;
            }
            this.isLoading = true;
            let obj = JSON.parse(text);
            obj.vehicles = this.normalizeVehicles(obj);

            const jsonString = JSON.stringify(obj, undefined, 4);

            this.conveyorRequestField = jsonString;
            this.vehicles = obj.vehicles;
            this.importAction = obj.action;
            this.isLoading = false;
        },
        callPrettyPrint() {
            this.formattedJSON = this.syntaxHighlight(this.conveyorRequestField);
            this.isLoading = false;
        },
        syntaxHighlight(json) {
            const objStr = json;

            return this.$options.filters.jsonPrettier(objStr);
        },
        normalizeVehicles(jsonObject) {
            let vehicles = jsonObject.vehicles;
            let i = 0;
            for (let item of vehicles) {
                item.vehicle.id = i++;
                item.vehicle.options = [];
                item.vehicle.media = [];
            }
            return vehicles;
        },
    },
};
</script>

<style lang="scss">
@import "@sass/jsonstyles.scss";
</style>
