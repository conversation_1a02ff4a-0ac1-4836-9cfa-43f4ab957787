<template>
    <v-container>
        <v-row align="start">
            <v-col cols="12">
                <v-text-field v-model="userId" label="User Id" name="userId"></v-text-field>
                <v-btn color="primary" @click="fetchPreApprovals"> Search </v-btn>
            </v-col>
        </v-row>
        <v-row>
            <v-col cols="12">
                <v-data-table
                    :disable-pagination="true"
                    :hide-default-footer="true"
                    :headers="[
                        { text: 'Financier Id', value: 'financierId' },
                        { text: 'Approval Number', value: 'approvalNumber' },
                        { text: 'Pre Approval Date', value: 'preApprovalDate' },
                        { text: 'Tier Bump', value: 'tierBump' },
                        { text: 'Expire', value: 'expire', sortable: false },
                        { text: 'Renew', value: 'renew', sortable: false },
                    ]"
                    :items="preApprovals"
                    class="elevation-1"
                >
                    <template #item.expire="{ item }">
                        <v-icon small @click="expirePreApproval(item.approvalNumber)"> mdi-minus-circle-off </v-icon>
                    </template>
                    <template #item.renew="{ item }">
                        <v-icon small @click="renewPreApproval(item.approvalNumber)"> mdi-check-bold </v-icon>
                    </template>
                </v-data-table>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import { sync, get } from "vuex-pathify";
import api from "Util/api";

export default {
    name: "QaToolPreApproval",
    data() {
        return {
            userId: null,
        };
    },
    computed: {
        user: sync("qaToolsStore/preApprovals@user"),
        preApprovals: sync("qaToolsStore/preApprovals@data"),
    },
    methods: {
        fetchPreApprovals() {
            api.get(`/qaTools/pre-approvals?userId=${this.userId}`)
                .then((response) => {
                    this.preApprovals = response.data;
                })
                .catch((response) => {
                    console.error(response);
                });
        },
        expirePreApproval(preApprovalNumber) {
            api.post(`/qaTools/pre-approvals/${preApprovalNumber}/expire?userId=${this.userId}`)
                .then((response) => {
                    this.fetchPreApprovals();
                })
                .catch((response) => {
                    console.error(response);
                });
        },
        renewPreApproval(preApprovalNumber) {
            api.post(`/qaTools/pre-approvals/${preApprovalNumber}/renew?userId=${this.userId}`)
                .then((response) => {
                    this.fetchPreApprovals();
                })
                .catch((response) => {
                    console.error(response);
                });
        },
    },
};
</script>

<style scoped></style>
