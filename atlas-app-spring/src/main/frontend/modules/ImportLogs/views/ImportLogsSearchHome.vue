<template>
    <search-page :key="dealerIds">
        <import-log-search-form slot="searchForm" :dealer-ids-page="dealerIds" />
        <import-log-list slot="searchList" :dealer-ids-page="dealerIds" :store="store" />
    </search-page>
</template>
<script>
import { call } from "vuex-pathify";

import SearchPage from "Components/Search";
import ImportLogSearchForm from "@/modules/ImportLogs/components/importLogsSearchForm";
import ImportLogList from "@/modules/ImportLogs/components/importLogsList";

export default {
    components: { ImportLogList, ImportLogSearchForm, SearchPage },
    props: {
        stockType: {
            type: String,
            required: false,
            default: "NEW",
        },
    },
    data: () => ({
        store: "importLogsSearch",
    }),
    computed: {
        dealerIds() {
            return this.$route.query.dealerIds;
        },
    },
    created() {
        this.setSearchUri(`/import-logs`);
    },
    methods: {
        setSearchUri: call("importLogsSearch/setSearchUri"),
    },
};
</script>
