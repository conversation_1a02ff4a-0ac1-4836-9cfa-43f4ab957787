<template>
    <reporting-metrics-row
        title="Deals"
        :is-loading="isLoading"
        :affected-by-date-range="true"
        :tooltip-content="headerTooltipContent"
    >
        <v-col cols="12" md="12">
            <div class="bar-deals-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" class="tooltip-base active-deals" small v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span> Deals represent when a customer views a proposed deal for a vehicle, not a sale </span>
                </v-tooltip>
                <apexchart
                    ref="dealsOverTime"
                    width="100%"
                    height="300"
                    type="line"
                    :series="dealsOverTime.series"
                    :options="dealsOverTime.chartOptions"
                />
            </div>
        </v-col>
        <v-col v-if="false" cols="12" md="6">
            <div class="bar-deals-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" class="tooltip-base average-sales" small v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span
                        >This is the sales price displayed on a deal in the system, not the price the vehicle was sold
                        for</span
                    >
                </v-tooltip>
                <apexchart
                    ref="dealsSalesAverage"
                    width="100%"
                    height="300"
                    type="line"
                    :series="dealsSalesAverage.series"
                    :options="dealsSalesAverage.chartOptions"
                />
            </div>
        </v-col>
        <v-col cols="12" md="6" lg="6">
            <div class="deals-chart-wrapper">
                <div class="pie-chart-contain">
                    <v-tooltip top open-on-focus>
                        <template #activator="{ on, attrs }">
                            <v-icon color="grey" dark v-bind="attrs" class="tooltip-base stock-type" small v-on="on">
                                mdi-information-outline
                            </v-icon>
                        </template>
                        <span> Stock type of proposed vehicle deals </span>
                    </v-tooltip>
                    <apexchart
                        width="100%"
                        height="275"
                        type="pie"
                        :series="dealsStockType.series"
                        :options="dealsStockType.chartOptions"
                    />
                </div>
            </div>
        </v-col>
        <v-col cols="12" md="6" lg="6">
            <div class="deals-chart-wrapper">
                <div class="pie-chart-contain">
                    <v-tooltip top open-on-focus>
                        <template #activator="{ on, attrs }">
                            <v-icon color="grey" dark v-bind="attrs" class="tooltip-base deal-type" small v-on="on">
                                mdi-information-outline
                            </v-icon>
                        </template>
                        <span> Finance type of proposed vehicle deals </span>
                    </v-tooltip>
                    <apexchart
                        width="100%"
                        height="275"
                        type="pie"
                        :series="dealsType.series"
                        :options="dealsType.chartOptions"
                    />
                </div>
            </div>
        </v-col>
        <v-col cols="12">
            <div class="deals-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon color="grey" dark v-bind="attrs" class="tooltip-base deal-term" small v-on="on">
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span> Term of proposed vehicle deals, both finance and lease </span>
                </v-tooltip>
                <apexchart
                    width="100%"
                    height="300"
                    type="bar"
                    :series="dealsTerm.series"
                    :options="dealsTerm.chartOptions"
                />
            </div>
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import ReportingMetricsRow from "@/modules/Reporting/components/ReportingMetricsRow";
import { get } from "vuex-pathify";
import moment from "moment";
import _ from "lodash";
import api from "Util/api";

export default {
    name: "ReportingDealMetrics",
    components: { ReportingMetricsRow },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            isLoading: false,
            dealsOverTime: {
                series: [
                    {
                        name: "Number of Deals",
                        data: [],
                    },
                ],
                chartOptions: {
                    chart: {
                        type: "area",
                        stacked: false,
                        height: 350,
                        zoom: {
                            type: "x",
                            enabled: true,
                            autoScaleYaxis: true,
                        },
                        toolbar: {
                            autoSelected: "zoom",
                            tools: {
                                download: false,
                                selection: true,
                                zoom: true,
                                zoomin: true,
                                zoomout: true,
                                pan: true,
                                reset: false,
                            },
                        },
                        events: {
                            beforeZoom: (chartContext, { xaxis }) => {
                                let series = chartContext.w.globals.seriesX;
                                let seriesRange = series[0][series[0].length - 1] - series[0][0];
                                let zoomRange = xaxis.max - xaxis.min;
                                if (zoomRange > seriesRange)
                                    return {
                                        xaxis: {
                                            min: series[0][0],
                                            max: series[0][series[0].length - 1],
                                        },
                                    };
                                else {
                                    return {
                                        xaxis: {
                                            min: xaxis.min,
                                            max: xaxis.max,
                                        },
                                    };
                                }
                            },
                        },
                    },
                    dataLabels: {
                        enabled: false,
                    },
                    stroke: {
                        curve: "smooth",
                    },
                    title: {
                        text: "Deals created by day",
                        align: "left",
                    },
                    yaxis: {
                        title: {
                            text: "Deals",
                        },
                    },
                    xaxis: {
                        type: "datetime",
                    },
                    tooltip: {
                        shared: false,
                    },
                },
            },
            dealsSalesAverage: {
                series: [
                    {
                        name: "Average Sale",
                        data: [],
                    },
                ],
                chartOptions: {
                    chart: {
                        type: "area",
                        stacked: false,
                        height: 350,
                        zoom: {
                            type: "x",
                            enabled: true,
                            autoScaleYaxis: true,
                        },
                        toolbar: {
                            autoSelected: "zoom",
                            tools: {
                                download: false,
                                selection: true,
                                zoom: true,
                                zoomin: true,
                                zoomout: true,
                                pan: true,
                                reset: false,
                            },
                        },
                    },
                    dataLabels: {
                        enabled: false,
                    },
                    stroke: {
                        curve: "smooth",
                    },
                    title: {
                        text: "Average Sales Price Over Time",
                        align: "left",
                    },
                    yaxis: {
                        labels: {
                            formatter: function (val) {
                                return val.toFixed(0);
                            },
                        },
                        title: {
                            text: "Sale Price",
                        },
                    },
                    xaxis: {
                        type: "datetime",
                    },
                    tooltip: {
                        shared: false,
                        y: {
                            formatter: function (val) {
                                return val.toFixed(2);
                            },
                        },
                    },
                },
            },
            dealsStockType: {
                series: [],
                chartOptions: {
                    chart: {
                        width: 500,
                        type: "pie",
                        toolbar: {
                            show: false,
                        },
                    },
                    dataLabels: {
                        enabled: true,
                        textAnchor: "middle",
                        distributed: false,
                        offsetX: 0,
                        offsetY: 0,
                        style: {
                            fontSize: "14px",
                            fontFamily: "Helvetica, Arial, sans-serif",
                            fontWeight: "bold",
                            colors: undefined,
                        },
                        background: {
                            enabled: true,
                            foreColor: "#fff",
                            padding: 4,
                            borderRadius: 2,
                            borderWidth: 1,
                            borderColor: "#fff",
                            opacity: 0.9,
                        },
                    },
                    labels: [],
                    legend: {
                        position: "bottom",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    noData: {
                        text: "No data available",
                    },
                    title: {
                        text: "Stock Type of Deals",
                        align: "center",
                    },
                },
            },
            dealsTerm: {
                series: [
                    {
                        data: [],
                    },
                ],
                chartOptions: {
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            distributed: true,
                            borderRadius: 4,
                            horizontal: true,
                            columnWidth: "100%",
                        },
                    },
                    dataLabels: {
                        enabled: false,
                        dropShadow: {
                            enabled: true,
                        },
                    },
                    legend: {
                        show: false,
                    },
                    noData: {
                        text: "No data available",
                    },
                    xaxis: {
                        categories: [],
                    },
                    title: {
                        text: "Deal Term",
                    },
                    tooltip: {
                        x: {
                            show: true,
                        },
                    },
                },
            },
            dealsType: {
                series: [],
                chartOptions: {
                    chart: {
                        width: 500,
                        type: "pie",
                        toolbar: {
                            show: false,
                        },
                    },
                    dataLabels: {
                        enabled: true,
                        textAnchor: "middle",
                        distributed: false,
                        offsetX: 0,
                        offsetY: 0,
                        style: {
                            fontSize: "14px",
                            fontFamily: "Helvetica, Arial, sans-serif",
                            fontWeight: "bold",
                            colors: undefined,
                        },
                        background: {
                            enabled: true,
                            foreColor: "#fff",
                            padding: 4,
                            borderRadius: 2,
                            borderWidth: 1,
                            borderColor: "#fff",
                            opacity: 0.9,
                        },
                    },
                    labels: [],
                    legend: {
                        position: "bottom",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    noData: {
                        text: "No data available",
                    },
                    title: {
                        text: "Finance Type",
                        align: "center",
                    },
                },
            },
            headerTooltipContent: "A deal is created whenever a user progresses to the My Deal page in the App",
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        dateRange: get("reportingStore/chartFilters@dateRange"),
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
    },
    watch: {
        searchResults() {
            this.loadAllCharts();
        },
        dateRange() {
            this.loadAllCharts();
        },
    },
    methods: {
        loadAllCharts() {
            this.fetchDealsOverTime();
            this.fetchDealsAverageSales();
            this.fetchDealsStockType();
            this.fetchDealsTerm();
            this.fetchDealsType();
        },
        fetchDealsOverTime() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/deals/count`, this.reportingRequest)
                .then((response) => {
                    let results = _.get(response, "data.lineChartPairs");
                    if (_.isNil(results)) return;

                    this.dealsOverTime.series[0].data.splice(0);
                    let seriesValues = this.dealsOverTime.series[0].data;
                    results.forEach((element) => {
                        _.forOwn(element, function (value, key) {
                            const name = _.upperFirst(_.lowerCase(key));
                            let formattedDate = new Date(Number(name)).toISOString().split("T")[0];

                            seriesValues.push({ x: formattedDate, y: value });
                        });
                    });

                    this.$refs.dealsOverTime.updateSeries(
                        [
                            {
                                data: seriesValues,
                            },
                        ],
                        false,
                        true
                    );
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchDealsAverageSales() {
            return; //ATS-433 request this chart to be removed. saving this code for future use.
            this.isLoading = true;
            api.post(`/reporting/${this.programId}/deals/salesAverage`, this.reportingRequest)
                .then((response) => {
                    let results = _.get(response, "data.lineChartPairs");
                    if (_.isNil(results)) return;

                    this.dealsSalesAverage.series[0].data.splice(0);
                    let seriesValues = this.dealsSalesAverage.series[0].data;
                    results.forEach((element) => {
                        _.forOwn(element, function (value, key) {
                            const name = _.upperFirst(_.lowerCase(key));
                            let formattedDate = new Date(Number(name)).toISOString().split("T")[0];

                            seriesValues.push({ x: formattedDate, y: value.toFixed(2) });
                        });
                    });

                    this.$refs.dealsSalesAverage.updateSeries(
                        [
                            {
                                data: seriesValues,
                            },
                        ],
                        false,
                        true
                    );
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchDealsStockType() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/deals/byStockType`, this.reportingRequest)
                .then((response) => {
                    let results = _.get(response, "data.results");
                    if (_.isNil(results)) return;

                    this.dealsStockType.chartOptions.labels.splice(0);
                    this.dealsStockType.series.splice(0);
                    _.map(results, (r) => {
                        const name = _.upperFirst(_.lowerCase(r.name));
                        this.dealsStockType.chartOptions.labels.push(name);
                        this.dealsStockType.series.push(r.count);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchDealsTerm() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/deals/byTerm`, this.reportingRequest)
                .then((response) => {
                    let results = _.get(response, "data.results");

                    if (_.isNil(results)) return;

                    this.dealsTerm.series = [
                        {
                            name: "Deal Term",
                            data: [],
                        },
                    ];
                    this.dealsTerm.chartOptions = {
                        ...this.dealsTerm.chartOptions,
                        xaxis: {
                            ...this.dealsTerm.chartOptions.xaxis,
                            categories: [],
                        },
                    };

                    _.map(results, (res) => {
                        this.dealsTerm.series[0].data.push({ x: `${res.name} months`, y: res.count });
                        this.dealsTerm.chartOptions.xaxis.categories.push(`${res.name} months`);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchDealsType() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/deals/byDealType`, this.reportingRequest)
                .then((response) => {
                    let results = _.get(response, "data.results");

                    if (_.isNil(results)) return;

                    this.dealsType.chartOptions.labels.splice(0);
                    this.dealsType.series.splice(0);
                    _.map(results, (r) => {
                        const name = _.upperFirst(_.lowerCase(r.name));
                        this.dealsType.chartOptions.labels.push(name);
                        this.dealsType.series.push(r.count);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        updateLineCharts(arrayWithPairs, chartData, type) {
            let xaxisLabels = [];
            let seriesValues = [];
            arrayWithPairs.forEach((element) => {
                _.forOwn(element, function (value, key) {
                    const name = _.upperFirst(_.lowerCase(key));
                    let formattedDate =
                        type === "dealsSalesAverage" ? new Date(Number(name)).toISOString().split("T")[0] : "";
                    type === "dealsSalesAverage" ? xaxisLabels.push(formattedDate) : xaxisLabels.push(name);
                    seriesValues.push(value);
                });
            });

            chartData.series[0].data = [...seriesValues];

            chartData.chartOptions = {
                ...chartData.chartOptions,
                ...{
                    xaxis: {
                        categories: xaxisLabels,
                    },
                },
            };
        },
    },
};
</script>

<style scoped>
.apexcharts-toolbar {
    z-index: 0 !important;
}
.pie-chart-contain {
    position: relative;
    width: 100%;
    max-width: 210px;
    margin: 0 auto;
}
i.tooltip-base {
    position: absolute;
    z-index: 1;
    top: 4px;
}
i.active-deals {
    top: 185px;
    left: 19px;
}
i.average-sales {
    left: 224px;
}
i.stock-type {
    left: 90%;
}
i.deal-type {
    left: 78%;
}
i.deal-term {
    top: auto;
    left: 108px;
    bottom: 311px;
}
</style>
