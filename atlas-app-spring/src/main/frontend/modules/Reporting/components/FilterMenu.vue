<template>
    <v-container fluid>
        <v-expansion-panels class="filter-menu" :style="{ top: top }" :class="isFixed ? 'stick' : 'unstick'">
            <v-expansion-panel>
                <v-expansion-panel-header class="primary-bg-color">
                    <div class="d-flex align-center">
                        <span class="filter-title">Filters</span>
                        <v-icon class="ml-1 text--primary">mdi-filter</v-icon>
                    </div>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                    <reporting-dealer-facets />
                </v-expansion-panel-content>
            </v-expansion-panel>
        </v-expansion-panels>
    </v-container>
</template>

<script>
import ReportingDealerFacets from "@/modules/Reporting/components/ReportingDealerFacets";
export default {
    name: "FilterMenu",
    components: { ReportingDealerFacets },
    data() {
        return {
            isFixed: false,
            topOffset: null,
        };
    },
    computed: {
        top() {
            return `${this.topOffset}px`;
        },
    },
    mounted() {
        window.addEventListener("scroll", this.handleScroll);
        this.topOffset = this.getMenuOffset() || 63;
    },
    destroyed() {
        window.removeEventListener("scroll", this.handleScroll);
    },
    methods: {
        handleScroll() {
            const offset = 745;

            if (window.scrollY > offset) {
                this.isFixed = true;
            } else if (window.scrollY < offset) {
                this.isFixed = false;
            }
        },
        getMenuOffset() {
            const el = document.querySelector(".v-toolbar__content");
            const offset = el ? el.offsetHeight : 0;

            return offset;
        },
    },
};
</script>

<style lang="scss">
.filter-menu {
    &.stick {
        position: fixed;
        right: 0;
        z-index: 13;
        width: 300px !important;
    }
    &.unstick {
        position: static;
    }
    .primary-bg-color {
        background-color: rgba(176, 217, 255, 0.5);
    }
}
</style>
