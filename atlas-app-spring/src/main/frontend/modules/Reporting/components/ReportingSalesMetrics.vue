<template>
    <reporting-metrics-row
        title="Vehicle Sales"
        :subtitle="subtitle"
        :is-loading="isLoading"
        :affected-by-date-range="true"
    >
        <v-col cols="12" md="4" class="d-flex align-center justify-center">
            <div class="sales-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon
                            color="grey"
                            dark
                            v-bind="attrs"
                            class="sales-tooltip-base sales-stock-type"
                            small
                            v-on="on"
                        >
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span> Stock type of actual sales related to {{ branding }} </span>
                </v-tooltip>
                <apexchart
                    width="100%"
                    height="100%"
                    type="pie"
                    :options="vehicleSaleByStockTypeMetrics.chartOptions"
                    :series="vehicleSaleByStockTypeMetrics.series"
                />
            </div>
        </v-col>
        <v-col cols="12" md="4" class="d-flex align-center justify-center">
            <div class="sales-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon
                            color="grey"
                            dark
                            v-bind="attrs"
                            class="sales-tooltip-base sales-sale-type"
                            small
                            v-on="on"
                        >
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span> Finance type of actual sales related to {{ branding }} </span>
                </v-tooltip>
                <apexchart
                    width="100%"
                    height="100%"
                    type="pie"
                    :options="vehicleSaleBySaleTypeMetrics.chartOptions"
                    :series="vehicleSaleBySaleTypeMetrics.series"
                />
            </div>
        </v-col>
        <v-col cols="12" md="4" class="d-flex align-center justify-center">
            <div class="sales-chart-wrapper">
                <v-tooltip top open-on-focus>
                    <template #activator="{ on, attrs }">
                        <v-icon
                            color="grey"
                            dark
                            v-bind="attrs"
                            class="sales-tooltip-base sales-lenders"
                            small
                            v-on="on"
                        >
                            mdi-information-outline
                        </v-icon>
                    </template>
                    <span> Lenders used for sales related to {{ branding }} </span>
                </v-tooltip>
                <apexchart
                    width="100%"
                    height="100%"
                    type="bar"
                    :options="vehicleSaleByLenderMetrics.chartOptions"
                    :series="vehicleSaleByLenderMetrics.series"
                />
            </div>
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import ReportingMetricsRow from "@/modules/Reporting/components/ReportingMetricsRow";
import { get } from "vuex-pathify";
import _ from "lodash";
import api from "@/util/api";

export default {
    name: "ReportingSalesMetrics",
    components: { ReportingMetricsRow },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            vehicleSaleByStockTypeMetrics: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "pie",
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: (val, opts) => {
                            return `${_.round(val)}% (${opts.w.config.series[opts.seriesIndex]})`;
                        },
                        textAnchor: "middle",
                        distributed: false,
                        offsetX: 0,
                        offsetY: 0,
                        style: {
                            fontSize: "14px",
                            fontFamily: "Helvetica, Arial, sans-serif",
                            fontWeight: "bold",
                            colors: undefined,
                        },
                        background: {
                            enabled: true,
                            foreColor: "#fff",
                            padding: 4,
                            borderRadius: 2,
                            borderWidth: 1,
                            borderColor: "#fff",
                            opacity: 0.9,
                            dropShadow: {
                                enabled: false,
                                top: 1,
                                left: 1,
                                blur: 1,
                                color: "#000",
                                opacity: 0.45,
                            },
                        },
                        dropShadow: {
                            enabled: false,
                            top: 1,
                            left: 1,
                            blur: 1,
                            color: "#000",
                            opacity: 0.45,
                        },
                    },
                    labels: [],
                    legend: {
                        position: "bottom",
                    },
                    noData: {
                        text: "No data available",
                    },
                    title: {
                        text: "Stock Type",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                },
            },
            vehicleSaleBySaleTypeMetrics: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "pie",
                    },
                    dataLabels: {
                        enabled: true,
                        formatter: (val, opts) => {
                            return `${_.round(val)}% (${opts.w.config.series[opts.seriesIndex]})`;
                        },
                        textAnchor: "middle",
                        distributed: false,
                        offsetX: 0,
                        offsetY: 0,
                        style: {
                            fontSize: "14px",
                            fontFamily: "Helvetica, Arial, sans-serif",
                            fontWeight: "bold",
                            colors: undefined,
                        },
                        background: {
                            enabled: true,
                            foreColor: "#fff",
                            padding: 4,
                            borderRadius: 2,
                            borderWidth: 1,
                            borderColor: "#fff",
                            opacity: 0.9,
                            dropShadow: {
                                enabled: false,
                                top: 1,
                                left: 1,
                                blur: 1,
                                color: "#000",
                                opacity: 0.45,
                            },
                        },
                        dropShadow: {
                            enabled: false,
                            top: 1,
                            left: 1,
                            blur: 1,
                            color: "#000",
                            opacity: 0.45,
                        },
                    },
                    labels: [],
                    legend: {
                        position: "bottom",
                    },
                    noData: {
                        text: "No data available",
                    },
                    title: {
                        text: "Finance Type",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                },
            },
            vehicleSaleByLenderMetrics: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: "100%",
                            dataLabels: {
                                enabled: true,
                            },
                        },
                    },
                    stroke: {
                        colors: ["transparent"],
                        width: 2,
                    },
                    labels: [],
                    legend: {
                        show: false,
                    },
                    noData: {
                        text: "No data available",
                    },
                    grid: {
                        show: true,
                        row: {
                            colors: ["#fff", "#f2f2f2"],
                        },
                    },
                    title: {
                        text: "Lenders",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    xaxis: {
                        categories: [],
                        labels: {
                            trim: true,
                            style: {
                                fontSize: "10",
                            },
                        },
                    },
                    yaxis: {
                        title: {
                            text: "Number of Vehicle Sales",
                        },
                        labels: {
                            formatter: (value) => {
                                return _.round(value);
                            },
                        },
                    },
                },
            },
            isLoading: false,
            branding: "Buy@Home",
            subtitle:
                "Sales figures represented in this dashboard are derived by/from completing a sales match between " +
                `rdrs and lead data submitted through the selected program. Sales can be matched based on the individual and or household.`,
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dateRange: get("reportingStore/chartFilters@dateRange"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
    },
    watch: {
        searchResults() {
            this.fetchSalesByStockType();
            this.fetchSalesBySaleType();
            this.fetchSalesByLender();
        },
        dateRange() {
            this.fetchSalesByStockType();
            this.fetchSalesBySaleType();
            this.fetchSalesByLender();
        },
    },
    methods: {
        fetchSalesByStockType() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/vehicle_sales/stocktype`, this.reportingRequest)
                .then((response) => {
                    const results = _.get(response, "data.results", []);

                    this.vehicleSaleByStockTypeMetrics.series.splice(0);
                    this.vehicleSaleByStockTypeMetrics.chartOptions.labels.splice(0);
                    _.map(results, (r) => {
                        this.vehicleSaleByStockTypeMetrics.series.push(r.count);
                        this.vehicleSaleByStockTypeMetrics.chartOptions.labels.push(r.name);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchSalesBySaleType() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/vehicle_sales/sale_type`, this.reportingRequest)
                .then((response) => {
                    const results = _.get(response, "data.results", []);

                    this.vehicleSaleBySaleTypeMetrics.series.splice(0);
                    this.vehicleSaleBySaleTypeMetrics.chartOptions.labels.splice(0);
                    _.map(results, (r) => {
                        this.vehicleSaleBySaleTypeMetrics.series.push(r.count);
                        this.vehicleSaleBySaleTypeMetrics.chartOptions.labels.push(r.name);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        fetchSalesByLender() {
            this.isLoading = true;
            api.post(`/reporting/${this.programId}/vehicle_sales/lender`, this.reportingRequest)
                .then((response) => {
                    const results = _.get(response, "data.results", []);
                    const topTen = _.slice(_.orderBy(results, "count", "desc"), 0, 10);

                    this.vehicleSaleByLenderMetrics.series.splice(0);
                    this.vehicleSaleByLenderMetrics.chartOptions.xaxis.categories.splice(0);
                    _.map(topTen, (r) => {
                        this.vehicleSaleByLenderMetrics.series.push({ name: r.name, data: [r.count] });
                        this.vehicleSaleByLenderMetrics.chartOptions.xaxis.categories.push(r.name);
                    });
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>
<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";

.sales-chart-wrapper {
    min-width: 275px;
    height: 300px;
}

@media #{map-get($display-breakpoints, 'md-and-up')} {
    .sales-chart-wrapper {
        max-width: 250px;
    }
}
.sales-chart-wrapper i.sales-tooltip-base {
    position: absolute;
    z-index: 1;
    top: 4px;
}
i.sales-stock-type {
    left: 87px;
}
i.sales-sale-type {
    left: 103px;
}
i.sales-lenders {
    left: 68px;
}
</style>
