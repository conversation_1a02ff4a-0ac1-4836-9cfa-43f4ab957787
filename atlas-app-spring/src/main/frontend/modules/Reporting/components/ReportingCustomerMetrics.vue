<template>
    <reporting-metrics-row
        title="Customers"
        class="customers-row"
        :is-loading="isLoading"
        :tooltip-content="tooltipContent"
        :subtitle="`Unique Visitors data starts at ${
            dateRangeFilter[0] ? dateRangeFilter[0] : googleAnalyticsStartDate
        }`"
    >
        <v-col cols="12" md="6">
            <apexchart
                width="100%"
                height="250"
                type="bar"
                :options="totalCustomerMetrics.chartOptions"
                :series="totalCustomerMetrics.series"
            />
        </v-col>
        <v-col cols="12" md="6">
            <apexchart
                type="radialBar"
                width="100%"
                height="250"
                :series="conversionRateMetrics.series"
                :options="conversionRateMetrics.chartOptions"
            />
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import api from "@/util/api";
import { get, sync } from "vuex-pathify";
import _ from "lodash";
import ReportingMetricsRow from "@/modules/Reporting/components/ReportingMetricsRow";
import moment from "moment";

export default {
    name: "ReportingCustomerMetrics",
    components: {
        ReportingMetricsRow,
    },
    props: {
        programId: {
            type: String,
            required: true,
        },
        googleAnalyticsStartDate: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            conversionRateMetrics: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "radialBar",
                        offsetY: -10,
                    },
                    plotOptions: {
                        radialBar: {
                            startAngle: -135,
                            endAngle: 135,
                            dataLabels: {
                                name: {
                                    show: true,
                                    fontSize: "16px",
                                    color: undefined,
                                    offsetY: 120,
                                },
                                value: {
                                    offsetY: 0,
                                    fontSize: "22px",
                                    color: undefined,
                                    formatter: (val) => {
                                        return val + "%";
                                    },
                                },
                            },
                        },
                    },
                    fill: {
                        type: "gradient",
                        gradient: {
                            shade: "dark",
                            shadeIntensity: 0.15,
                            inverseColors: false,
                            opacityFrom: 1,
                            opacityTo: 1,
                            stops: [0, 50, 65, 91],
                        },
                    },
                    stroke: {
                        dashArray: 4,
                    },
                    labels: ["Sign Up Conversion Rate"],
                    tooltip: {
                        enabled: true,
                        custom: function ({ series, seriesIndex, dataPointIndex, w }) {
                            return (
                                '<div class="arrow_box pa-2">' +
                                "<span>The conversion rate is calculated by " +
                                "<br />dividing the total number of Sign-Ups" +
                                "<br />by the total number of Unique Visitors</span> </div>"
                            );
                        },
                        theme: "dark",
                    },
                },
            },
            totalCustomerMetrics: {
                series: [
                    { name: "Unique Visitors", data: [0] },
                    { name: "Sign Ups", data: [0] },
                ],
                chartOptions: {
                    chart: {
                        width: 500,
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            borderRadius: 4,
                            columnWidth: "100%",
                        },
                    },
                    xaxis: {
                        categories: ["Unique Visitors", "Sign Ups"],
                        labels: {
                            show: false,
                        },
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                        enabled: true,
                        fixed: {
                            enabled: true,
                            position: "topRight",
                        },
                        theme: "dark",
                    },
                    labels: [],
                    responsive: [
                        {
                            breakpoint: 480,
                            options: {
                                legend: {
                                    position: "bottom",
                                },
                            },
                        },
                    ],
                    noData: {
                        text: "No data available",
                    },
                    title: {
                        text: "",
                    },
                    stroke: {
                        colors: ["transparent"],
                        width: 3,
                    },
                },
            },
            isLoading: false,
            tooltipContent:
                "The conversion rate is calculated by dividing the total number of Sign Ups by the total Unique Visitors",
        };
    },

    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        dateRangeFilter: sync("reportingStore/chartFilters@dateRange"),
        dateRange() {
            const defaultStartDate = "08/25/2021";
            const currentDate = this.dateRangeFilter ? this.dateRangeFilter[1] : new Date();
            const formattedDate = moment(currentDate).format("MM/DD/YYYY");

            return [this.dateRangeFilter ? this.dateRangeFilter[0] : defaultStartDate, formattedDate];
        },
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
    },

    watch: {
        searchResults() {
            this.getCustomerConversion();
        },
        dateRange() {
            this.getCustomerConversion();
        },
    },

    methods: {
        getCustomerConversion() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/customer_conversion`, this.reportingRequest)
                .then((response) => {
                    const totalUniquePageviews = _.get(response, "data.uniquePageviews", 0);
                    const totalSignUps = _.get(response, "data.totalSignups", 0);
                    const conversionRate = _.get(response, "data.conversionRate", 0);

                    this.totalCustomerMetrics.series.splice(0);
                    this.totalCustomerMetrics.series.push(
                        { name: "Unique Visitors", data: [totalUniquePageviews] },
                        { name: "Sign Ups", data: [totalSignUps] }
                    );

                    this.conversionRateMetrics.series.splice(0);
                    this.conversionRateMetrics.series.push(conversionRate.toFixed(1));
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        setDateRangeFilter(dateRange) {
            this.dateRange = dateRange;
            this.getCustomerConversion();
        },
    },
};
</script>
