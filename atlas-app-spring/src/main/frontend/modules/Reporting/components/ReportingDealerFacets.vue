<template>
    <div>
        <v-divider />
        <date-range-filter />

        <v-divider />

        <facet-list-group facet-group-label="Dealers" facet-icon="mdi-car-hatchback">
            <facet-checkbox facet-label="Dealer" store="dealerSearch" facet-name="dealers" filter-name="ids" />
        </facet-list-group>

        <v-divider />

        <facet-list-group facet-group-label="Location" facet-icon="mdi-map-marker">
            <facet-checkbox facet-label="DMA" store="dealerSearch" facet-name="dmas" filter-name="dmaCodes" />
            <facet-checkbox facet-label="States" store="dealerSearch" facet-name="states" filter-name="states" />
        </facet-list-group>

        <v-divider />
    </div>
</template>

<script>
import FacetListGroup from "Components/Facets/FacetListGroup";
import FacetCheckbox from "Components/Facets/FacetCheckbox";
import DateRangeFilter from "@/modules/Reporting/components/DateRangeFilter";
export default {
    name: "ReportingDealerFacets",
    components: { DateRangeFilter, FacetCheckbox, FacetListGroup },
};
</script>
