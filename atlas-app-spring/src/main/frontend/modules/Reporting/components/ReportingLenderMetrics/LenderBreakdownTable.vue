<template>
    <div>
        <div class="font-weight-bold mb-2">Lender Breakdown</div>
        <v-data-table
            id="lender-table"
            :headers="headers"
            :items="lenders"
            :items-per-page="15"
            hide-default-footer
            disable-sort
        >
            <template #[`item.percentFinanced`]="{ item }"> {{ item.percentFinanced }}% </template>
            <template #[`item.percentLeased`]="{ item }"> {{ getPercentageLeased(item.percentFinanced) }} % </template>
            <template #[`item.averageRequestedAmountFinanced`]="{ item }">
                {{ item.averageRequestedAmountFinanced | numeral("$0,0.00") }}
            </template>
        </v-data-table>
    </div>
</template>
<script>
import _ from "lodash";

export default {
    name: "LenderBreakdownTable",
    props: {
        lenderList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            headers: [
                {
                    text: "Lender",
                    value: "lenderName",
                    sortable: false,
                },
                {
                    text: "# Apps",
                    value: "applications",
                    sortable: false,
                },
                {
                    text: "# Approvals",
                    value: "approvals",
                    sortable: false,
                },
                {
                    text: "% Financed",
                    value: "percentFinanced",
                    sortable: false,
                },
                {
                    text: "% Leased",
                    value: "percentLeased",
                    sortable: false,
                },
                {
                    text: "Average Amount Requested",
                    value: "averageRequestedAmountFinanced",
                    sortable: false,
                },
            ],
            lenders: [],
        };
    },
    watch: {
        lenderList(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.getLenders();
            }
        },
    },
    created() {
        this.getLenders();
    },
    methods: {
        getLenders() {
            this.lenders.splice(0);

            _.map(this.lenderList, (lender) => {
                this.lenders.push(lender);
            });
        },
        getPercentageLeased(percentageFinanced) {
            let percentageLeased = 100 - percentageFinanced;

            return _.round(percentageLeased, 2);
        },
    },
};
</script>
