<template>
    <div class="lender-chart-wrapper">
        <span class="lease-tooltip">
            <v-tooltip top open-on-focus>
                <template #activator="{ on, attrs }">
                    <v-icon color="grey" dark v-bind="attrs" small v-on="on"> mdi-information-outline </v-icon>
                </template>
                <span>
                    NMAC penetration rate is being provided as directional in nature due to the frequency in which we
                    receive captive flags; monthly batch files.
                </span>
            </v-tooltip>
        </span>
        <apexchart
            ref="leaseAndFinanceOverTime"
            width="100%"
            height="100%"
            type="line"
            :series="leaseAndFinanceOverTime.series"
            :options="leaseAndFinanceOverTime.chartOptions"
        />
    </div>
</template>
<script>
export default {
    name: "FinanceLeaseChart",
    props: {
        nmacHistogramList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            leaseAndFinanceOverTime: {
                series: [
                    {
                        name: "Finance Apps",
                        data: [],
                    },
                    {
                        name: "Lease Apps",
                        data: [],
                    },
                ],
                chartOptions: {
                    chart: {
                        type: "line",
                        dropShadow: {
                            enabled: true,
                            color: "#000",
                            top: 18,
                            left: 7,
                            blur: 10,
                            opacity: 0.2,
                        },
                        stacked: false,
                        zoom: {
                            type: "x",
                            enabled: true,
                            autoScaleYaxis: true,
                        },
                        toolbar: {
                            autoSelected: "zoom",
                            tools: {
                                download: false,
                                selection: true,
                                zoom: true,
                                zoomin: true,
                                zoomout: true,
                                pan: true,
                                reset: false,
                            },
                        },
                        events: {
                            beforeZoom: (chartContext, { xaxis }) => {
                                let series = chartContext.w.globals.seriesX;
                                let seriesRange = series[0][series[0].length - 1] - series[0][0];
                                let zoomRange = xaxis.max - xaxis.min;
                                if (zoomRange > seriesRange)
                                    return {
                                        xaxis: {
                                            min: series[0][0],
                                            max: series[0][series[0].length - 1],
                                        },
                                    };
                                else {
                                    return {
                                        xaxis: {
                                            min: xaxis.min,
                                            max: xaxis.max,
                                        },
                                    };
                                }
                            },
                        },
                    },
                    dataLabels: {
                        enabled: true,
                    },
                    stroke: {
                        curve: "smooth",
                        width: [4, 4],
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: "20%",
                        },
                    },
                    xaxis: {
                        type: "datetime",
                    },
                    yaxis: {
                        labels: {
                            formatter: function (val) {
                                return val.toFixed(0);
                            },
                        },
                        title: {
                            text: "Number of Finance Applications",
                        },
                    },
                    grid: {
                        borderColor: "#e7e7e7",
                        row: {
                            colors: ["#f3f3f3", "transparent"],
                            opacity: 0.5,
                        },
                    },
                    markers: {
                        size: 1,
                    },
                    legend: {
                        position: "top",
                        horizontalAlign: "right",
                        floating: true,
                        offsetY: -5,
                        offsetX: -5,
                    },
                    title: {
                        text: "NMAC Finance App vs. NMAC Lease Apps",
                        align: "left",
                    },
                },
            },
        };
    },
    watch: {
        nmacHistogramList(newValue, oldValue) {
            if (newValue !== oldValue) {
                this.setLeaseAndFinanceOvertime();
            }
        },
    },
    mounted() {
        this.setLeaseAndFinanceOvertime();
    },
    methods: {
        setLeaseAndFinanceOvertime() {
            const nmacHistogram = this.nmacHistogramList;
            this.leaseAndFinanceOverTime.series[0].data.splice(0);
            this.leaseAndFinanceOverTime.series[1].data.splice(0);

            let financeSeriesValues = this.leaseAndFinanceOverTime.series[0].data;
            let leaseSeriesValues = this.leaseAndFinanceOverTime.series[1].data;

            nmacHistogram.forEach((element) => {
                let formattedDate = new Date(element.date);
                financeSeriesValues.push({ x: formattedDate, y: element.financedCount });
                leaseSeriesValues.push({ x: formattedDate, y: element.leasedCount });
            });

            this.$refs.leaseAndFinanceOverTime.updateSeries(
                [
                    {
                        data: financeSeriesValues,
                    },
                    {
                        data: leaseSeriesValues,
                    },
                ],
                false,
                true
            );
        },
    },
};
</script>
