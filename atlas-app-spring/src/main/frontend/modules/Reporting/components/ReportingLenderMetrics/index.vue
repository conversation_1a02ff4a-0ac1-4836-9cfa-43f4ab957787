<template>
    <reporting-metrics-row
        title="Lenders"
        :is-loading="isLoading"
        :affected-by-date-range="true"
        :tooltip-content="headerTooltipContent"
    >
        <v-col cols="12" md="6">
            <nmac-overtime-chart v-if="nmacHistogram" :nmac-histogram-list="nmacHistogram" />
        </v-col>

        <v-col cols="12" md="6" class="mb-2">
            <finance-lease-chart v-if="nmacHistogram" :nmac-histogram-list="nmacHistogram" />
        </v-col>

        <v-col cols="12" class="mb-2">
            <lender-breakdown-table v-if="lendersReportList" :lender-list="lendersReportList" />
        </v-col>

        <v-col cols="12">
            <lender-chart v-if="lendersReportList" :lender-list="lendersReportList" />
        </v-col>

        <v-col v-if="false" cols="12">
            <credit-range-chart v-if="lendersReportList" :lender-list="lendersReportList" />
        </v-col>

        <v-col v-if="false" cols="12">
            <rate-chart v-if="lendersReportList" :lender-list="lendersReportList" />
        </v-col>

        <v-col v-if="false" cols="12">
            <term-chart v-if="lendersReportList" :lender-list="lendersReportList" />
        </v-col>
    </reporting-metrics-row>
</template>

<script>
import ReportingMetricsRow from "@/modules/Reporting/components/ReportingMetricsRow";
import api from "@/util/api";
import { get } from "vuex-pathify";
import _ from "lodash";
import CreditRangeChart from "@/modules/Reporting/components/ReportingLenderMetrics/CreditRangeChart";
import RateChart from "@/modules/Reporting/components/ReportingLenderMetrics/RateChart";
import TermChart from "@/modules/Reporting/components/ReportingLenderMetrics/TermChart";
import LenderBreakdownTable from "@/modules/Reporting/components/ReportingLenderMetrics/LenderBreakdownTable";
import LenderChart from "@/modules/Reporting/components/ReportingLenderMetrics/LenderChart";
import NmacOvertimeChart from "@/modules/Reporting/components/ReportingLenderMetrics/NmacOvertimeChart";
import FinanceLeaseChart from "@/modules/Reporting/components/ReportingLenderMetrics/FinanceLeaseChart";

export default {
    name: "ReportingLenderMetrics",
    components: {
        FinanceLeaseChart,
        NmacOvertimeChart,
        LenderChart,
        LenderBreakdownTable,
        TermChart,
        RateChart,
        CreditRangeChart,
        ReportingMetricsRow,
    },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            isLoading: false,
            lendersReportList: null,
            nmacHistogram: null,
            headerTooltipContent: "Lender information associated with proposed financing, not actual sales",
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
        dateRange: get("reportingStore/chartFilters@dateRange"),
        dealerIds() {
            return _.map(this.searchResults, "id");
        },
        reportingRequest() {
            return {
                dealerIds: this.dealerIds,
                dateRange: this.dateRange,
            };
        },
    },
    watch: {
        searchResults() {
            this.fetchLenderMetrics();
        },
        dateRange() {
            this.fetchLenderMetrics();
        },
    },
    methods: {
        fetchLenderMetrics() {
            this.isLoading = true;

            api.post(`/reporting/${this.programId}/lender-breakdown`, this.reportingRequest)
                .then((response) => {
                    this.lendersReportList = _.get(response, "data.lenderReportList", []);
                    this.nmacHistogram = _.get(response, "data.nmacHistogram", []);
                })
                .catch((err) => {
                    console.trace("Failed to load lender metrics: ", err);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>
<style lang="scss">
#lender-table {
    border: 1px solid #eeeeee;
}
.lender-chart-wrapper {
    height: 400px;
}
.apexcharts-toolbar {
    z-index: 0 !important;
}
.lender-chart-wrapper .v-icon.v-icon {
    position: absolute;
    left: 327px;
    top: 4px;
    z-index: 1;
}
.lender-chart-wrapper .lease-tooltip .v-icon.v-icon {
    position: absolute;
    left: 303px;
    top: 4px;
    z-index: 1;
}
</style>
