<template>
    <div class="lender-chart-wrapper">
        <apexchart
            width="100%"
            height="100%"
            type="bar"
            :series="creditScoreRanges.series"
            :options="creditScoreRanges.chartOptions"
        />
    </div>
</template>
<script>
import _ from "lodash";

export default {
    name: "CreditRangeChart",
    props: {
        lenderList: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            creditScoreRanges: {
                series: [],
                chartOptions: {
                    chart: {
                        type: "bar",
                        toolbar: {
                            show: false,
                        },
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: "100%",
                            dataLabels: {
                                enabled: true,
                            },
                        },
                    },
                    stroke: {
                        colors: ["transparent"],
                        width: 3,
                    },
                    labels: [],
                    legend: {
                        show: false,
                    },
                    noData: {
                        text: "No data available",
                    },
                    grid: {
                        show: true,
                        row: {
                            colors: ["#fff", "#f2f2f2"],
                        },
                    },
                    title: {
                        text: "Number of Finance Applications grouped by Credit Range",
                    },
                    tooltip: {
                        x: {
                            show: false,
                        },
                    },
                    xaxis: {
                        categories: ["740+", "739-700", "699-660", "659-620", "619-600", "600 <"],
                        labels: {
                            trim: true,
                            style: {
                                fontSize: "12",
                            },
                        },
                        title: {
                            text: "Credit Score Ranges",
                        },
                    },
                    yaxis: {
                        title: {
                            text: "Number of Apps",
                        },
                    },
                },
            },
            excellentCreditRange: [],
            greatCreditRange: [],
            goodCreditRange: [],
            fairCreditRange: [],
            poorCreditRange: [],
            veryPoorCreditRange: [],
        };
    },
    created() {
        this.setCreditRanges();
    },
    methods: {
        setCreditRanges() {
            const lenders = this.lenderList;

            this.creditScoreRanges.series.splice(0);

            _.forEach(lenders, (lender) => {
                _.forEach(lender.creditScoreList, (creditScore) => {
                    this.setCreditRangeBuckets(creditScore);
                });
            });

            this.creditScoreRanges.series.push({ name: "740 +", data: [this.excellentCreditRange.length] });
            this.creditScoreRanges.series.push({ name: "739-700", data: [this.greatCreditRange.length] });
            this.creditScoreRanges.series.push({ name: "660-699", data: [this.goodCreditRange.length] });
            this.creditScoreRanges.series.push({ name: "620-659", data: [this.fairCreditRange.length] });
            this.creditScoreRanges.series.push({ name: "600-619", data: [this.poorCreditRange.length] });
            this.creditScoreRanges.series.push({ name: "< 600", data: [this.veryPoorCreditRange.length] });
        },
        setCreditRangeBuckets(creditScore) {
            if (creditScore >= 740) {
                this.excellentCreditRange.push(creditScore);
            }
            if (creditScore >= 700 && creditScore <= 739) {
                this.greatCreditRange.push(creditScore);
            }
            if (creditScore >= 660 && creditScore <= 699) {
                this.goodCreditRange.push(creditScore);
            }
            if (creditScore >= 620 && creditScore <= 659) {
                this.fairCreditRange.push(creditScore);
            }
            if (creditScore >= 600 && creditScore <= 619) {
                this.poorCreditRange.push(creditScore);
            }
            if (creditScore < 600) {
                this.veryPoorCreditRange.push(creditScore);
            }
        },
    },
};
</script>
