import { make } from "vuex-pathify";
import loader from "@/util/loader";
import searchUtils, { QueryModeEnum } from "@/util/searchUtils";

const uriRoot = "/reporting";

const initialState = {
    ...searchUtils.state(),
    searchUri: null,
    initialLoad: true,
    queryMode: QueryModeEnum.SCROLL,
    pushHistoryEnabled: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "name,desc",
        page: 1,
    }),
    facets: {},
    filterNames: null,
    filters: searchUtils.parseFiltersFromUrl(),
    searchLoader: {
        loader: loader.defaultState(),
        data: [],
    },
    displayFields: ["name"],
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0,
    },
    /**
     * pills schema
     *      {
                "name": {
                    label: 'Name',
                    enabled: false,
                },
                "warrantyStatuses": {
                    label: 'Warranty',
                    enabled: true
                },
                "topDmas": {
                    type: 'range'
                }
            }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     *     type: the type of filter i.e. 'range' filter
     * }
     */
    pills: {
        name: {
            enabled: false,
        },
        dmaCodes: {
            label: "DMAs",
            facet: "dmas",
        },
    },
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations(),
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "Program Dealer Search"),
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters(),
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
