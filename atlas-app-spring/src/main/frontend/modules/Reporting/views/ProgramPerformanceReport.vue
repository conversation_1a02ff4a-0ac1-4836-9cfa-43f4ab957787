<template>
    <div class="reporting-performance-report pb-0">
        <v-container fluid class="main search pb-0">
            <v-row class="search-list-row">
                <v-col cols="12">
                    <reporting-search-form slot="searchForm" :program-id="programId" />
                </v-col>
            </v-row>
        </v-container>

        <reporting-key-metrics-count
            v-if="isMetricEnabled('reportingKeyMetricsCount')"
            :program-id="programId"
            class="report-row"
        />

        <filter-menu />

        <reporting-customer-metrics
            v-if="isMetricEnabled('reportingCustomerMetrics')"
            :program-id="programId"
            :google-analytics-start-date="googleAnalyticsStartDate"
            class="report-row"
        />

        <reporting-module-usage
            v-if="isMetricEnabled('reportingModuleUsage')"
            :program-id="programId"
            class="report-row"
        />

        <reporting-deal-metrics
            v-if="isMetricEnabled('reportingDealMetrics')"
            :program-id="programId"
            class="report-row"
        />

        <reporting-lead-metrics
            v-if="isMetricEnabled('reportingLeadMetrics')"
            :program-id="programId"
            class="report-row"
        />

        <reporting-sales-metrics
            v-if="isMetricEnabled('reportingSalesMetrics')"
            :program-id="programId"
            class="report-row"
        />

        <reporting-trade-metrics
            v-if="isMetricEnabled('reportingTradeMetrics')"
            :program-id="programId"
            class="report-row"
        />

        <reporting-lender-metrics
            v-if="isMetricEnabled('reportingLenderMetrics')"
            :program-id="programId"
            class="report-row"
        />

        <model-view-metrics v-if="isMetricEnabled('modelViewMetrics')" :program-id="programId" class="report-row" />
    </div>
</template>

<script>
import ReportingSearchForm from "../components/ReportingSearchForm";
import { call, get } from "vuex-pathify";
import ReportingCustomerMetrics from "@/modules/Reporting/components/ReportingCustomerMetrics";
import ReportingLeadMetrics from "@/modules/Reporting/components/ReportingLeadMetrics";
import ReportingSalesMetrics from "@/modules/Reporting/components/ReportingSalesMetrics";
import ReportingTradeMetrics from "@/modules/Reporting/components/ReportingTradeMetrics";
import ReportingDealMetrics from "@/modules/Reporting/components/ReportingDealMetrics";
import ReportingKeyMetricsCount from "@/modules/Reporting/components/ReportingKeyMetricsCount";
import ReportingLenderMetrics from "@/modules/Reporting/components/ReportingLenderMetrics/index";
import ReportingModuleUsage from "@/modules/Reporting/components/ReportingModuleUsage";
import ModelViewMetrics from "@/modules/Reporting/components/ModelViewMetrics";
import FilterMenu from "@/modules/Reporting/components/FilterMenu";

export default {
    name: "ProgramPerformanceReport",
    components: {
        FilterMenu,
        ReportingLenderMetrics,
        ReportingKeyMetricsCount,
        ReportingDealMetrics,
        ReportingSalesMetrics,
        ReportingLeadMetrics,
        ReportingCustomerMetrics,
        ReportingSearchForm,
        ReportingTradeMetrics,
        ModelViewMetrics,
        ReportingModuleUsage,
    },
    props: {
        programId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            // Release date of the Upgrade Program: Google Tag Manager, Version 18.
            // The Dealer ID variable was created in this release. This is important because
            // we filter data fetching by Dealer ID for reporting purposes.
            googleAnalyticsStartDate: new Date(2021, 7, 25).toLocaleDateString(),
            isFixed: false,
            enabledMetrics: [
                "reportingKeyMetricsCount",
                "reportingDealMetrics",
                //"reportingSalesMetrics",
                "reportingLeadMetrics",
                "reportingCustomerMetrics",
                "reportingTradeMetrics",
                "reportingModuleUsage",
                "reportingLenderMetrics",
                "modelViewMetrics",
            ],
            topOffset: 80,
        };
    },
    computed: {
        searchResults: get("dealerSearch/searchLoader@data"),
    },

    created() {
        this.setSearchUri(`/reporting/${this.programId}/dealers`);
    },

    mounted() {
        this.doPageLoad();
    },

    methods: {
        setSearchUri: call("dealerSearch/setSearchUri"),
        doPageLoad: call("dealerSearch/doPageLoad"),
        isMetricEnabled(name) {
            return this.enabledMetrics.indexOf(name) != -1;
        },
    },
};
</script>

<style lang="scss" scoped>
.reporting-performance-report {
    background: $grey-200;

    .report-row {
        padding-top: 0;
        padding-bottom: 0;
    }
    .report-row:last-child {
        padding-bottom: 8px;
    }

    .filter-title {
        font-size: 20px;
        font-weight: 500;
    }
    .mdi-filter {
        font-size: 20px;
    }

    .primary-bg-color {
        background-color: rgba(176, 217, 255, 0.5);
    }

    .v-expansion-panel-content__wrap {
        padding: 0 !important;
    }
}
</style>
