<template>
    <div>
        <v-card max-width="850">
            <v-container>
                <v-row>
                    <v-col cols="12">
                        <v-card-title class="text-capitalize">
                            <div class="mr-auto">
                                {{ user.firstName }}
                                {{ user.lastName }}
                            </div>
                            <div>
                                <div>
                                    <v-btn class="ss-menu-btn" small outlined @click="openChangePasswordDialog">
                                        Change Password
                                    </v-btn>
                                </div>
                                <div>
                                    <v-btn class="ss-menu-btn" small outlined @click="openEditUser"> Edit User </v-btn>
                                </div>
                            </div>
                        </v-card-title>
                        <v-card-subtitle class="pb-0">
                            <div class="d-flex flex-column">
                                <div>
                                    Customer Since:
                                    {{ user.createdDate | formatEpochDate }}
                                </div>
                            </div>
                        </v-card-subtitle>
                        <v-list>
                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon color="primary"> mdi-account </v-icon>
                                </v-list-item-icon>
                                <v-list-item-content>
                                    <v-list-item-title>
                                        {{ user.jobTitle }}
                                    </v-list-item-title>
                                </v-list-item-content>
                            </v-list-item>

                            <v-divider inset />

                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon color="primary"> mdi-phone </v-icon>
                                </v-list-item-icon>
                                <v-list-item-content>
                                    <v-list-item-title>
                                        <a :href="`tel: ${user.phoneNumber}`">
                                            {{ user.phoneNumber | phoneFormatter }}
                                        </a>
                                        <span v-if="user.phoneNumberExt" class="phoneExt">
                                            Ext. {{ user.phoneNumberExt }}</span
                                        >
                                    </v-list-item-title>
                                    <v-list-item-subtitle v-if="user.phoneNumberInfo">
                                        {{ user.phoneNumberInfo.carrier.type }}
                                    </v-list-item-subtitle>
                                </v-list-item-content>
                            </v-list-item>

                            <v-divider inset />

                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon color="primary"> mdi-email </v-icon>
                                </v-list-item-icon>
                                <v-list-item-content>
                                    <v-list-item-title>
                                        <a :href="`mailto: ${user.email}`">
                                            {{ user.email }}
                                        </a>
                                    </v-list-item-title>
                                </v-list-item-content>
                            </v-list-item>

                            <v-divider inset />

                            <v-list-item>
                                <v-list-item-icon>
                                    <v-icon color="primary"> mdi-cellphone-message </v-icon>
                                </v-list-item-icon>

                                <v-list-item-content>
                                    <v-list-item-title>
                                        <boolean-indicator small :value="smsAllowed" />
                                        SMS Enabled
                                    </v-list-item-title>
                                </v-list-item-content>
                            </v-list-item>
                        </v-list>
                    </v-col>
                </v-row>
            </v-container>
        </v-card>
        <v-dialog v-if="dialog" v-model="dialog" persistent width="400">
            <change-password-dialog />
        </v-dialog>
    </div>
</template>

<script>
import _ from "lodash";
import { get, sync } from "vuex-pathify";
import BooleanIndicator from "Components/BooleanIndicator";
import ChangePasswordDialog from "../components/ChangPasswordDialog";
import userStore from "@/modules/UserSelfService/store/userStore";

export default {
    name: "UserDetailsCard",
    components: { BooleanIndicator, ChangePasswordDialog },
    computed: {
        dialog: sync("userStore/passwordDialog"),
        editingUser: sync("userStore/editingUser"),
        user: get("userStore/userModel@data"),
        availableJobTitlesLoader: get("userStore/availableJobTitlesModel@loader"),
        availableJobTitles: get("userStore/availableJobTitlesModel@data"),
        smsAllowed() {
            return _.get(this.user, "internalSms", false) || false;
        },
        fullAddress() {
            return `${this.user.address}, ${this.user.city}, ${this.user.stateCode} ${this.user.zipCode}`;
        },
    },
    methods: {
        openChangePasswordDialog() {
            this.dialog = !this.dialog;
        },
        openEditUser() {
            this.editingUser = !this.editingUser;
        },
    },
};
</script>
<style lang="scss" scoped>
.value {
    @media only screen and (min-width: 768px) {
        max-width: 315px;
    }
}
.ss-menu-btn {
    width: 158px;
}
.phoneExt {
    padding-left: 5px;
    font-size: small;
}
</style>
