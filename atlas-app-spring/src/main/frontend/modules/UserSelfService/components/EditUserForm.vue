<template>
    <div>
        <v-card max-width="850" :loading="loading">
            <v-container fluid>
                <validation-observer ref="observer" v-slot="{ invalid }">
                    <v-form @submit.prevent="submit">
                        <v-row>
                            <v-col cols="12">
                                <v-card-title class="text-capitalize">
                                    Edit {{ user.firstName }} {{ user.lastName }}
                                </v-card-title>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="6">
                                <validation-provider v-slot="{ errors }" name="First Name" rules="required">
                                    <v-text-field
                                        v-model="user.firstName"
                                        label="First Name"
                                        outlined
                                        dense
                                        required
                                        :error-messages="errors"
                                    />
                                </validation-provider>
                            </v-col>
                            <v-col cols="6">
                                <validation-provider v-slot="{ errors }" name="Last Name" rules="required">
                                    <v-text-field
                                        v-model="user.lastName"
                                        label="Last Name"
                                        outlined
                                        dense
                                        required
                                        :error-messages="errors"
                                    />
                                </validation-provider>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="6">
                                <validation-provider v-slot="{ errors }" name="Email Address" rules="required">
                                    <v-text-field
                                        v-model="user.email"
                                        label="Email Address"
                                        outlined
                                        dense
                                        required
                                        :error-messages="errors"
                                    />
                                </validation-provider>
                            </v-col>
                            <v-col cols="4">
                                <validation-provider v-slot="{ errors }" name="Phone Number" rules="required">
                                    <v-text-field
                                        v-model="user.phoneNumber"
                                        label="Phone Number"
                                        outlined
                                        dense
                                        required
                                        :error-messages="errors"
                                    />
                                </validation-provider>
                            </v-col>
                            <v-col cols="2">
                                <validation-provider v-slot="{ errors }" name="Phone Extension" rules="">
                                    <v-text-field
                                        v-model="user.phoneNumberExt"
                                        label="Phone Ext. "
                                        outlined
                                        dense
                                        :error-messages="errors"
                                    />
                                </validation-provider>
                            </v-col>
                        </v-row>
                        <v-row v-if="availableJobTitlesLoader.isComplete">
                            <v-col cols="6">
                                <v-select
                                    v-model="user.jobTitleId"
                                    :items="availableJobTitles"
                                    label="Job Title"
                                    outlined
                                    dense
                                    item-text="title"
                                    item-value="id"
                                />
                            </v-col>
                            <v-col cols="2" class="toggleFix">
                                <v-switch v-model="user.internalSms" color="success" :label="`SMS Enabled`"></v-switch>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12">
                                <v-card-actions v-if="!complete" class="pt-0">
                                    <v-btn :disabled="invalid || loading" class="px-10" type="submit"> Save </v-btn>
                                    <v-btn class="px-10" text @click="closeEditUser"> Cancel </v-btn>
                                </v-card-actions>
                            </v-col>
                        </v-row>
                    </v-form>
                </validation-observer>
            </v-container>
        </v-card>
    </div>
</template>

<script>
import { get, sync, call } from "vuex-pathify";
import api from "Util/api";

export default {
    name: "EditUserForm",
    data: () => ({
        complete: false,
        loading: false,
    }),
    computed: {
        dialog: sync("userStore/passwordDialog"),
        editingUser: sync("userStore/editingUser"),
        user: get("userStore/userModel@data"),
        availableJobTitlesLoader: get("userStore/availableJobTitlesModel@loader"),
        availableJobTitles: get("userStore/availableJobTitlesModel@data"),
    },
    methods: {
        setUserModel: call("userStore/setUserModel"),
        openChangePasswordDialog() {
            this.dialog = !this.dialog;
        },
        closeEditUser() {
            this.editingUser = false;
        },
        submit() {
            if (this.$refs.observer.validate()) {
                this.loading = true;
                api.patch("/account/profile/update-logged-in-user", {
                    firstName: this.user.firstName,
                    lastName: this.user.lastName,
                    phoneNumber: this.user.phoneNumber,
                    phoneNumberExt: this.user.phoneNumberExt,
                    email: this.user.email,
                    jobTitleId: this.user.jobTitleId,
                    internalSms: this.user.internalSms,
                })
                    .then((response) => {
                        this.setUserModel({ response });
                        this.loading = false;
                        this.complete = true;
                        this.closeEditUser();
                    })
                    .catch(() => {
                        this.loading = false;
                    });
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.value {
    @media only screen and (min-width: 768px) {
        max-width: 315px;
    }
}
.toggleFix {
    margin-top: -20px;
}
</style>
