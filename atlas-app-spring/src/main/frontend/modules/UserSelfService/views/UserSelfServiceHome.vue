<template>
    <v-container class="user-ss-page-container" fluid>
        <v-row>
            <v-col md="12" cols="12" class="d-flex flex-column pr-md-0" order-md="1" order="2">
                <user-details-card v-if="!editingUser && userLoader.isComplete" />
                <edit-user-form v-else-if="userLoader.isComplete" />
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import UserDetailsCard from "../components/UserDetailsCard";
import EditUserForm from "../components/EditUserForm";
import { call, get, sync } from "vuex-pathify";

export default {
    name: "UserSelfServiceHome",
    components: {
        UserDetailsCard,
        EditUserForm,
    },
    computed: {
        userLoader: get("userStore/userModel@loader"),
        editingUser: sync("userStore/editingUser"),
    },
    mounted() {
        this.fetchUser();
        this.fetchJobTitles();
    },
    methods: {
        fetchUser: call("userStore/fetchUser"),
        fetchJobTitles: call("userStore/fetchFormData"),
    },
};
</script>
<style lang="scss">
.user-ss-page-container {
    background-color: $gray-200;
    height: 100%;
}
</style>
