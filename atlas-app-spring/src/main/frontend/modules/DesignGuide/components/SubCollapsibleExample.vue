<template>
    <v-expansion-panels accordion multiple>
        <SubCollapsible :video="video">
            <template #title> {{ title }} </template>
            <template #description>
                {{ description }}
            </template>
        </SubCollapsible>
    </v-expansion-panels>
</template>

<script>
import { defineComponent } from "vue";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible";
export default defineComponent({
    name: "SubCollapsibleExample",
    components: {
        SubCollapsible,
    },
    props: {
        title: {
            type: String,
            default: "SubTitle",
        },
        description: {
            type: String,
            default: "Description",
        },
        video: {
            type: String,
            default: "https://www.youtube.com/",
        },
    },
});
</script>
