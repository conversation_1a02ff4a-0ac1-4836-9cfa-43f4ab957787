/**
 * Following the Layout Config components
 * key: Key can be any (just to uniquely identify component)
 * name: Name is a friendly name given to the component
 * component: Actually name of the component
 */

const LAYOUT_CONFIG = Object.freeze([
    {
        key: "breadcrumbBar",
        name: "Breadcrumb bar",
        component: "BreadcrumbBar",
    },
    {
        key: "updateBtn",
        name: "Update Button",
        component: "UpdateBtn",
    },
    {
        key: "CollapsibleExample",
        name: "Collapsible",
        component: "CollapsibleExample",
        title: "SubTitle",
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
            incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
            exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.`,
        video: "https://www.youtube.com/",
    },
    {
        key: "subCollapsibleExample",
        name: "SubCollapsible",
        component: "SubCollapsibleExample",
        title: "SubTitle",
        description: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
            incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
            exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.`,
    },
]);

/**
 * Following the form builder object
 * collapsible: If the form is collapsible, default true for all
 * upperLevelConfigs: Upper level configs for the form (Component level)
 * key: Key can be any (use same key sent from the backend, but not to bother here)
 * name: Name is a friendly name given to the input
 * type: Should be mapped to the component
 * hint: Placeholder
 * label: Label
 * default: default state
 * allowedValues: All the possible values an input can take (usually used in dropdowns)
 * function: function can be any function name (use same function name sent from the backend, but not to bother here)
 * ... few other self explanatory params
 */
const FORM_INPUT_BUILDER_CONFIG = Object.freeze([
    {
        key: "toggleKey",
        type: "TOGGLE",
        name: "Toogle Button",
        default: "false",
        label: "Toggle",
    },
    {
        key: "dropDownKey",
        type: "DROPDOWN",
        name: "Dropdown",
        allowedValues: [
            {
                value: "Text 1",
                text: "Text 1",
                description: null,
            },
            {
                value: "Text 2",
                text: "Text 2",
                description: null,
            },
            {
                value: "Text 3",
                text: "Text 3",
                description: null,
            },
        ],
        label: "Select",
        function: "getSomething()",
    },
    {
        key: "inputWithMultiplier",
        type: "INPUT_WITH_MULTIPLIER",
        name: "Input with multiplier",
        hint: ".0004",
        label: "Multiplier (x 2400)",
        originType: "INPUT_DECIMAL",
        multiplier: "2400",
        resultLabel: "Result Label",
    },
    {
        key: "grouped_toogle_key",
        type: "GROUPED_TOGGLE",
        name: "Grouped Toggle",
        default: "radioLabel1", // Value of the group radio input
        groups: [
            [
                {
                    key: "type",
                    type: "RADIO",
                    value: "radioLabel1",
                    label: "Radio Label 1",
                },
                {
                    key: "amount",
                    type: "INPUT_PERCENTAGE",
                    label: "Enter percentage",
                    description: "Input description",
                    hint: "Placeholder 1",
                },
            ],
            [
                {
                    key: "type",
                    type: "RADIO",
                    value: "fixedDollarAmount",
                    label: "Radio label 2",
                },
                {
                    key: "amount",
                    type: "INPUT_MONEY",
                    label: "Enter Money",
                    description: "Input description",
                    hint: "Placeholder 2",
                },
            ],
        ],
    },
    {
        key: "checkbox_key",
        type: "CHECKBOX",
        name: "Checkboxes",
        allowedValues: [
            {
                value: "Value 1",
                text: "Label 1",
                description: null,
            },
            {
                value: "Value 2",
                text: "Label 2",
                description: null,
            },
            {
                value: "Value 3",
                text: "Label 3",
                description: null,
            },
            {
                value: "Value 4",
                text: "Label 4",
                description: null,
            },
            {
                value: "Value 5",
                text: "Label 5",
                description: null,
            },
            {
                value: "Value 6",
                text: "Label 6",
                description: null,
            },
        ],
    },
    {
        key: "inputColor",
        type: "INPUT_COLOR",
        name: "Input Color",
        default: "#2E2E2E",
        placeholder: "#2E2E2E",
        label: "Text Color",
    },
    {
        key: "inputRange",
        type: "INPUT_RANGE",
        name: "Input Range",
        hint: "Range: 300-850",
        label: "Ranged Input",
        range: {
            start: 300,
            end: 850,
            endExclusive: 851,
        },
    },
    {
        key: "enabledGroup",
        type: "ENABLED_GROUP",
        name: "Enabled Group",
        label: "Use Custom Colors",
        group: [
            {
                key: "key1",
                type: "INPUT_COLOR",
                name: "Input Color",
                placeholder: "#2E2E2E",
                hint: "#2E2E2E",
                label: "Text Color",
            },
        ],
    },
    {
        key: "enabledGroupV2",
        type: "ENABLED_GROUP",
        name: "Enabled Group with Input and Nested Enabled Group",
        label: "Toggle",
        group: [
            {
                key: "key1",
                type: "INPUT",
                name: "Input 1",
                label: "Input 1",
                hint: "Placeholder 1",
                description: "This is a input field",
            },
            {
                key: "key2",
                type: "INPUT",
                name: "Input 2",
                label: "Input 2",
                hint: "Placeholder 2",
                description: "This is a input field",
            },
            {
                key: "key3",
                type: "INPUT",
                name: "Input 3",
                label: "Input 3",
                hint: "Placeholder 3",
                description: "This is a input field",
            },
            {
                key: "nestedEnabledGroup",
                type: "ENABLED_GROUP",
                name: "Enabled Group",
                label: "Nested Enabled Group",
                group: [
                    {
                        key: "key4",
                        type: "INPUT",
                        name: "Input 4",
                        label: "Input 4",
                    },
                ],
            },
        ],
    },
    {
        key: "radioGroup",
        type: "RADIO",
        name: "Radio Group (2+ items)",
        label: "Radio Button",
        allowedValues: [
            { value: "Item 1", text: "Item 1", description: null },
            { value: "Item 2", text: "Item 2", description: null },
            { value: "Item 3", text: "Item 3", description: null },
        ],
    },
    {
        key: "radioGroupv2",
        type: "RADIO",
        name: "Radio Group with description (2 items)",
        label: "Radio Button",
        allowedValues: [
            { value: "Item 1", text: "Item 1", description: null },
            {
                value: "Item 2",
                text: "Item 2",
                description:
                    "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut" +
                    " labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud.",
            },
        ],
    },
    {
        key: "actionableTable",
        name: "Actionable Table",
        type: "TABLE",
        title: "Table",
        collapsible: false,

        upperLevelConfigs: {
            title: {
                key: "paymentCalculations/upfrontTaxesFees/dealerFeesUpfrontTaxes/title",
                type: "LABEL",
                value: "Dealer Fees",
                tooltip: "This preference allows you to give preferential treatment to a specific finance company.",
            },
        },

        columns: [
            // {
            //     hidden: true,
            //     header: "ID",
            //     attribute: "id",
            //     sortEnabled: false,
            //     type: "ID",
            // },
            {
                header: "Fee Type",
                attribute: "feeType",
                sortEnabled: true,
                type: "TEXT",
                function: null,
            },
            {
                header: "Stock Type",
                attribute: "stockType",
                sortEnabled: true,
                type: "TEXT",
                function: null,
            },
            {
                header: "Name",
                attribute: "name",
                sortEnabled: true,
                type: "TEXT",
                function: null,
            },
            {
                header: "Amount",
                attribute: "amount",
                sortEnabled: true,
                type: "MONEY",
                function: null,
            },
            {
                header: "Deal Type",
                attribute: "dealType",
                sortEnabled: true,
                type: "TEXT",
                function: null,
            },
            {
                header: "Lease | Cap Cost / Inception",
                attribute: "leaseCapCostInception",
                sortEnabled: true,
                type: "COMPUTED",
                function: "compare(isInception,isCapFee)",
            },
            {
                header: "Taxable",
                attribute: "Taxable",
                sortEnabled: true,
                type: "COMPUTED",
                function: "yesNo(isTaxable)",
            },
            {
                header: "Description",
                attribute: "description",
                sortEnabled: true,
                type: "TEXT",
                function: null,
            },
        ],
        actions: {
            header: "Actions",
            functions: [
                {
                    type: "EDIT",
                    value: "No idea here",
                },
                {
                    type: "REMOVE",
                    value: "No idea here",
                },
            ],
        },
        tableBody: [
            {
                id: "fea4eba6-725f-4359-b538-559025a6a051",
                feeType: "OTHER",
                stockType: "New",
                name: "Doc Fee",
                amount: "120.0",
                dealType: "LEASE",
                isInception: true,
                isCapFee: null,
                isTaxable: true,
                description: "Cap Cost Dealer Tax Fee",
                leaseCapCostInception: "isInception",
                Taxable: "Yes",
            },
            {
                id: "54afb534-a604-4d6d-a7c2-9e85a02cc661",
                feeType: "OTHER",
                stockType: "Used",
                name: "Acquisition Fee",
                amount: "130.0",
                dealType: "LEASE",
                isInception: true,
                isCapFee: null,
                isTaxable: false,
                description: "Inception Dealer Without Tax Fee",
                leaseCapCostInception: "isInception",
                Taxable: "No",
            },
        ],
    },
    {
        key: "groupedItem",
        type: "GROUPED_TERM",
        name: "Grouped Term for new / old car default payments",
        group: [
            {
                key: "paymentCalculations/newCarFinanceSettings/paymentDefaults/newCarTypeAndTerm/paymentType",
                type: "DROPDOWN",
                allowedValues: [
                    {
                        value: "CASH",
                        text: "Cash",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "FINANCE",
                        text: "Finance",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "LEASE",
                        text: "Lease",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "LOWEST",
                        text: "Lowest",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                ],
                default: "LOWEST",
                label: "Default Payment Type",
            },
            {
                key: "paymentCalculations/newCarFinanceSettings/paymentDefaults/newCarTypeAndTerm/term",
                type: "DROPDOWN",
                allowedValues: [
                    {
                        value: "12",
                        text: "12",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "24",
                        text: "24",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "36",
                        text: "36",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "39",
                        text: "39",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "48",
                        text: "48",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "60",
                        text: "60",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "66",
                        text: "66",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "84",
                        text: "84",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                    {
                        value: "96",
                        text: "96",
                        label: null,
                        description: null,
                        disabled: false,
                    },
                ],
                default: "36",
                label: "Default Term",
            },
        ],
    },
]);

export { LAYOUT_CONFIG, FORM_INPUT_BUILDER_CONFIG };
