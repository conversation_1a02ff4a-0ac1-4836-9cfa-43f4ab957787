import { make } from "vuex-pathify";
import api from "@/util/api";
import loader from "@/util/loader";
import isNil from "lodash/isNil";
import mergeWith from "lodash/mergeWith";
import sortBy from "lodash/sortBy";
import cloneDeep from "lodash/cloneDeep";
import lodashFind from "lodash/find";

const initialState = {
    dealerNesnaProtectionProducts: {
        data: null,
        loader: loader.defaultState(),
    },
    dealerSavedNesnaProtectionProducts: [],
};

const actions = {
    ...make.actions(initialState),

    fetchDealerNesnaProtectionProducts({ commit }, dealerId) {
        commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS_LOADER", loader.started());

        return api
            .get(`/nesna/dealers/${dealerId}/nesna-products`)
            .then((response) => {
                const products = response.data?.dealerProductDetailsResponse;

                commit("SET_DEALER_SAVED_NESNA_PROTECTION_PRODUCTS", JSON.parse(JSON.stringify(products)));
                commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS", products);

                commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS_LOADER", loader.successful());
                return _.get(response, "data");
            })
            .catch((error) => {
                console.error("fetchProspect error: ", error);
                commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS_LOADER", loader.error(error));
                return Promise.reject(error);
            });
    },
    updateEnabledField({ commit }, { index, value }) {
        commit("SET_ENABLED_FIELD", { index, value });
    },

    updateLevelsField({ commit }, { id, value }) {
        commit("SET_LEVELS_FIELD", { id, value });
    },

    saveDealerNesnaProducts({ commit, state }, requestObject) {
        commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS_LOADER", loader.started());

        return api
            .post(`/nesna/dealers/${requestObject.dealerIds}/nesna-products`, requestObject.requestBody)
            .then((resp) => {
                const currentSavedProducts = cloneDeep(state.dealerSavedNesnaProtectionProducts) || [];
                const updatedProducts = cloneDeep(resp.data?.nesnaProtectionProducts) || [];
                let mergedProducts = [];

                //merge each updated products object with the corresponding current saved products matching by masterNesnaProductId
                updatedProducts.forEach((updatedProduct) => {
                    const currentProduct = lodashFind(currentSavedProducts, {
                        masterNesnaProductId: updatedProduct.masterNesnaProductId,
                    });

                    if (currentProduct) {
                        mergedProducts.push(mergeWith(currentProduct, updatedProduct, customizer));
                    } else {
                        mergedProducts.push(updatedProduct);
                    }
                });

                function customizer(objValue, srcValue) {
                    return isNil(srcValue) ? objValue : srcValue;
                }

                commit("SET_DEALER_SAVED_NESNA_PROTECTION_PRODUCTS", JSON.parse(JSON.stringify(mergedProducts)));
                commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS", mergedProducts);
                commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS_LOADER", loader.successful());
            })
            .catch((error) => {
                console.error("saveDealerNesnaProducts error: ", error);
                commit("SET_DEALER_NESNA_PROTECTION_PRODUCTS_LOADER", loader.error(error));
                return Promise.reject(error);
            });
    },
};

const mutations = {
    ...make.mutations(initialState),

    SET_DEALER_NESNA_PROTECTION_PRODUCTS: (state, payload) => {
        state.dealerNesnaProtectionProducts.data = sortBy(payload, ["name", "coverageGroupDescription"]);
    },

    SET_DEALER_SAVED_NESNA_PROTECTION_PRODUCTS: (state, payload) => {
        state.dealerSavedNesnaProtectionProducts = sortBy(payload, ["name", "coverageGroupDescription"]);
    },

    SET_DEALER_NESNA_PROTECTION_PRODUCTS_LOADER: (state, payload) => {
        state.dealerNesnaProtectionProducts.loader = payload;
    },

    SET_ENABLED_FIELD: (state, { index, value }) => {
        const item = state.dealerNesnaProtectionProducts.data.find((item, i) => i === index);
        if (item) {
            item.enabled = value;
        }
    },

    SET_LEVELS_FIELD: (state, { id, value }) => {
        const item = state.dealerNesnaProtectionProducts.data.find((item) => item.id === id);
        if (item) {
            item.levels = value.map((a) => {
                return { ...a };
            });
        }
    },
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
};
