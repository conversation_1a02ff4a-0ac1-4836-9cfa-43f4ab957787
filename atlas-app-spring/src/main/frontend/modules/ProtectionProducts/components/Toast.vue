<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "Toast",
    data() {
        return {
            isToastVisible: false,
        };
    },
    methods: {
        showToast() {
            this.isToastVisible = true;
            setTimeout(() => {
                this.isToastVisible = false;
            }, 2000); // Hide toast after 2 seconds
        },
    },
});
</script>

<template>
    <transition name="fade">
        <div v-if="isToastVisible" class="toast-wrapper">
            <div class="toast-container success-toast">
                <div class="toast">
                    <v-icon>mdi-check-circle-outline</v-icon>
                    <span class="toast-text"> Your changes have been successfully saved. </span>
                </div>
            </div>
        </div>
    </transition>
</template>

<style scoped lang="scss">
.toast-wrapper {
    padding: 16px 16px 0 16px;
    .toast-container {
        height: 64px;
        display: grid;
        place-items: center;
        border-radius: 8px;
        .toast {
            display: inline-flex;
            gap: 8px;
            align-items: center;
            .toast-text {
                font-size: 13px;
                font-weight: 500;
            }
        }
    }
    .success-toast {
        border: #75b798;
        background-color: #d1e7dd;
        color: #0f5132;
    }
}
.fade-enter-active {
    transition: opacity 0.1s;
}
.fade-leave-active {
    transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
    opacity: 0;
}
</style>
