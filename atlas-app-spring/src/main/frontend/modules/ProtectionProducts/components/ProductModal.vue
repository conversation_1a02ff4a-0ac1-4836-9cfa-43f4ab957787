<template>
    <v-dialog v-model="dialog" width="500">
        <template #activator="{ on, attrs }">
            <div class="product-name" v-bind="attrs" v-on="on">
                <span class="font-weight-bold">{{ product.productTypeDescription }}</span>
                <span> - {{ product?.coverageGroupDescription }}</span>
            </div>
        </template>

        <v-card>
            <v-card-text class="pa-0">
                <v-card-title class="d-flex justify-space-between align-center">
                    {{ product.productTypeDescription }}
                    <v-icon @click="dialog = false">mdi-close</v-icon>
                </v-card-title>
                <!-- eslint-disable-next-line vue/no-v-html -->
                <div v-if="dialog" class="iframe-container" v-html="getVideoIframe"></div>
                <div class="px-4 pt-2 pb-4 d-flex flex-column">
                    <div class="mb-1 font-weight-bold">{{ product?.coverageGroupDescription }}</div>
                    <div>{{ product.description || "No description available for this product" }}</div>
                </div>
            </v-card-text>
        </v-card>
    </v-dialog>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "ProductModal",
    props: {
        product: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            dialog: false,
        };
    },
    computed: {
        getVideoIframe() {
            return this.getYouTubeIframe(this.product.videoLink);
        },
    },
    methods: {
        getYouTubeIframe(text) {
            const iframeRegex = /<iframe[\s\S]*?<\/iframe>/gi;
            const match = text.match(iframeRegex);
            return match ? this.sanitizeIframeTag(match[0]) : null;
        },
        sanitizeIframeTag(iframeTag) {
            // Replace escaped double quotes with plain double quotes
            let sanitizedTag = iframeTag.replace(/""/g, '"');
            // Remove height and width attributes using regular expressions
            sanitizedTag = sanitizedTag.replace(/\s*width="[^"]*"/i, "");
            sanitizedTag = sanitizedTag.replace(/\s*height="[^"]*"/i, "");
            // Add style attribute for width 100% and aspect ratio 16:9
            const style = 'style="width: 100%; aspect-ratio: 16/9;"';
            sanitizedTag = sanitizedTag.replace("<iframe", `<iframe ${style}`);
            return sanitizedTag;
        },
    },
});
</script>

<style lang="scss" scoped>
.product-name {
    text-decoration: underline;
    cursor: pointer;
    color: #027dc5;
}
</style>
