<template>
    <div>
        <div v-show="pricingTiers && pricingTiers.length > 0" class="col">
            <v-form ref="form" v-model="valid" class="row gap-2 my-1">
                <div v-for="(tier, index) in pricingTiers" :key="index">
                    <div class="pricing-tier-row">
                        <div class="col-span-1 mt-2">
                            <span>({{ index + 1 }}/5)</span>
                        </div>
                        <div class="col-span-2 w-full">
                            <v-tooltip top>
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="tier.minCost"
                                        :rules="[rules.required, rules.numeric, rules.price]"
                                        disabled
                                        prefix="$"
                                        label="Min. Cost"
                                        outlined
                                        dense
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <span>{{
                                    disabledTier(index)
                                        ? "Subsequent tiers should be removed to enable editing for this tier"
                                        : "Min. Cost is by default set to zero or to previous tier Max. Cost + 1"
                                }}</span>
                            </v-tooltip>
                        </div>
                        <div class="col-span-2 w-full">
                            <v-tooltip top :disabled="!disabledTier(index)">
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        :id="'tierMaxCost' + index"
                                        ref="tierMaxCostRef"
                                        v-model="tier.maxCost"
                                        :rules="[
                                            rules.required,
                                            rules.numeric,
                                            rules.price,
                                            rules.min(minValue(index), tier.maxCost),
                                        ]"
                                        :disabled="disabledTier(index)"
                                        prefix="$"
                                        :min="minValue(index)"
                                        label="Max. Cost"
                                        outlined
                                        dense
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <span>Subsequent tiers should be removed to enable editing for this tier</span>
                            </v-tooltip>
                        </div>
                        <div class="col-span-2 w-full">
                            <v-tooltip top :disabled="!disabledTier(index)">
                                <template #activator="{ on, attrs }">
                                    <div v-on="on">
                                        <v-select
                                            v-model="tier.adjustedValue"
                                            :rules="[rules.required]"
                                            :disabled="disabledTier(index)"
                                            :items="['Retail Price']"
                                            label="Adjusted Value"
                                            outlined
                                            dense
                                            v-bind="attrs"
                                        ></v-select>
                                    </div>
                                </template>
                                <span>Subsequent tiers should be removed to enable editing for this tier</span>
                            </v-tooltip>
                        </div>
                        <div class="col-span-2 w-full">
                            <v-tooltip top :disabled="!disabledTier(index)">
                                <template #activator="{ on, attrs }">
                                    <div v-on="on">
                                        <v-select
                                            v-model="tier.markupType"
                                            :rules="[rules.required]"
                                            :disabled="disabledTier(index)"
                                            :items="['$', '%']"
                                            label="Markup Type"
                                            outlined
                                            dense
                                            v-bind="attrs"
                                        ></v-select>
                                    </div>
                                </template>
                                <span>Subsequent tiers should be removed to enable editing for this tier</span>
                            </v-tooltip>
                        </div>
                        <div class="col-span-2 w-full">
                            <v-tooltip top :disabled="!disabledTier(index)">
                                <template #activator="{ on, attrs }">
                                    <v-text-field
                                        v-model="tier.markupAmount"
                                        :rules="[rules.required, rules.numeric, rules.amount]"
                                        :disabled="disabledTier(index)"
                                        label="Markup Amount"
                                        outlined
                                        dense
                                        v-bind="attrs"
                                        v-on="on"
                                    ></v-text-field>
                                </template>
                                <span>Subsequent tiers should be removed to enable editing for this tier</span>
                            </v-tooltip>
                        </div>
                        <div v-if="!disabledTier(index)" class="col-span-1 mt-1" @click="removeTier">
                            <v-icon medium class="d-inline-block justify-content-start align-content-start"
                                >mdi-delete</v-icon
                            >
                        </div>
                    </div>
                </div>
            </v-form>
        </div>
        <a x-small class="d-flex align-center mb-2" :disabled="addTierDisabled" @click="addTier">
            <v-icon class="mr-1" x-small>mdi-plus-circle-outline</v-icon>
            <div>Add a custom adjustment range</div>
        </a>
    </div>
</template>

<script>
import { call, get, sync } from "vuex-pathify";

export default {
    props: {
        id: {
            type: String,
            required: true,
        },

        levels: {
            type: Array,
            required: false,
            default: () => {
                return [];
            },
        },
    },
    data: () => ({
        pricingTiers: [],
        rules: {
            required: (value) => !!value || value === 0 || "This field is required.",
            numeric: (value) =>
                new RegExp(/^\d*$/).test(value) || "Value must be an integer, no commas or negative values.",
            price: (v) => (v >= 0 && v <= 9999) || "Price must be between $0 and $9999",
            amount: (v) => (v >= 0 && v <= 999) || "Amount must be between $0 and $999",
            min: (min, v) => v >= min || `Value must be at least ${min}`,
        },
        valid: false,
    }),

    computed: {
        dealerNesnaProtectionProductsLoader: get("protectionProducts/dealerNesnaProtectionProducts@loader"),

        minValue() {
            return (index) => parseInt(this.pricingTiers[index].minCost) + 1;
        },

        addTierDisabled() {
            return this.pricingTiers.length >= 5 || !this.valid;
        },
    },

    watch: {
        pricingTiers: {
            handler() {
                this.updateLevelsField({ id: this.id, value: this.pricingTiers });
            },
            deep: true,
        },
        valid(newVal) {
            this.$emit("onValidityChange", { id: this.id, validity: newVal });
        },
    },

    mounted() {
        if (this.levels != null) {
            this.pricingTiers = this.levels.map((a) => {
                return { ...a };
            });
        }
    },

    methods: {
        addTier() {
            let tier = {};
            if (this.pricingTiers.length >= 5) {
                return;
            }
            if (this.pricingTiers.length !== 0 && !this.valid) {
                return;
            }

            tier = {
                minCost:
                    this.pricingTiers.length === 0
                        ? 0
                        : parseInt(this.pricingTiers[this.pricingTiers.length - 1].maxCost) + 1,
                maxCost: null,
                adjustedValue: "Retail Price",
                markupType: "$",
                markupAmount: 0,
            };

            this.pricingTiers.push(tier);

            this.$nextTick(() => {
                setTimeout(() => {
                    // Focus on last tier max cost
                    const element = this.$refs.tierMaxCostRef[this.pricingTiers.length - 1];
                    element.focus();
                }, 200);
            });
        },

        disabledTier(index) {
            return index + 1 !== this.pricingTiers.length;
        },

        removeTier() {
            if (this.pricingTiers.length >= 1) {
                this.pricingTiers.pop();
            }
        },

        updateLevelsField: call("protectionProducts/updateLevelsField"),
    },
};
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";

a[disabled="disabled"] {
    color: grey;
}
.pricing-tier-row {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: 16px;
    place-items: start;
}
.col-span-1 {
    grid-column: span 1 / span 1;
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        grid-column: span 12 / span 12;
    }
}
.col-span-2 {
    grid-column: span 2 / span 2;
    @media #{map-get($display-breakpoints, 'md-and-down')} {
        grid-column: span 12 / span 12;
    }
}
.w-full {
    width: 100%;
}
</style>
