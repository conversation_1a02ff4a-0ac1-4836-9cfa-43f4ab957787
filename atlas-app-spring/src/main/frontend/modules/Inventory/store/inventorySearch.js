import { make } from "vuex-pathify";
import loader from "@/util/loader";
import searchUtils from "@/util/searchUtils";
import _ from "lodash";

const uriRoot = "/inventory";

const initialState = {
    ...searchUtils.state(),
    searchUri: null,
    initialLoad: true,
    pageable: searchUtils.parsePageableFromUrl({
        sort: "year,desc",
        page: 1,
    }),
    facets: null,
    filters: searchUtils.parseFiltersFromUrl(),
    searchLoader: {
        loader: loader.defaultState(),
        data: [],
    },
    displayFields: [
        "stockNumber",
        "pricingValid",
        "dealer.name",
        "vin",
        "stockType",
        "ymm",
        "color",
        "price",
        "actions",
    ],
    pageMetadata: {
        size: 0,
        totalElements: 0,
        totalPages: 0,
        number: 0,
    },
    /**
     * pills schema
     * {
                    "name": {
                        label: 'Name',
                        enabled: false,
                    },
                    "warrantyStatuses": {
                        label: 'Warranty',
                        enabled: true
                    }
                }
     *     label: label to use for display
     *     facet: the name of the search facet
     *     enabled: is the pill show be displayed
     * }
     */
    pills: {
        dealerIds: {
            enabled: false,
        },
        vin: {
            enabled: false,
        },
        stockNumber: {
            enabled: false,
        },
        dmaCodes: {
            label: "DMAs",
            facet: "dmas",
        },
        topDmas: {
            type: "range",
        },
        programIds: {
            enabled: false,
        },
    },
    maxMile: null,
    mileageOptions: null,
    selectedMiles: null,
};

const mutations = {
    ...make.mutations(initialState),
    ...searchUtils.mutations(),
    SET_MILE_FILTER({ state }, mile) {
        state.filters.miles = mile;
    },
};

const actions = {
    ...make.actions(initialState),
    ...searchUtils.actions(uriRoot, "Vehicle Search"),
    setSearchUri({ commit, state }, searchUri) {
        commit("SET_SEARCH_URI", searchUri);
    },
};

const getters = {
    ...make.getters(initialState),
    ...searchUtils.getters(),
};

export default {
    namespaced: true,
    state: initialState,
    mutations,
    actions,
    getters,
};
