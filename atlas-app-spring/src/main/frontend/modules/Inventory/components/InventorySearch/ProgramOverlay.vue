<template>
    <v-container fluid>
        <v-row>
            <v-col cols="3">
                <v-select
                    v-model="program"
                    :items="campaigns"
                    item-value="id"
                    item-text="name"
                    placeholder="Select Program"
                    :disabled="disabledProgramSelection"
                >
                </v-select>
            </v-col>
            <v-col cols="9">
                <div class="d-flex">
                    <v-tooltip :disabled="!isInactiveVehicle" top open-on-focus>
                        <template #activator="{ on, attrs }">
                            <v-text-field ref="urlField" :value="programUrl" v-bind="attrs" readonly v-on="on">
                            </v-text-field>
                        </template>
                        <span>vehicle is inactive</span>
                    </v-tooltip>
                    <v-checkbox
                        v-model="isShortUrlChecked"
                        hide-details
                        label="Short URL"
                        :disabled="disableShortUrlBtn"
                        class="ml-3"
                        @change="shortCheckboxChange"
                    >
                    </v-checkbox>
                    <div class="ml-3 mt-4">
                        <v-btn
                            v-if="canCopy"
                            ref="copyBtn"
                            :disabled="programUrl === null"
                            color="primary"
                            small
                            :loading="isCopyButtonLoading"
                            @click="copy(programUrl)"
                        >
                            <v-icon left small>mdi-content-copy</v-icon>
                            <span ref="copyBtnLabel">Copy</span>
                        </v-btn>
                    </div>
                </div>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import axios from "axios";
import api from "@/util/api";
import lodashGet from "lodash/get";
import lodashIsNil from "lodash/isNil";
import lodashIsNull from "lodash/isNull";
import { get } from "vuex-pathify";

export default {
    name: "ProgramOverlay",
    props: {
        vehicleDoc: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            campaigns: [],
            canCopy: false,
            loading: false,
            program: null,
            programUrl: null,
            longUrl: null,
            shortUrl: null,
            isCopyButtonLoading: false,
            isShortUrlChecked: false,
            isInactiveVehicle: false,
            disabledProgramSelection: false,
        };
    },
    computed: {
        featureFlags: get("loggedInUser/featureFlags"),
        selectedProgram: get("inventorySearch/filters@programIds"),
        selectedDealerEnabledForDigitalRetail: get("loggedInUser/selectedDealer@enabledForDigitalRetail"),
        campaignsExist() {
            return !lodashIsNil(this.campaigns) && this.campaigns.length > 0;
        },
        disableShortUrlBtn() {
            return lodashIsNull(this.programUrl) || this.isInactiveVehicle;
        },
        dealerizeUrlVersionFeatureEnabled() {
            return lodashGet(this.featureFlags, "VEHICLE_URL_DEALERIZE_VERSION", false) || false;
        },
        isProgramUser() {
            return this.$acl.hasAuthority("ROLE_PROGRAM");
        },
    },

    watch: {
        program() {
            this.generate();
        },
    },

    created() {
        this.canCopy = !!navigator.clipboard;
    },

    mounted() {
        const dealerId = this.vehicleDoc.dealer.id;
        this.loading = true;
        api.get(`/dealer/${dealerId}/inventory/${this.vehicleDoc.id}/campaigns`, {
            programId: this.$route.query.programIds,
        })
            .then((response) => {
                this.campaigns = lodashGet(response, "data", null);

                // Default select first option from Select Program dropdown
                if (this.campaignsExist) {
                    this.program = this.campaigns[0].id;
                }
                // Disable Select Program dropdown if there is only one or less program in list and program is selected from top
                if (!!this.selectedProgram && this.campaigns.length <= 1) {
                    this.disabledProgramSelection = true;
                }
            })
            .catch((error) => {
                console.log(error);
            })
            .finally(() => {
                this.loading = false;
            });
    },

    methods: {
        generate() {
            if (!lodashIsNil(this.program)) {
                const vin = this.vehicleDoc.vin;
                const dealerId = this.vehicleDoc.dealer.id;
                this.loading = true;
                let url = `${this.getApiDomain()}/apex/dealerplugin/v1/vehicle/active?campaignId=${
                    this.program
                }&dealerId=${dealerId}&vin=${vin}`;

                if (!this.isProgramUser && this.dealerizeUrlVersionFeatureEnabled) {
                    url = `${this.getApiDomain()}/apex/deeplink/vehicle?campaignId=${
                        this.program
                    }&dealerId=${dealerId}&vin=${vin}`;
                }

                axios
                    .get(url)
                    .then((response) => {
                        const active = lodashGet(response.data, "active", false);
                        if (active) {
                            const data = response.data;
                            let domain = lodashIsNil(data.dealerizeDomain) ? data.domain : data.dealerizeDomain;
                            let utmParams = `&utm_source=dealer-crm&utm_medium=email&utm_campaign=${data.dealerSlug}`;

                            if (this.useEntryPointFromApex(data)) {
                                this.longUrl = data.entryPoint + utmParams;
                            } else {
                                this.longUrl = `https://${domain}/plugin/dealer/${data.dealerId}?vin=${vin}${utmParams}`;
                            }
                            this.programUrl = this.longUrl;
                        } else {
                            this.programUrl = "This vehicle is not active for the associated Campaign";
                            this.$refs.copyBtn.disabled = true;
                            this.isInactiveVehicle = true;
                        }
                    })
                    .catch(() => {
                        this.programUrl = "Error fetching Link";
                        this.$refs.copyBtn.disabled = true;
                    })
                    .finally(() => {
                        this.loading = false;
                        this.shortUrl = null;
                        this.isShortUrlChecked = false;
                        this.$refs.copyBtnLabel.innerHTML = "Copy";
                    });
            } else {
                this.programUrl = "Select a program to generate URL";
            }
        },
        generateShortUrl() {
            if (!lodashIsNil(this.shortUrl)) {
                return;
            }

            this.isCopyButtonLoading = true;
            const params = {
                targetUrl: this.longUrl,
            };
            api.post(`/vehicles/shortVehicleURL`, params)
                .then((response) => {
                    this.shortUrl = response.data;
                    this.programUrl = this.shortUrl;
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isCopyButtonLoading = false;
                });
        },
        async copy(s) {
            await navigator.clipboard.writeText(s);
            this.$refs.copyBtnLabel.innerHTML = "Copied";
        },
        getApiDomain() {
            const hostname = window.location.host;
            if (hostname === "atlas.carsaver.com") {
                return "https://api.carsaver.com";
            } else if (hostname === "atlas.dev.carsaver.com") {
                return "https://api-dev.carsaver.com";
            } else if (hostname === "atlas.qa.carsaver.com") {
                return "https://api-qa.carsaver.com";
            }
            return "https://api-beta.carsaver.com";
        },
        shortCheckboxChange() {
            this.$refs.copyBtnLabel.innerHTML = "Copy";
            if (this.isShortUrlChecked) {
                this.generateShortUrl();
                this.programUrl = this.shortUrl;
            } else {
                this.programUrl = this.longUrl;
            }
        },
        useEntryPointFromApex(data) {
            if (!data) {
                return false;
            }
            let domain = lodashGet(data, "domain", "");
            let isDigitalRetail = domain.includes("digital-retail") || domain === "nissanathome.carsaver.com";
            return isDigitalRetail && this.selectedDealerEnabledForDigitalRetail === true;
        },
    },
};
</script>
