<template>
    <div>
        <component :is="getComponent" :page-data="builderData" />
        <v-dialog v-model="showLeaveConfirmDialog" max-width="400">
            <v-card>
                <v-card-title class="headline">Unsaved Changes</v-card-title>
                <v-card-text>Are you sure you want to leave this page? Any unsaved changes will be lost.</v-card-text>
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn text @click="handleLeaveConfirmation(false)">Stay</v-btn>
                    <v-btn color="primary" @click="handleLeaveConfirmation(true)">Leave</v-btn>
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<script>
import { call, get } from "vuex-pathify";
import { PREVIEW_COMPONENT_MAPPING } from "Util/ConfigManager/components";

export default {
    name: "PreviewFormPage",
    components: {
        // PAGE TITLE COMPONENT MAPPING
        [PREVIEW_COMPONENT_MAPPING["Hamburger Menu"]]: () => import("Components/PreviewForm/HamburgerMenu.vue"),
        [PREVIEW_COMPONENT_MAPPING["CTA Buttons"]]: () => import("Components/PreviewForm/CTAButtons/index.vue"),
        [PREVIEW_COMPONENT_MAPPING["Smart Links QR Codes"]]: () =>
            import("Components/PreviewForm/SmartLinksQrCodes/index.vue"),
        [PREVIEW_COMPONENT_MAPPING["Overlay Settings"]]: () =>
            import("Components/PreviewForm/OverlaySettings/index.vue"),
        [PREVIEW_COMPONENT_MAPPING["Graphics And Images"]]: () =>
            import("Components/PreviewForm/GraphicsAndImages/index.vue"),
    },
    beforeRouteLeave(to, from, next) {
        if (this.page === "Overlay Settings" && this.hasUnsavedChanges) {
            this.showLeaveConfirmDialog = true;
            this.navigationGuard = next;
        } else {
            next();
        }
    },
    props: {
        dealerIds: {
            type: String,
            required: false,
            default: null,
        },
        page: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            showLeaveConfirmDialog: false,
            navigationGuard: null,
        };
    },
    computed: {
        userId: get("loggedInUser/userId"),
        selectedDealerId: get("loggedInUser/selectedDealer@id"),
        builderData: get("pageConfigs/pageBuilderData"),
        formData: get("pageConfigs/form@data"),
        formDefaults: get("pageConfigs/form@defaults"),
        dealerId() {
            return this.dealerIds || this.selectedDealerId;
        },
        getComponent() {
            return PREVIEW_COMPONENT_MAPPING[this.page];
        },
        hasUnsavedChanges() {
            if (!this.formData || !this.formDefaults) return false;
            return Object.keys(this.formData).some(
                (key) => this.formData[key]?.toString() !== this.formDefaults[key]?.toString()
            );
        },
    },
    watch: {
        dealerId: {
            handler(value) {
                if (value) {
                    this.init();
                }
            },
            immediate: true,
        },
    },
    methods: {
        handleLeaveConfirmation(confirmed) {
            this.showLeaveConfirmDialog = false;
            if (this.navigationGuard) {
                this.navigationGuard(confirmed);
                this.navigationGuard = null;
            }
        },
        clearFormData: call("pageConfigs/clearFormData"),
        fetchPageBuilder: call("pageConfigs/fetchPageBuilder"),
        fetchDefaults: call("pageConfigs/fetchDefaults"),
        initialFormData(payload) {
            this.clearFormData();
            this.fetchPageBuilder(payload);
            this.fetchDefaults(payload);
        },
        init() {
            const payload = {
                page: this.page,
                dealerId: this.dealerId,
                userId: this.userId,
            };

            this.initialFormData(payload);
        },
    },
};
</script>
