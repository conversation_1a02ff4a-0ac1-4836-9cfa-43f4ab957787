import { make } from "vuex-pathify";
import Vue from "vue";
import configApi from "Util/configApi";
import loader from "@/util/loader";
import lodashCapitalize from "lodash/capitalize";
import lodashCloneDeep from "lodash/cloneDeep";
import lodashIsEqual from "lodash/isEqual";

// This function is used to flatten the builder object and return a flat object
// containing only key-value pairs where both 'key' and 'value' exist in the source.
function flattenObject(obj) {
    const flattened = {};

    function flatten(object) {
        if (Array.isArray(object)) {
            // Handle arrays by processing each element
            object.forEach((item) => flatten(item));
            return;
        }

        if (object && typeof object === "object") {
            // Check if the current object has BOTH key and value properties
            if ("key" in object && "value" in object) {
                // Only add to flattened if both key and value exist
                flattened[object.key] = object.value;
                // We don't return here, as this object might contain nested structures to flatten
            }

            // Regardless of whether key/value were found at this level,
            // continue traversing deeper into the object's properties.
            Object.keys(object).forEach((key) => {
                // Avoid infinite loops if the value property itself is an object
                // that we just processed above. Check if the property is not 'key' or 'value'.
                // Or more simply, just ensure the value is an object or array before recursing.
                const value = object[key];
                if (value && typeof value === "object") {
                    // Recurse into nested objects/arrays
                    flatten(value);
                }
            });
        }
    }

    flatten(obj);
    return flattened;
}

function getPathList(path) {
    if (typeof path !== "string") return [];

    const pathStr = path.substring(1).replaceAll("-", " ");
    const pathList = pathStr.split("/");

    return pathList;
}

function capitalizeEachWord(str) {
    if (typeof str !== "string") return "";

    return str.split(" ").map(lodashCapitalize).join(" ");
}

// This function is used to transform the path to a more human-readable format
// This is only for special exceptions
function pathTransformer(path) {
    const pathTransforms = {
        "smart links qr codes": "Smart Links/QR Codes",
        "cta buttons": "CTA Buttons",
    };

    return pathTransforms[path] || null;
}

function buildBreadcrumbItems(pathList) {
    const breadcrumbItems = [];
    let pathTransform;
    let text;

    if (!pathList) return breadcrumbItems;

    pathList.forEach((path) => {
        pathTransform = pathTransformer(path);

        if (pathTransform) {
            text = pathTransform;
        } else {
            text = capitalizeEachWord(path);
        }

        breadcrumbItems.push({
            text,
            disabled: true,
        });
    });

    return breadcrumbItems;
}

function getPanelMap(sections) {
    let sectionMap = {};

    if (!sections) return sectionMap;

    sections.forEach((section, index) => {
        sectionMap[section.title] = index;
    });

    return sectionMap;
}

function getOpenPanelsState(sections) {
    return [...sections.keys()];
}

function getOpenSubPanelState(sections) {
    let allOpenedSubPanelsState = {};

    if (!sections) return allOpenedSubPanelsState;

    sections.forEach((section) => {
        let sectionTitle = section.title;

        allOpenedSubPanelsState[sectionTitle] = [];

        section.components?.forEach((component, index) => {
            allOpenedSubPanelsState[sectionTitle].push(index);
        });
    });

    return allOpenedSubPanelsState;
}

function getFormChanges(formData, formDefaults) {
    const formChanges = {};

    for (const key in formData) {
        // Use deep comparison instead of shallow toString() comparison
        if (!lodashIsEqual(formData[key], formDefaults[key])) {
            formChanges[key] = formData[key];
        }
    }

    return formChanges;
}

/////// resetState ///////
const resetFormState = {
    isMultipartForm: false,
    defaults: {},
    data: {},
    dataDefaultsMapping: {}, // [{key: string, value: string},{},{}]
    loader: loader.defaultState(),
};
const resetFormValidationState = {
    forms: {}, // Form Name: valid: boolean
    lastSubmitted: null,
};

const resetState = {
    debugMode: false,
    open: null,
    expandAll: false,
    disableExpandAll: false,
    panels: [],
    subPanels: {},
    allOpenedSubPanelsState: {},
    allOpenedPanelsState: [],
    mini: false,
    menuVersionSelected: "v2", //temporary options: "v1", "v2"
    selectedCategory: null,
    pageBuilderData: {},
    flattenedPageBuilderData: {},
    panelMap: {},
    loader: loader.defaultState(),
    breadcrumbItems: [
        {
            text: "Dashboard",
            disabled: false,
            href: "breadcrumbs_dashboard",
        },
        {
            text: "Link 1x",
            disabled: false,
            href: "breadcrumbs_link_1",
        },
        {
            text: "Link 2x",
            disabled: true,
            href: "breadcrumbs_link_2",
        },
    ],
    form: { ...resetFormState },
    saveForm: {
        loader: loader.defaultState(),
    },
    validateForm: { ...resetFormValidationState },
    toast: {
        show: false,
        timeout: 8000,
        message: "",
        type: "success", // success, error
    },
    auditData: {
        data: [],
        pageData: {},
        loader: loader.defaultState(),
    },
};
/////// end resetState ///////

const initialState = lodashCloneDeep(resetState);

const mutations = {
    ...make.mutations(initialState),
    CLEAR_FORM_DATA(state) {
        const initState = { ...resetFormState };
        Vue.set(state, "form", initState);
    },
    CLEAR_VALIDATION_FORM(state) {
        const initState = { ...resetFormValidationState };
        Vue.set(state, "validateForm", initState);
    },
    SET_IS_MULTIPART_FORM(state, isMultipart) {
        Vue.set(state.form, "isMultipartForm", isMultipart);
    },
    SET_FORM_DATA(state, data) {
        Vue.set(state.form, "data", data);
    },
    SET_FORM_DEFAULTS(state, defaults) {
        Vue.set(state.form, "defaults", defaults);
    },
    SET_FORM_DATA_DEFAULTS_MAPPING(state, data) {
        state.form.dataDefaultsMapping = data;
    },
    SET_DATA_DEFAULTS_MAPPING(state, mapping) {
        Vue.set(state.form, "dataDefaultsMapping", mapping);
    },
    SET_TOAST_DATA(state, { show, message, type, timeout = 8000 }) {
        state.toast = {
            show,
            message,
            type,
            timeout,
        };
    },
    SET_FORM_LOADER(state, loader) {
        Vue.set(state.form, "loader", loader);
    },
    SET_SAVE_FORM_LOADER(state, loader) {
        Vue.set(state.saveForm, "loader", loader);
    },
    RESET_TOAST_DATA(state) {
        state.toast = {
            show: false,
            message: "",
            type: "",
            timeout: 8000,
        };
    },
    SET_LAST_SUBMITTED(state) {
        state.validateForm.lastSubmitted = new Date();
    },
    SET_VALIDATE_FORM(state, { formName, valid }) {
        Vue.set(state.validateForm.forms, formName, valid);
    },
    REMOVE_VALIDATE_FORM(state, { formName }) {
        Vue.delete(state.validateForm.forms, formName);
    },
    RESET_VALIDATE_FORM(state) {
        state.validateForm.forms = {};
        state.validateForm.lastSubmitted = null;
    },
    RESET_STATE(state) {
        // Get a fresh copy of the initial state
        const freshState = lodashCloneDeep(initialState);

        // Clear the existing state first (remove any properties that might not exist in initialState)
        Object.keys(state).forEach((key) => {
            delete state[key];
        });

        // Add all properties from the fresh state
        Object.keys(freshState).forEach((key) => {
            Vue.set(state, key, freshState[key]);
        });
    },
};

const getters = {
    ...make.getters(initialState),
    pageTitle: (state) => {
        return state.pageBuilderData.title;
    },
    pageName: (state) => {
        return state.pageBuilderData.page;
    },
    sectionIndexes: (state) => {
        const sections = state.pageBuilderData.sections.map((section, index) => index);
        return sections;
    },
    areAllPanelsClosed: (state) => {
        return state.panels.length === 0;
    },
    areAllPanelsOpen: (state) => {
        //compare subPanels to allOpenedSubPanelsState
        const subPanels = state.subPanels || {};
        const allOpenedSubPanelsState = state.allOpenedSubPanelsState;

        if (Object.keys(subPanels).length !== Object.keys(allOpenedSubPanelsState).length) {
            return false;
        }

        for (let panel in subPanels) {
            if (subPanels[panel].length !== allOpenedSubPanelsState[panel].length) {
                return false;
            }
        }

        return true;
    },
    getValidateFormByName: (state) => (name) => {
        return state.validateForm.forms[name] || true;
    },
};

const actions = {
    ...make.actions(initialState),
    openPanel({ commit, state }, { title }) {
        const newPanelIndex = state.panelMap[title];

        if (newPanelIndex !== undefined && state.panels.indexOf(newPanelIndex) === -1) {
            commit("SET_PANELS", [...state.panels, newPanelIndex]);
        }
    },
    closePanel({ commit, state }, { title }) {
        const newPanelIndex = state.panelMap[title];

        if (newPanelIndex !== undefined && state.panels.indexOf(newPanelIndex) !== -1) {
            commit(
                "SET_PANELS",
                state.panels.filter((panelIndex) => panelIndex !== newPanelIndex)
            );
        }
    },
    closeSubPanel({ commit, state }, { title }) {
        if (state.subPanels) {
            commit("SET_SUB_PANELS", {
                ...state.subPanels,
                [title]: [],
            });
        }
    },
    openSubPanel({ commit, state }, { title }) {
        if (state.subPanels) {
            commit("SET_SUB_PANELS", {
                ...state.subPanels,
                [title]: state.allOpenedSubPanelsState[title],
            });
        }
    },
    clearFormData({ commit }) {
        commit("CLEAR_FORM_DATA");
    },
    resetState({ commit }) {
        commit("RESET_STATE");
    },
    fetchPageBuilder({ commit, state, dispatch }, { page, dealerId, userId }) {
        commit("SET_LOADER", loader.started());
        commit("SET_EXPAND_ALL", false);
        commit("CLEAR_FORM_DATA");
        commit("RESET_VALIDATE_FORM");

        configApi
            .getPageBuilder(page, dealerId, userId)
            .then((response) => {
                commit("SET_PAGE_BUILDER_DATA", response.data);
                commit("SET_FLATTENED_PAGE_BUILDER_DATA", flattenObject(response.data));
                commit("SET_LOADER", loader.successful());

                const sections = response.data.sections;
                commit("SET_ALL_OPENED_PANELS_STATE", getOpenPanelsState(sections));
                commit("SET_ALL_OPENED_SUB_PANELS_STATE", getOpenSubPanelState(sections));
                commit("SET_PANEL_MAP", getPanelMap(sections));
            })
            .catch((error) => {
                const errorMessage = `Page Builder Error: dealerId: ${dealerId}, userId: ${userId}, pageName: ${page}`;
                console.error(errorMessage, error);
                commit("SET_LOADER", loader.error(errorMessage));
                dispatch("failedToLoad");
            });
    },
    fetchDefaults({ commit, dispatch }, { page, dealerId, userId }) {
        commit("SET_FORM_LOADER", loader.started());
        configApi
            .getDefaults(page, dealerId, userId)
            .then((response) => {
                commit("SET_FORM_DEFAULTS", response.data);
                commit("SET_FORM_LOADER", loader.successful());
            })
            .catch((error) => {
                const errorMessage = `Defaults Error: dealerId: ${dealerId}, userId: ${userId}, pageName: ${page}`;
                console.log(errorMessage, error);
                commit("SET_FORM_LOADER", loader.error(errorMessage));
                dispatch("failedToLoad");
            });
    },
    failedToLoad({ dispatch }) {
        dispatch("setToastData", {
            show: true,
            message: "Failed to Load Page, please refresh",
            type: "error",
            timeout: 999999999,
        });
    },

    setToastData({ commit }, { show, message, type, timeout = 8000 }) {
        commit("SET_TOAST_DATA", { show, message, type, timeout });
        setTimeout(() => {
            commit("RESET_TOAST_DATA");
        }, timeout);
    },
    setIsMultipartForm({ commit }, { isMultipart = true }) {
        commit("SET_IS_MULTIPART_FORM", isMultipart);
    },
    manualFormChange({ commit, state }, { key, value }) {
        commit("SET_FORM_DATA_DEFAULTS_MAPPING", {
            ...state.form.dataDefaultsMapping,
            [key]: value,
        });
    },
    saveFormData({ commit, state, dispatch }, { page, dealerId, userId }) {
        commit("SET_FORM_DATA_DEFAULTS_MAPPING", {
            ...getFormChanges(state.form.data, state.form.defaults),
        });
        commit("SET_SAVE_FORM_LOADER", loader.started());
        commit("SET_LAST_SUBMITTED");
        const noChanges = Object.keys(state.form.dataDefaultsMapping).length === 0;

        if (!Object.values(state.validateForm.forms).every((valid) => !!valid)) {
            dispatch(
                "setToastData",
                {
                    show: true,
                    message: "Please Correct Errors Before Saving",
                    type: "error",
                },
                commit("SET_SAVE_FORM_LOADER", loader.error("Validation Error"))
            );
            return;
        }

        if (noChanges) {
            dispatch(
                "setToastData",
                {
                    show: true,
                    message: "No Changes to Save",
                    type: "success",
                },
                commit("SET_SAVE_FORM_LOADER", loader.successful())
            );
            return;
        }

        if (state.form.isMultipartForm) {
            dispatch("saveMultipartFormData", { page, dealerId, userId, formData: state.form.dataDefaultsMapping })
                .then((response) => {
                    dispatch(
                        "setToastData",
                        {
                            show: true,
                            message: "Form Saved Successfully",
                            type: "success",
                        },
                        commit("SET_SAVE_FORM_LOADER", loader.successful())
                    );
                })
                .catch((error) => {
                    const errorMessage = `Save Error: dealerId: ${dealerId}, userId: ${userId}, pageName: ${page}`;
                    const badImageMessage = "Unable to save file, please check file integrity and try again.";
                    const genericErrorMessage = "Form Save Failed";

                    // Check if the error object's string representation matches the exact 403 error string
                    const isForbiddenError = String(error) === "Error: Request failed with status code 403";
                    const toastMessage = isForbiddenError ? badImageMessage : genericErrorMessage;

                    console.log(errorMessage, error);
                    commit("SET_SAVE_FORM_LOADER", loader.error(errorMessage));
                    dispatch("setToastData", {
                        show: true,
                        message: toastMessage,
                        type: "error",
                    });
                });
        } else {
            configApi
                .save(page, dealerId, userId, state.form.dataDefaultsMapping)
                .then((response) => {
                    dispatch(
                        "setToastData",
                        {
                            show: true,
                            message: "Form Saved Successfully",
                            type: "success",
                        },
                        commit("SET_SAVE_FORM_LOADER", loader.successful())
                    );
                    commit("SET_FORM_DEFAULTS", { ...state.form.defaults, ...state.form.data });
                })
                .catch((error) => {
                    const errorMessage = `Save Error: dealerId: ${dealerId}, userId: ${userId}, pageName: ${page}`;
                    console.log(errorMessage, error);
                    commit("SET_SAVE_FORM_LOADER", loader.error(errorMessage));
                    dispatch("setToastData", {
                        show: true,
                        message: "Form Save Failed",
                        type: "error",
                    });
                });
        }
    },
    saveMultipartFormData({ commit, dispatch }, { page, dealerId, userId, formData }) {
        //if formData is empty, return
        if (!formData) return;

        //if formData is not an object of type FormData then create a new FormData, and append all object properties return
        if (!(formData instanceof FormData)) {
            let newFormData = new FormData();
            Object.keys(formData).forEach((key) => {
                if (formData[key] instanceof File) {
                    newFormData.append(key, formData[key], formData[key].name);
                } else {
                    newFormData.append(key, formData[key]);
                }
            });
            formData = newFormData;
        }

        return configApi
            .uploadFiles(page, dealerId, userId, formData)
            .then((response) => {
                return response.data;
            })
            .catch((error) => {
                return Promise.reject(error);
            });
    },
    configureBreadcrumbs({ state, commit }, route) {
        const breadcrumbItems = [];
        const pathList = getPathList(route.path);

        pathList.unshift(state.selectedCategory);
        breadcrumbItems.push(...buildBreadcrumbItems(pathList));
        commit("SET_BREADCRUMB_ITEMS", breadcrumbItems);
    },

    fetchAuditData({ commit }, { page = 1, dealerId, pageName, size = 20 }) {
        commit("SET_AUDIT_DATA", {
            data: [],
            pageData: {},
            loader: loader.started(),
        });
        configApi
            .getAuditData(pageName, dealerId, page, size)
            .then((response) => {
                commit("SET_AUDIT_DATA", {
                    data: response.data.data,
                    pageData: {
                        page: response.data.page,
                        itemsPerPage: response.data.size,
                        totalItems: response.data.totalCount,
                    },
                    loader: loader.successful(),
                });
            })
            .catch((error) => {
                console.log(error);
                commit("SET_AUDIT_DATA", {
                    data: [],
                    pageData: {},
                    loader: loader.error(error),
                });
            });
    },
    resetSnackbar({ commit }) {
        commit("RESET_TOAST_DATA");
    },
    setFormData({ commit }, data) {
        commit("SET_FORM_DATA", lodashCloneDeep(data));
    },
    updateFormData({ commit, state }, { key, value }) {
        if (!key || value === undefined) return;
        const data = { ...state.form.data };
        data[key] = value;

        commit("SET_FORM_DATA", lodashCloneDeep(data));
    },
    batchUpdateFormData({ commit, state }, newFormDataObject) {
        const mergedData = {
            ...state.form.data,
            ...newFormDataObject,
        };
        commit("SET_FORM_DATA", mergedData);
    },
    syncListingsWithDetailsStore({ commit, state }, { vdpBase, vlpBase }) {
        if (!vdpBase || !vlpBase) {
            console.error("syncListingsWithDetailsStore: Missing vdpBase or vlpBase in payload.", { vdpBase, vlpBase });
            return; // Stop execution if paths are not provided
        }
        //checks, e.g., ensure paths end with '/'
        if (!vdpBase.endsWith("/") || !vlpBase.endsWith("/")) {
            console.warn("syncListingsWithDetailsStore: Base paths should ideally end with a '/'. Proceeding anyway.");
        }

        const currentData = state.form.data;
        const updatesForDestination = {};

        for (const key in currentData) {
            if (key.startsWith(vdpBase)) {
                const keySuffix = key.substring(vdpBase.length);
                const destinationKey = vlpBase + keySuffix;
                updatesForDestination[destinationKey] = currentData[key];
            }
        }

        const mergedData = {
            ...currentData,
            ...updatesForDestination,
        };

        commit("SET_FORM_DATA", mergedData);
    },
    setDataDefaultsMapping({ commit }, mapping) {
        commit("SET_DATA_DEFAULTS_MAPPING", mapping);
    },
    setValidateForm({ commit }, { name, valid }) {
        commit("SET_VALIDATE_FORM", { formName: name, valid });
    },
    removeValidateForm({ commit }, name) {
        commit("REMOVE_VALIDATE_FORM", { formName: name });
    },
    handleResetValidateForm({ commit }) {
        commit("RESET_VALIDATE_FORM");
    },
    handleSetPanels({ commit }, payload) {
        commit("SET_PANELS", payload);
    },
    handleSetSubPanels({ commit }, payload) {
        commit("SET_SUB_PANELS", payload);
    },
};

export default {
    namespaced: true,
    state: initialState,
    getters,
    actions,
    mutations,
};
