import { make } from "vuex-pathify";
import loader from "Util/loader";
import api from "Util/api";
import { NOTIFICATION_ACTIONS } from "Components/InAppNotifications/constants";

// Constants
// const EXPIRY_TIME = 60 * 60 * 1000; // 60 min
const POLLING_INTERVAL = 30 * 1000; // 30 sec
const STORAGE_KEY = "notifications";

const initialState = {
    notifications: {
        data: [],
        loader: loader.defaultState(),
        pollingInstance: null,
    },
};

// Helper functions
// const getStoredNotifications = () => {
//     try {
//         return JSON.parse(sessionStorage.getItem(STORAGE_KEY)) || {};
//     } catch (error) {
//         console.error("Error parsing stored notifications:", error);
//         return {};
//     }
// };

// const storeNotifications = (notifications) => {
//     try {
//         sessionStorage.setItem(STORAGE_KEY, JSON.stringify(notifications));
//     } catch (error) {
//         console.error("Error storing notifications:", error);
//     }
// };

const mutations = {
    ...make.mutations(initialState),
    SET_NOTIFICATIONS_LOADER(state, value) {
        state.notifications.loader = value;
    },
    SET_NOTIFICATIONS_DATA(state, notificationItems) {
        state.notifications.data = notificationItems;

        // const storedNotifications = getStoredNotifications();
        // notificationItems.forEach((notification) => {
        //     if (!storedNotifications[notification.time]) {
        //         storedNotifications[notification.time] = notification.expiry;
        //     }
        // });
        // storeNotifications(storedNotifications);
    },
    SET_POLLING_INSTANCE(state, instance) {
        state.notifications.pollingInstance = instance;
    },
};

const actions = {
    ...make.actions(initialState),

    async fetchNotifications({ state, commit, dispatch }, { userId, dealerId }) {
        if (!dealerId || !userId) {
            console.error("Missing required parameters: dealerId or userId");
            commit("SET_NOTIFICATIONS_LOADER", loader.failed());
            return;
        }

        commit("SET_NOTIFICATIONS_LOADER", loader.started());

        // Clear existing polling interval if any
        if (state.notifications.pollingInstance) {
            clearInterval(state.notifications.pollingInstance);
        }

        const getNotifications = async () => {
            try {
                const response = await api.get(`/notifications`, { dealerId, userId });
                return response.data || [];
            } catch (error) {
                console.error("Error fetching notifications:", error);
                commit("SET_NOTIFICATIONS_LOADER", loader.failed());
                return [];
            }
        };

        // Initial fetch
        try {
            const notifications = await getNotifications();
            // const notificationsWithExpiry = notifications.map((notification) => ({
            //     ...notification,
            //     expiry: new Date().getTime() + EXPIRY_TIME,
            // }));
            commit("SET_NOTIFICATIONS_DATA", notifications);
            commit("SET_NOTIFICATIONS_LOADER", loader.successful());
        } catch (error) {
            console.error("Error in initial notifications fetch:", error);
            commit("SET_NOTIFICATIONS_LOADER", loader.failed());
        }

        // Setup polling
        const pollingInstance = setInterval(async () => {
            try {
                // await dispatch("removedExpiredNotifications");

                const notifications = await getNotifications();
                // const currentTime = new Date().getTime();

                const newNotifications = notifications.filter(
                    (notification) => !state.notifications.data.find((n) => n.time === notification.time)
                );
                // .map((notification) => ({
                //     ...notification,
                //     expiry: currentTime + EXPIRY_TIME,
                // }));

                if (newNotifications.length > 0) {
                    commit("SET_NOTIFICATIONS_DATA", [...state.notifications.data, ...newNotifications]);
                }
            } catch (error) {
                console.error("Error in notifications polling:", error);
            }
        }, POLLING_INTERVAL);

        commit("SET_POLLING_INSTANCE", pollingInstance);
    },

    async updateNotification({ commit }, { id, eventType, userId }) {
        if (!id || !eventType || !userId) {
            console.error("Missing required parameters for notification update");
            return null;
        }

        try {
            return await api.put(`/notifications`, {
                eventType,
                userId,
                time: id,
            });
        } catch (error) {
            console.error("Error updating notification:", error);
            return null;
        }
    },

    async removeNotifications({ commit, state, dispatch }, { id, eventType, userId }) {
        const existingNotifications = [...state.notifications.data]; // Backup current notifications

        // Optimistically remove the notification from the state
        const updatedNotifications = existingNotifications.filter((notification) => notification.time !== id);
        commit("SET_NOTIFICATIONS_DATA", updatedNotifications);

        const response = await dispatch("updateNotification", { id, eventType, userId });
        if (!response) {
            console.error("Error removing notification");

            // Roll back the state change if the API call fails
            commit("SET_NOTIFICATIONS_DATA", existingNotifications);
        }
    },

    removeAllNotifications({ commit, state }) {
        if (state.notifications.pollingInstance) {
            clearInterval(state.notifications.pollingInstance);
            commit("SET_POLLING_INSTANCE", null);
        }
        commit("SET_NOTIFICATIONS_DATA", []);
        // sessionStorage.removeItem(STORAGE_KEY);
    },

    // async removedExpiredNotifications({ commit, state, dispatch }) {
    //     const currentTime = new Date().getTime();
    //
    //     const { expiredNotifications, validNotifications } = state.notifications.data.reduce(
    //         (acc, notification) => {
    //             if (notification.expiry < currentTime) {
    //                 acc.expiredNotifications.push(notification);
    //             } else {
    //                 acc.validNotifications.push(notification);
    //             }
    //             return acc;
    //         },
    //         { expiredNotifications: [], validNotifications: [] }
    //     );
    //
    //     if (!expiredNotifications.length) return;
    //
    //     try {
    //         await Promise.allSettled(
    //             expiredNotifications.map((notification) =>
    //                 dispatch("updateNotification", {
    //                     id: notification.time,
    //                     userId: notification.userId,
    //                     eventType: NOTIFICATION_ACTIONS.DISPLAY_EXPIRED,
    //                 })
    //             )
    //         );
    //         commit("SET_NOTIFICATIONS_DATA", validNotifications);
    //     } catch (error) {
    //         console.error("Error removing expired notifications:", error);
    //     }
    // },
};

const getters = {};

export default {
    namespaced: true,
    state: initialState,
    getters,
    mutations,
    actions,
};
