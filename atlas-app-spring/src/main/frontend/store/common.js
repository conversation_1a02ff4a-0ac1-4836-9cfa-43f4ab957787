// Common store modules used across multiple entrypoints
import Vuex from "vuex";
import drawer from "@/store/drawer";
import loggedInUser from "@/store/loggedInUser";
import pageConfigs from "@/store/pageConfigs";
import inAppNotification from "@/store/inAppNotification";
import dealerStore from "Modules/Dealer/store/dealer";

// Function to get common store modules
export const getCommonStores = () => ({
    drawer,
    loggedInUser,
    pageConfigs,
    inAppNotification,
    dealerStore,
});

// Function to create a store with common modules and additional modules
export const createStore = (additionalModules = {}, plugins = []) => {
    const modules = {
        ...getCommonStores(),
        ...additionalModules,
    };

    const debug = process.env.NODE_ENV !== "production";

    return new Vuex.Store({
        plugins,
        modules,
        strict: debug,
    });
};
