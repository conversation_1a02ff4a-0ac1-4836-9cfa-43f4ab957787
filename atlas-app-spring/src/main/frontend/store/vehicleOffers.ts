import { dispatch, make } from "vuex-pathify";
import { Commit } from "vuex";
import api from "@/util/api.js";
import loader from "@/util/loader.js";
import lodashSet from "lodash/set";
import { sanitize } from "@/util/sanitize.js";
interface Loader {
    isLoading: boolean;
    isError: boolean;
    isComplete: boolean;
    errorMessages: string[] | string | null;
}
interface State {
    dealerId: string | null;
    tradeVehicleId: string | null;
    selectedUserId?: string | null;
    discrepancy: {
        data: Discrepancy | null;
        loader: Loader;
    };
    offer: {
        data: Offer | null;
        loader: Loader;
    };
    viewDiscrepancyModal: {
        isOpen: boolean;
    };
    editDiscrepancyModal: {
        isOpen: boolean;
    };
    editOfferModal: {
        isOpen: boolean;
    };
}
interface Offer {
    id: string;
    quoteAmount: number | string;
    valueTradeAmount?: number | string;
    expirationDate: string;
    userId: string;
    vin: string;
    vehicle: Vehicle;
}
interface Vehicle {
    id: string;
    imgUrl?: string;
    year: string | number;
    make: string;
    model: string;
    trim: string;
}
interface Discrepancy {
    id?: string;
    date?: string;
    description?: string;
    notes?: string;
}

interface payloadDiscrepancy extends Pick<Discrepancy, "date" | "description" | "notes"> {}

const state: State = {
    dealerId: null,
    tradeVehicleId: null,
    selectedUserId: null,
    offer: {
        data: null,
        loader: loader.defaultState(),
    },
    discrepancy: {
        data: {
            id: "",
            date: "",
            description: "",
            notes: "",
        },
        loader: loader.defaultState(),
    },
    viewDiscrepancyModal: {
        isOpen: false,
    },
    editDiscrepancyModal: {
        isOpen: false,
    },
    editOfferModal: {
        isOpen: false,
    },
};

const sanitizeContent = (content: string) => {
    if (!content) return "";
    return sanitize(content.trim());
};

export default {
    namespaced: true,
    state,
    mutations: {
        ...make.mutations(state),
        SET_DISCREPANCY_LOADER: (state: State, payload: Loader) => {
            lodashSet(state, "discrepancy.loader", payload);
        },
        SET_DISCREPANCY_DATA: (state: State, payload: Discrepancy) => {
            lodashSet(state, "discrepancy.data", payload);
        },
        SET_OFFER_DATA: (state: State, payload: Offer) => {
            lodashSet(state, "offer.data", payload);
        },
        SET_OFFER_LOADER: (state: State, payload: Loader) => {
            lodashSet(state, "offer.loader", payload);
        },
    },

    actions: {
        resetDiscrepancy({ commit }: { commit: Commit }) {
            commit("SET_DISCREPANCY_DATA", {
                id: "",
                date: "",
                description: "",
                notes: "",
            });
        },
        openEditDiscrepancyModal(
            { commit, dispatch }: { commit: Commit; dispatch: any },
            {
                dealerId,
                tradeVehicleId,
                discrepancy,
            }: { dealerId: string; tradeVehicleId: string; discrepancy: Discrepancy }
        ) {
            commit("SET_EDIT_DISCREPANCY_MODAL", { isOpen: true });
            if (discrepancy) {
                commit("SET_DISCREPANCY_DATA", discrepancy);
            } else {
                dispatch("resetDiscrepancy");
            } //reset discrepancy
            commit("SET_DEALER_ID", dealerId);
            commit("SET_TRADE_VEHICLE_ID", tradeVehicleId);
        },
        openAddDiscrepancyModal(
            { commit, dispatch }: { commit: Commit; dispatch: any },
            { dealerId, tradeVehicleId }: { dealerId: string; tradeVehicleId: string }
        ) {
            commit("SET_EDIT_DISCREPANCY_MODAL", { isOpen: true });
            commit("SET_DEALER_ID", dealerId);
            commit("SET_TRADE_VEHICLE_ID", tradeVehicleId);
            dispatch("resetDiscrepancy");
        },
        closeEditDiscrepancyModal({ commit, dispatch }: { commit: Commit; dispatch: any }) {
            commit("SET_EDIT_DISCREPANCY_MODAL", { isOpen: false });
            dispatch("resetDiscrepancy");
        },
        openEditOfferModal(
            { commit }: { commit: Commit },
            {
                dealerId,
                tradeVehicleId,
                selectedUserId,
                offer,
            }: { dealerId: string; tradeVehicleId: string; selectedUserId: string; offer: Offer }
        ) {
            commit("SET_EDIT_OFFER_MODAL", { isOpen: true });
            commit("SET_DEALER_ID", dealerId);
            commit("SET_TRADE_VEHICLE_ID", tradeVehicleId);
            commit("SET_OFFER_DATA", offer);
            commit("SET_SELECTED_USER_ID", selectedUserId);
        },
        closeEditOfferModal({ commit }: { commit: Commit }) {
            commit("SET_EDIT_OFFER_MODAL", { isOpen: false });
            commit("SET_OFFER_DATA", null);
        },
        openViewDiscrepancyModal(
            { commit }: { commit: Commit },
            {
                dealerId,
                tradeVehicleId,
                discrepancy,
            }: { dealerId: string; tradeVehicleId: string; discrepancy: Discrepancy }
        ) {
            commit("SET_VIEW_DISCREPANCY_MODAL", {
                isOpen: true,
            });
            commit("SET_DISCREPANCY_DATA", discrepancy);
            commit("SET_DEALER_ID", dealerId);
            commit("SET_TRADE_VEHICLE_ID", tradeVehicleId);
        },
        closeViewDiscrepancyModal({ commit }: { commit: Commit; dispatch: any }) {
            commit("SET_VIEW_DISCREPANCY_MODAL", {
                isOpen: false,
            });
        },
        deleteDiscrepancy(
            { commit, dispatch }: { commit: Commit; dispatch: any },
            { dealerId, tradeVehicleId }: { dealerId: string; tradeVehicleId: string }
        ) {
            if (!dealerId || !tradeVehicleId) {
                const error = new Error("Dealer ID and trade vehicle ID are required.");
                commit("SET_DISCREPANCY_LOADER", loader.error(error));
                return Promise.reject(error);
            }

            const apiEndpoint = `/dealer/${dealerId}/users/trades/${tradeVehicleId}/discrepancy`;
            commit("SET_DISCREPANCY_LOADER", loader.started());

            return api
                .delete(apiEndpoint)
                .then(() => {
                    commit("SET_DISCREPANCY_LOADER", loader.successful());
                    dispatch("resetDiscrepancy");
                })
                .catch((error: Error) => {
                    commit("SET_DISCREPANCY_LOADER", loader.error(error));
                    throw error; // Re-throw to handle in component
                });
        },
        saveDiscrepancy({ commit, state }: { commit: Commit; state: State }, discrepancy: Discrepancy) {
            if (
                !state.dealerId ||
                !state.tradeVehicleId ||
                !discrepancy.description ||
                !discrepancy.description.trim() ||
                !discrepancy.notes ||
                !discrepancy.notes.trim()
            ) {
                const error = new Error("Dealer ID, trade vehicle ID, description, and notes are required.");
                commit("SET_DISCREPANCY_LOADER", loader.error(error));
                return Promise.reject(error);
            }

            commit("SET_DISCREPANCY_LOADER", loader.started());

            const apiEndpoint = `/dealer/${state.dealerId}/users/trades/${state.tradeVehicleId}/discrepancy`;
            const payload = {
                ...state.discrepancy.data,
                description: sanitizeContent(discrepancy.description),
                notes: sanitizeContent(discrepancy.notes),
            };

            return api
                .patch(apiEndpoint, payload)
                .then((response: any) => {
                    console.log("Discrepancy saved successfully:", response);
                    commit("SET_DISCREPANCY_LOADER", loader.successful());
                    return response;
                })
                .catch((error: Error) => {
                    console.error("Error saving discrepancy:", error);
                    commit("SET_DISCREPANCY_LOADER", loader.error(error));
                    throw error; // Re-throw to handle in component
                });
        },
        saveOffer({ commit, state }: { commit: Commit; state: State }, offer: Offer): Promise<any> {
            if (!state.dealerId || !state.tradeVehicleId || !offer.quoteAmount || !offer.expirationDate) {
                const error = new Error("Dealer ID, trade vehicle ID, cash offer, and expiration date are required.");
                commit("SET_OFFER_LOADER", loader.error(error));
                return Promise.reject(error);
            }
            commit("SET_OFFER_LOADER", loader.started());

            const payload: any = {
                id: offer.id,
                dealerId: state.dealerId,
                userId: state.selectedUserId,
                expiration: offer.expirationDate,
                vin: offer.vin,
                quoteAmount: parseFloat(offer.quoteAmount.toString()),
            };

            return api
                .post(`/dealer/${state.dealerId}/vehicle/quote`, payload)
                .then((response: any) => {
                    commit("SET_OFFER_LOADER", loader.successful());
                    commit("SET_OFFER_DATA", null);
                    return response;
                })
                .catch((error: Error) => {
                    commit("SET_OFFER_LOADER", loader.error(error));
                    throw error;
                });
        },
    },
};
