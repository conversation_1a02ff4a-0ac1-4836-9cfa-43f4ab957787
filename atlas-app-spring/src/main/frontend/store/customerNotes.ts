import { make } from "vuex-pathify";
import api from "@/util/api.js";
import loader from "@/util/loader.js";
import { AxiosResponse } from "axios";
import { Note, NotePayload } from "../modules/Customers/components/CustomerNotes/types/note";
import { sanitize } from "Util/sanitize.js";

interface State {
    notes: Note[];
    loader: {
        isLoading: boolean;
        isError: boolean;
        errorMessage?: string;
    };
    noteModal: {
        isOpen: boolean;
        note: string | null;
        editMode: boolean;
    };
}

const state: State = {
    notes: [],
    loader: loader.defaultState(),
    noteModal: {
        isOpen: false,
        note: null,
        editMode: false,
    },
};

function sanitizeNoteContent(content: string | undefined | null): string {
    if (!content) {
        return "";
    }
    const trimmedContent = content.trim();
    return sanitize(trimmedContent);
}

const getters = {
    ...make.getters(state),
};

const mutations = {
    ...make.mutations(state),
    SET_LOADER(state: State, loaderState: typeof state.loader): void {
        state.loader = loaderState;
    },
    SET_NOTES(state: State, notes: Note[]): void {
        state.notes = notes;
    },
    ADD_NOTE(state: State, note: Note): void {
        state.notes.unshift(note);
    },
    UPDATE_NOTE(state: State, updatedNote: Note): void {
        const index = state.notes.findIndex((note) => note.id === updatedNote.id);
        if (index !== -1) {
            state.notes.splice(index, 1, updatedNote);
        }
    },
};

interface ActionContext {
    commit: (type: string, payload?: any) => void;
    state: State;
}

const actions = {
    openCreateNoteModal({ commit }: ActionContext): void {
        commit("SET_NOTE_MODAL", { isOpen: true, editMode: false, note: null });
    },
    openEditNoteModal({ commit }: ActionContext, note: Note): void {
        commit("SET_NOTE_MODAL", { isOpen: true, editMode: true, note });
    },
    closeNoteModal({ commit }: ActionContext): void {
        commit("SET_NOTE_MODAL", { isOpen: false, editMode: false, note: null });
    },
    async fetchNotes(
        { commit }: ActionContext,
        { userId, dealerId }: { userId: string; dealerId: string }
    ): Promise<void> {
        commit("SET_LOADER", loader.started());
        const params = { customerId: userId, dealerId };

        try {
            const response: AxiosResponse<Note[]> = await api.get("/user-note", params);
            commit("SET_NOTES", response.data);
            commit("SET_LOADER", loader.successful());
        } catch (error: any) {
            commit("SET_LOADER", loader.error(error));
            throw error;
        }
    },
    async saveNote(
        { commit }: ActionContext,
        note: Partial<Pick<Note, "id" | "content" | "dealerVisible" | "entityId" | "dealerId" | "userId" | "noteType">>
    ): Promise<void> {
        commit("SET_LOADER", loader.started());
        try {
            let response: AxiosResponse<Note>;

            note.content = sanitizeNoteContent(note.content);

            if (note.id) {
                // Update existing note
                response = await api.post(`/user-note/upsert`, note);
                commit("UPDATE_NOTE", response.data);
            } else {
                // Create new note
                response = await api.post("/user-note/upsert", note);
                commit("ADD_NOTE", response.data);
            }
            commit("SET_LOADER", loader.successful());
        } catch (error: any) {
            commit("SET_LOADER", loader.error(error));
            throw error;
        }
    },
};

export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions,
};
