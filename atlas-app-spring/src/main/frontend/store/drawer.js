import { make } from "vuex-pathify";

const initialState = {
    open: null,
    mini: false,
    menuVersionSelected: "v2", //temporary options: "v1", "v2"
};

const mutations = {
    ...make.mutations(initialState),
};

const getters = {
    ...make.getters(initialState),
};

const actions = {
    ...make.actions(initialState),
};

export default {
    namespaced: true,
    state: initialState,
    getters,
    actions,
    mutations,
};
