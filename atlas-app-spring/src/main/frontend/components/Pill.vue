<script lang="ts">
import { defineComponent, PropType } from "vue";

const PILL_COLORS = Object.freeze({
    lightBlue: { bg: "#01579B", text: "#FFF" },
    blue: { bg: "#1976D2", text: "#FFF" },
    blueGrey: { bg: "#546E7A", text: "#FFFFFF" },
    grey: { bg: "#EEE", text: "#212121" },
    info: { bg: "#2196F3", text: "#FFFFFF" },
    green: { bg: "#81C784", text: "#212121" },
    black: { bg: "#212121", text: "#FFFFFF" },
    teal: { bg: "#80CBC4", text: "#212121" },
    orange: { bg: "#FF9800", text: "#212121" },
});

export default defineComponent({
    name: "PillComponent",
    props: {
        prependIcon: {
            type: String,
            required: false,
            default: "",
        },
        color: {
            type: String as PropType<keyof typeof PILL_COLORS>,
            required: false,
            default: "grey",
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
    },
    data() {
        return {
            colors: PILL_COLORS,
        };
    },
    computed: {
        getBgColor() {
            return this.colors?.[this.color]?.bg || "#212121";
        },
        getTextColor() {
            return this.colors?.[this.color]?.text || "#EEE";
        },
    },
});
</script>

<template>
    <v-chip :color="getBgColor" :v-bind="$attrs" :text-color="getTextColor" pill class="custom-pill">
        <slot name="prependIcon">
            <v-icon v-if="prependIcon" left class="left-icon">
                {{ prependIcon }}
            </v-icon>
        </slot>
        <slot name="content"></slot>
        <v-tooltip v-if="hint" bottom max-width="450">
            <template #activator="{ on, attrs }">
                <v-icon small right v-bind="attrs" class="hint" v-on="on">mdi-information-outline</v-icon>
            </template>
            <span>{{ hint }}</span>
        </v-tooltip>
    </v-chip>
</template>

<style scoped lang="scss">
.custom-pill {
    width: fit-content;
    .left-icon {
        font-size: px2rem(20) !important;
        margin-left: 0 !important;
        margin-right: 8px !important;
    }
    .hint {
        font-size: px2rem(18) !important;
        margin-right: 0 !important;
    }
}
</style>
