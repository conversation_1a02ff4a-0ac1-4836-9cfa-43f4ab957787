<template>
    <div class="d-flex otto-chat-toggle-block ml-2">
        <v-switch v-model="isOttoChatbotEnabled"></v-switch>
        <div class="ml-2">Enable OTTO AI Chatbot</div>
    </div>
</template>
<script>
import lodashGet from "lodash/get";
import { call, get } from "vuex-pathify";

export default {
    name: "OttoChatbotToggle",
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    computed: {
        userId: get("loggedInUser/userId"),
        isOttoChatbotEnabled: {
            get() {
                let isOttoChatbotEnabled = lodashGet(this.program, "isOttoChatbotEnabled", false);
                return isOttoChatbotEnabled;
            },
            set(value) {
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "otto_chatbot",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
        programId() {
            return lodashGet(this.program, "programId", null);
        },
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
    },
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
    },
};
</script>
