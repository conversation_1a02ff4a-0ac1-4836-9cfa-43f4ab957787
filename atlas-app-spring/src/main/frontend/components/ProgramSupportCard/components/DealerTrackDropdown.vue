<template>
    <v-row class="py-4 mr-2">
        <v-col cols="auto" class="body-2"
            >LMS Preference
            <v-tooltip top max-width="250px">
                <template #activator="{ on, attrs }">
                    <v-icon v-attrs="attrs" small v-on="on">mdi-information-outline</v-icon>
                </template>
                <span>All NMAC applications are serviced through DealerTrack, irrespective of the LMS Preference.</span>
            </v-tooltip>
        </v-col>
        <div class="d-flex justify-space-between align-center ml-auto">
            <v-select
                v-model="selectedItem"
                placeholder="Select..."
                outlined
                dense
                hide-details
                :items="dropdownItems"
            />
        </div>
    </v-row>
</template>

<script>
import { call, get } from "vuex-pathify";
import lodashGet from "lodash/get";

export default {
    name: "DealerTrackDropdown",
    data() {
        return {
            dropdownItems: ["DealerTrack", "RouteOne"],
        };
    },
    computed: {
        preferences: get("dealerStore/<EMAIL>"),
        programs: get("dealerStore/programs"),
        selectedItem: {
            get() {
                const lmsPreference = lodashGet(this.preferences, "lmsPreference", "");
                return lmsPreference;
            },
            set(value) {
                this.storeLMSPreference({ lmsPreference: value });
            },
        },
    },
    methods: {
        storeLMSPreference: call("dealerStore/storeLenderManagementSystemPreference"),
    },
};
</script>

<style scoped></style>
