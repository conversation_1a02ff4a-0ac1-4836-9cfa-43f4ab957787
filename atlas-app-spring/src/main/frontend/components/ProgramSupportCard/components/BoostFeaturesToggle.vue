<template>
    <div>
        <v-divider>inset</v-divider>
        <v-row class="py-4">
            <v-col cols="auto" class="body-2">Boost+ Features</v-col>
            <div class="d-flex justify-space-between align-center ml-auto p-2 px-3">
                <v-switch v-model="isBoostFeaturesEnabled" :disabled="!isAdminUser"></v-switch>
            </div>
        </v-row>
    </div>
</template>
<script>
import lodashGet from "lodash/get";
import { call, get } from "vuex-pathify";

export default {
    name: "BoostFeaturesToggle",
    props: {
        program: {
            type: Object,
            required: true,
        },
    },
    computed: {
        userId: get("loggedInUser/userId"),
        isBoostFeaturesEnabled: {
            get() {
                let isBoostFeaturesEnabled = lodashGet(this.program, "isBoostFeaturesEnabled", false);
                return isBoostFeaturesEnabled;
            },
            set(value) {
                this.toggleDealerProgramFeature({
                    programId: this.programId,
                    configType: "boost_features",
                    isEnabled: value,
                    userId: this.userId,
                });
            },
        },
        programId() {
            return lodashGet(this.program, "programId", null);
        },
        isAdminUser() {
            return this.$acl.hasAuthority("ROLE_ADMIN");
        },
    },
    methods: {
        toggleDealerProgramFeature: call("dealerStore/toggleDealerProgramFeature"),
    },
};
</script>
