<template>
    <v-card class="fill-height mb-4 d-flex flex-column" :loading="isLoading">
        <v-card-title> Program Support </v-card-title>

        <v-card-text class="text--primary">
            <program v-for="(program, index) in programs" :key="index" :program="program" :dealer-id="dealerId" />

            <v-alert
                v-if="showDealerInfoMessage"
                class="mb-0 mt-2"
                border="top"
                colored-border
                type="info"
                elevation="2"
            >
                For assistance with updating dealership details please contact a Client Services team member.
            </v-alert>
        </v-card-text>
    </v-card>
</template>

<script>
import Program from "./components/Program";
import { call, get } from "vuex-pathify";

export default {
    name: "ProgramSupportCard",
    components: { Program },
    props: {
        showDealerInfoMessage: {
            type: Boolean,
            required: false,
            default: true,
        },
        dealerId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            dropdownItems: ["DealerTrack", "RouteOne"],
            selectedItem: "",
            isLoading: false,
        };
    },
    computed: {
        programs: get("dealerStore/programs"),
    },
    mounted() {
        this.onLoad();
    },
    methods: {
        onLoad() {
            this.fetchDealerPrograms(this.dealerId);
        },
        fetchDealerPrograms: call("dealerStore/fetchDealerPrograms"),
    },
};
</script>
