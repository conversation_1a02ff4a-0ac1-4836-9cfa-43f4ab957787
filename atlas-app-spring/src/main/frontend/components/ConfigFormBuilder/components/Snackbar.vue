<template>
    <v-snackbar :value="toast.show" :timeout="toast.timeout" :color="toast.type" right dark class="snackbar">
        <div class="snackbar-content">
            <v-icon v-if="toast.type === 'success'">mdi-check-circle-outline</v-icon>
            <v-icon v-else-if="toast.type === 'error'"> mdi-alert-circle-outline </v-icon>
            <span> {{ toast.message }} </span>
        </div>
        <template #action="{ attrs }">
            <v-btn dark text v-bind="attrs" icon @click="resetSnackbar">
                <v-icon small>mdi-close</v-icon>
            </v-btn>
        </template>
    </v-snackbar>
</template>

<script>
import { defineComponent } from "vue";
import { call, get } from "vuex-pathify";

export default defineComponent({
    name: "Snackbar",
    computed: {
        toast: get("pageConfigs/toast"),
    },
    methods: {
        saveFormData: call("pageConfigs/saveFormData"),
        resetSnackbar: call("pageConfigs/resetSnackbar"),
    },
});
</script>

<style scoped lang="scss">
.snackbar {
    .snackbar-content {
        display: flex;
        gap: 16px;
        align-items: center;
        color: white;
    }
}
</style>
