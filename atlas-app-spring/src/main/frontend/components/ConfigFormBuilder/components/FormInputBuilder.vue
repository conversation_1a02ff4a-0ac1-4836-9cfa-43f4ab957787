<template>
    <v-form :ref="formName" v-model="valid" :lazy-validation="false">
        <v-row class="align-baseline">
            <v-col v-for="input in inputList" :key="input.key" cols="12">
                <FormInput :component="componentData" :input="input" :form-name="formName" />
            </v-col>
        </v-row>
    </v-form>
</template>
<script>
import FormInput from "Components/ConfigFormBuilder/components/FormInput.vue";
import { call } from "vuex-pathify";
import { COMPONENT_MAPPING, FORMAT_COMPONENT_DATA } from "Util/ConfigManager/components";

export default {
    name: "FormInputBuilder",
    components: { FormInput },
    props: {
        context: {
            type: Object,
            required: true,
            default: () => ({}),
        },
    },
    data() {
        return {
            valid: true,
            uid: null,
        };
    },
    computed: {
        componentData() {
            return FORMAT_COMPONENT_DATA(this.context);
        },
        inputList() {
            return this.context.inputs;
        },
        formName() {
            return "FormInputBuilder" + this.uid;
        },
    },
    watch: {
        valid: {
            handler(isValid) {
                this.setValidateForm({ name: this.formName, valid: isValid });
            },
            immediate: true,
        },
    },
    created() {
        this.initForm();
    },
    beforeDestroy() {
        this.removeValidateForm({ name: this.formName });
    },
    methods: {
        setValidateForm: call("pageConfigs/setValidateForm"),
        removeValidateForm: call("pageConfigs/removeValidateForm"),
        initForm() {
            //create a unique id for the form
            this.uid = Math.random().toString(36).substring(2, 15);
        },
        type2Component(type) {
            return COMPONENT_MAPPING[type];
        },
    },
};
</script>

<style scoped lang="scss">
.input-component {
    min-width: 45%;
}
</style>
