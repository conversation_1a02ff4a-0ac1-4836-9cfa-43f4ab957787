<template>
    <InputField :id="id" :value.sync="input" :hint="hint" :label="label" type="number" @update="handleUpdate" />
</template>

<script>
import { defineComponent } from "vue";
import InputField from "Components/FormInputs/InputField.vue";

export default defineComponent({
    name: "InputMoney",
    components: { InputField },
    props: {
        id: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [String, Number, null],
            required: false,
            default: "",
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        range: {
            type: Object,
            required: false,
            default: () => ({}),
        },
    },
    data() {
        return {
            input: this.value,
        };
    },
    methods: {
        handleUpdate(payload) {
            if (payload.id === this.id) {
                this.input = payload.value;
            }
        },
    },
});
</script>
