<template>
    <InputField
        :id="id"
        :value.sync="input"
        :hint="hint"
        :label="label"
        :placeholder="placeholder"
        type="number"
        :disabled="disabled"
        :required="required"
        :rules="rules"
        @update="handleUpdate"
    />
</template>

<script>
import { defineComponent } from "vue";
import { call } from "vuex-pathify";
import InputField from "Components/FormInputs/InputField.vue";

export default defineComponent({
    name: "InputNumber",
    components: { InputField },
    props: {
        id: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [String, Number, null],
            required: false,
            default: "",
        },
        placeholder: {
            type: [String, Number, null],
            required: false,
            default: "",
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        range: {
            type: Object,
            required: false,
            default: () => ({}),
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        required: {
            type: Boolean,
            required: false,
            default: false,
        },
        rules: {
            type: Array,
            required: false,
            default: () => [],
        },
    },
    data() {
        return {
            input: this.value,
        };
    },
    watch: {
        input(newValue) {
            this.$emit("update", { id: this.id, value: newValue });
            this.updateFormData({ key: this.id, value: newValue });
        },
    },
    methods: {
        handleUpdate(payload) {
            if (payload.id === this.id) {
                this.input = payload.value;
            }
        },
        updateFormData: call("pageConfigs/updateFormData"),
    },
});
</script>
