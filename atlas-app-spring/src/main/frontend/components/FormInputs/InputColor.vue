<template>
    <div class="input-color">
        <v-menu v-model="showColorPicker" :close-on-content-click="false" :disabled="disabled" offset-y>
            <template #activator="{ on, attrs }">
                <div
                    class="color-tile"
                    :style="{ background: input }"
                    v-bind="attrs"
                    :class="{ clickable: !disabled }"
                    v-on="on"
                ></div>
            </template>
            <v-color-picker v-model="input" mode="hexa" hide-mode-switch flat @update:model-value="handleColorChange" />
        </v-menu>
        <InputField
            :id="id"
            class="input-tile"
            :value.sync="input"
            :format-rules="[colorFormat]"
            :placeholder="placeholder"
            :label="label"
            :disabled="disabled"
            :required="required"
            @update="handleUpdateInput"
        />
    </div>
</template>

<script>
import InputField from "./InputField";
import { defineComponent } from "vue";
import { validateHex } from "Util/ConfigManager/validations";
export default defineComponent({
    name: "InputColor",
    components: {
        InputField,
    },
    props: {
        id: {
            type: [String, Number, null],
            default: null,
        },
        label: {
            type: String,
            default: "",
        },
        placeholder: {
            type: String,
            default: "",
        },
        default: {
            type: String,
            default: "",
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        value: {
            type: String,
            default: "",
        },
        required: {
            type: Boolean,
            default: false,
        },
    },
    emits: ["change"],
    data() {
        return {
            input: "",
            showColorPicker: false,
            colorFormat: (value) =>
                validateHex(value) || "Invalid hex color code. Please provide a valid hex color code (e.g., #123abc).",
        };
    },
    watch: {
        value(newValue) {
            this.input = newValue;
        },
        input(newValue) {
            // Emit change when input changes
            this.$emit("change", newValue);
        },
    },
    mounted() {
        this.input = this.value;
    },
    methods: {
        handleUpdateInput({ id, value }) {
            if (id === this.id) {
                this.input = value;
            }
        },
        handleColorChange(color) {
            // Ensure the color is in HEX format
            let hexColor = color;
            if (typeof color === "object" && color.hex) {
                hexColor = color.hex;
            } else if (typeof color === "string") {
                // If it's already a string, ensure it starts with #
                hexColor = color.startsWith("#") ? color : `#${color}`;
            }
            this.input = hexColor;
        },
    },
});
</script>

<style lang="scss" scoped>
.input-color {
    display: flex;
    align-items: flex-start;
    flex-shrink: 0;
    gap: 8px;
    position: relative;
}
.color-tile {
    width: 40px;
    height: 40px;
    border: 1px solid $grey-300;
    flex-shrink: 0;
    border-radius: 4px;

    &.clickable {
        cursor: pointer;
        transition: border-color 0.2s ease;

        &:hover {
            border-color: darken($grey-300, 20%);
        }
    }
}
.input-tile {
    width: 100%;
}
</style>
