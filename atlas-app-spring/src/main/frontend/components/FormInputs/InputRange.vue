<template>
    <InputField
        :id="id"
        :value.sync="input"
        :format-rules="[rangeFormat]"
        :hint="hint"
        :label="label"
        type="number"
        persistent-hint
        @update="handleUpdateInput"
    />
</template>

<script>
import { defineComponent } from "vue";
import InputField from "Components/FormInputs/InputField.vue";
import { isNumberInRange } from "Util/ConfigManager/validations";

export default defineComponent({
    name: "InputRange",
    components: { InputField },
    props: {
        id: {
            type: [String, Number, null],
            default: null,
        },
        label: {
            type: String,
            default: "",
        },
        hint: {
            type: String,
            default: "",
        },
        range: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            input: "",
            rangeFormat: (value) =>
                isNumberInRange(value, this.range.start, this.range.end) ||
                `The number is not within the specified range
                (${this.range.start} to ${this.range.end}).`,
        };
    },
    methods: {
        handleUpdateInput({ id, value }) {
            if (id === this.id) {
                this.input = value;
            }
        },
    },
});
</script>

<style scoped lang="scss"></style>
