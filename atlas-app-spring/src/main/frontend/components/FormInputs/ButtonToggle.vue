<template>
    <v-btn-toggle v-model="input" :rules="rules" dense>
        <slot />
    </v-btn-toggle>
</template>

<script>
import { defineComponent } from "vue";
import { call } from "vuex-pathify";

export default defineComponent({
    name: "But<PERSON><PERSON>og<PERSON>",
    props: {
        id: {
            type: String,
            required: true,
        },
        value: {
            type: String,
            required: false,
            default: "",
        },
        rules: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            input: "",
        };
    },
    watch: {
        value: {
            handler(newValue) {
                this.input = newValue;
            },
            immediate: true,
        },
        input(newValue) {
            this.$emit("input", newValue); // For parent component v-model
            this.$emit("update", { id: this.id, value: newValue });
            this.updateFormData({ key: this.id, value: newValue });
        },
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
    },
});
</script>

<style lang="scss" scoped></style>
