<template>
    <v-radio-group v-model="selected" class="mt-0 ml-0" hide-details mandatory>
        <v-row>
            <v-col v-for="group in groups" :key="group[0].label" sm="6">
                <v-radio :label="group[0].label" :value="group[0].value"> </v-radio>
                <FormInput
                    class="mt-3"
                    :input="group[1]"
                    :disabled="selected !== group[0].value"
                    :required="selected === group[0].value"
                    :base-id="id"
                />
            </v-col>
        </v-row>
    </v-radio-group>
</template>
<script>
import { call, get } from "vuex-pathify";
import FormInput from "Components/ConfigFormBuilder/components/FormInput.vue";

export default {
    name: "GroupedToggle",
    components: {
        FormInput,
    },
    props: {
        id: {
            type: String,
            required: true,
        },
        baseId: {
            type: String,
            required: false,
            default: "",
        },
        groups: {
            type: Array,
            required: true,
        },
        default: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [Boolean, String],
            required: false,
            default: null,
        },
    },
    data() {
        return {
            selected: null,
            radioBtnPostfix: "/type",
        };
    },
    computed: {
        formDefaults: get("pageConfigs/form@defaults"),
        getDefaultIndex() {
            return this.groups.findIndex((group) => group[0].value === this.default) ?? null;
        },
    },
    watch: {
        selected(newValue) {
            this.$emit("input", newValue);
            this.updateFormData({ key: this.id + this.radioBtnPostfix, value: newValue });
        },
        getDefaultIndex: {
            handler(newValue) {
                if (newValue !== null) {
                    this.selected = newValue;
                }
            },
            immediate: true,
        },
    },
    created() {
        this.init();
    },
    methods: {
        updateFormData: call("pageConfigs/updateFormData"),
        init() {
            this.selected = this.value;
        },
    },
};
</script>
<style lang="scss" scoped>
.v-input {
    margin-left: 36px;
}
</style>
