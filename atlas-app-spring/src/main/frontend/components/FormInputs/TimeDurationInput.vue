<template>
    <InputField
        :id="id"
        :value="input"
        :format-rules="[inputFormat]"
        :placeholder="placeholder"
        :label="label"
        :disabled="disabled"
    />
</template>

<script>
import { defineComponent } from "vue";
import InputField from "Components/FormInputs/InputField.vue";

export default defineComponent({
    name: "TimeDurationInput",
    components: { InputField },
    props: {
        id: {
            type: [String, Number, null],
            required: false,
            default: null,
        },
        label: {
            type: String,
            required: false,
            default: "",
        },
        placeholder: {
            type: String,
            required: false,
            default: "",
        },
        default: {
            type: String,
            required: false,
            default: "",
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        required: {
            type: Boolean,
            required: false,
            default: false,
        },
        value: {
            type: String,
            required: false,
            default: "",
        },
    },
    data() {
        return {
            input: "",
            allowedPatterns: ["min"],
        };
    },
    watch: {
        value: {
            handler() {
                this.input = this.value;
            },
            immediate: true,
            deep: true,
        },
    },
    methods: {
        updateError(error) {
            console.log("error hello: ", error);
        },
        validateString(input) {
            const regex = new RegExp(`^\\d+\\s(${this.allowedPatterns.join("|")})$`);
            return regex.test(input);
        },
        inputFormat(value) {
            if (this.disabled) return true;
            return (
                this.validateString(value) ||
                `Invalid input format. Please enter a number followed by ${this.allowedPatterns.join("|")}.`
            );
        },
    },
});
</script>

<style scoped lang="scss"></style>
