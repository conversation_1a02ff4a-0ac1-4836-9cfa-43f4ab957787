<template>
    <v-checkbox
        v-if="label"
        v-model="input"
        :label="label"
        :disabled="disabled"
        :hint="hint || description"
        :persistent-hint="persistentHint"
        hide-details="auto"
        @change="handleChange"
    ></v-checkbox>
    <v-checkbox
        v-else
        v-model="input"
        :disabled="disabled"
        :hint="hint || description"
        :persistent-hint="persistentHint"
        hide-details="auto"
        @change="handleChange"
    >
        <template #label>
            <slot />
        </template>
    </v-checkbox>
</template>
<script>
export default {
    name: "Checkbox",
    props: {
        label: {
            type: String,
            required: false,
            default: "",
        },
        value: {
            type: [<PERSON><PERSON>an, String],
            required: false,
            default: true,
        },
        description: {
            type: String,
            required: false,
            default: "",
        },
        hint: {
            type: String,
            required: false,
            default: "",
        },
        persistentHint: {
            type: Boolean,
            required: false,
            default: false,
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
        valueText: {
            type: [<PERSON><PERSON><PERSON>, String],
            required: false,
            default: true,
        },
    },
    data() {
        return {
            input: this.value,
        };
    },
    watch: {
        value(newValue) {
            this.input = newValue;
        },
        input(newValue) {
            this.$emit("input", newValue);
        },
    },
    methods: {
        handleChange() {
            this.$emit("change", { label: this.label, state: this.input, value: this.valueText });
        },
    },
};
</script>
<style lang="scss" scoped></style>
