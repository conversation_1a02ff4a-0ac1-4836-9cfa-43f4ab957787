<template>
    <div class="rate-sheet-configurator">
        <div v-if="formData" class="fallback-rates-section">
            <div class="enable-section">
                <h4>Enable Fallback Rates:</h4>
                <BaseToggle
                    v-model="formData.enableFallbackRates"
                    label="Display Fallback Rates"
                    :disabled="disabled"
                    @input="updateSubmissionFormData"
                />
            </div>

            <v-slide-y-transition mode="out-in">
                <div v-show="formData.enableFallbackRates" class="fallback-content">
                    <hr class="section-divider" />

                    <h4>Define your credit tiers:</h4>
                    <v-row class="credit-tiers-row">
                        <v-col
                            v-for="(tier, index) in creditTierKeys"
                            :key="tier"
                            cols="12"
                            sm="6"
                            md="3"
                            class="credit-tier-col"
                        >
                            <div v-if="formData[tier]" class="credit-tier">
                                <h5>Credit Tier {{ 4 - index }}</h5>
                                <div class="tier-range">
                                    <div class="range-input">
                                        <BaseSelect
                                            v-model="formData[tier].min"
                                            :items="getCreditScoreMinOptions(tier)"
                                            label="Start"
                                            :placeholder="getDefaultTierValue(tier, 'min').toString()"
                                            :disabled="disabled"
                                            @input="updateSubmissionFormData"
                                        />
                                    </div>
                                    <div class="range-input">
                                        <BaseSelect
                                            v-model="formData[tier].max"
                                            :items="getCreditScoreMaxOptions(tier)"
                                            label="End"
                                            :placeholder="getDefaultTierValue(tier, 'max').toString()"
                                            :disabled="disabled"
                                            @input="updateSubmissionFormData"
                                        />
                                    </div>
                                </div>
                            </div>
                        </v-col>
                    </v-row>

                    <div v-if="formData.terms" class="terms-section">
                        <h4>Select your rates:</h4>
                        <div
                            v-for="(termData, termIndex) in formData.terms"
                            :key="'term' + termIndex"
                            class="term-configuration"
                        >
                            <div class="term-header">
                                <div class="term-selector">
                                    <BaseToggle
                                        v-model="termData.enable"
                                        :disabled="disabled"
                                        @input="updateSubmissionFormData"
                                    />
                                    <BaseSelect
                                        v-model="termData.term"
                                        class="term-select"
                                        :items="getAvailableTermsForIndex(termIndex)"
                                        label="Term"
                                        :disabled="disabled || !termData.enable"
                                        :hint="
                                            getAvailableTermsForIndex(termIndex).length === 0
                                                ? 'All terms are already selected'
                                                : ''
                                        "
                                        persistent-hint
                                        @input="updateSubmissionFormData"
                                    />
                                </div>
                            </div>

                            <div v-if="termData.rates" class="rate-buckets" :class="{ disabled: !termData.enable }">
                                <div
                                    v-for="(rateBucket, bucketIndex) in termData.rates"
                                    :key="bucketIndex"
                                    class="rate-bucket"
                                >
                                    <v-row class="vehicle-age-and-rates-row">
                                        <v-col class="py-0" cols="12">
                                            <h6 v-if="bucketIndex === 0" class="section-label my-0">Vehicle Age</h6>
                                        </v-col>

                                        <v-col cols="12" sm="6" md="4" class="age-input-col">
                                            <v-btn
                                                v-if="termData.rates.length > 1"
                                                class="remove-bucket-btn"
                                                :class="{ 'offset-top__40': bucketIndex === 0 }"
                                                icon
                                                small
                                                :disabled="disabled || !termData.enable"
                                                @click="removeRateBucket(termIndex, bucketIndex)"
                                            >
                                                <v-icon small>mdi-delete-outline</v-icon>
                                            </v-btn>
                                            <div v-if="rateBucket.ageRange" class="age-input-wrapper">
                                                <BaseSelect
                                                    v-model="rateBucket.ageRange.min"
                                                    :items="ageOptions"
                                                    label="Min"
                                                    :disabled="disabled || !termData.enable"
                                                    @input="onAgeRangeMinChange(termIndex, bucketIndex, $event)"
                                                />
                                                <BaseSelect
                                                    v-model="rateBucket.ageRange.max"
                                                    :items="ageOptions"
                                                    label="Max"
                                                    :disabled="disabled || !termData.enable"
                                                    @input="onAgeRangeMaxChange(termIndex, bucketIndex, $event)"
                                                />
                                            </div>
                                        </v-col>

                                        <!-- Interest Rates Section - All 4 tiers on same row -->
                                        <v-col
                                            v-for="(tier, tierIndex) in creditTierKeys"
                                            :key="tier"
                                            cols="12"
                                            sm="6"
                                            md="2"
                                            class="interest-rate-col"
                                        >
                                            <div class="interest-rate-input">
                                                <BaseInputNumber
                                                    v-model="rateBucket[getTierRateKey(tier)]"
                                                    label="Interest Rate"
                                                    placeholder="0.00"
                                                    :disabled="disabled || !termData.enable"
                                                    :min="0"
                                                    :max="100"
                                                    :step="0.01"
                                                    prepend-inner-icon="mdi-percent-outline"
                                                    @input="updateSubmissionFormData"
                                                />
                                                <small class="rate-description"
                                                    >Rate for Tier {{ 4 - tierIndex }} ({{ getTierRange(tier) }})</small
                                                >
                                            </div>
                                        </v-col>
                                        <v-col class="add-bucket-section" cols="12">
                                            <v-btn
                                                v-if="bucketIndex === termData.rates.length - 1"
                                                style="text-transform: none; letter-spacing: normal"
                                                text
                                                color="primary"
                                                :disabled="disabled || !termData.enable"
                                                @click="addRateBucket(termIndex)"
                                            >
                                                <v-icon left>mdi-plus-circle-outline</v-icon>
                                                Add another year bucket
                                            </v-btn>
                                        </v-col>
                                    </v-row>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </v-slide-y-transition>
        </div>
    </div>
</template>

<script>
import BaseToggle from "Components/FormInputs/BaseToggle.vue";
import BaseInputNumber from "Components/FormInputs/BaseInputNumber.vue";
import BaseSelect from "Components/FormInputs/BaseSelect.vue";
import { get, sync } from "vuex-pathify";

export default {
    name: "RateSheetConfigurator",
    components: {
        BaseToggle,
        BaseInputNumber,
        BaseSelect,
    },
    props: {
        value: {
            type: [Boolean, Object],
            required: false,
            default: false,
        },
        disabled: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            creditTierKeys: Object.freeze(["creditTierFour", "creditTierThree", "creditTierTwo", "creditTierOne"]),
            availableTerms: Object.freeze([
                { text: "36 months", value: 36 },
                { text: "48 months", value: 48 },
                { text: "60 months", value: 60 },
                { text: "66 months", value: 66 },
                { text: "72 months", value: 72 },
            ]),
            resetState: {
                enableFallbackRates: null,
                creditTierFour: { min: null, max: null },
                creditTierThree: { min: null, max: null },
                creditTierTwo: { min: null, max: null },
                creditTierOne: { min: null, max: null },
                terms: [
                    {
                        term: null,
                        enable: null,
                        order: 1,
                        rates: [
                            {
                                order: 1,
                                ageRange: { min: null, max: null },
                                tier1Rate: null,
                                tier2Rate: null,
                                tier3Rate: null,
                                tier4Rate: null,
                            },
                        ],
                    },
                    {
                        term: null,
                        enable: null,
                        order: 2,
                        rates: [
                            {
                                order: 1,
                                ageRange: { min: null, max: null },
                                tier1Rate: null,
                                tier2Rate: null,
                                tier3Rate: null,
                                tier4Rate: null,
                            },
                        ],
                    },
                    {
                        term: null,
                        enable: null,
                        order: 3,
                        rates: [
                            {
                                order: 1,
                                ageRange: { min: null, max: null },
                                tier1Rate: null,
                                tier2Rate: null,
                                tier3Rate: null,
                                tier4Rate: null,
                            },
                        ],
                    },
                    {
                        term: null,
                        enable: null,
                        order: 4,
                        rates: [
                            {
                                order: 1,
                                ageRange: { min: null, max: null },
                                tier1Rate: null,
                                tier2Rate: null,
                                tier3Rate: null,
                                tier4Rate: null,
                            },
                        ],
                    },
                    {
                        term: null,
                        enable: null,
                        order: 5,
                        rates: [
                            {
                                order: 1,
                                ageRange: { min: null, max: null },
                                tier1Rate: null,
                                tier2Rate: null,
                                tier3Rate: null,
                                tier4Rate: null,
                            },
                        ],
                    },
                ],
            },
            formData: null,
        };
    },
    computed: {
        savedValues: get("pageConfigs/form@defaults"),
        submissionFormData: sync("pageConfigs/form@data"),
        ageOptions() {
            const options = [
                "New",
                "0",
                "1",
                "2",
                "3",
                "4",
                "5",
                "6",
                "7",
                "8",
                "9",
                "10",
                "11",
                "12",
                "13",
                "14",
                "15+",
            ];

            return options.map((option) => ({ text: option, value: option }));
        },
        creditScoreOptions() {
            const options = [];
            // Generate credit score options from 300 to 850 in increments of 10
            for (let i = 300; i <= 850; i += 1) {
                options.push({ text: i.toString(), value: i });
            }
            return options;
        },
    },
    watch: {
        savedValues: {
            handler(newValue, oldValue) {
                // Skip the first call (when oldValue is undefined) since we handle that in mounted()
                if (oldValue === undefined) {
                    return;
                }

                // Prevent infinite loops by checking if savedValues actually changed
                if (newValue === oldValue) {
                    return;
                }

                // Simple hash comparison to detect real changes
                const newHash = JSON.stringify(newValue);
                const oldHash = JSON.stringify(oldValue);

                if (newHash === oldHash) {
                    return;
                }

                this.init();
            },
            immediate: false, // Don't trigger on initial load
        },
        formData: {
            handler(newValue) {
                // Emit input for parent component
                this.$emit("input", newValue);

                // Only validate if formData is not null
                if (newValue) {
                    // Validate and clear invalid range values
                    this.validateCreditTierRanges();
                    // Validate term selections to prevent duplicates
                    this.validateTermSelections();
                }
            },
            deep: true,
        },
        value: {
            handler(newValue) {
                if (newValue && typeof newValue === "object" && this.formData) {
                    this.formData = { ...this.formData, ...newValue };
                }
            },
            immediate: true,
        },
    },
    mounted() {
        // Initialize once on mount
        this.init();
    },
    methods: {
        init() {
            // Always start with resetState as the base (all values nulled out)
            this.formData = JSON.parse(JSON.stringify(this.resetState));

            // Normalize and merge savedValues with formData
            if (this.savedValues && typeof this.savedValues === "object") {
                const normalizedSavedValues = this.normalizeSavedValues(this.savedValues);
                this.formData = { ...this.formData, ...normalizedSavedValues };
            }
        },
        normalizeSavedValues(savedValues) {
            // Start with a deep clone of resetState to ensure all properties exist
            const normalized = JSON.parse(JSON.stringify(this.resetState));

            // Merge savedValues on top, but handle null values properly
            if (savedValues && typeof savedValues === "object") {
                // Handle top-level properties
                Object.keys(savedValues).forEach((key) => {
                    const savedValue = savedValues[key];

                    if (savedValue === null) {
                        // Keep the resetState value for null savedValues
                        // normalized[key] already has the resetState value
                        return;
                    }

                    if (key === "terms" && Array.isArray(savedValue)) {
                        // Handle terms array specially
                        normalized.terms = this.normalizeTermsArray(savedValue);
                    } else if (this.creditTierKeys.includes(key) && typeof savedValue === "object") {
                        // Handle credit tier objects
                        normalized[key] = this.normalizeCreditTier(savedValue, this.resetState[key]);
                    } else {
                        // For other properties, use savedValue directly
                        normalized[key] = savedValue;
                    }
                });
            }

            return normalized;
        },

        normalizeTermsArray(savedTerms) {
            // Ensure we always have exactly 5 terms
            const normalizedTerms = [];

            for (let i = 0; i < 5; i++) {
                const savedTerm = savedTerms[i];
                const resetTerm = this.resetState.terms[i];

                if (!savedTerm || savedTerm === null) {
                    // Use resetState term if savedTerm is null/undefined
                    normalizedTerms.push(JSON.parse(JSON.stringify(resetTerm)));
                } else {
                    // Merge savedTerm with resetTerm, replacing null values
                    normalizedTerms.push(this.normalizeTermObject(savedTerm, resetTerm));
                }
            }

            return normalizedTerms;
        },

        normalizeTermObject(savedTerm, resetTerm) {
            const normalized = { ...resetTerm };

            // Handle each property of the term
            Object.keys(savedTerm).forEach((key) => {
                const savedValue = savedTerm[key];

                if (savedValue === null || savedValue === undefined) {
                    // Keep resetState value for null/undefined
                    return;
                }

                if (key === "rates" && Array.isArray(savedValue)) {
                    // Handle rates array
                    normalized.rates = savedValue.map((savedRate) => {
                        const resetRate = resetTerm.rates[0]; // Use first rate as template
                        return this.normalizeRateObject(savedRate, resetRate);
                    });
                } else {
                    normalized[key] = savedValue;
                }
            });

            return normalized;
        },

        normalizeRateObject(savedRate, resetRate) {
            const normalized = { ...resetRate };

            if (savedRate && typeof savedRate === "object") {
                Object.keys(savedRate).forEach((key) => {
                    const savedValue = savedRate[key];

                    if (savedValue === null || savedValue === undefined) {
                        // Keep resetState value
                        return;
                    }

                    if (key === "ageRange" && typeof savedValue === "object") {
                        // Handle ageRange object
                        normalized.ageRange = {
                            min:
                                savedValue.min !== null && savedValue.min !== undefined
                                    ? savedValue.min
                                    : resetRate.ageRange.min,
                            max:
                                savedValue.max !== null && savedValue.max !== undefined
                                    ? savedValue.max
                                    : resetRate.ageRange.max,
                        };
                    } else {
                        normalized[key] = savedValue;
                    }
                });
            }

            return normalized;
        },

        normalizeCreditTier(savedTier, resetTier) {
            if (!savedTier || typeof savedTier !== "object") {
                return { ...resetTier };
            }

            return {
                min: savedTier.min !== null && savedTier.min !== undefined ? savedTier.min : resetTier.min,
                max: savedTier.max !== null && savedTier.max !== undefined ? savedTier.max : resetTier.max,
            };
        },

        updateSubmissionFormData() {
            // This method is called to update the submission form data
            if (!this.formData) {
                console.warn("updateSubmissionFormData called but formData is null");
                return null;
            }

            // Create a deep clone to prevent external mutations
            const clonedData = JSON.parse(JSON.stringify(this.formData));

            // Only update if the data has actually changed
            const currentHash = JSON.stringify(this.submissionFormData);
            const newHash = JSON.stringify(clonedData);

            if (currentHash !== newHash) {
                this.submissionFormData = clonedData;
            }

            // Return the cloned data for external use
            return clonedData;
        },
        getAvailableTermsForIndex(termIndex) {
            if (!this.formData || !this.formData.terms) return this.availableTerms;
            const currentTermValue = this.formData.terms[termIndex]?.term;

            // Filter out terms that are already selected by other term configurations
            return this.availableTerms.filter((termOption) => {
                // Always include the current term value for this specific selector
                if (termOption.value === currentTermValue) {
                    return true;
                }

                // Exclude terms that are selected by other term configurations (excluding null values)
                const isSelectedElsewhere = this.formData.terms.some((term, index) => {
                    return (
                        index !== termIndex &&
                        term.term !== null &&
                        term.term !== undefined &&
                        term.term === termOption.value
                    );
                });

                return !isSelectedElsewhere;
            });
        },
        getCreditScoreMinOptions(tier) {
            if (!this.formData) return this.creditScoreOptions;
            const maxValue = this.formData[tier]?.max;
            if (!maxValue) return this.creditScoreOptions;

            // Filter options to only show values less than the max value
            return this.creditScoreOptions.filter((option) => option.value < maxValue);
        },
        getCreditScoreMaxOptions(tier) {
            if (!this.formData) return this.creditScoreOptions;
            const minValue = this.formData[tier]?.min;
            if (!minValue) return this.creditScoreOptions;

            // Filter options to only show values greater than the min value
            return this.creditScoreOptions.filter((option) => option.value > minValue);
        },
        getDefaultTierValue(tier, type) {
            const defaults = {
                creditTierFour: { min: 620, max: 649 },
                creditTierThree: { min: 650, max: 679 },
                creditTierTwo: { min: 680, max: 739 },
                creditTierOne: { min: 740, max: 850 },
            };
            return defaults[tier]?.[type] || null;
        },
        getTierRateKey(tier) {
            const mapping = {
                creditTierFour: "tier4Rate",
                creditTierThree: "tier3Rate",
                creditTierTwo: "tier2Rate",
                creditTierOne: "tier1Rate",
            };
            return mapping[tier];
        },
        getTierRange(tier) {
            const maxValue = 850;

            if (!this.formData) {
                const defaultMin = this.getDefaultTierValue(tier, "min");
                const defaultMax = this.getDefaultTierValue(tier, "max");
                return `${defaultMin}-${defaultMax}`;
            }
            const min = this.formData[tier]?.min || this.getDefaultTierValue(tier, "min");
            const max = this.formData[tier]?.max || this.getDefaultTierValue(tier, "max");

            if (parseInt(max) === maxValue) {
                return `${min}+`;
            }

            return `${min}-${max}`;
        },
        addRateBucket(termIndex) {
            if (!this.formData || !this.formData.terms || !this.formData.terms[termIndex]) return;
            const newBucket = {
                order: this.formData.terms[termIndex].rates.length + 1,
                ageRange: { min: null, max: null },
                tier1Rate: null,
                tier2Rate: null,
                tier3Rate: null,
                tier4Rate: null,
            };
            this.formData.terms[termIndex].rates.push(newBucket);
            this.updateSubmissionFormData();
        },
        removeRateBucket(termIndex, bucketIndex) {
            if (!this.formData || !this.formData.terms || !this.formData.terms[termIndex]) return;
            if (this.formData.terms[termIndex].rates.length > 1) {
                this.formData.terms[termIndex].rates.splice(bucketIndex, 1);
                // Update order numbers for remaining buckets
                this.formData.terms[termIndex].rates.forEach((bucket, index) => {
                    bucket.order = index + 1;
                });
                this.updateSubmissionFormData();
            }
        },
        validateCreditTierRanges() {
            if (!this.formData) return;
            this.creditTierKeys.forEach((tier) => {
                const tierData = this.formData[tier];
                if (tierData && tierData.min !== null && tierData.max !== null) {
                    // If min is greater than or equal to max, clear the invalid value
                    if (tierData.min >= tierData.max) {
                        // Clear the max value to force user to select a valid one
                        tierData.max = null;
                    }
                }
            });
        },
        validateTermSelections() {
            if (!this.formData || !this.formData.terms) return;
            // Track seen term values to detect duplicates (excluding null values)
            const seenTerms = new Set();
            const duplicateIndices = [];

            this.formData.terms.forEach((term, index) => {
                if (term.term !== null && term.term !== undefined) {
                    if (seenTerms.has(term.term)) {
                        // Mark this as a duplicate
                        duplicateIndices.push(index);
                    } else {
                        seenTerms.add(term.term);
                    }
                }
            });

            // Clear duplicate term values (keep the first occurrence)
            duplicateIndices.forEach((index) => {
                // Find the first available term that's not already selected
                const availableTerms = this.getAvailableTermsForIndex(index);
                if (availableTerms.length > 0) {
                    // Set to the first available term, or null if none available
                    this.formData.terms[index].term = availableTerms[0]?.value || null;
                } else {
                    this.formData.terms[index].term = null;
                }
            });
        },
        onAgeRangeMinChange(termIndex, bucketIndex, value) {
            if (this.formData && this.formData.terms && this.formData.terms[termIndex]) {
                const rateBucket = this.formData.terms[termIndex].rates[bucketIndex];
                if (rateBucket && rateBucket.ageRange) {
                    // If "New" is selected for min, automatically set max to "New" as well
                    if (value === "New") {
                        rateBucket.ageRange.max = "New";
                    }
                    // If changing from "New" to any other value, set max to match
                    else if (rateBucket.ageRange.max === "New") {
                        rateBucket.ageRange.max = value;
                    }
                }
            }

            this.updateSubmissionFormData();
        },
        onAgeRangeMaxChange(termIndex, bucketIndex, value) {
            if (this.formData && this.formData.terms && this.formData.terms[termIndex]) {
                const rateBucket = this.formData.terms[termIndex].rates[bucketIndex];
                if (rateBucket && rateBucket.ageRange) {
                    // If "New" is selected for max, automatically set min to "New" as well
                    if (value === "New") {
                        rateBucket.ageRange.min = "New";
                    }
                    // If changing from "New" to any other value, set min to match
                    else if (rateBucket.ageRange.min === "New") {
                        rateBucket.ageRange.min = value;
                    }
                }
            }

            this.updateSubmissionFormData();
        },
    },
};
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";

.interest-rate-col,
.age-input-col {
    padding-bottom: 0px !important;
}

.remove-bucket-btn {
    position: absolute;
    right: -27px;
    top: 16px;

    &.offset-top__40 {
        top: 40px !important;
    }

    @media #{map-get($display-breakpoints, 'md-and-down')} {
        right: 0px;
        top: -20px;

        &.offset-top__40 {
            top: 0px !important;
        }
    }
}

h4 {
    font-size: px2rem(14);
    font-weight: 600;
    color: #333;
}
.rate-sheet-configurator {
    padding: 0;
}

.fallback-rates-section {
    h4 {
        margin-bottom: 8px;
    }

    .section-description {
        font-size: 14px;
        color: #666;
        margin-bottom: 20px;
    }
}

.enable-section {
    padding: 0;
}

.fallback-content {
    h4 {
        margin: 24px 0;
    }
}

.credit-tiers-row {
    margin-bottom: 24px !important;

    .credit-tier-col {
        .credit-tier {
            h5 {
                font-size: px2rem(12) !important;
                font-weight: 700 !important;
                color: #616161;
                margin-bottom: 18px !important;
            }

            .tier-range {
                display: flex !important;
                gap: 12px !important;

                .range-input {
                    flex: 1;
                }
            }
        }
    }
}

.terms-section {
    h4 {
        margin-bottom: 12px;
    }

    .term-configuration {
        margin-bottom: 32px !important;

        .term-header {
            margin-bottom: 20px !important;

            .term-selector {
                display: flex !important;
                align-items: center !important;

                .term-select {
                    max-width: 182px !important;
                }
            }
        }

        .rate-buckets {
            &.disabled {
                opacity: 0.6;
                pointer-events: none;
            }

            .rate-bucket {
                margin-bottom: 0px !important;
                padding: 0px 56px !important;

                @media #{map-get($display-breakpoints, 'md-and-down')} {
                    margin-bottom: 0px !important;
                    padding: 0px !important;
                }

                .rate-bucket-header {
                    display: flex !important;
                    justify-content: flex-end !important;
                    align-items: center !important;
                    margin-bottom: 8px !important;
                }

                .vehicle-age-and-rates-row {
                    position: relative;

                    .section-label {
                        font-size: 12px !important;
                        font-weight: 600 !important;
                        color: #495057 !important;
                        min-height: 20px !important;
                    }

                    .age-input-col {
                        .age-input-wrapper {
                            display: flex !important;
                            gap: 8px !important;
                        }
                    }

                    .interest-rate-col {
                        .interest-rate-input {
                            .rate-description {
                                display: block !important;
                                font-size: px2rem(12) !important;
                                margin-top: 6px !important;
                                line-height: 1.3 !important;
                                color: #616161 !important;
                            }
                        }
                    }
                }
            }

            .add-bucket-section {
                display: flex;
                justify-content: flex-start;
                padding-top: 0px !important;
                max-width: 100%;

                button {
                    font-size: px2rem(16);
                    font-weight: normal;
                    padding: 0;
                }
                @media #{map-get($display-breakpoints, 'sm-and-down')} {
                    margin-bottom: 16px !important;
                }
            }
        }
    }
}

// Enhanced responsive design
@media #{map-get($display-breakpoints, 'md-and-down') } {
    .age-range {
        flex-direction: column !important;
    }

    .term-selector {
        align-items: flex-start !important;
        max-width: none !important;
    }
}

@media #{map-get($display-breakpoints, 'sm-and-down')} {
    .rate-sheet-configurator {
        padding: 0;
    }
}

.section-divider {
    background: #bdbdbd;
    height: 1px;
    border: none;
    margin: 24px 0;
}
</style>
<style lang="scss">
.interest-rate-input {
    .v-icon.mdi-percent-outline {
        font-size: 20px !important;
    }
}
</style>
