<template>
    <v-card class="mx-auto pa-2" elevation="1">
        <v-card-title class="d-flex justify-space-between">
            <h2 class="title">
                <template v-if="title">
                    {{ title }}
                </template>
                <template v-else>
                    <slot name="title" />
                </template>
            </h2>
            <v-switch v-model="expandAll" label="Expand All" inset hide-details class="mt-0 pt-0"></v-switch>
        </v-card-title>
        <v-card-text>
            <slot name="body" />
        </v-card-text>
    </v-card>
</template>
<script>
import { sync } from "vuex-pathify";
export default {
    name: "CollapsibleControllerWrapper",
    props: {
        title: {
            type: String,
            required: false,
            default: "",
        },
    },
    data: () => ({
        show: false,
    }),
    computed: {
        expandAll: sync("pageConfigs/expandAll"),
    },
    created() {},
    methods: {},
};
</script>
<style lang="scss" scoped></style>
