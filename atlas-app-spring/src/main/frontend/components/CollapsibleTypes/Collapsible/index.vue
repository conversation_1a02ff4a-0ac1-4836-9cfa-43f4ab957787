<template>
    <v-expansion-panel class="no-box-shadow no-border collapsible">
        <v-expansion-panel-header v-slot="{ open }" class="grey lighten-4 pa-6 align-start">
            <div class="header-wrapper mr-3">
                <div class="title d-flex justify-space-between">
                    <template v-if="title"> {{ title }} </template>
                    <template v-else>
                        <slot name="title" />
                    </template>
                    <expand-all-btn v-if="expandAllEnabled" :selected-sub-group="title" :open="open" />
                </div>

                <div class="description">
                    <template v-if="description">{{ description }}</template>
                    <template v-else>
                        <slot name="description" />
                    </template>
                </div>
            </div>
        </v-expansion-panel-header>
        <v-expansion-panel-content color="grey lighten-4">
            <slot name="body" />
        </v-expansion-panel-content>
    </v-expansion-panel>
</template>

<script>
import { get } from "vuex-pathify";
import ExpandAllBtn from "./components/ExpandAllBtn.vue";
export default {
    name: "Collapsible",
    components: {
        ExpandAllBtn,
    },
    props: {
        title: {
            type: String,
            required: false,
            default: "",
        },
        description: {
            type: String,
            required: false,
            default: "",
        },
        expandAllEnabled: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            expandPanels: false,
        };
    },
    computed: {
        expandAll: get("pageConfigs/expandAll"),
        subPanels: get("pageConfigs/subPanels"),
    },
    watch: {
        expandAll: {
            handler(value) {
                this.expandPanels = value;
            },
            immediate: true,
        },
        expandPanels: {
            handler(value) {
                this.$emit("update:expandAll", value);
            },
        },
    },
};
</script>

<style scoped lang="scss">
.expandbtn {
    z-index: 9;
}
.header-wrapper {
}
.title {
    font-size: px2rem(20) !important;
    line-height: 1.6 !important;
}
.description {
    font-size: px2rem(14) !important;
    line-height: 1.4 !important;
}
.no-box-shadow,
.no-box-shadow::before {
    box-shadow: none !important;
}
.no-border,
.no-border::after {
    border: none !important;
}
</style>
<style lang="scss">
.collapsible {
    .v-expansion-panel-header__icon {
        margin-top: 0 !important;
    }
}
</style>
