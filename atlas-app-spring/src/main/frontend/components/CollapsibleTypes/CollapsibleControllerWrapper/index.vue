<template>
    <v-card class="mx-auto pa-2 collapsible-controller-wrapper" elevation="1">
        <v-card-title class="d-flex justify-space-between">
            <h2 class="title">
                <template v-if="title">
                    {{ title }}
                </template>
                <template v-else>
                    <slot name="title" />
                </template>
            </h2>
            <ExpandAllBtn v-if="showExpandAll" />
        </v-card-title>
        <v-card-text>
            <slot name="body" />
        </v-card-text>
    </v-card>
</template>
<script>
import { get, sync } from "vuex-pathify";
import ExpandAllBtn from "Components/CollapsibleTypes/Collapsible/components/ExpandAllBtn.vue";
export default {
    name: "CollapsibleControllerWrapper",
    components: { ExpandAllBtn },
    props: {
        title: {
            type: String,
            required: false,
            default: "",
        },
        noExpand: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    data() {
        return {
            defaultExpanded: true,
        };
    },
    computed: {
        expandAll: sync("pageConfigs/expandAll"),
        builderData: get("pageConfigs/pageBuilderData"),
        loaderComplete: get("pageConfigs/loader@isComplete"),
        showExpandAll() {
            return !this.noExpand;
        },
    },
    watch: {
        loaderComplete: {
            handler(val) {
                if (val) {
                    this.expandAll = this.defaultExpanded;
                }
            },
        },
    },
    methods: {
        init() {
            this.expandAll = true;
        },
    },
};
</script>
<style lang="scss">
.collapsible-controller-wrapper {
    .v-input__control {
        .v-text-field__details {
            padding: 0 !important;
        }
    }
}
</style>
