<script lang="ts">
import { defineComponent, PropType } from "vue";

export default defineComponent({
    name: "<PERSON>ert<PERSON>",
    props: {
        mode: {
            type: String as PropType<"success" | "error">,
            required: true,
        },
        message: {
            type: String,
            required: true,
        },
    },
    computed: {
        modeClass() {
            return `custom-${this.mode}`;
        },
        modeIconAndColor() {
            const obj = {
                success: {
                    icon: "mdi-check-circle-outline",
                    color: "#0F5132",
                },
                error: {
                    icon: "mdi-alert",
                    color: "#FF5252",
                },
            };

            return obj[this.mode];
        },
    },
});
</script>

<template>
    <v-alert dense text :class="`new-custom-alert ${modeClass}`">
        <v-icon :color="modeIconAndColor.color">{{ modeIconAndColor.icon }}</v-icon>
        {{ message }}
    </v-alert>
</template>

<style lang="scss">
.new-custom-alert {
    display: flex;
    justify-content: center;
    font-size: px2rem(13);
    font-weight: 500;
    padding: 16px;
    .v-alert__content {
        display: flex;
        align-items: center;
        gap: 8px;
    }
}
.new-custom-alert.custom-error {
    border: 1px solid #ff5252 !important;
    background: rgba(255, 82, 82, 0.06) !important;
    color: #ff5252 !important;
}
.new-custom-alert.custom-success {
    border: 1px solid #75b798 !important;
    background: #d1e7dd !important;
    color: #0f5132 !important;
}
</style>
