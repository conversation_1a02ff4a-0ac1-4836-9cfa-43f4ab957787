<template>
    <div class="px-sm-0 mx-auto">
        <v-skeleton-loader v-if="isLoading" type="table" />
        <div v-else-if="isDigitalRetail">
            <digital-retail :deal-model="dealModel" />
        </div>
        <div v-else id="confirmation-page" class="d-flex flex-column">
            <div class="d-none d-sm-block color-block" />
            <div class="d-flex flex-column content">
                <deal
                    v-if="deal"
                    :deal="deal"
                    :trade="trade"
                    :order="orderStatus"
                    :stock-type="vehicleStockyType"
                    :active="dealModel.vehicle.active"
                    class="mb-3"
                />
                <v-alert v-else color="warning" type="info" show outlined>
                    <strong>NOTE:</strong> The customer never proceeded to generate a deal.
                </v-alert>

                <div v-if="trade">
                    <trade-in
                        v-if="trade.vehicleQuoteId"
                        :user-vehicle-id="trade.userVehicleId"
                        class="mb-3"
                        :dealer-id="dealerId"
                        :active="dealModel.vehicle.active"
                        @quote-updated="refreshDeal"
                    />

                    <trade-in-zero-value
                        v-else
                        :dealer-id="dealerId"
                        :trade-vehicle-id="dealModel.trade.userVehicleId"
                        :initial-discrepancy="dealModel.trade.discrepancy"
                        :vehicle="dealModel.trade"
                        :vin="dealModel.trade.vin"
                        :mileage="dealModel.trade.mileage"
                    />
                </div>

                <remote-delivery-info :delivery="remoteDelivery" class="mb-3" />

                <purchased-vehicle :inventory-id="dealModel.inventoryId" class="mb-3" />
            </div>
        </div>
    </div>
</template>

<script>
import TradeIn from "./components/TradeIn";
import TradeInZeroValue from "./components/TradeInZeroValue";
import PurchasedVehicle from "./components/PurchasedVehicle";
import Deal from "./components/Deal";

import api from "Util/api";
import * as _ from "lodash";
import RemoteDeliveryInfo from "Components/Deal/components/RemoteDeliveryInfo.vue";
import DigitalRetail from "Components/Deal/components/DigitalRetail/index.vue";

export default {
    components: {
        Deal,
        PurchasedVehicle,
        TradeIn,
        TradeInZeroValue,
        RemoteDeliveryInfo,
        DigitalRetail,
    },
    props: {
        dealerId: {
            type: String,
            required: true,
        },

        dealId: {
            required: true,
            type: Number,
        },
    },
    data() {
        return {
            dealModel: {},
            isLoading: true,
        };
    },
    computed: {
        isDigitalRetail() {
            return _.get(this.dealModel, "source", "") === "digital-retail";
        },
        deal() {
            return _.get(this.dealModel, "deal", {});
        },
        orderStatus() {
            return _.get(this.dealModel, "orderStatus", {});
        },
        trade() {
            return _.get(this.dealModel, "trade");
        },
        vehicleStockyType() {
            return _.get(this.dealModel, "vehicle.stockType");
        },
        remoteDelivery() {
            return _.get(this.dealModel, "remoteDelivery", null);
        },
    },
    created() {
        this.fetchDealModel(this.dealId);
    },
    methods: {
        refreshDeal() {
            this.fetchDealModel(this.dealId);
        },
        fetchDealModel(dealId) {
            api.get(`/deal/${dealId}`)
                .then((response) => {
                    this.dealModel = _.get(response, "data", null);
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
    },
};
</script>

<style lang="scss">
#confirmation-page {
    .color-block {
        height: 4px;
        background-color: $grey-200;
    }
    .content {
        padding: 20px 23px 25px 23px;

        .deal-name-block {
            .deal-name {
                color: $grey;
                font-size: 24px;
                line-height: 29px;
            }
        }
        .customer-info-block {
            border: 1px solid #d3d3d3;
            border-radius: 2px;
            background-color: #ffffff;

            .customer-name {
                font-size: 14px;
            }
            .applicant-type {
                font-size: 12px;
                font-style: oblique;
            }
            .customer-address {
                width: 210px;
                .address {
                    color: $grey;
                    font-size: 14px;
                }
            }
            .qr-code {
                height: 110px;
            }
        }
    }
}
</style>
