<template>
    <v-card outlined class="mb-8">
        <v-card-title>Additional Fee or Tax</v-card-title>
        <v-card-text>
            <v-form id="dealerFeeEdit" ref="feeEditorForm" v-model="feeEditorValid" lazy-validation>
                <v-text-field
                    v-model="fee.name"
                    label="Name"
                    class="value"
                    required
                    outlined
                    :rules="feeValidationRules.name"
                >
                    <template #label> Name </template>
                </v-text-field>
                <v-text-field
                    v-model="fee.description"
                    label="Description"
                    class="value"
                    required
                    outlined
                    :rules="feeValidationRules.description"
                />
                <v-text-field
                    v-model="fee.amount"
                    label="Amount"
                    class="value"
                    required
                    outlined
                    prefix="$"
                    :rules="feeValidationRules.amount"
                />
            </v-form>
        </v-card-text>
        <v-card-actions>
            <v-btn @click="saveFee"> Save </v-btn>
            <v-btn text @click="cancelEditing"> Cancel </v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import api from "Util/api";

export default {
    name: "DealerFeeEditor",
    props: {
        initialFee: {
            type: Object,
            required: false,
            default: function () {
                return {
                    name: "",
                    description: "",
                    amount: null,
                };
            },
        },
    },
    data() {
        return {
            fee: { ...this.initialFee },
            feeEditorValid: true,
            feeValidationRules: {
                description: [
                    (v) => !!v || "Description is required",
                    (v) => v.length <= 50 || "Maximum of 250 characters",
                ],
                name: [(v) => !!v || "Name is required", (v) => v.length <= 50 || "Maximum of 50 characters"],
                amount: [(v) => !!v || "Amount is required", (v) => v >= 0 || "Amount must be more than 0"],
            },
        };
    },
    methods: {
        cancelEditing() {
            this.$emit("cancelled");
        },
        saveFee() {
            const valid = this.$refs.feeEditorForm.validate();
            if (valid) {
                this.$emit("saved", this.fee);
            }
        },
    },
};
</script>

<style lang="scss">
#dealerFeeEdit {
    .v-text-field--outlined .v-label {
        top: 6px;
    }
}
</style>
