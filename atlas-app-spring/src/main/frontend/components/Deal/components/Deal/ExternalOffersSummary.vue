<template>
    <div class="d-flex justify-space-between align-center">
        <div class="label">
            Customer Rebates
            <info-tooltip v-if="tooltipDescription">{{ tooltipDescription }}</info-tooltip>
        </div>
        <div class="value">
            -
            <span>{{ totalCustomerRebate | numeral("$0,0.00") }}</span>
        </div>
    </div>
</template>

<script>
import _ from "lodash";
import InfoTooltip from "Components/InfoTooltip";

export default {
    name: "ExternalOffersSummary",
    components: { InfoTooltip },
    props: {
        externalOffers: {
            type: Object,
            require: true,
            default: () => {},
        },
    },
    computed: {
        tooltipDescription() {
            const lineItems = _.get(this.externalOffers, "lineItems", []);
            const lineItemDescriptions = _.map(lineItems, "description");

            return _.join(lineItemDescriptions, ", ");
        },
        totalCustomerRebate() {
            return _.get(this.externalOffers, "totalAmount", 0);
        },
    },
};
</script>
