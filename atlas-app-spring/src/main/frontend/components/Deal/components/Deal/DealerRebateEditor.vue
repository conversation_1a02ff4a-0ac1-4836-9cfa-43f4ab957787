<template>
    <v-card outlined class="mb-8">
        <v-card-title>Additional Rebate</v-card-title>
        <v-card-text>
            <v-form id="dealerRebateEdit" ref="rebateEditorForm" v-model="rebateEditorValid" lazy-validation>
                <v-text-field
                    v-model="rebate.name"
                    label="Name"
                    class="value"
                    required
                    outlined
                    :rules="rebateValidationRules.name"
                >
                    <template #label> Name </template>
                </v-text-field>
                <v-text-field
                    v-model="rebate.amount"
                    label="Amount"
                    class="value"
                    required
                    outlined
                    prefix="- $"
                    :rules="rebateValidationRules.amount"
                />
            </v-form>
        </v-card-text>
        <v-card-actions>
            <v-btn @click="saveRebate"> Save </v-btn>
            <v-btn text @click="cancelEditing"> Cancel </v-btn>
        </v-card-actions>
    </v-card>
</template>

<script>
import api from "Util/api";

export default {
    name: "DealerRebateEditor",
    props: {
        initialRebate: {
            type: Object,
            required: false,
            default: function () {
                return {
                    name: "",
                    amount: null,
                };
            },
        },
    },
    data() {
        return {
            rebate: { ...this.initialRebate },
            rebateEditorValid: true,
            rebateValidationRules: {
                name: [(v) => !!v || "Name is required", (v) => v.length <= 50 || "Maximum of 50 characters"],
                amount: [(v) => !!v || "Amount is required", (v) => v >= 0 || "Amount must be more than 0"],
            },
        };
    },
    methods: {
        cancelEditing() {
            this.$emit("cancelled");
        },
        saveRebate() {
            const valid = this.$refs.rebateEditorForm.validate();
            if (valid) {
                this.$emit("saved", this.rebate);
            }
        },
    },
};
</script>

<style lang="scss">
#dealerRebateEdit {
    .v-text-field--outlined .v-label {
        top: 6px;
    }
}
</style>
