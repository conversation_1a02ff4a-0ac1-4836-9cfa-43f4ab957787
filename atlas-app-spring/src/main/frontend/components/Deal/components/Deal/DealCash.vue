<template>
    <v-skeleton-loader v-if="isLoading" type="table"></v-skeleton-loader>
    <div v-else id="deal">
        <div class="pa-3 p-md-4">
            <v-row class="mb-3" :loading="isLoading">
                <v-col cols="12">
                    <div class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Deal Type</div>
                        <div class="value">
                            {{ selectedDeal.dealType }}
                        </div>
                    </div>
                    <div v-if="showMsrpBlock">
                        <div class="d-flex justify-space-between align-center mb-1">
                            <div class="label">MSRP</div>
                            <div class="value">
                                {{ selectedDeal.msrp | numeral("$0,0.00") }}
                            </div>
                        </div>

                        <discount-line-item :discount="selectedDeal.discount"></discount-line-item>
                        <div class="divider" />
                    </div>
                    <div class="sale-line-item d-flex justify-space-between align-baseline mb-3">
                        <div class="label">Sale Price</div>
                        <div v-if="editingEnabled" class="value">
                            <v-text-field
                                v-model="selectedDeal.salePrice"
                                prefix="$"
                                required
                                dense
                                outlined
                                :rules="[rules.required, rules.validAmount]"
                            />
                        </div>
                        <div v-else class="value">
                            {{ selectedDeal.salePrice | numeral("$0,0.00") }}
                        </div>
                    </div>

                    <div class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Rebates</div>
                        <div class="value">- {{ selectedDeal.rebates | numeral("$0,0.00") }}</div>
                    </div>

                    <dealer-rebates
                        :dealer-rebates="selectedDeal.dealerRebates"
                        :stock-type="stockType"
                        :editing-enabled="editingEnabled"
                        @addDealerRebate="addDealerRebate"
                        @removeDealerRebate="removeRebate"
                    />
                    <external-offers-summary class="mb-1" :external-offers="externalOffers" />

                    <div v-if="hasReservationDeposit" class="d-flex justify-space-between align-baseline mb-1">
                        <div class="label">Reservation Deposit</div>
                        <div class="value">- {{ reservationDeposit | numeral("$0,0") }}</div>
                    </div>

                    <deal-selected-accessories-details :selected-accessories-deal-details="selectedAccessories" />
                    <selected-protection-products :protection-products="selectedProtectionProducts" />

                    <div class="divider" />
                    <div class="sale-line-item d-flex justify-space-between align-center mb-3">
                        <div class="label font-weight-bold">Purchase Price</div>
                        <div class="value font-weight-bold">
                            {{ adjustedPurchasePrice | numeral("$0,0.00") }}
                        </div>
                    </div>

                    <dealer-fees
                        :dealer-fees="selectedDeal.dealerFees"
                        :stock-type="stockType"
                        :editing-enabled="editingEnabled"
                        @addDealerFee="addDealerFee"
                        @removeDealerFee="removeFee"
                    />

                    <div class="d-flex justify-space-between align-baseline mb-1">
                        <div class="label">License/Registration</div>
                        <div class="value">+ {{ selectedDeal.registration | numeral("$0,0.00") }}</div>
                    </div>

                    <div class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Tax</div>
                        <div class="value">+ {{ selectedDeal.tax | numeral("$0,0.00") }}</div>
                    </div>

                    <div v-if="selectedDeal.netCashOffer" class="d-flex justify-space-between align-center mb-1">
                        <div v-if="selectedDeal.netCashOffer >= 0" class="label">Trade-in Allowance</div>
                        <div v-else class="label">Trade-in Payoff</div>

                        <div v-if="selectedDeal.netCashOffer >= 0" class="value">
                            -
                            {{ selectedDeal.netCashOffer | abs | numeral("$0,0.00") }}
                        </div>
                        <div v-else class="value">
                            +
                            {{ selectedDeal.netCashOffer | abs | numeral("$0,0.00") }}
                        </div>
                    </div>

                    <div v-if="selectedDeal.cashBack" class="d-flex justify-space-between align-center mb-1">
                        <div class="label">Cash back</div>
                        <div class="value">+ {{ selectedDeal.cashBack | numeral("$0,0.00") }}</div>
                    </div>

                    <div class="divider" />

                    <div class="d-flex justify-space-between align-center mb-3">
                        <div class="label max font-weight-bold">Total</div>
                        <div class="label max font-weight-bold">
                            {{ adjustedTotal | numeral("$0,0.00") }}
                        </div>
                    </div>
                    <div class="d-flex justify-space-between align-baseline mt-16 pl-4">
                        <consumer-link v-if="editingEnabled" :link-value="selectedDeal.dealURL" />
                    </div>
                </v-col>
            </v-row>
            <v-row v-if="editingEnabled">
                <v-col cols="12">
                    <v-btn class="mr-4" :disabled="!dealChanged" @click="saveDeal"> Update Deal </v-btn>
                    <v-btn @click="resetDeal"> reset </v-btn>
                </v-col>
            </v-row>
            <v-row>
                <v-col cols="12">
                    <div class="disclosure">
                        Sale price subject to change without notice.
                        <span v-if="tradeExpirationDate">
                            Trade offer good through
                            {{ tradeExpirationDate }}.
                        </span>
                        Final terms subject to approval by the dealership.
                    </div>
                </v-col>
            </v-row>
        </div>
    </div>
</template>
<script>
import api from "@/util/api";
import _ from "lodash";
import ExternalOffersSummary from "Components/Deal/components/Deal/ExternalOffersSummary";
import DealerFees from "Components/Deal/components/Deal/DealerFees";
import DiscountLineItem from "Components/Deal/components/Deal/DiscountLineItem";
import DealerRebates from "Components/Deal/components/Deal/DealerRebates";
import EventBus from "Util/eventBus";
import formRules from "Util/formRules";
import ConsumerLink from "Components/Deal/components/Deal/ConsumerLink";
import DealSelectedAccessoriesDetails from "./DealSelectedAccessoriesDetails.vue";
import SelectedProtectionProducts from "Components/Deal/components/Deal/SelectedProtectionProducts.vue";

export default {
    name: "DealCash",
    components: {
        SelectedProtectionProducts,
        DealSelectedAccessoriesDetails,
        ExternalOffersSummary,
        DealerFees,
        DiscountLineItem,
        DealerRebates,
        ConsumerLink,
    },
    props: {
        deal: {
            type: Object,
            required: true,
        },
        stockType: {
            type: String,
            required: true,
        },
        selectedAccessories: {
            type: Object,
            required: false,
            default: null,
        },
        selectedProtectionProducts: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            expirationData: null,
            selectedDeal: null,
            dealChanged: false,
            editingEnabled: true,
            isLoading: false,
            rules: formRules,
        };
    },
    computed: {
        showMsrpBlock() {
            return this.stockType === "NEW";
        },
        showCashDealDiscount() {
            const discountAlreadyShowing = this.showMsrpBlock;
            return !discountAlreadyShowing;
        },
        tradeExpirationDate() {
            return _.get(this.expirationData, "tradeExpirationDate", null);
        },
        externalOffers() {
            return _.get(this.deal, "externalOffers", null);
        },
        dealId() {
            return !_.get(this.selectedDeal, "certificateId")
                ? _.get(this.deal, "certificateId")
                : _.get(this.selectedDeal, "certificateId");
        },
        dealerId() {
            return this.$route.params.dealerId;
        },
        selectedAccessoriesTotal() {
            return _.get(this.selectedAccessories, "total", 0) || 0;
        },
        adjustedPurchasePrice() {
            let purchasePrice = _.get(this.selectedDeal, "purchasePrice", 0) || 0;
            let adjustedPurchasePrice = purchasePrice + this.selectedAccessoriesTotal;

            return adjustedPurchasePrice;
        },
        adjustedTotal() {
            let total = _.get(this.selectedDeal, "total", 0) || 0;
            let adjustedTotal = total + this.selectedAccessoriesTotal;

            return adjustedTotal;
        },
        reservationDeposit() {
            return _.get(this.deal, "reservationDeposit", 0) || 0;
        },
        hasReservationDeposit() {
            return this.reservationDeposit > 0;
        },
    },
    watch: {
        selectedDeal: {
            deep: true,
            handler(val) {
                this.dealChanged = !_.isEqual(val, this.deal);
            },
        },
    },
    created() {
        this.fetchExpirationData();
        this.cloneSelectedDeal();
        this.setFeatures();
        if (!this.selectedDeal.modifiedByDealer) {
            this.editingEnabled = false;
        }
    },
    methods: {
        setFeatures() {
            // if (isEnabled('DealEditing')) {
            //     this.editingEnabled = true;
            // }
        },
        saveDeal() {
            this.updateDeal();
        },
        resetDeal() {
            if (this.dealChanged) {
                this.cloneSelectedDeal();
            }
        },
        cloneSelectedDeal() {
            this.selectedDeal = _.cloneDeep(this.deal);
        },
        fetchExpirationData() {
            return api
                .get(`/deal/${this.dealId}/expiration`)
                .then((response) => {
                    this.expirationData = response.data;
                })
                .catch((error) => {
                    console.error(error);
                });
        },
        updateDeal() {
            this.isLoading = true;
            return api
                .patch(`/deal/${this.dealId}`, this.selectedDeal)
                .then((response) => {
                    this.selectedDeal = response.data.deal;
                    EventBus.$emit("refresh-deals");
                    this.$emit("open", this.dealId);
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        addDealerFee(fee) {
            console.log(fee);
            this.selectedDeal.dealerFees.push(fee);
        },
        removeFee(fee) {
            this.selectedDeal.dealerFees.splice(this.selectedDeal.dealerFees.indexOf(fee), 1);
        },
        addDealerRebate(fee) {
            this.selectedDeal.dealerRebates.push(fee);
        },
        removeRebate(fee) {
            this.selectedDeal.dealerRebates.splice(this.selectedDeal.dealerRebates.indexOf(fee), 1);
        },
    },
};
</script>
<style lang="scss">
#deal {
    .v-text-field__slot {
        input {
            text-align: right;
        }
    }
    .value .v-input__slot {
        min-height: 32px;
    }
    .shade-box {
        background-color: $gray-200;
        .disclosure {
            color: $gray-700;
            font-size: 10px;
            font-style: oblique;
            line-height: 12px;
        }
    }
    .title {
        color: $gray-800;
        font-size: 20px;
    }
    .label {
        color: $gray-800;
        font-size: 14px;
    }
    .label.max {
        font-size: 16px;
    }
    .value {
        color: $gray-600;
        font-size: 14px;
    }
    .sale-line-item {
        margin-bottom: px2rem(35);
    }
    .divider {
        height: 1px;
        width: 100%;
        background-color: $gray-300;
        margin: px2rem(2) 0 px2rem(10) 0;
    }
    .disclosure {
        color: $gray-600;
        font-size: 12px;
        font-style: oblique;
    }
}
</style>
