<template>
    <section-template v-if="vehicle" :add-section="true">
        <template #section-title> Vehicle Being Purchased </template>
        <template #section-content>
            <v-skeleton-loader v-if="loading" type="table" />
            <div v-else class="d-flex flex-column flex-md-row align-center pa-3 pa-md-4">
                <div class="w-100">
                    <in-transit-flag v-if="vehicle.inTransit" :vehicle="vehicle" />

                    <div class="purchase-ymm font-weight-bold mb-2">
                        {{ vehicle.year }} {{ vehicle.make }}
                        {{ vehicle.model }}
                    </div>
                    <div class="grey--text mb-2">
                        {{ vehicle.trim }}
                    </div>

                    <div class="d-flex flex-column flex-md-row justify-md-space-between">
                        <div class="d-flex flex-column">
                            <div class="mb-1">
                                <span class="value"> Exterior: {{ vehicle.exteriorColor }} </span>
                            </div>
                            <div class="mb-1">
                                <span class="value"> Interior: {{ vehicle.interiorColor }} </span>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <div class="mb-1">
                                <span class="value"> VIN: {{ vehicle.vin }} </span>
                            </div>
                            <div>
                                <span v-if="vehicle.inTransit" class="value red--text">
                                    Stock Number: N/A (In-Transit)
                                </span>
                                <span v-else class="value"> Stock Number: {{ vehicle.stockNumber }} </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #additional-section-content>
            <v-skeleton-loader v-if="loading" type="table" />
            <div v-else class="pa-3 pa-md-4">
                <icons-block :vehicle="vehicle" />

                <v-divider />
                <h3 class="font-weight-bold mb-3">Installed Options</h3>

                <div class="installed-options-block">
                    <ul class="list-horizontal mb-0">
                        <li v-for="(option, index) in vehicle.options" :key="index" class="mr-2">
                            {{ option }}
                        </li>
                    </ul>
                </div>
            </div>
        </template>
    </section-template>
</template>
<script>
import SectionTemplate from "./SectionTemplate";
import api from "@/util/api";
import IconsBlock from "./IconsBlock";
import _ from "lodash";
import InTransitFlag from "Components/InTransitFlag";

export default {
    name: "PurchasedVehicle",
    components: {
        InTransitFlag,
        IconsBlock,
        SectionTemplate,
    },
    props: {
        inventoryId: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            loading: true,
            vehicle: null,
        };
    },
    computed: {
        dealerPrimaryImageUrl() {
            return _.get(this.vehicle, "dealerPrimaryImageUrl", null);
        },
        stockImageUrl() {
            return _.get(this.vehicle, "stockImageUrl", null);
        },
        bodyStyle() {
            const bodyStyle = _.get(this.vehicle, "bodyStyle", null);
            return _.isNil(bodyStyle) ? "sedan" : bodyStyle;
        },
    },
    created() {
        this.fetchVehicleDetails();
    },
    methods: {
        fetchVehicleDetails() {
            return api
                .get(`/vehicles/${this.inventoryId}`)
                .then((response) => {
                    this.vehicle = response.data;
                    this.loading = false;
                })
                .catch((error) => {
                    console.error("error =", error);
                    this.loading = false;
                });
        },
    },
};
</script>
<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.purchase-img-wrapper {
    height: 103px;
    width: 152px;
    img {
        width: 100%;
        height: 100%;
    }
}
.w-100 {
    width: 100%;
}
.purchase-ymm,
.purchase-trim {
    font-size: 20px;
}
.value {
    font-size: 14px;
}

.installed-options-block {
    .list-horizontal {
        padding-left: px2rem(28);
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;

        li {
            text-transform: uppercase;
            margin-bottom: px2rem(8);
            color: $grey;
            font-size: px2rem(12);
            line-height: px2rem(15);
        }
    }

    @media #{map-get($display-breakpoints, 'md-and-up')} {
        .list-horizontal {
            -webkit-column-count: 4;
            -moz-column-count: 4;
            column-count: 4;
        }
    }
}
</style>
