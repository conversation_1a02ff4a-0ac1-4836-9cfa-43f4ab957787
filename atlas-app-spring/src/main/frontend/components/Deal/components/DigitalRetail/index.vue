<template>
    <div>
        <my-deal :deal-model="dealModel" />
        <my-trade v-if="hasTradeInfo" :trade-info="tradeInfo" />
    </div>
</template>

<script>
import MyDeal from "Components/Deal/components/DigitalRetail/MyDeal.vue";
import MyTrade from "Components/Deal/components/DigitalRetail/MyTrade.vue";
import lodashGet from "lodash/get";

export default {
    name: "DigitalRetail",
    components: { MyDeal, MyTrade },
    props: {
        dealModel: {
            type: Object,
            required: true,
        },
    },
    computed: {
        tradeInfo() {
            return lodashGet(this.dealModel, "trade", {});
        },
        hasTradeInfo() {
            return lodashGet(this.dealModel, "tradeExists", false) || false;
        },
    },
};
</script>
