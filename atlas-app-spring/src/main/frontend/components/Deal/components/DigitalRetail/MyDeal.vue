<template>
    <v-expansion-panels v-model="topPanel" multiple class="mb-2" :value="1">
        <v-expansion-panel :key="11" class="border bg-white border-radius-small certificate-card">
            <v-expansion-panel-header class="card-title">
                <div class="d-flex justify-space-between">
                    <span class="font-weight-bold font-xl">My Deal</span>
                </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content class="pt-2 fw-400 fs-14">
                <div class="">
                    <div class="d-flex flex-column">
                        <div class="basic-details d-flex flex-column mb-20">
                            <div class="vehicle-details p-2 p-md-0 mb-2">
                                <div class="car-img-row">
                                    <vehicle-image :src="vehicleInfo.imageUrl" class="car-img" />
                                    <div class="d-flex flex-column gap-2">
                                        <div class="d-flex flex-column mb-2">
                                            <p class="mt-1 lh-40">
                                                {{ vehicleInfo.stockType }}
                                            </p>
                                            <p class="font-large fw-bold text-primary lh-40">
                                                {{ vehicleInfo.year }} {{ vehicleInfo.make }}
                                                {{ vehicleInfo.model }}
                                            </p>
                                            <p class="lh-40">
                                                {{ vehicleInfo.trim }}
                                            </p>
                                        </div>
                                        <div class="lh-0">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <strong>MSRP:</strong>
                                                    {{ vehicleInfo.msrp | numeral("$0,0.00") }}
                                                </div>
                                                <div class="col-md-4"><strong>VIN:</strong> {{ vehicleInfo.vin }}</div>
                                                <div class="col-md-4">
                                                    <strong>Stock #:</strong> {{ vehicleInfo.stockNumber }}
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>Miles:</strong> {{ vehicleInfo.miles }}
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>Exterior:</strong> {{ vehicleInfo.exteriorColor }}
                                                </div>
                                                <div class="col-md-4">
                                                    <strong>Interior:</strong> {{ vehicleInfo.interiorColor }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Show div if finance application is there -->
                            <div
                                v-if="dealInfo.applicationNumber"
                                class="application-details p-2 p-md-0 d-flex flex-column gap-12px"
                            >
                                <div>
                                    <p class="m-0 fst-italic text-primary lh-40">Based on finance programs from:</p>
                                    <h4 class="fw-bold text-secondary overflow-ellipsis font-xl">
                                        {{ dealInfo.financierName }}
                                    </h4>
                                </div>
                                <div>
                                    <ul class="list-unstyled text-secondary d-flex flex-column gap-1">
                                        <li><strong>Status:</strong> {{ dealInfo.status }}</li>
                                        <li><strong>Finance Type:</strong> {{ dealInfo.dealType }}</li>
                                        <li><strong>Application Type:</strong> {{ dealInfo.applicationType }}</li>
                                        <li>
                                            <strong>Pre-qualification Date:</strong>
                                            {{ dealInfo.preQualificationDate | formatDate }}
                                        </li>
                                        <li><strong>Application Number:</strong> {{ dealInfo.applicationNumber }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="hasSummaryDetail" class="offer-details mb-1 mt-10 mb-md-0">
                        <h1 class="mb-2">Offer Details</h1>
                        <div class="d-flex flex-column gap-1">
                            <v-expansion-panels v-model="insidePanel" multiple>
                                <v-expansion-panel
                                    v-for="row in summaryDetail"
                                    :key="row.title"
                                    class="sections text-primary"
                                >
                                    <v-expansion-panel-header class="card-title">
                                        <div class="d-flex justify-space-between">
                                            <span class="font-weight-bold">{{ row.title }}</span>
                                            <span class="font-weight-bold">{{ row.value }}</span>
                                        </div>
                                    </v-expansion-panel-header>
                                    <v-expansion-panel-content class="pt-2 fw-400 fs-14">
                                        <div v-if="row.details">
                                            <div
                                                v-for="child in row.details"
                                                :key="child.lineItem"
                                                class="d-flex justify-space-between align-baseline mb-3"
                                            >
                                                <div class="label">{{ child.lineItem }}</div>
                                                <div class="value">{{ child.value }}</div>
                                            </div>
                                        </div>
                                        <div v-if="row.subSection">
                                            <div v-for="child in row.subSection" :key="child.title">
                                                <div class="label ml-4 mb-3 font-large font-weight-bold">
                                                    {{ child.title }}
                                                    <span class="word-divider"></span>
                                                </div>
                                                <div
                                                    v-for="(item, key) in child.details"
                                                    :key="key + 'item'"
                                                    class="d-flex justify-space-between align-baseline mb-3"
                                                >
                                                    <div class="label">{{ item.lineItem }}</div>
                                                    <div class="value">{{ item.value }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </v-expansion-panel-content>
                                </v-expansion-panel>
                            </v-expansion-panels>
                        </div>
                    </div>
                </div>
            </v-expansion-panel-content>
        </v-expansion-panel>
    </v-expansion-panels>
</template>

<script>
import * as _ from "lodash";
import moment from "moment/moment";
import VehicleImage from "Components/VehicleImage.vue";

export default {
    name: "MyDeal",
    components: { VehicleImage },
    filters: {
        formatDate(val) {
            const formattedDate = val ? moment(val).format("MM/DD/YYYY") : "";
            return formattedDate;
        },
    },
    props: {
        dealModel: {
            type: Object,
            required: true,
        },
    },
    data: () => ({
        insidePanel: [0, 1, 2, 3, 4, 5, 6],
        topPanel: [0],
        disabled: false,
    }),
    computed: {
        dealInfo() {
            return _.get(this.dealModel, "deal", {});
        },
        vehicleInfo() {
            return _.get(this.dealModel, "vehicle", {});
        },
        summaryDetail() {
            const summary = _.get(this.dealInfo, "summaryDetail", {});
            return _.get(summary, "sections", {});
        },
        hasSummaryDetail() {
            const hasVal = this.summaryDetail && this.summaryDetail.length > 0;
            return hasVal;
        },
    },
};
</script>

<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.word-divider {
    position: relative;
}

.word-divider::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 100%;
    margin-left: 10px;
    border-top: 1px solid #e0e0e0;
    width: 90px;
}

.basic-details {
    gap: 1.25rem;
    border-radius: 0.75rem;
    .car-img {
        max-width: 150px;
    }
    .vehicle-details {
        .car-img-row {
            display: flex !important;
            gap: 0.75rem;
            @media #{map-get($display-breakpoints, 'md-and-down')} {
                display: flow !important;
            }
        }
    }
}
.application-details {
    ul {
        list-style-type: none;
        padding-left: 0;
    }
}
.nissan-font {
    font-family: "Nissan Brand W01 Regular", serif;
}
.offer-details {
    .sections {
        margin-top: 0;
    }
    .sections::before {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.2);
    }
    .v-expansion-panel--active:not(:first-child)::after,
    .v-expansion-panel--active + .v-expansion-panel::after {
        opacity: 1;
    }
}
.fst-italic {
    font-style: italic;
}
.fw-bold {
    font-weight: 700;
}
.font-large {
    font-size: 1rem;
}

.font-xl {
    font-size: 1.375rem;
}
.lh-40 {
    line-height: 0.4;
}
.lh-0 {
    line-height: 0;
}
.gap-12px {
    gap: 0.75rem;
}
.overflow-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
}
.border-radius-small {
    border-radius: 0.85em !important;
}
.certificate-card {
    .lh-40 {
        line-height: 0.4;
    }
    .card-title {
        border-width: 0 !important;
    }
    margin-left: 10px;
    padding-right: 12px;
    border-style: solid;
    border-color: lightgray;
    border-width: thin;
}
</style>
