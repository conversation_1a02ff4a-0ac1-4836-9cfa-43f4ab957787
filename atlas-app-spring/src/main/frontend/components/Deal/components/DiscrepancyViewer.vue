<template>
    <div>
        <v-alert v-if="viewerShowing" icon="mdi-alert-outline" type="warning" colored-border border="top" elevation="1">
            <div>
                <strong>
                    A discrepancy was noted for this vehicle on {{ discrepancy.dateObserved | formatEpochDate }}.
                </strong>

                <div v-if="userCanAccessFullDiscrepancy" class="mt-2">
                    <div v-if="discrepancy.description">
                        Description:
                        {{ discrepancy.description }}
                    </div>
                    <div v-if="discrepancy.notes">
                        Notes:
                        {{ discrepancy.notes }}
                    </div>
                    <v-row justify="start" class="mt-4 mb-2">
                        <v-btn color="success" small class="mr-4" @click="showEditor">
                            <v-icon small left> mdi-pencil </v-icon>
                            Edit
                        </v-btn>

                        <v-btn color="error" small @click="removeDiscrepancy">
                            <v-icon small left> mdi-trash-can </v-icon>
                            Remove
                        </v-btn>
                    </v-row>
                </div>
            </div>

            <v-dialog v-model="deleteWarningShowing" max-width="500px">
                <v-card>
                    <v-card-title> Confirm Remove </v-card-title>
                    <v-card-text> Are you sure you want to remove this discrepancy? </v-card-text>
                    <v-card-actions>
                        <v-btn color="blue darken-1" text @click="cancelDeletion"> Cancel </v-btn>
                        <v-btn color="blue darken-1" text @click="deletionConfirmed"> OK </v-btn>
                        <v-spacer></v-spacer>
                    </v-card-actions>
                </v-card>
            </v-dialog>
        </v-alert>

        <div v-if="editorShowing">
            <discrepancy-editor
                :initial-discrepancy="discrepancy"
                :dealer-id="dealerId"
                :trade-vehicle-id="tradeVehicleId"
                @saved="handleSaved"
                @cancelled="handleCancelEditing"
            />
        </div>
    </div>
</template>

<script>
import _ from "lodash";
import DiscrepancyEditor from "./DiscrepancyEditor";
import api from "Util/api";

export default {
    name: "DiscrepancyViewer",
    components: { DiscrepancyEditor },
    props: {
        tradeVehicleId: {
            type: String,
            required: true,
        },
        dealerId: {
            type: String,
            required: true,
        },

        initialDiscrepancy: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            editorShowing: false,
            viewerShowing: true,
            discrepancy: {
                ...this.initialDiscrepancy,
            },
            deleteWarningShowing: false,
        };
    },
    computed: {
        hasDiscrepancy() {
            return !_.isNil(this.discrepancy);
        },
        userCanAccessFullDiscrepancy() {
            if (this.hasDiscrepancy) {
                const discrepancyDealer = _.get(this, "discrepancy.dealerId", null);

                return this.$acl.hasAnyDealerPermission(discrepancyDealer);
            } else {
                return false;
            }
        },
    },
    methods: {
        showEditor() {
            this.editorShowing = true;
            this.viewerShowing = false;
        },
        hideEditor() {
            this.editorShowing = false;
            this.viewerShowing = true;
        },
        removeDiscrepancy() {
            this.showDeleteWarning();
        },
        handleSaved(savedDiscrepancy) {
            this.discrepancy = { ...savedDiscrepancy };
            this.hideEditor();
        },
        handleCancelEditing() {
            this.hideEditor();
        },
        showDeleteWarning() {
            this.deleteWarningShowing = true;
        },
        cancelDeletion() {
            this.hideDeleteWarning();
        },
        hideDeleteWarning() {
            this.deleteWarningShowing = false;
        },
        deletionConfirmed() {
            const apiEndpoint = `/dealer/${this.dealerId}/users/trades/${this.tradeVehicleId}/discrepancy`;

            api.delete(apiEndpoint).then(() => this.$emit("discrepancy-removed"));
        },
    },
};
</script>

<style scoped></style>
