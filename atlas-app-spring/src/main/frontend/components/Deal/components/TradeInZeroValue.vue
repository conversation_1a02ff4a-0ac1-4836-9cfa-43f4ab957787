<template>
    <section-template>
        <template #section-title> Trade-in* </template>
        <template #section-content>
            <div id="trade-in">
                <div class="px-3 pt-3 pb-1 pb-md-4 px-md-4 pt-md-4">
                    <div class="mb-3 d-flex flex-column flex-sm-row justify-space-between">
                        <div class="trade-ymm font-weight-bold">{{ vehicle.ymm }} {{ vehicle.trim }}</div>

                        <v-btn
                            v-if="!discrepancy && !discrepancyEditorShowing"
                            x-small
                            outlined
                            color="error"
                            @click="showDiscrepancyEditor"
                        >
                            Add Discrepancy
                        </v-btn>
                    </div>
                    <div>
                        <discrepancy-viewer
                            v-if="discrepancy"
                            :initial-discrepancy="discrepancy"
                            :trade-vehicle-id="tradeVehicleId"
                            :dealer-id="dealerId"
                            @discrepancy-removed="handleRemovedDiscrepancy"
                        />

                        <div v-else>
                            <discrepancy-editor
                                v-if="discrepancyEditorShowing"
                                :dealer-id="dealerId"
                                :trade-vehicle-id="tradeVehicleId"
                                @saved="handleSavedDiscrepancy"
                                @cancelled="handleCancelDiscrepancyEditor"
                            />
                        </div>
                    </div>
                    <v-row>
                        <v-col cols="12" class="left-col">
                            <div class="d-flex justify-space-between mb-2">
                                <div class="label">Net Cash Offer</div>
                                <div class="value font-weight-bold">NA</div>
                            </div>
                            <div class="d-flex justify-space-between mb-2">
                                <div class="label">Trim:</div>
                                <div class="value">{{ vehicle.trim }}</div>
                            </div>
                            <div class="d-flex justify-space-between mb-2">
                                <div class="label">Mileage:</div>
                                <div class="value">
                                    {{ mileage | numeral("0,0") }}
                                </div>
                            </div>
                            <div class="d-flex justify-space-between mb-2">
                                <div class="label">Vin:</div>
                                <div class="value">
                                    {{ vin }}
                                </div>
                            </div>
                        </v-col>
                    </v-row>
                </div>
            </div>
        </template>
    </section-template>
</template>
<script>
import SectionTemplate from "./SectionTemplate";
import DiscrepancyEditor from "./DiscrepancyEditor";
import DiscrepancyViewer from "./DiscrepancyViewer";

export default {
    components: {
        SectionTemplate,
        DiscrepancyViewer,
        DiscrepancyEditor,
    },
    props: {
        dealerId: {
            type: String,
            required: true,
            default: null,
        },
        tradeVehicleId: {
            type: String,
            required: true,
            default: null,
        },
        initialDiscrepancy: {
            type: Object,
            required: false,
            default: () => null,
        },
        vehicle: {
            type: Object,
            required: false,
            default: null,
        },
        vin: {
            type: String,
            required: false,
            default: null,
        },
        mileage: {
            type: Number,
            required: true,
            default: null,
        },
    },
    data() {
        return {
            discrepancy: !_.isNil(this.initialDiscrepancy) ? { ...this.initialDiscrepancy } : null,
            discrepancyEditorShowing: false,
        };
    },
    methods: {
        showDiscrepancyEditor() {
            this.discrepancyEditorShowing = true;
        },
        hideDiscrepancyEditor() {
            this.discrepancyEditorShowing = false;
        },
        handleRemovedDiscrepancy() {
            this.discrepancy = null;
        },
        handleSavedDiscrepancy(savedDiscrepancy) {
            this.discrepancy = { ...savedDiscrepancy };
            this.hideDiscrepancyEditor();
        },
        handleCancelDiscrepancyEditor() {
            this.hideDiscrepancyEditor();
        },
    },
};
</script>
<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
#trade-in {
    .trade-ymm {
        color: $grey-800;
        font-size: 16px;
    }
    .label {
        color: $grey-800;
        font-size: 14px;
    }
    .value {
        font-size: 14px;
    }
}
</style>
