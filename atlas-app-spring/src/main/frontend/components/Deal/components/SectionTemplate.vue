<template>
    <div class="d-flex flex-column section">
        <div class="d-flex justify-end mb-1">
            <slot name="section-top" />
        </div>

        <div class="d-flex align-center section-title mb-2">
            <h2 class="d-flex align-items-center">
                <v-icon class="mr-1">mdi-label-variant</v-icon>
                <slot name="section-title" />
            </h2>
        </div>

        <div class="d-flex align-center mb-2">
            <h2 class="d-flex align-items-center">
                <slot name="section-subtitle" />
            </h2>
        </div>

        <div class="section-content mb-2">
            <slot name="section-content" />
        </div>

        <div v-if="addSection" class="section-content mb-2">
            <slot name="additional-section-content" />
        </div>
    </div>
</template>
<script>
export default {
    name: "SectionTemplate",
    props: {
        addSection: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
};
</script>
<style lang="scss">
.section {
    .section-title {
        h2 {
            color: black;
            font-size: 20px;
        }
    }
    .section-content {
        border-radius: 2px;
        border: 1px solid $grey;
        position: relative;
    }
}
</style>
