<template>
    <facet-list-sub-group v-bind="$props" ref="facetListSubGroup" no-list custom-facet-loader>
        <v-list-item v-if="sliderFacetLoading"> Loading... </v-list-item>
        <v-list-item v-else-if="noFacetsAvailable"> No Facets Available </v-list-item>
        <v-form v-else ref="form" class="facet-slider">
            <div class="facet-slider-labels">
                <v-tooltip top :disabled="!isMinMaxValuesEqual">
                    <template #activator="{ on, attrs }">
                        <v-text-field
                            ref="minValueField"
                            v-model.number="minSliderValue"
                            :placeholder="min.toString()"
                            label="Minimum"
                            class="input"
                            type="number"
                            :prefix="prefix"
                            :suffix="suffix"
                            :disabled="isMinMaxValuesEqual"
                            outlined
                            hide-details
                            dense
                            :rules="[rules.validAmount]"
                            v-bind="attrs"
                            @input="handleMinSliderValueChange"
                            v-on="on"
                        ></v-text-field>
                    </template>
                    <span>Adjust filters to find other {{ facetLabel }} values</span>
                </v-tooltip>
                <span>-</span>

                <v-tooltip top :disabled="!isMinMaxValuesEqual">
                    <template #activator="{ on, attrs }">
                        <v-text-field
                            ref="maxValueField"
                            v-model.number="maxSliderValue"
                            :placeholder="max.toString()"
                            label="Maximum"
                            class="input"
                            type="number"
                            :prefix="prefix"
                            :suffix="suffix"
                            :disabled="isMinMaxValuesEqual"
                            outlined
                            hide-details
                            dense
                            :rules="[rules.validAmount]"
                            v-bind="attrs"
                            @input="handleMaxSliderValueChange"
                            v-on="on"
                        ></v-text-field>
                    </template>
                    <span>Adjust filters to find other {{ facetLabel }} values</span>
                </v-tooltip>
            </div>
            <v-range-slider
                v-if="!isMinMaxValuesEqual"
                v-model="range"
                :max="max"
                :min="min"
                hide-details
                @input="handleRangeChange"
            ></v-range-slider>
        </v-form>
    </facet-list-sub-group>
</template>

<script>
import { defineComponent } from "vue";
import FacetListSubGroup from "Components/Facets/FacetListSubGroup.vue";
import debounce from "lodash/debounce";
import { get } from "vuex-pathify";
import lodashGet from "lodash/get";

export default defineComponent({
    name: "FacetSlider",
    components: { FacetListSubGroup },
    extends: FacetListSubGroup,
    props: {
        prefix: {
            type: String,
            default: "",
            required: false,
        },
        suffix: {
            type: String,
            default: "",
            required: false,
        },
    },

    data() {
        return {
            submitValuesDebounce: _.debounce(this.submitValues, 800),
            min: 0,
            max: 0,
            range: [0, 0],
            minSliderValue: 0,
            maxSliderValue: 0,
            facetLoaded: false,
            rules: {
                validMinAmount: (value) =>
                    value <= this.max || "Minimum value should be less than or equal to maximum value",
                validMaxAmount: (value) =>
                    value >= this.min || "Maximum value should be greater than minimum value and greater than Max",
                validAmount: (value) => value >= this.min && value <= this.max,
            },
        };
    },
    computed: {
        getFacetsRawValueByName: get("userSearch/getFacetsRawValueByName"),
        getFiltersByName: get("userSearch/getFiltersByName"),
        getFacets() {
            return {
                start: this.getFacetsRawValueByName(this.facetName + "Min"),
                end: this.getFacetsRawValueByName(this.facetName + "Max"),
            };
        },
        noFacetsAvailable() {
            return (
                this.getFacets.start == null ||
                this.getFacets.start === "Infinity" ||
                this.getFacets.start === "-Infinity" ||
                this.getFacets.end == null ||
                this.getFacets.end === "Infinity" ||
                this.getFacets.end === "-Infinity"
            );
        },
        isMinMaxValuesEqual() {
            const start = this.getFacets.start;
            const end = this.getFacets.end;

            if (typeof start !== "number" || typeof end !== "number") {
                return false;
            }

            return start === end;
        },
        getFilter() {
            return this.getFiltersByName(this.filterName) || {};
        },
        sliderFacetLoading() {
            const facetProp = this.store + "." + this.facetField;
            const minFacetProp = facetProp + "." + this.facetName + "Min";
            const maxFacetProp = facetProp + "." + this.facetName + "Max";

            const minFacetDataProp = minFacetProp + ".loader.isLoading";
            const maxFacetDataProp = maxFacetProp + ".loader.isLoading";
            return (
                lodashGet(this.$store.state, minFacetDataProp, false) &&
                lodashGet(this.$store.state, maxFacetDataProp, false)
            );
        },
    },
    watch: {
        getFacets(newFacets) {
            let maxFilter = this.getFilter.end ? this.getFilter.end : 0;
            let minFilter = this.getFilter.start ? this.getFilter.start : 0;
            let minFacet = newFacets.start ? Math.floor(newFacets.start) : 0;
            let maxFacet = newFacets.end ? Math.ceil(newFacets.end) : 0;
            let min = minFilter > minFacet ? minFilter : minFacet;
            let max = maxFilter > maxFacet ? maxFacet : maxFilter;

            this.min = minFacet;
            this.max = maxFacet;

            if (!this.getFilter.start && !this.getFilter.end) {
                this.resetFilter();
            } else if (
                this.checkForValidNumber(newFacets.start) &&
                this.checkForValidNumber(newFacets.end) &&
                !this.facetLoaded
            ) {
                this.minSliderValue = this.min;
                this.maxSliderValue = this.max;
                this.range = [min, max];
                this.facetLoaded = true;
            }
        },
        range(newRange) {
            this.minSliderValue = newRange[0];
            this.maxSliderValue = newRange[1];
        },
        getFilter(newFilter) {
            if (!newFilter) {
                this.resetFilter();
            }
        },
    },
    mounted() {
        this.handleLoadFacet();
    },
    methods: {
        async handleLoadFacet() {
            await Promise.all([
                this.$refs.facetListSubGroup.loadFacetInfo(this.facetName + "Min"),
                this.$refs.facetListSubGroup.loadFacetInfo(this.facetName + "Max"),
            ]);
        },
        checkForValidNumber(val) {
            return (
                val != null && val !== "Infinity" && val !== "-Infinity" && !Array.isArray(val) && !isNaN(Number(val))
            );
        },
        handleRangeChange() {
            this.submitValues();
        },
        handleMinSliderValueChange(val) {
            const max = this.range[1];
            // Update the value on to the range, if the value is valid
            if (this.rules.validAmount(val)) {
                this.range[0] = val;
            } else {
                this.range[0] = this.min;
                this.minSliderValue = this.min;
            }
            this.submitValuesDebounce();
        },
        handleMaxSliderValueChange(val) {
            // Update the value on to the range, if the value is valid
            if (this.rules.validAmount(val)) {
                this.range[1] = val;
            } else {
                this.range[1] = this.max;
                this.maxSliderValue = this.max;
            }
            this.submitValuesDebounce();
        },
        handleFilterRangeChange() {
            const obj = {
                filterName: this.filterName,
                facetId: { start: this.range[0], end: this.range[1] },
            };
            this.addPositiveFilter(obj);
        },
        resetFilter() {
            this.minSliderValue = this.min;
            this.maxSliderValue = this.max;
            this.range = [this.min, this.max];
        },
        submitValues() {
            if (this.rules.validAmount(this.minSliderValue) && this.rules.validAmount(this.maxSliderValue)) {
                this.debouncedHandleFilterRangeChange();
            }
        },
        debouncedHandleFilterRangeChange: debounce(function () {
            this.handleFilterRangeChange();
        }, 1000),
    },
});
</script>

<style lang="scss">
.facet-slider {
    padding: 12px 16px 18px 36px;
    .facet-slider-labels {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;

        span {
            color: var(--grey-grey-darken-2, #616161);
        }
        .input {
            max-width: 94px;
            input[type="number"]::-webkit-inner-spin-button,
            input[type="number"]::-webkit-outer-spin-button {
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                margin: 0;
            }
            input[type="number"] {
                -moz-appearance: textfield;
            }
        }
    }
}
</style>
