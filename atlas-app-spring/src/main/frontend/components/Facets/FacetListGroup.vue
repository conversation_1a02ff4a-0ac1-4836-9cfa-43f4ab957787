<template>
    <v-list-group :key="facetGroupLabel" :prepend-icon="facetIcon" no-action>
        <template #activator>
            <v-list-item-content>
                <v-list-item-title v-text="facetGroupLabel"></v-list-item-title>
            </v-list-item-content>
        </template>

        <slot />
    </v-list-group>
</template>

<script>
export default {
    name: "FacetListGroup",

    props: {
        facetIcon: {
            type: String,
            default: "",
            required: false,
        },
        facetGroupLabel: {
            type: String,
            required: true,
        },
    },
};
</script>
