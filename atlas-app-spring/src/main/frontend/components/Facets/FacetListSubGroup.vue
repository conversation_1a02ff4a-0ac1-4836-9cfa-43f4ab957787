<template>
    <v-list-group
        :value="false"
        no-action
        :sub-group="!noGroup"
        :prepend-icon="noGroup ? facetIcon : undefined"
        @click="showCollapse = !showCollapse"
    >
        <template #activator>
            <v-list-item-content>
                <v-list-item-title>
                    <div class="d-flex justify-space-between align-center">
                        <span>{{ facetLabel }}</span>
                        <small v-if="isFiltered" @click.stop="clearFilter(filterName)"> clear </small>
                    </div>
                </v-list-item-title>
            </v-list-item-content>
        </template>

        <v-list-item v-if="facetLoading"> Loading... </v-list-item>
        <template v-else>
            <div v-if="!noList" :class="{ 'pl-4': noGroup }">
                <div v-if="numberOfFacets > 10" class="pa-2">
                    <v-text-field
                        v-model="searchText"
                        append-icon="mdi-magnify"
                        outlined
                        hide-details
                        dense
                        label="Filter values"
                    />
                </div>

                <div v-if="hasFacets">
                    <v-list-item v-for="(facet, index) in displayOptions" :key="index" link>
                        <slot :facet="facet" />
                    </v-list-item>
                </div>

                <v-list-item v-else> No Facets Available </v-list-item>
            </div>
            <template v-else>
                <slot />
            </template>
        </template>
    </v-list-group>
</template>

<script>
import { FACET_TYPE } from "Util/searchUtils";
import { get } from "vuex-pathify";
import numeral from "numeral";
import lodashIsNil from "lodash/isNil";
import lodashSize from "lodash/size";
import lodashFilter from "lodash/filter";
import lodashMap from "lodash/map";
import lodashToLower from "lodash/toLower";
import lodashGet from "lodash/get";
import lodashIsEmpty from "lodash/isEmpty";
import lodashRange from "lodash/range";
import lodashSum from "lodash/sum";
import lodashForEach from "lodash/forEach";
import lodashToString from "lodash/toString";
import lodashIsArray from "lodash/isArray";

export default {
    name: "FacetListSubGroup",

    props: {
        facetLabel: {
            type: String,
            default: "",
            required: false,
        },
        store: {
            type: String,
            required: true,
        },
        facetField: {
            type: String,
            default: "facets",
            required: false,
        },
        facetType: {
            type: String,
            default: FACET_TYPE.MULTIPLE.toString(),
            required: false,
        },
        filterField: {
            type: String,
            default: "filters",
            required: false,
        },
        facetName: {
            type: String,
            required: true,
        },
        filterName: {
            type: String,
            required: true,
        },
        showCount: {
            type: Boolean,
            required: false,
            default: true,
        },
        facetIcon: {
            type: String,
            default: "",
            required: false,
        },
        noGroup: {
            type: Boolean,
            required: false,
            default: false,
        },
        noList: {
            type: Boolean,
            required: false,
            default: false,
        },
        customFacetLoader: {
            type: Boolean,
            required: false,
            default: false,
        },
        useUniqueCount: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    emits: ["custom-facet-load"],
    data() {
        return {
            showCollapse: false,
            searchText: "",
            maxMile: null,
            stepMilesBy: 10000,
            defaultMaxMileage: 250000,
        };
    },
    computed: {
        miles: get(`inventorySearch/<EMAIL>`),
        maxMileage() {
            if (this.miles) {
                const milesLength = this.miles.length;
                const lastElement = this.miles[milesLength - 1];
                this.$store.commit("inventorySearch/SET_MAX_MILE", Number(lastElement.id));
                return Number(lastElement.id);
            }
            return this.defaultMaxMileage;
        },
        isPositiveSearchMethod() {
            return this.searchMethod === "POSITIVE";
        },
        isNegativeSearchMethod() {
            return this.searchMethod !== "POSITIVE";
        },
        displayOptions() {
            if (this.facetName === "miles") {
                return this.mileOptions;
            } else {
                return this.facetsToShow;
            }
        },
        facetsToShow() {
            if (lodashIsNil(this.searchText) || this.searchText === "") {
                return this.facets;
            }

            return lodashFilter(this.facets, (facet) => {
                return (
                    lodashToLower(facet.id).indexOf(this.searchText.toLowerCase()) !== -1 ||
                    lodashToLower(facet.name).indexOf(this.searchText.toLowerCase()) !== -1
                );
            });
        },
        numberOfFacets() {
            return lodashSize(this.facets);
        },
        facets() {
            const getterName = `${this.store}/getFacetsByName`;
            return this.$store.getters[getterName](this.facetName);
        },
        filters() {
            const getterName = `${this.store}/getFiltersByName`;
            const f = this.$store.getters[getterName](this.filterName);
            const type = this.facetType.toString();
            if (lodashIsNil(f)) {
                if (type === FACET_TYPE.MULTIPLE.toString()) {
                    return [];
                }
                return null;
            }

            if (type === FACET_TYPE.MULTIPLE.toString() && !lodashIsArray(f)) {
                return [f];
            }

            return f;
        },
        searchMethod() {
            const filterProp = this.store + ".searchMethods." + this.filterName;
            const f = lodashGet(this.$store.state, filterProp);
            return lodashIsNil(f) ? "NEGATIVE" : f;
        },
        facetLoading() {
            const facetProp = this.store + "." + this.facetField + "." + this.facetName;
            const facetDataProp = facetProp + ".loader.isLoading";
            return lodashGet(this.$store.state, facetDataProp, false);
        },
        isFiltered() {
            return !lodashIsEmpty(this.filters);
        },
        hasFacets() {
            return !lodashIsEmpty(this.facets);
        },
        mileOptions() {
            let mileageOptions = [];
            let generatedMileOptions = this.generateMileOptions(0, this.maxMileage);

            lodashForEach(generatedMileOptions, (value) => {
                if (value > 0) {
                    const formattedValue = numeral(value).format("0,0");
                    const stringValue = lodashToString(value);

                    mileageOptions.push({
                        id: {
                            start: 0,
                            end: value,
                        },
                        value: stringValue,
                        name: formattedValue + " or less",
                        count: this.getCount({
                            start: 0,
                            end: value,
                        }),
                        text: {
                            start:
                                value - this.stepMilesBy === 0
                                    ? value - this.stepMilesBy
                                    : value - this.stepMilesBy + 1,
                            end: value,
                        },
                    });
                }
            });

            this.$store.commit("inventorySearch/SET_MILEAGE_OPTIONS", mileageOptions);
            return this.filteredMilesOptions(mileageOptions);
        },
    },
    watch: {
        showCollapse: function (newVal) {
            if (newVal === true) {
                this.customFacetLoader ? this.$emit("custom-facet-load") : this.loadFacetInfo(this.facetName);
            }
        },
    },
    methods: {
        addPositiveFilter(filter) {
            return this.$store.dispatch(`${this.store}/addPositiveFilter`, filter);
        },
        addNegativeFilter(filter) {
            return this.$store.dispatch(`${this.store}/addNegativeFilter`, filter);
        },
        removeFilter(filter) {
            return this.$store.dispatch(`${this.store}/removeFilter`, filter);
        },
        clearFilter(filter) {
            return this.$store.dispatch(`${this.store}/clearFilter`, filter);
        },
        async loadFacetInfo(facetInfo) {
            return this.$store.dispatch(`${this.store}/loadFacetInfo`, facetInfo);
        },
        facetId(facet) {
            if (this.facetName === "miles") {
                return `${this.filterName}_${facet.value}`;
            }
            return `${this.filterName}_${facet.id}`;
        },
        clearCurrentFilter() {
            this.clearFilter(this.filterName);
        },
        generateMileOptions(minimumMileage, maximumMileage) {
            // start greater than zero because "Any" option will cover this
            const mileageStart = minimumMileage === 0 ? this.stepMilesBy : minimumMileage;
            const mileageEnd = maximumMileage <= 250000 ? maximumMileage : 250000;

            return this.rangeInclusive(mileageStart, mileageEnd, this.stepMilesBy);
        },
        // lodash ranges are not inclusive (upper bounds)
        rangeInclusive(start, end, step = 1) {
            return lodashRange(start, end + step, step);
        },
        getCount(rang) {
            let count = 0;
            const start = 0;
            const end = rang.end;
            const rangArray = lodashFilter(this.miles, (mile) => Number(mile.id) >= start && Number(mile.id) < end);
            if (rangArray.length > 0) {
                let countArray = lodashMap(rangArray, "count");
                count = lodashSum(countArray);
            }
            return count;
        },
        filteredMilesOptions(mileageOptions) {
            if (lodashIsNil(this.searchText) || this.searchText === "" || isNaN(Number(this.searchText))) {
                return mileageOptions;
            } else if (this.searchText && !isNaN(Number(this.searchText))) {
                return lodashFilter(mileageOptions, (facet) => {
                    let searchNum = Number(this.searchText);
                    return facet.text.start <= searchNum && searchNum <= facet.text.end;
                });
            } else {
                return mileageOptions;
            }
        },
        determineCountToDisplay(facet) {
            return this.useUniqueCount === true ? facet.uniqueCount : facet.count;
        },
    },
};
</script>

<style lang="scss">
.facet-input {
    width: 100%;
}

.facet {
    width: 100%;
    font-size: 14px;
}

.facet-count {
    font-size: 12px;
}
</style>
