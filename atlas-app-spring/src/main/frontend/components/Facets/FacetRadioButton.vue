<template>
    <v-list-group ref="FacetRadioButton" :value="false" no-action sub-group @click="showCollapse = !showCollapse">
        <template #activator>
            <v-list-item-content>
                <v-list-item-title>
                    <div class="d-flex justify-space-between">
                        <span>{{ facetLabel }}</span>
                        <small v-if="isFiltered" @click.stop="clearFilter(filterName)"> clear </small>
                    </div>
                </v-list-item-title>
            </v-list-item-content>
        </template>

        <v-list-item v-if="facetLoading"> Loading... </v-list-item>
        <div v-else>
            <div v-if="numberOfFacets > 10" class="pa-2">
                <v-text-field
                    v-model="searchText"
                    append-icon="mdi-magnify"
                    outlined
                    hide-details
                    dense
                    label="Filter values"
                />
            </div>
            <div v-if="displayOptions" class="margin-top-15 pl-7 pt-1">
                <v-radio-group
                    v-model="selectedMiles"
                    class="facet-radio-group v-list-group__items"
                    @change="onCheck()"
                >
                    <v-radio
                        v-for="(facet, index) in displayOptions"
                        :id="facetId(facet)"
                        :key="index"
                        class="mb-5"
                        :name="filterName"
                        :value="facet.id"
                    >
                        <template #label>
                            <div class="facet d-flex justify-content-between">
                                <span class="facet-name mr-auto text--primary">
                                    {{ facet.name }}
                                </span>
                                &nbsp;
                                <span v-if="showCount" class="facet-count">
                                    <span>({{ facet.count }})</span>
                                </span>
                            </div>
                        </template>
                    </v-radio>
                </v-radio-group>
            </div>
            <v-list-item v-else> No Facets Available </v-list-item>
        </div>
    </v-list-group>
</template>

<script>
import FacetListSubGroup from "Components/Facets/FacetListSubGroup";
import { FACET_TYPE } from "Util/searchUtils";
import { get, sync } from "vuex-pathify";

export default {
    name: "FacetRadio",
    components: {},
    extends: FacetListSubGroup,
    props: {
        facetType: {
            type: String,
            required: false,
            default: FACET_TYPE.SINGLE.toString(),
        },
    },
    data() {
        return {
            hover: {},
        };
    },
    computed: {
        getFilters: sync("inventorySearch/filters"),
        selectedMileRange: sync("inventorySearch/selectedMiles"),
        selectedMiles: {
            get() {
                return this.selectedMileRange;
            },
            set(value) {
                console.log(value);
                if (_.isNil(value)) {
                    this.selectedMileRange = {
                        start: null,
                        end: null,
                    };
                } else {
                    this.selectedMileRange = {
                        start: value.start,
                        end: value.end,
                    };
                }
            },
        },
        filters() {
            const getterName = `${this.store}/getFiltersByName`;
            const f = this.$store.getters[getterName](this.filterName);
            const type = this.facetType;
            if (_.isNil(f)) {
                if (type === FACET_TYPE.MULTIPLE.toString()) {
                    return [];
                }
                return null;
            }

            if (type === FACET_TYPE.MULTIPLE.toString() && !_.isArray(f)) {
                return [f];
            }

            return f;
        },
    },
    methods: {
        checked(facet) {
            if (this.getFilters === facet.id) {
                return facet.id;
            }
            if (this.filterName === "miles" && this.getFilters && this.getFilters.miles) {
                if (facet.value === this.getFilters.miles.end) {
                    return `miles_${facet.value}`;
                }
            }
            return null;
        },
        onCheck() {
            this.$store.commit("SET_MILE_FILTER", this.selectedMiles);
            this.addPositiveFilter({
                filterName: this.filterName,
                facetId: this.selectedMiles,
                facetType: FACET_TYPE.SINGLE,
            });
        },
        isActive(facet) {
            if (this.filterName === "miles" && this.getFilters && this.getFilters.miles) {
                if (facet.value === this.getFilters.miles.end) {
                    return `miles_${facet.value}`;
                }
            }
            return null;
        },
        clearFilter(filter) {
            delete this.getFilters.miles;
            this.selectedMiles = null;
            return this.$store.dispatch(`${this.store}/clearFilter`, filter);
        },
    },
};
</script>
<style lang="scss" scoped>
.facet-radio-group {
    width: 80%;
}
.margin-top-15 {
    margin-top: -15px !important;
    padding-bottom: -25px !important;
}
</style>
