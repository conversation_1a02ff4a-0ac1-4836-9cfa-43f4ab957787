<template>
    <div>
        <!-- Your component's template -->
    </div>
</template>

<script>
export default {
    data() {
        return {
            // Track if there are unsaved changes
            hasUnsavedChanges: false,
        };
    },
    created() {
        // Add the event listener when component is created
        window.addEventListener("beforeunload", this.handleBeforeUnload);
    },
    beforeDestroy() {
        // Remove the event listener when component is destroyed
        window.removeEventListener("beforeunload", this.handleBeforeUnload);
    },
    methods: {
        handleBeforeUnload(event) {
            // Only show warning if there are unsaved changes
            if (this.hasUnsavedChanges) {
                // The message set here isn't actually displayed in most modern browsers
                // for security reasons, but setting it is required to show the dialog
                const message = "You have unsaved changes. Are you sure you want to leave?";
                event.returnValue = message; // Standard for most browsers
                return message; // For older browsers
            }
        },
        // Set this flag to true when user makes changes
        onFormChange() {
            this.hasUnsavedChanges = true;
        },
        // Reset the flag after saving
        onFormSave() {
            this.hasUnsavedChanges = false;
        },
    },
};
</script>
