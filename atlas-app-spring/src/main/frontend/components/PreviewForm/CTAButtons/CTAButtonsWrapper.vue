<template>
    <v-expansion-panels v-model="panels" class="d-flex flex-column wrapper-container" accordion multiple>
        <PreviewBuilder
            title="Customize Plugins"
            description="This determines where CTAs appear on your website and how they are displayed."
        >
            <template #header>
                <SectionWell
                    title="Dealer Compliance"
                    subtitle="Dealer is fully responsible for CTA configurations and designs,
                    which should comply with OEM rules and guidelines."
                    italic-subtitle
                >
                </SectionWell>
                <div class="">
                    <CustomToggleGroup v-model="configForm.togglePage" :list="toggleOptions" />
                </div>
            </template>

            <template #sub-header>
                <div class="sub-header">
                    <div class="hidden-sm-and-down"></div>
                    <div v-if="configForm.togglePage === 'listings'">
                        <ToggleBtn
                            v-model="applySameConfig"
                            class="ml-md-3"
                            label="Apply the same characteristics as vehicle details page"
                            @input="handleToggle"
                        />
                    </div>
                </div>
            </template>

            <template #preview>
                <div>
                    <Preview :cta-buttons-form="configForm[configForm.togglePage]" :base-key-path="baseKeyPath" />
                </div>
            </template>

            <template #configuration>
                <v-expansion-panels v-model="subPanels" accordion multiple class="configuration">
                    <SubCollapsible
                        title="Formatting Options"
                        description="This determines the size and shape of the Primary CTA Button."
                    >
                        <template #body>
                            <FormattingOptions
                                :options="configForm[configForm.togglePage].formattingOptions"
                                :base-key-path="baseKeyPath"
                                @change="handleChangeForFormattingOptions"
                            />
                        </template>
                    </SubCollapsible>
                    <SubCollapsible
                        v-for="option in configForm[configForm.togglePage].displayOptions"
                        :key="option.id"
                        :title="option.title"
                        :description="option.description"
                    >
                        <template #body>
                            <DisplayOptions
                                :id="option.id"
                                :values="option.values"
                                :link-destination-options="displayOptionLinkDestinationOptions"
                                :base-key-path="baseKeyPath"
                                @change="handleChangeForDisplayOptions(option.id, $event)"
                                @toggle="handleChangeForDisplayOptions(option.id, $event)"
                            />
                        </template>
                    </SubCollapsible>
                </v-expansion-panels>
            </template>

            <template #footer>
                <update-btn :loading="saveButtonLoading.isLoading" @clicked="handleSubmit" />
            </template>
        </PreviewBuilder>
    </v-expansion-panels>
</template>

<script>
import { defineComponent } from "vue";
import PreviewBuilder from "Components/ConfigFormBuilder/PreviewFormBuilder/index.vue";
import UpdateBtn from "Components/UpdateBtn.vue";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible/index.vue";
import FormattingOptions from "Components/PreviewForm/CTAButtons/FormattingOptions.vue";
import CustomToggleGroup from "./CustomToggleGroup.vue";
import DisplayOptions from "Components/PreviewForm/CTAButtons/DisplayOptions.vue";
import Preview from "./Preview.vue";
import ToggleBtn from "Components/FormInputs/ToggleBtn.vue";
import lodashIsEqual from "lodash/isEqual";
import { get, sync, call } from "vuex-pathify";
import SectionWell from "Components/ConfigFormBuilder/components/SectionWell.vue";

export default defineComponent({
    name: "CTAButtonswrapper",
    components: {
        SectionWell,
        ToggleBtn,
        Preview,
        UpdateBtn,
        PreviewBuilder,
        CustomToggleGroup,
        SubCollapsible,
        FormattingOptions,
        DisplayOptions,
    },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            CONFIG_KEYS: {
                VDP_BASE: "websiteConfigurations/ctaButtons/customizePlugins/location/vehicleDetailsPage/",
                VLP_BASE: "websiteConfigurations/ctaButtons/customizePlugins/location/listingsPage/",
                FORMATTING: {
                    FONT_FAMILY: "formattingOptions/font/font-family",
                    FONT_SIZE: "formattingOptions/font/font-size",
                    FONT_WEIGHT: "formattingOptions/font/font-weight",
                    ALIGN: "formattingOptions/font/align-content",
                    WIDTH: "formattingOptions/width",
                    HEIGHT: "formattingOptions/height",
                    PADDING: "formattingOptions/padding",
                    MARGIN: "formattingOptions/margin",
                    RADIUS: "formattingOptions/radius",
                },
                BUTTONS: {
                    PRIMARY: "primaryButton/",
                    SECOND: "secondButton/",
                    THIRD: "thirdButton/",
                    FOURTH: "fourthButton/",
                },
                PROPERTIES: {
                    DISPLAY: "display",
                    ENABLE_IMAGE: "enable-button-image",
                    BUTTON_TEXT: "button-text",
                    LINK_DESTINATION: "link-destination",
                    TEXT_COLOR: "text-color",
                    BG_COLOR: "background-color",
                    HOVER_TEXT_COLOR: "hover-text-color",
                    HOVER_BG_COLOR: "hover-background-color",
                    IMAGE_NAME: "image-name",
                    IMAGE: "image",
                    IMAGE_URL: "image-url",
                    IMAGE_SIZE: "image-size",
                    IMAGE_BACKGROUND_COLOR: "image-background-color",
                    IMAGE_DISPLAY_NAME: "image-display-name",
                },
            },
            localPanels: [0],
            localSubPanels: [0, 1, 2, 3, 4],
            // TODO: Keeping this static, need to visit later when backend has accounted for changes

            applySameConfig: false,

            views: {
                "vlp-view": "listings",
                "vdp-view": "details",
            },
            // OPTIONS
            toggleOptions: [
                { label: "vehicle details page", value: "details" },
                { label: "vehicle listings page", value: "listings" },
            ],
            displayOptionLinkDestinationOptions: [
                { label: "Deal Page", value: "DEAL_PAGE" },
                { label: "Vehicle Details Page", value: "VDP" },
                { label: "Get Prequalified", value: "PRE_QUALIFY" },
                { label: "Get Trade Value", value: "TRADE_VALUE" },
                { label: "Standalone Trade", value: "TRADE_VALUE_STAND_ALONE" },
                { label: "Sell@Home", value: "SELL_AT_HOME" },
                { label: "Finance Application", value: "FINANCE" },
                { label: "Schedule Appointment", value: "APPOINTMENT" },
                { label: "Check Availability (Contact Dealer)", value: "CONTACT_DEALER" },
                { label: "Upgrade Landing Page", value: "RETENTION" },
            ],
            // OPTIONS

            // MAIN FORM
            configForm: {
                togglePage: "details", // details, listings
                details: {},
                listings: {},
            },
            // MAIN FORM

            // COMMON FORM
            form: {
                formattingOptions: {
                    fontFamily: {
                        value: null,
                        key: "formattingOptions/font/font-family",
                    },
                    fontSize: {
                        value: null,
                        key: "formattingOptions/font/font-size",
                    },
                    fontAlign: {
                        value: null,
                        key: "formattingOptions/font/align-content",
                    },
                    fontWeight: {
                        value: null,
                        key: "formattingOptions/font/font-weight",
                    },
                    buttonWidth: {
                        value: null,
                        key: "formattingOptions/width",
                    },
                    buttonHeight: {
                        value: null,
                        key: "formattingOptions/height",
                    },
                    buttonPadding: {
                        value: null,
                        key: "formattingOptions/padding",
                    },
                    buttonMargin: {
                        value: null,
                        key: "formattingOptions/margin",
                    },
                    buttonRadius: {
                        value: null,
                        key: "formattingOptions/radius",
                    },
                },
                displayOptions: [
                    {
                        id: "primaryButton/",
                        title: "Display Options | Primary Call To Action",
                        description: "This determines the display name and colors used for the Primary CTA Button.",
                        values: {
                            display: {
                                value: true,
                                key: "primaryButton/display",
                            },
                            enableImageCta: {
                                value: false,
                                key: "primaryButton/enable-image-cta",
                            },
                            defaultTextColor: {
                                value: "#FFFFFF",
                                key: "primaryButton/text-color",
                            },
                            defaultBackgroundColor: {
                                value: "#C3002F",
                                key: "primaryButton/background-color",
                            },
                            hoverTextColor: {
                                value: "#000000",
                                key: "primaryButton/hover-text-color",
                            },
                            hoverBackgroundColor: {
                                value: "#C3002F",
                                key: "primaryButton/hover-background-color",
                            },
                            buttonText: {
                                value: "Primary Call to Action",
                                key: "primaryButton/button-text",
                            },
                            linkDestination: {
                                value: "",
                                key: "primaryButton/link-destination",
                            },
                            imageName: {
                                value: "",
                                key: "primaryButton/image-name",
                            },
                            imageSize: {
                                value: "",
                                key: "primaryButton/image-size",
                            },
                            image: {
                                value: null,
                                key: "primaryButton/image", //this is image file
                            },
                            imageBackgroundColor: {
                                value: "#FFFFFF",
                                key: "primaryButton/image-background-color",
                            },
                            imageDisplayName: {
                                value: "",
                                key: "primaryButton/image-display-name",
                            },
                            imageUrl: {
                                value: null,
                                key: "primaryButton/image-url",
                            },

                            fontWeight: {
                                value: "normal",
                                key: "primaryButton/font-weight",
                            },
                        },
                    },
                    {
                        id: "secondButton/",
                        title: "Display Options | Button #2",
                        description: "This determines the display name and colors used for Button #2.",
                        values: {
                            display: {
                                value: true,
                                key: "secondButton/display",
                            },
                            enableImageCta: {
                                value: false,
                                key: "secondButton/enable-image-cta",
                            },
                            defaultTextColor: {
                                value: "#C3002F",
                                key: "secondButton/text-color",
                            },
                            defaultBackgroundColor: {
                                value: "#F6F6F6",
                                key: "secondButton/background-color",
                            },
                            hoverTextColor: {
                                value: "#000000",
                                key: "secondButton/hover-text-color",
                            },
                            hoverBackgroundColor: {
                                value: "#C3002F",
                                key: "secondButton/hover-background-color",
                            },
                            buttonText: {
                                value: "Button #2",
                                key: "secondButton/button-text",
                            },
                            linkDestination: {
                                value: "",
                                key: "secondButton/link-destination",
                            },
                            imageName: {
                                value: "",
                                key: "secondButton/image-name",
                            },
                            imageSize: {
                                value: "",
                                key: "secondButton/image-size",
                            },
                            image: {
                                value: null,
                                key: "secondButton/image",
                            },
                            imageBackgroundColor: {
                                value: "#FFFFFF",
                                key: "secondButton/image-background-color",
                            },
                            imageDisplayName: {
                                value: "",
                                key: "secondButton/image-display-name",
                            },
                            imageUrl: {
                                value: null,
                                key: "secondButton/image-url",
                            },
                            fontWeight: {
                                value: "normal",
                                key: "secondButton/font-weight",
                            },
                        },
                    },
                    {
                        id: "thirdButton/",
                        title: "Display Options | Button #3",
                        description: "This determines the display name and colors used for Button #3.",
                        values: {
                            display: {
                                value: true,
                                key: "thirdButton/display",
                            },
                            enableImageCta: {
                                value: false,
                                key: "thirdButton/enable-image-cta",
                            },
                            defaultTextColor: {
                                value: "#C3002F",
                                key: "thirdButton/text-color",
                            },
                            defaultBackgroundColor: {
                                value: "#F6F6F6",
                                key: "thirdButton/background-color",
                            },
                            hoverTextColor: {
                                value: "#000000",
                                key: "thirdButton/hover-text-color",
                            },
                            hoverBackgroundColor: {
                                value: "#C3002F",
                                key: "thirdButton/hover-background-color",
                            },
                            buttonText: {
                                value: "Button #3",
                                key: "thirdButton/button-text",
                            },
                            linkDestination: {
                                value: "",
                                key: "thirdButton/link-destination",
                            },
                            imageName: {
                                value: "",
                                key: "thirdButton/image-name",
                            },
                            imageSize: {
                                value: "",
                                key: "thirdButton/image-size",
                            },
                            image: {
                                value: null,
                                key: "thirdButton/image",
                            },
                            imageBackgroundColor: {
                                value: "#FFFFFF",
                                key: "thirdButton/image-background-color",
                            },
                            imageDisplayName: {
                                value: "",
                                key: "thirdButton/image-display-name",
                            },
                            imageUrl: {
                                value: null,
                                key: "thirdButton/image-url",
                            },
                            fontWeight: {
                                value: "normal",
                                key: "thirdButton/font-weight",
                            },
                        },
                    },
                    {
                        id: "fourthButton/",
                        title: "Display Options | Button #4",
                        description: "This determines the display name and colors used for Button #4.",
                        values: {
                            display: {
                                value: true,
                                key: "fourthButton/display",
                            },
                            enableImageCta: {
                                value: false,
                                key: "fourthButton/enable-image-cta",
                            },
                            defaultTextColor: {
                                value: "#C3002F",
                                key: "fourthButton/text-color",
                            },
                            defaultBackgroundColor: {
                                value: "#F6F6F6",
                                key: "fourthButton/background-color",
                            },
                            hoverTextColor: {
                                value: "#000000",
                                key: "fourthButton/hover-text-color",
                            },
                            hoverBackgroundColor: {
                                value: "#C3002F",
                                key: "fourthButton/hover-background-color",
                            },
                            buttonText: {
                                value: "Button #4",
                                key: "fourthButton/button-text",
                            },
                            linkDestination: {
                                value: "",
                                key: "fourthButton/link-destination",
                            },
                            imageName: {
                                value: "",
                                key: "fourthButton/image-name",
                            },
                            imageSize: {
                                value: "",
                                key: "fourthButton/image-size",
                            },
                            image: {
                                value: null,
                                key: "fourthButton/image",
                            },
                            imageBackgroundColor: {
                                value: "#FFFFFF",
                                key: "fourthButton/image-background-color",
                            },
                            imageDisplayName: {
                                value: "",
                                key: "fourthButton/image-display-name",
                            },
                            imageUrl: {
                                value: null,
                                key: "fourthButton/image-url",
                            },
                            fontWeight: {
                                value: "normal",
                                key: "fourthButton/font-weight",
                            },
                        },
                    },
                ],
            },
            // COMMON FORM
        };
    },
    computed: {
        getPageBuilderLoader: get("pageConfigs/loader"),
        getFormDefaultsLoader: get("pageConfigs/form@loader"),
        getPageBuilderData: get("pageConfigs/pageBuilderData"),
        panels: sync("pageConfigs/panels"),
        subPanels: sync("pageConfigs/subPanels"),
        expandAll: sync("pageConfigs/expandAll"),
        formData: sync("pageConfigs/form@data"),
        formDefaults: get("pageConfigs/form@defaults"),
        flattenedPageBuilderData: sync("pageConfigs/flattenedPageBuilderData"),
        saveButtonLoading: get("pageConfigs/saveForm@loader"),
        userId: get("loggedInUser/userId"),
        pageName: get("pageConfigs/pageBuilderData@page"),
        selectedDealer: get("loggedInUser/selectedDealer"),
        dealerId() {
            return this.selectedDealer?.id || this.$route.params.dealerId || 11; // Fallback to 11 for testing
        },
        isInitLoadingComplete() {
            return this.getPageBuilderLoader.isComplete && this.getFormDefaultsLoader.isComplete;
        },
        baseKeyPath() {
            return this.CONFIG_KEYS[this.configForm.togglePage === "details" ? "VDP_BASE" : "VLP_BASE"];
        },
        isLoading() {
            return this.getPageBuilderLoader.isLoading || this.getFormDefaultsLoader.isLoading;
        },
        isDetailsAndListingsConfigSame() {
            return lodashIsEqual(this.configForm.details, this.configForm.listings);
        },
        isSubPanelsExpanded() {
            return this.subPanels.length === this.localSubPanels.length;
        },
        isPanelsExpanded() {
            return this.panels.length === this.localPanels.length;
        },
        isAllLocalPanelsExpanded() {
            return this.isSubPanelsExpanded && this.isPanelsExpanded;
        },
    },
    watch: {
        isInitLoadingComplete: {
            handler(val) {
                if (val) {
                    this.initFormData();
                }
            },
            immediate: true,
        },
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllPanels();
                } else {
                    this.closeAllPanels();
                }
            },
            immediate: true,
        },

        isDetailsAndListingsConfigSame(newValue) {
            if (this.configForm.togglePage === "listings" && !newValue) {
                this.applySameConfig = false;
            }
        },
    },
    mounted() {
        this.handleSetPanels(this.localPanels);
        this.handleSetSubPanels(this.localSubPanels);
        this.setIsMultipartForm({ isMultipart: true });
        // Form initialization is now handled in initFormData
    },
    methods: {
        handleSetPanels: call("pageConfigs/handleSetPanels"),
        handleSetSubPanels: call("pageConfigs/handleSetSubPanels"),
        updateFormData: call("pageConfigs/updateFormData"),
        setIsMultipartForm: call("pageConfigs/setIsMultipartForm"),
        saveFormData: call("pageConfigs/saveFormData"),
        syncListingsWithDetailsStore: call("pageConfigs/syncListingsWithDetailsStore"),

        getDefaultValueByKey(key) {
            return this.flattenPageBuilderData[key];
        },
        initFormData() {
            this.formData = { ...this.flattenedPageBuilderData, ...this.formDefaults };

            // Initialize with default values first
            this.configForm.listings = JSON.parse(JSON.stringify(this.form));
            this.configForm.details = JSON.parse(JSON.stringify(this.form));

            // Then apply non-null values from formData
            this.mapFormDataToConfigForm();
        },

        mapFormDataToConfigForm() {
            // Skip processing if formData is empty
            if (!this.formData || Object.keys(this.formData).length === 0) return;

            // Process details and listings pages
            this.mapValuesForPage("details");
            this.mapValuesForPage("listings");
        },

        mapValuesForPage(pageType) {
            const basePath = this.CONFIG_KEYS[pageType === "details" ? "VDP_BASE" : "VLP_BASE"];

            // Map formatting options
            const formattingOptions = this.configForm[pageType].formattingOptions;
            for (const key in formattingOptions) {
                const fullPath = basePath + formattingOptions[key].key;
                const formDataValue = this.formData[fullPath];

                // Only update if value is not null or undefined
                if (formDataValue !== null && formDataValue !== undefined) {
                    formattingOptions[key].value = formDataValue;
                }
            }

            // Map display options
            this.configForm[pageType].displayOptions.forEach((option) => {
                for (const key in option.values) {
                    const fullPath = basePath + option.values[key].key;
                    const formDataValue = this.formData[fullPath];

                    // Only update if value is not null or undefined
                    if (formDataValue !== null && formDataValue !== undefined) {
                        // Handle boolean values that come as strings
                        if (formDataValue === "true" || formDataValue === "false") {
                            option.values[key].value = formDataValue === "true";
                        } else {
                            option.values[key].value = formDataValue;
                        }
                    }
                }
            });

            // Special handling for any fields with naming differences between formData and configForm
            this.handleSpecialCases(pageType, basePath);
        },

        handleSpecialCases(pageType, basePath) {
            // Handle cases where backend field names differ from the frontend model
            // For example, mapping enable-image-cta to enable-button-image
            const buttonTypes = ["primaryButton", "secondButton", "thirdButton", "fourthButton"];

            buttonTypes.forEach((buttonType) => {
                // Find the corresponding button in config
                const button = this.configForm[pageType].displayOptions.find((option) =>
                    option.values.buttonText?.key.includes(buttonType)
                );

                if (!button) return;

                // Example: Map enable-image-cta to enable-button-image if needed
                const imageEnabledPath = `${basePath}${buttonType}/enable-image-cta`;

                // Handle image URLs if they exist in formData
                const imageUrlPath = `${basePath}${buttonType}/image-Url`;
                if (this.formData[imageUrlPath]) {
                    // Further handling would depend on how your app handles image files
                }
            });
        },
        expandAllPanels() {
            this.expandPanels();
            this.expandAllSubPanels();
        },
        closeAllPanels() {
            this.panels = [];
        },
        expandPanels() {
            this.panels = [...this.localPanels];
        },
        expandAllSubPanels() {
            this.subPanels = [...this.localSubPanels];
        },
        closeAllSubPanels() {
            this.subPanels = [];
        },

        handleChangeForFormattingOptions({ value, property }) {
            // Update the value in the form
            this.configForm[this.configForm.togglePage].formattingOptions[property].value = value;
            const valueKey = this.configForm[this.configForm.togglePage].formattingOptions[property].key;
            const keyPath = this.baseKeyPath + valueKey;

            if (valueKey) {
                this.updateFormData({ key: keyPath, value });
            }

            if (this.applySameConfig && this.configForm.togglePage === "details") {
                this.syncListingsWithDetails();
            }
        },
        handleChangeForDisplayOptions(id, { value, property, key }) {
            const option = this.configForm[this.configForm.togglePage].displayOptions.find(
                (option) => option.id === id
            );

            // Handle special case for image file uploads
            if (property === "imageButtonFile") {
                // Find the image property in option.values
                const imageProperty = option.values.image;
                if (imageProperty && imageProperty.key === key) {
                    // Set the File object directly
                    imageProperty.value = value;

                    // Get the key path and add the base path
                    const keyPath = this.baseKeyPath + key;

                    // Update the form data
                    this.updateFormData({ key: keyPath, value });

                    if (this.applySameConfig && this.configForm.togglePage === "details") {
                        this.syncListingsWithDetails();
                    }

                    return;
                }
            }

            // Update the value in the option
            // Find the object in option.values that has a key matching the property parameter
            const targetProperty = Object.values(option.values).find((item) => item.key === key);
            if (targetProperty) {
                targetProperty.value = value;
            }

            // Get the key path and add the base path
            const keyPath = this.baseKeyPath + key;

            // Update the flattened form data for saving to backend
            if (key) {
                this.updateFormData({ key: keyPath, value });
            }

            if (this.applySameConfig && this.configForm.togglePage === "details") {
                this.syncListingsWithDetails();
            }
        },
        handleToggle(value) {
            if (value) {
                this.syncListingsWithDetails();
            }
        },
        syncListingsWithDetails() {
            // Create a deep copy without using JSON methods which can crash with circular references
            const detailsCopy = {};
            // Copy formattingOptions
            detailsCopy.formattingOptions = {};
            for (const key in this.configForm.details.formattingOptions) {
                detailsCopy.formattingOptions[key] = {
                    ...this.configForm.details.formattingOptions[key],
                };
            }

            // Copy displayOptions
            detailsCopy.displayOptions = this.configForm.details.displayOptions.map((option) => {
                const values = {};
                for (const key in option.values) {
                    values[key] = { ...option.values[key] };
                }
                return {
                    id: option.id,
                    title: option.title,
                    description: option.description,
                    values,
                };
            });

            this.configForm.listings = detailsCopy;
            this.syncListingsWithDetailsStore({
                vdpBase: this.CONFIG_KEYS.VDP_BASE,
                vlpBase: this.CONFIG_KEYS.VLP_BASE,
            });
        },
        buildKeyPath(pageType, section, property) {
            const basePath =
                pageType === this.views["vdp_view"] ? this.CONFIG_KEYS.VDP_BASE : this.CONFIG_KEYS.VLP_BASE;

            // For formatting options
            if (section === "formatting") {
                return basePath + this.CONFIG_KEYS.FORMATTING[property?.toUpperCase()];
            }

            // For button options
            let buttonPath;
            switch (section) {
                case "primary":
                    buttonPath = this.CONFIG_KEYS.BUTTONS.PRIMARY;
                    break;
                case "button2":
                    buttonPath = this.CONFIG_KEYS.BUTTONS.SECOND;
                    break;
                case "button3":
                    buttonPath = this.CONFIG_KEYS.BUTTONS.THIRD;
                    break;
                case "button4":
                    buttonPath = this.CONFIG_KEYS.BUTTONS.FOURTH;
                    break;
                default:
                    buttonPath = "";
            }

            // Map property to constant key
            let propKey;
            switch (property) {
                case "display":
                    propKey = this.CONFIG_KEYS.PROPERTIES.DISPLAY;
                    break;
                case "enableImage":
                    propKey = this.CONFIG_KEYS.PROPERTIES.ENABLE_IMAGE;
                    break;
                case "displayName":
                    propKey = this.CONFIG_KEYS.PROPERTIES.BUTTON_TEXT;
                    break;
                case "linkDestination":
                    propKey = this.CONFIG_KEYS.PROPERTIES.LINK_DESTINATION;
                    break;
                case "defaultTextColor":
                    propKey = this.CONFIG_KEYS.PROPERTIES.TEXT_COLOR;
                    break;
                case "defaultBackgroundColor":
                    propKey = this.CONFIG_KEYS.PROPERTIES.BG_COLOR;
                    break;
                case "hoverTextColor":
                    propKey = this.CONFIG_KEYS.PROPERTIES.HOVER_TEXT_COLOR;
                    break;
                case "hoverBackgroundColor":
                    propKey = this.CONFIG_KEYS.PROPERTIES.HOVER_BG_COLOR;
                    break;
                default:
                    propKey = property;
            }

            return basePath + buttonPath + propKey;
        },
        handleSubmit() {
            this.saveFormData({
                userId: this.userId,
                page: this.pageName || "ctaButtons",
                dealerId: this.dealerId,
            });
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.preview-wrapper {
    display: flex;
    justify-content: center;
}
.cta-button-toggle {
    display: flex;
    width: 100% !important;
    border: 1px solid #1976d2 !important;
}
.cta-button-toggle--active {
    background: #1976d2 !important;
    color: white !important;
}
.cta-button-toggle-button {
    flex-grow: 1;
}
.configuration {
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.sub-header {
    display: flex;
    align-items: center;
    margin-top: 16px;
    div {
        flex-basis: 50%;
        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            flex-basis: 100%;
        }
    }
}
</style>
