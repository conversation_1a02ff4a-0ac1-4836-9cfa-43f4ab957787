<template>
    <div class="cta-buttons-preview">
        <button
            v-for="(buttonData, id) in orderedDisplayOptions"
            :key="buttonData.id"
            :style="buttonStyles[id]"
            @mouseover="setHoverStyles(id, true)"
            @mouseleave="setHoverStyles(id, false)"
        >
            <img
                v-if="shouldShowImage(buttonData)"
                :src="getImageUrl(buttonData.values?.imageUrl?.value, buttonData.values?.image?.value)"
                :alt="buttonData.values?.imageDisplayName?.value"
                class="button-image"
            />
            <span v-else>
                {{ buttonData.values?.buttonText?.value || "Button" }}
            </span>
        </button>
    </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "CtaButtonPreview",
    props: {
        ctaButtonsForm: {
            type: Object,
            required: false,
            default: () => ({
                formattingOptions: {},
                displayOptions: [],
            }),
        },
        baseKeyPath: {
            type: String,
            required: true,
        },
    },
    data() {
        return {
            isHovered: [],
            imageUrls: {},
        };
    },
    computed: {
        displayOptions() {
            return this.ctaButtonsForm?.displayOptions || [];
        },
        orderedDisplayOptions() {
            // reverse the order of the displayOptions
            return [...this.displayOptions];
        },
        getWidth() {
            const width = this.ctaButtonsForm?.formattingOptions?.buttonWidth?.value;
            return width ? width : "100%";
        },
        getHeight() {
            const height = this.ctaButtonsForm?.formattingOptions?.buttonHeight?.value;
            return height ? height : "100%";
        },
        baseStyle() {
            const options = this.ctaButtonsForm?.formattingOptions || {};
            return {
                fontFamily: options.fontFamily?.value,
                fontSize: options.fontSize?.value,
                width: this.getWidth,
                height: this.getHeight,
                padding: options.buttonPadding?.value,
                margin: options.buttonMargin?.value,
                borderRadius: options.buttonRadius?.value,
                textAlign: options.fontAlign?.value,
                fontWeight: options.fontWeight?.value,
            };
        },
        buttonStyles() {
            return this.orderedDisplayOptions.map((option, index) => {
                const values = option.values || {};
                const isImageCtaEnabled = values.enableImageCta?.value;
                const backgroundColor = isImageCtaEnabled
                    ? values.imageBackgroundColor?.value
                    : values.defaultBackgroundColor?.value;
                return {
                    color: this.isHovered[index] ? values.hoverTextColor?.value : values.defaultTextColor?.value,
                    backgroundColor: this.isHovered[index] ? values.hoverBackgroundColor?.value : backgroundColor,
                    display: values.display?.value ? "block" : "none",
                    ...this.baseStyle,
                };
            });
        },
    },
    beforeDestroy() {
        // Clean up object URLs to prevent memory leaks
        Object.values(this.imageUrls).forEach((url) => {
            URL.revokeObjectURL(url);
        });
    },
    methods: {
        setHoverStyles(index, value) {
            this.$set(this.isHovered, index, value);
        },
        shouldShowImage(buttonData) {
            if (!buttonData || !buttonData.values) {
                return false;
            }
            return Boolean(buttonData.values.enableImageCta?.value);
        },
        getImageUrl(imageValue, localImageFile) {
            if (!imageValue && !localImageFile) {
                return "";
            }

            // If it's already a string URL
            if (typeof imageValue === "string") {
                return imageValue;
            }

            // If it's a File object
            if (localImageFile instanceof File) {
                const key = `${localImageFile.name}-${localImageFile.size}`;

                // Create object URL if we haven't already
                if (!this.imageUrls[key]) {
                    this.imageUrls[key] = URL.createObjectURL(localImageFile);
                }

                return this.imageUrls[key];
            }

            return "";
        },
    },
});
</script>

<style scoped lang="scss">
.cta-buttons-preview {
    display: flex;
    flex-direction: column;
    align-items: center;

    & > button {
        box-shadow: 0 0 4px 0 rgba(255, 255, 255, 0.15);
        transition: color 300ms, background-color 300ms;

        .button-image {
            max-width: 100%;
            max-height: 100%;
            overflow: hidden;
            flex-shrink: 0;
            display: inline-block;
        }
    }
}
</style>
