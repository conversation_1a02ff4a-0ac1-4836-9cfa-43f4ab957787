<template>
    <div>
        <Loading v-if="isLoading" />
        <CTAButtonsWrapper v-else />
    </div>
</template>

<script>
import { defineComponent } from "vue";
import { get } from "vuex-pathify";
import Loading from "./Loading.vue";
import CTAButtonsWrapper from "./CTAButtonsWrapper.vue";

export default defineComponent({
    name: "CTAButtons",
    components: { CTAButtonsWrapper, Loading },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    computed: {
        getPageBuilderLoader: get("pageConfigs/loader"),
        getFormDefaultsLoader: get("pageConfigs/form@loader"),
        isLoading() {
            return this.getPageBuilderLoader.isLoading || this.getFormDefaultsLoader.isLoading;
        },
    },
});
</script>
