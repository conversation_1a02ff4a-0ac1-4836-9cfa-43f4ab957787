<template>
    <div>
        <div class="label mb-2">Font</div>
        <div class="select-group">
            <v-select
                id="font-family"
                :key="options.fontFamily?.key"
                :value="options.fontFamily?.value"
                :items="fontOptions"
                dense
                outlined
                class="family"
                @input="handleChange($event, 'fontFamily')"
            />
            <v-select
                id="font-size"
                :key="options.fontSize?.key"
                :value="removePx(options.fontSize?.value)"
                :items="fontSizeOptions"
                dense
                outlined
                class="size"
                @input="handleChange(addPx($event), 'fontSize')"
            />

            <v-btn
                id="font-bold"
                :key="options.fontWeight?.key"
                :value="fontBold"
                :class="['bold-toggle', { 'bold-toggle--active': options.fontWeight?.value === 'bold' }]"
                text
                tile
                dense
                @click="handleBoldClick()"
            >
                <span class="bold-text">B</span>
            </v-btn>

            <v-btn-toggle
                id="font-align"
                :key="options.fontAlign?.key"
                :value="options.fontAlign?.value"
                class="align"
                dense
                tile
                borderless
                @change="handleChange($event, 'fontAlign')"
            >
                <v-btn v-for="(item, key) in alignOptions" :key="key + item" text :value="item">
                    <v-icon small>mdi-align-horizontal-{{ item }}</v-icon>
                </v-btn>
            </v-btn-toggle>
        </div>

        <div class="label mb-2 mt-2">Button Options</div>
        <div>
            <div class="input-group">
                <div v-for="(input, idx) in inputGroup" :key="idx + input">
                    <v-text-field
                        v-if="isNumberType(options[input.value]?.key)"
                        :key="options[input.value]?.key"
                        :value="removePx(options[input.value]?.value)"
                        type="number"
                        :label="input.label"
                        outlined
                        dense
                        hide-details
                        @input="handleChange(addPx($event), input.value)"
                    />
                    <v-text-field
                        v-else
                        :key="options[input.value]?.key"
                        :value="options[input.value]?.value"
                        :label="input.label"
                        :rules="cssValueRules"
                        type="text"
                        outlined
                        hide-details
                        dense
                        @input="handleChange($event.trim(), input.value)"
                    />
                    px
                </div>
            </div>
            <div class="mt-4 post-text">Image buttons will be formatted based on the width above.</div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "FormattingOptions",
    props: {
        options: {
            type: Object,
            required: false,
            default: () => ({}),
        },
        baseKeyPath: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            cssValueRules: [
                (v) =>
                    !v ||
                    /^(\d+(?:px|em|rem|%)?(?:\s+\d+(?:px|em|rem|%)?){0,3})$/.test(v) ||
                    "Only numbers and units (px, em, rem, %) allowed",
            ],
            fontBold: false,
            fontSizeOptions: [12, 14, 16, 18, 20],
            fontOptions: [
                "Helvetica",
                "Arial",
                "Verdana",
                "Tahoma",
                "Times New Roman",
                "Georgia",
                "Courier New",
                "Courier",
                "Lucida Console",
                "Impact",
                "Arial Black",
                "Trebuchet MS",
            ],
            alignOptions: ["left", "center", "right"],
            inputGroup: [
                { label: "Width", value: "buttonWidth" },
                { label: "Height", value: "buttonHeight" },
                { label: "Padding", value: "buttonPadding" },
                { label: "Margin", value: "buttonMargin" },
                { label: "Radius", value: "buttonRadius" },
            ],
        };
    },
    computed: {
        isBold() {
            return this.fontBold;
        },
        fontWeight() {
            return this.isBold ? "bold" : "normal";
        },
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.fontBold = this.options.fontWeight?.value === "bold";
        },
        handleBoldClick() {
            this.fontBold = !this.fontBold;
            this.$emit("change", { value: this.fontWeight, property: "fontWeight" });
        },
        handleChange(value, property) {
            this.$emit("change", { value, property });
        },
        isNumberType(key) {
            const strTest = "margin";
            if (key.includes(strTest)) {
                return false;
            }
            return true;
        },
        removePx(value) {
            // Handle null/undefined values
            if (value == null) return "";

            // If it's already a number, just return it
            if (typeof value === "number") return value;

            // Convert string values, removing 'px' if present
            if (typeof value === "string") {
                // Remove 'px' suffix if present
                const valueWithoutPx = value.replace(/px$/, "");

                // Try to convert to number
                const numValue = parseFloat(valueWithoutPx);

                // Return the number if valid, otherwise the original value
                return isNaN(numValue) ? value : numValue;
            }

            // Return original value for anything else
            return value;
        },
        addPx(value) {
            if (!value) return null;
            return value + "px";
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
#font-bold {
    margin-right: 40px;
}
#font-align {
    i {
        color: #666666;
    }
}
.label {
    font-size: px2rem(14);
    font-weight: 500;
}
.select-group {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    .family {
        flex-grow: 1;
        margin-right: 12px;

        @media #{map-get($display-breakpoints, 'md-and-down')} {
            max-width: 180px;
        }
    }
    .size {
        width: 75px;
        margin-right: 12px;
        flex-shrink: 1;
    }
    .bold-toggle {
        min-width: 32px !important;
        width: 32px !important;
        height: 32px !important;
        margin-right: 12px;
        padding: 0 !important;
        display: flex !important;
        align-items: center;
        justify-content: center;
        border-radius: 50% !important;
        color: #616161;

        &--active {
            background-color: #f5f5f5 !important;

            .bold-text {
                color: rgba(0, 0, 0, 0.87);
            }
        }

        .bold-text {
            font-weight: 700;
            font-size: 24px;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 24px;
        }
    }

    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        flex-wrap: wrap;
    }
}
.input-group {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 12px 24px;
    div {
        display: inline-flex;
        align-items: end;
        gap: 8px;
    }
    div:last-child {
        grid-column: span 2 / span 2;
    }
}
.post-text {
    font-size: 14px;
    font-style: italic;
    color: var(--grey-grey-darken-2, #616161);
}
</style>
