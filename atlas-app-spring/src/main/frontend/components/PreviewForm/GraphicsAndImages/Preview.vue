<template>
    <div class="graphics-preview h-100">
        <!-- Logo Preview -->
        <div v-if="type === 'logo'">
            <LogoPreview :logo-url="form.logoPreviewUrl" />
        </div>

        <!-- Favicon Preview -->
        <div v-else-if="type === 'favicon'">
            <FaviconPreview :favicon-url="form.faviconPreviewUrl" />
        </div>

        <!-- Dealership Images Preview -->
        <div v-else-if="type === 'dealership'" class="dealership-preview py-6">
            <!-- Mobile Preview Section -->
            <div class="preview-section">
                <h3 class="section-title">Mobile</h3>
                <div v-if="form.dealershipFile1PreviewUrl" class="image-preview">
                    <img
                        :src="form.dealershipFile1PreviewUrl"
                        alt="Mobile Dealership Image"
                        class="dealership-image square"
                    />
                </div>
                <div v-else class="preview-container mobile-preview">
                    <div class="placeholder-content">
                        <v-icon large color="grey">mdi-image-outline</v-icon>
                        <p class="placeholder-text">Your image here</p>
                    </div>
                </div>
            </div>

            <!-- Desktop Preview Section -->
            <div class="preview-section">
                <h3 class="section-title">Desktop</h3>
                <div v-if="form.dealershipFile2PreviewUrl" class="image-preview">
                    <img
                        :src="form.dealershipFile2PreviewUrl"
                        alt="Desktop Dealership Image"
                        class="dealership-image wide"
                    />
                </div>
                <div v-else class="preview-container desktop-preview">
                    <div class="placeholder-content">
                        <v-icon large color="grey">mdi-image-outline</v-icon>
                        <p class="placeholder-text">Your image here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LogoPreview from "Components/PreviewForm/GraphicsAndImages/LogoPreview.vue";
import FaviconPreview from "Components/PreviewForm/GraphicsAndImages/FaviconPreview.vue";

export default {
    name: "Preview",
    components: { FaviconPreview, LogoPreview },
    props: {
        form: {
            type: Object,
            required: true,
        },
        type: {
            type: String,
            required: true,
            validator: (value) => ["logo", "favicon", "dealership"].includes(value),
        },
    },
};
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
.graphics-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 120px;
    justify-content: center;

    .logo-preview,
    .favicon-preview,
    .dealership-preview,
    .placeholder-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .logo-image {
        max-width: 200px;
        max-height: 80px;
        object-fit: contain;
        margin-bottom: 8px;
        border: 1px solid #e0e0e0;
        padding: 8px;
        border-radius: 4px;
        background: white;
    }

    .favicon-image {
        width: 32px;
        height: 32px;
        object-fit: contain;
        margin-bottom: 8px;
        border: 1px solid #e0e0e0;
        padding: 4px;
        border-radius: 4px;
        background: white;
    }

    .dealership-preview {
        display: flex;
        flex-direction: column;
        gap: 24px;
        width: 100%;
        max-width: 100%;
    }

    .preview-section {
        width: 100%;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        padding: 16px;
        background: white;
    }

    .section-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
        text-align: left;
    }

    .preview-container {
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 24px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 120px;
        background: #f8f8f8;
        position: relative;
        width: 100%;
        margin-top: 8px;
    }

    .mobile-preview {
        min-height: 140px;
    }

    .desktop-preview {
        min-height: 120px;
    }

    .image-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .placeholder-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #888;
    }

    .placeholder-text {
        font-size: 13px;
        color: #888;
        margin: 8px 0 0 0;
        font-weight: 400;
    }

    .dealership-image-container {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .dealership-image {
        object-fit: cover;
        margin-bottom: 8px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        background: white;

        &.square {
            width: 320px;
            height: 320px;
        }

        &.wide {
            width: 320px;
            height: 125px;
        }

        @media #{map-get($display-breakpoints, 'sm-and-up')} {
            &.square {
                width: 454px;
                height: 454px;
            }

            &.wide {
                width: 454px;
                height: 255px;
            }
        }
    }

    .preview-label {
        font-size: 12px;
        color: #666;
        margin: 8px 0 0 0;
        font-weight: 500;
    }

    .placeholder-preview {
        color: #999;

        .v-icon {
            margin-bottom: 8px;
        }
    }
}
</style>
