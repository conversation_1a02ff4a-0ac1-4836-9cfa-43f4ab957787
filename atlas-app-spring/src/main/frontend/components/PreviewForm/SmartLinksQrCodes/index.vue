<template>
    <div>
        <Loading v-if="getPageBuilderLoader.isLoading || getFormDefaultsLoader.isLoading" />
        <QrCodeWrapper v-else />
    </div>
</template>

<script>
import { defineComponent } from "vue";
import { get } from "vuex-pathify";
import Loading from "Components/PreviewForm/SmartLinksQrCodes/Loading.vue";
import QrCodeWrapper from "Components/PreviewForm/SmartLinksQrCodes/QrCodeWrapper.vue";

export default defineComponent({
    name: "SmartLinksQrCodes",
    components: { QrCodeWrapper, Loading },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    computed: {
        getPageBuilderLoader: get("pageConfigs/loader"),
        getFormDefaultsLoader: get("pageConfigs/form@loader"),
    },
});
</script>
