<template>
    <Collapsible>
        <template #title> {{ getTitle }} </template>
        <template #description> Status: <span v-if="form.optIn">Active</span><span v-else>Inactive</span></template>
        <template #body>
            <v-expansion-panels :value="subPanels[getTitle]" class="qr-code-manager" @change="handleSubPanelChange">
                <SubCollapsible
                    title="Opt-In to Smart Links/QR Codes*"
                    description="This determines if your dealership is participating in the Smart Links/QR Code feature."
                    :video="documentationLink"
                >
                    <template #body>
                        <div>
                            <div class="title-text">Opt-In to Smart Links/QR Codes</div>
                            <div class="mt-4">
                                <ToggleBtn
                                    :id="blockName + '/enabled'"
                                    v-model="form.optIn"
                                    label="Smart Links/QR Codes"
                                    :description="description"
                                    @input="handleToggleChange"
                                >
                                </ToggleBtn>
                            </div>
                        </div>
                    </template>
                </SubCollapsible>
            </v-expansion-panels>
        </template>
    </Collapsible>
</template>

<script>
import Collapsible from "Components/CollapsibleTypes/Collapsible";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible";
import ToggleBtn from "Components/FormInputs/ToggleBtn.vue";
import { call, get, sync } from "vuex-pathify";
import { normalizeBoolean } from "Util/helpers";

export default {
    name: "QrCodeManager",
    components: { Collapsible, SubCollapsible, ToggleBtn },
    props: {},
    data() {
        return {
            form: {
                optIn: false,
            },
            description:
                "*Dealer will be charged $.89 for all QR code scans and Smart Link clicks by new customers. Clicks and scans by customers who have agreed to the terms and conditions will not result in a charge. Charges are subject to change.",
            blockName: "marketingSettings/smartLinkQrCode/optIn",
        };
    },
    computed: {
        getForm: get("pageConfigs/form"),
        getBuilderData: get("pageConfigs/pageBuilderData"),
        subPanels: sync("pageConfigs/subPanels"),
        allOpenedSubPanelsState: sync("pageConfigs/allOpenedSubPanelsState"),
        expandAll: get("pageConfigs/expandAll"),

        getSection() {
            return this.getBuilderData.sections?.find((block) => block.title.startsWith("Smart Links/QR Code Opt-in"));
        },
        getTitle() {
            return this.getSection?.title || "";
        },
        documentationLink() {
            return this.getSection?.components?.[0]?.documentation.url || null;
        },
    },
    watch: {
        expandAll: {
            handler(val) {
                if (val) {
                    this.expandAllSubPanels();
                } else {
                    this.closeAllSubPanels();
                }
            },
            immediate: true,
        },
    },
    mounted() {
        this.form.optIn = normalizeBoolean(this.getForm.defaults?.[this.blockName + "/enabled"], false);
    },
    methods: {
        setFormData: call("pageConfigs/setFormData"),
        setDataDefaultsMapping: call("pageConfigs/setDataDefaultsMapping"),
        handleResetValidateForm: call("pageConfigs/handleResetValidateForm"),
        expandAllSubPanels() {
            this.subPanels = { ...this.subPanels, [this.getTitle]: 0 };
        },
        closeAllSubPanels() {
            this.subPanels = { ...this.subPanels, [this.getTitle]: null };
        },
        handleToggleChange(value) {
            if (!value) {
                this.handleResetValidateForm();
            }
        },
        handleSubPanelChange(value) {
            this.subPanels = { ...this.subPanels, [this.getTitle]: value };
        },
    },
};
</script>

<style lang="scss" scoped>
.qr-code-manager {
    > div {
        width: 100% !important;
    }
}

.title-text {
    color: var(--grey-grey-darken-3, #424242);
    font-weight: 600;
}
</style>
