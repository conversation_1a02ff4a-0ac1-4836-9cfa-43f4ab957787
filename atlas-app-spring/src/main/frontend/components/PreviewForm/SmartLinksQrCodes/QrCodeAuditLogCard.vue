<template>
    <SubCollapsible :expand="true">
        <!-- Title Section -->
        <template #title>
            <div class="timeline-card__title">
                <!-- Icon floating independently -->
                <v-icon size="18" class="timeline-card__icon">mdi-monitor-account</v-icon>
                <!-- Text aligned next to the icon -->
                <div class="d-flex flex-column">
                    <div class="timeline-card__user">
                        <span class="timeline-card__label">Changes:</span>
                        <span class="timeline-card__value">{{ event.userName }}</span>
                    </div>
                    <div class="timeline-card__description w-100">
                        <span class="timeline-card__date">{{ event.date | formatEpochDate | dateTimeFormat }}</span>
                        <span class="timeline-card__days-ago"> {{ getDaysToDate(event.date) }} days ago </span>
                    </div>
                </div>
            </div>
        </template>

        <!-- Body Section -->
        <template #body>
            <div class="timeline-card__content">
                <timeline :dot-size="8" dot-color="#4db6ac" :dense="true" :items="event.fields">
                    <template #content="{ item }">
                        <div class="change-log">
                            <span class="change-log__label">{{ item.fieldName }}:</span>
                            <span class="change-log__value">
                                Changed from {{ item.oldValue }} to {{ item.newValue }}</span
                            >
                        </div>
                    </template>
                </timeline>
            </div>
        </template>
    </SubCollapsible>
</template>

<script>
import { defineComponent } from "vue";
import SubCollapsible from "Components/CollapsibleTypes/SubCollapsible";
import { getDaysToDate } from "Util/helpers";
import timeline from "Components/Timeline";
export default defineComponent({
    name: "TimelineCard",
    components: { SubCollapsible, timeline },
    filters: {
        dateTimeFormat(dateString) {
            if (!dateString) return "";

            const parts = dateString.split(" ");
            if (parts.length < 2) return dateString; // If format is incorrect, return original

            const date = parts[0];
            const time = parts.slice(1).join(" ");

            return `${date} - ${time}`;
        },
    },
    props: {
        event: {
            type: Object,
            required: true,
            validator(value) {
                return typeof value === "object" && value.userName && value.date && Array.isArray(value.fields);
            },
        },
    },
    methods: {
        getDaysToDate,
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";

.timeline-card__title {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding-bottom: 20px;
    }
}

.timeline-card__icon {
    float: left;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px !important;
    height: 32px !important;
    background-color: var(--teal-teal-lighten-2, #4db6ac);
    border-radius: 50%;
    color: #000;
    padding: 8px;
}

.timeline-card__user {
    display: flex;
    flex-direction: row;
    gap: 8px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        flex-direction: column;
        gap: 0px;
    }
}

.timeline-card__label {
    font-size: 16px;
    font-weight: 600;
    color: var(--grey-grey-darken-4, #212121);
    line-height: 24px;
}

.timeline-card__value {
    font-size: 16px;
    color: var(--grey-grey-darken-3, #424242);
    line-height: 24px;
    font-weight: 500 !important;
}
.timeline-card__description {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}
.timeline-card__date {
    font-size: 14px;
    color: var(--grey-grey-darken-2, #616161);
    font-weight: 500 !important;
}
.timeline-card__days-ago {
    font-size: 14px;
    position: absolute;
    right: 12px;
    bottom: 14px;
    font-weight: 500 !important;
    color: var(--grey-grey-darken-2, #616161);
}
.timeline-card__timeline-item {
    margin-bottom: 12px;
}
.change-log {
    display: flex;
    flex-direction: row;
    gap: 4px;
    color: var(--grey-dark, #212121);
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        flex-direction: column;
    }
}
.change-log__label {
    font-weight: bold;
}
.change-log__value {
    color: var(--grey-grey-darken-4, #212121);
}
</style>
