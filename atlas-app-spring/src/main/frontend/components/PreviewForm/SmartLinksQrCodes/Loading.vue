<template>
    <div class="skeleton-loader">
        <!-- Loader for the first collapsible item -->
        <div class="skeleton-item">
            <div class="skeleton-title"></div>
            <div class="skeleton-status"></div>
        </div>

        <!-- Loader for the second collapsible item -->
        <div class="skeleton-item">
            <div class="skeleton-title"></div>
            <div class="skeleton-description"></div>
        </div>

        <div class="skeleton-item">
            <div class="skeleton-title"></div>
            <div class="skeleton-description"></div>
        </div>
    </div>
</template>

<script>
export default {
    name: "SkeletonLoader",
};
</script>

<style lang="scss" scoped>
.skeleton-loader {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .skeleton-item {
        background: #f4f4f4;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .skeleton-title,
        .skeleton-status,
        .skeleton-description {
            background: linear-gradient(90deg, #e0e0e0, #f4f4f4, #e0e0e0);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
            border-radius: 4px;
        }

        .skeleton-title {
            width: 60%;
            height: 1.2rem;
        }

        .skeleton-status {
            width: 40%;
            height: 1rem;
        }

        .skeleton-description {
            width: 80%;
            height: 1rem;
        }
    }
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
</style>
