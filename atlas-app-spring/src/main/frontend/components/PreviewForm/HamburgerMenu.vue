<template>
    <PreviewBuilder
        title="Hamburger Menu Settings"
        description="This determines the items shown in the hamburger menu."
    >
        <template #preview>
            <div class="preview-wrapper">
                <div class="preview">
                    <div v-for="(item, index) in filteredPreviewMenuItems" :key="index">
                        <div v-for="(subItem, idx) in item" :key="idx + subItem" class="preview-subItem">
                            <img class="preview-subItem-icon" :src="getIcon(subItem)" :alt="subItem.name" />
                            <h3 class="preview-subItem-title">{{ subItem.displayName }}</h3>
                        </div>
                        <v-divider
                            v-if="
                                item.length &&
                                index !== menuItems.length - 1 &&
                                filteredPreviewMenuItems[index + 1]?.length
                            "
                            class="preview-divider"
                        ></v-divider>
                    </div>
                </div>
            </div>
        </template>
        <template #configuration>
            <v-card class="configuration-layout">
                <h2 class="configuration-layout-text">Display</h2>
                <p class="configuration-layout-description">Choose items to display in the Hamburger Menu.</p>
                <v-divider></v-divider>
                <div class="configuration">
                    <div v-for="(item, index) in menuItems" :key="index" class="configuration-item">
                        <div v-for="(subItem, idx) in item" :key="idx + subItem" class="configuration-subItem">
                            <Checkbox
                                :label="subItem.name"
                                :value="subItem.selected"
                                :disabled="subItem.disabled"
                                class="configuration-checkbox"
                                @input="handleCheckboxChange($event, subItem.name)"
                            />
                        </div>
                        <v-divider v-if="index !== menuItems.length - 1" class="configuration-divider"></v-divider>
                    </div>
                    <p class="configuration-footer">Default items in grey.</p>
                </div>
            </v-card>
        </template>

        <template #footer>
            <update-btn />
        </template>
    </PreviewBuilder>
</template>

<script>
import { defineComponent } from "vue";
import PreviewBuilder from "Components/ConfigFormBuilder/PreviewFormBuilder/index.vue";
import Checkbox from "Components/FormInputs/Checkbox.vue";
import UpdateBtn from "Components/UpdateBtn.vue";

const ICONS = {
    "My Garage": require("@/assets/svgs/my-garage.svg"),
    "Shop New": require("@/assets/svgs/shop-new.svg"),
    "Shop Used": require("@/assets/svgs/shop-used.svg"),
    "Get Pre-qualified": require("@/assets/svgs/pre-qualified.svg"),
    "Trade Value": require("@/assets/svgs/trade-value.svg"),
    "Cash Offer": require("@/assets/svgs/cash-offer.svg"),
    Appointments: require("@/assets/svgs/appointments.svg"),
    "How It Works": require("@/assets/svgs/how-it-works.svg"),
    FAQs: require("@/assets/svgs/faqs.svg"),
    Profile: require("@/assets/svgs/profile.svg"),
    "Sign Out": require("@/assets/svgs/sign-out.svg"),
};

export default defineComponent({
    name: "HamburgerMenuPreview",
    components: { UpdateBtn, Checkbox, PreviewBuilder },
    props: {
        pageData: {
            type: Object,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            // TODO: Keeping this list static, need to visit later when backend has accounted for changes
            menuItems: [
                [{ name: "My Garage", displayName: "My Garage (2)", selected: true, disabled: false }],
                [
                    { name: "Shop New", displayName: "Shop New", selected: true, disabled: false },
                    { name: "Shop Used", displayName: "Shop Used", selected: true, disabled: false },
                    { name: "Get Pre-qualified", displayName: "Get Pre-qualified", selected: true, disabled: false },
                    { name: "Trade Value", displayName: "Trade Value", selected: true, disabled: false },
                    { name: "Cash Offer", displayName: "Cash Offer", selected: true, disabled: false },
                ],
                [
                    { name: "Appointments", displayName: "Appointments (2)", selected: true, disabled: false },
                    { name: "How It Works", displayName: "How It Works", selected: true, disabled: false },
                    { name: "FAQs", displayName: "FAQs", selected: true, disabled: false },
                    { name: "Profile", displayName: "Profile", selected: true, disabled: false },
                    { name: "Sign Out", displayName: "Sign Out", selected: true, disabled: false },
                ],
            ],
        };
    },
    computed: {
        filteredPreviewMenuItems() {
            return this.menuItems.map((row) => row.filter((item) => item.selected));
        },
    },
    methods: {
        getIcon(item) {
            return ICONS[item.name];
        },
        handleCheckboxChange(value, name) {
            for (let i = 0; i < this.menuItems.length; i++) {
                for (let j = 0; j < this.menuItems[i].length; j++) {
                    if (this.menuItems[i][j].name === name) {
                        this.menuItems[i][j].selected = !this.menuItems[i][j].selected;
                        break; // No need to continue loop once item is found
                    }
                }
            }
        },
    },
});
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";
// PREVIEW CARD
.preview-wrapper {
    display: flex;
    justify-content: center;
}
.preview {
    width: 265px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 8px;
    border-radius: 16px;
    border: 1px solid #d3d3d3;
    background: #fff;
    box-shadow: 0 4px 10px 0 rgba(63, 71, 79, 0.15);
}
.preview-subItem {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
}
.preview-subItem-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}
.preview-divider {
    margin: 2px 16px;
}
.preview-subItem-title {
    color: #2e2e2e;
    font-size: px2rem(14);
    font-weight: 500;
}
// PREVIEW CARD

// CONFIGURATION LAYOUT
.configuration-layout {
    height: fit-content;
    padding: 16px 24px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        padding: 16px 8px;
    }
}
.configuration-layout-text {
    font-size: px2rem(16);
    margin-bottom: 4px;
}
.configuration-layout-description {
    font-size: px2rem(14);
    margin-bottom: 16px;
}
// CONFIGURATION LAYOUT

// CONFIGURATION
.configuration {
    margin-top: 24px;
}
.configuration-item {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 12px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        gap: 4px;
    }
}
.configuration-divider {
    grid-column: span 2 / span 2;
    margin: 0 16px 12px 16px;
}
.configuration-subItem {
    display: flex;
    align-items: center;
    gap: 8px;
}
.configuration-checkbox {
    margin: 0 !important;
    padding: 0 !important;
}
.configuration-footer {
    margin-top: 24px;
    margin-bottom: 0;
    font-size: px2rem(14);
    color: rgba(0, 0, 0, 0.6);
    font-style: italic;
}
// CONFIGURATION
</style>
