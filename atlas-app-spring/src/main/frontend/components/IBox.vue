<template>
    <div class="ibox">
        <div v-if="title" class="ibox-title">
            <h5>
                {{ title }}
                <v-btn v-if="description" :title="description">
                    <i aria-hidden="true" class="fas fa-info-circle" />
                </v-btn>
                <v-btn v-if="alertMessage" :title="alertMessage">
                    <i aria-hidden="true" class="fas fa-exclamation-triangle" />
                </v-btn>
            </h5>
            <div class="ibox-tools">
                <slot name="header-actions" />
                <a v-if="collapsible" class="collapse-link">
                    <i aria-hidden="true" class="fa fa-chevron-up" />
                </a>
            </div>
        </div>
        <div class="ibox-content clearfix" :class="{ 'sk-loading': loading }">
            <div class="sk-spinner sk-spinner-wave">
                <div class="sk-rect1" />
                <div class="sk-rect2" />
                <div class="sk-rect3" />
                <div class="sk-rect4" />
                <div class="sk-rect5" />
            </div>
            <slot />
        </div>
    </div>
</template>

<script>
export default {
    props: {
        title: {
            type: String,
            required: false,
            default: null,
        },
        description: {
            type: String,
            required: false,
            default: null,
        },
        alertMessage: {
            type: String,
            required: false,
            default: null,
        },
        loading: {
            type: Boolean,
            required: false,
            default: false,
        },
        collapsible: {
            type: Boolean,
            required: false,
            default: true,
        },
    },
};
</script>

<style lang="scss">
.fa-exclamation-triangle {
    color: red;
}

.ibox-tools a.btn-light {
    color: #212529;
}
</style>
