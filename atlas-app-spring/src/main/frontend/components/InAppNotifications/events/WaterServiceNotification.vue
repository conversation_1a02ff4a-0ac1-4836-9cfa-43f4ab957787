<template>
    <div class="notification">
        <a :href="customerPage" class="link" @click="$emit('click')">{{ notification.fullName }}</a>
        is at your dealership and requested water. Please take immediate action.
    </div>
</template>

<script>
import NotificationNavigationMixin from "Components/InAppNotifications/mixin/NotificationNavigationMixin";

export default {
    name: "WaterServiceNotification",
    mixins: [NotificationNavigationMixin],

    props: {
        notification: {
            type: Object,
            required: true,
        },
    },
};
</script>
<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.notification {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 12px;
    }
    .link {
        color: white;
        text-decoration: underline;
    }
}
</style>
