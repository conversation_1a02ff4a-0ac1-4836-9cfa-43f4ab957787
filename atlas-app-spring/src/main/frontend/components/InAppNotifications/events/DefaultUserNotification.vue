<template>
    <div class="notification">
        <a :href="customerPage" class="link" @click="$emit('click')">{{ notification.fullName }}</a>
        {{ notification.message }}
    </div>
</template>

<script>
import { defineComponent } from "vue";
import NotificationNavigationMixin from "Components/InAppNotifications/mixin/NotificationNavigationMixin";

export default defineComponent({
    name: "DefaultNotification",
    mixins: [NotificationNavigationMixin],
    props: {
        notification: {
            type: Object,
            required: true,
        },
    },
});
</script>

<style lang="scss" scoped>
@import "~vuetify/src/styles/settings/_variables";
.notification {
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        font-size: 12px;
    }
    .link {
        color: white;
        text-decoration: underline;
    }
}
</style>
