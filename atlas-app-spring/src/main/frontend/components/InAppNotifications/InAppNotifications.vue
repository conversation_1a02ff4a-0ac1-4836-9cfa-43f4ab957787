<template>
    <div class="notification-container">
        <transition-group name="slide-right" tag="div">
            <v-snackbar
                v-for="(notification, idx) in visibleNotifications"
                :key="notification.time"
                :value="true"
                :timeout="notification.timeout || -1"
                :style="{
                    'margin-bottom': `${idx * 74}px`,
                    transition: isInitialLoad ? `all 0.5s ease ${idx * 0.25}s` : 'all 0.5s ease',
                }"
                color="info"
                right
                dark
                max-width="500"
                class="notification-item"
            >
                <!-- NOTIFICATION CONTENT -->
                <div class="snackbar-content">
                    <v-icon> mdi-information-outline </v-icon>
                    <NotificationContent
                        :notification="notification"
                        @click="
                            handleNotificationClicked({
                                userId: notification.userId,
                                id: notification.time,
                            })
                        "
                    />
                </div>
                <!-- NOTIFICATION CONTENT -->

                <template #action="{ attrs }">
                    <v-btn
                        dark
                        text
                        v-bind="attrs"
                        icon
                        @click="
                            handleCloseNotification({
                                userId: notification.userId,
                                id: notification.time,
                            })
                        "
                    >
                        <v-icon small>mdi-close</v-icon>
                    </v-btn>
                </template>
            </v-snackbar>
        </transition-group>
    </div>
</template>

<script>
import { defineComponent } from "vue";
import { get, call } from "vuex-pathify";
import NotificationContent from "Components/InAppNotifications/NotificationContent.vue";
import { NOTIFICATION_ACTIONS } from "./constants";

export default defineComponent({
    name: "InAppNotifications",
    components: { NotificationContent },
    data() {
        return {
            isInitialLoad: true,
        };
    },
    computed: {
        userId: get("loggedInUser/userId"),
        getNotifications: get("inAppNotification/notifications@data"),
        visibleNotifications() {
            return this.getNotifications;
        },
        dealerId() {
            return this.$route.query.dealerIds;
        },
    },
    watch: {
        dealerId: {
            async handler(newDealerId) {
                this.isInitialLoad = true;
                this.removeAllNotifications();
                await this.handleFetchNotifications({ dealerId: newDealerId });
            },
        },
    },
    async mounted() {
        setTimeout(() => {
            this.handleFetchNotifications({ dealerId: this.dealerId });
        }, 1000);
    },
    methods: {
        fetchNotifications: call("inAppNotification/fetchNotifications"),
        removeNotifications: call("inAppNotification/removeNotifications"),
        removeAllNotifications: call("inAppNotification/removeAllNotifications"),
        handleCloseNotification({ userId, id }) {
            this.removeNotifications({ id, userId, eventType: NOTIFICATION_ACTIONS.DISPLAYED });
        },
        handleNotificationClicked({ userId, id }) {
            this.removeNotifications({ id, userId, eventType: NOTIFICATION_ACTIONS.CTA_CLICKED });
        },
        async handleFetchNotifications({ dealerId }) {
            await this.fetchNotifications({ userId: this.userId, dealerId });
            setTimeout(() => {
                this.isInitialLoad = false;
            }, 50);
        },
    },
});
</script>

<style scoped lang="scss">
.notification-item {
    .snackbar-content {
        display: flex;
        gap: 16px;
        align-items: center;
        color: white;
    }
}

/* Slide-right transition styles */
.slide-right-enter-active,
.slide-right-leave-active {
    transition: all 0.5s ease;
}
.slide-right-enter {
    transform: translateX(100%); /* Start from off-screen to the right */
    opacity: 0;
}
.slide-right-leave-to {
    transform: translateX(100%); /* Exit off-screen to the right */
    opacity: 0;
}
</style>
