<template>
    <div v-if="filtersNotEmpty">
        <v-container fluid>
            <v-row>
                <v-col cols="12">
                    <v-chip-group>
                        <div v-for="(value, key) in filters" :key="key" class="chip-wrapper">
                            <v-chip
                                v-if="hasFilters(key)"
                                :title="getValue(key, value, false)"
                                close
                                color="primary"
                                outlined
                                @click:close="clearFilter(key)"
                            >
                                <span>
                                    {{ getLabel(key) }}:

                                    <span>{{ getValue(key, value, true) }}</span>
                                </span>
                            </v-chip>
                        </div>
                    </v-chip-group>
                </v-col>
            </v-row>
        </v-container>
    </div>
</template>

<script>
import _ from "lodash";
import { renameDisplayValue } from "@/util/renameUtils";
import { isRangedFilter, rangedFilters, snakeToTitleCase, filterNameTransforms } from "Util/searchUtils";

export default {
    name: "FilterChips",
    props: {
        store: {
            type: String,
            required: true,
        },
        filterField: {
            type: String,
            default: "filters",
            required: false,
        },
        facetField: {
            type: String,
            default: "facets",
            required: false,
        },
        pillsField: {
            type: String,
            default: "pills",
            required: false,
        },
    },

    computed: {
        pills() {
            const filterProp = this.store + "." + this.pillsField;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? [] : f;
        },
        facets() {
            const filterProp = this.store + "." + this.facetField;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? [] : f;
        },
        filters() {
            const filterProp = this.store + "." + this.filterField;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? [] : f;
        },
        filtersNotEmpty() {
            return !_.isEmpty(this.filters);
        },
    },

    methods: {
        searchMethod(field) {
            const filterProp = this.store + ".searchMethods." + field;
            const f = _.get(this.$store.state, filterProp);
            return _.isNil(f) ? "NEGATIVE" : f;
        },
        isFilterChipEnabled(key, filter, isEnabled) {
            if (key === "programs" && this.filters != null && this.facets != null) {
                let filterPrograms = this.filters["programs"];
                let facetPrograms = this.facets["programs"];

                if (facetPrograms == null || typeof facetPrograms === "undefined" || facetPrograms.length === 0) {
                    this.$store.dispatch(`${this.store}/loadFacetInfo`, "programs");
                }

                facetPrograms = this.facets["programs"];
                if (facetPrograms.data == null || facetPrograms.data.length === 0) {
                    isEnabled = false;
                }

                if (filterPrograms == null || filterPrograms.length === 0) {
                    isEnabled = false;
                }
            } else if (key === "dmaCodes" && this.filters != null && this.facets != null) {
                let filterDMAs = this.filters["dmaCodes"];
                let facetDMAs = this.facets["dmas"];

                if (facetDMAs == null || typeof facetDMAs === "undefined" || facetDMAs.length === 0) {
                    this.$store.dispatch(`${this.store}/loadFacetInfo`, "dmas");
                }

                facetDMAs = this.facets["dmas"];
                if (facetDMAs.data == null || facetDMAs.data.length === 0) {
                    isEnabled = false;
                }

                if (filterDMAs == null || filterDMAs.length === 0) {
                    isEnabled = false;
                }
            } else {
                isEnabled = !_.isNil(filter);
            }

            return isEnabled;
        },
        hasFilters(key) {
            // if the key isn't excluded we show the pills for it
            let isEnabled = _.get(this.pills, key + ".enabled", true);
            const filter = _.get(this.filters, key);

            if (key.toLowerCase() === "topdmas") {
                if (_.isNil(filter)) {
                    return false;
                }

                isEnabled = !(
                    _.get(this.filters, key + ".start") === null && _.get(this.filters, key + ".end") === null
                );
            } else if (isEnabled) {
                isEnabled = this.isFilterChipEnabled(key, filter, isEnabled);
            }

            if (key.toLowerCase() === "miles.start" || key.toLowerCase() === "miles.end") {
                return false;
            }
            if (key.toLowerCase() === "selecteduserprogramid") {
                isEnabled = false;
            }
            return isEnabled;
        },
        getLabel(key) {
            const label = _.get(this.pills[key], "label", _.startCase(key));
            const namedLabel = filterNameTransforms[key];

            return namedLabel || label;
        },
        getValue(key, value, shorten) {
            // we will attempt to see if the associated filter maps to a facet and get the display value of that
            // This is useful when using userId or other IDs and we want to display a proper value
            value = renameDisplayValue(value);
            const facetName = _.get(this.pills[key], "facet", key);
            const type = _.get(this.pills[key], "type");

            const prefix = this.searchMethod(key) === "NEGATIVE" ? "Removing: " : "";

            if (shorten && _.isArray(value) && value.length > 2) {
                return `(${value.length} terms)`;
            }

            if (_.isNil(this.facets[facetName])) {
                const getFilterNameFromStore = (v) => {
                    const filterNameProp = this.store + ".filterNames." + facetName + ".data";
                    const filterData = _.get(this.$store.state, filterNameProp);
                    const filterName = _.find(filterData, function (o) {
                        return _.toString(o.id) === _.toString(v);
                    });
                    const filter = _.get(filterName, "name", v);
                    if (facetName === "tradePaymentType") {
                        return snakeToTitleCase(filter);
                    }
                    return filter;
                };
                if (_.isArray(value)) {
                    const formattedValues = [];
                    _.forEach(value, (v) => {
                        let filterName = getFilterNameFromStore(v);
                        formattedValues.push(filterName);
                    });
                    return prefix + _.join(formattedValues, ", ");
                }
                if (facetName === "miles" && value) {
                    return this.getMileRangeValue(value);
                }
                if (value && isRangedFilter(key)) {
                    const filter = rangedFilters.find((item) => item.key === key);
                    if (filter?.prefix != null) {
                        return `${filter.prefix}${value.start} - ${filter.prefix}${value.end}`;
                    }
                    if (filter?.suffix != null) {
                        return `${value.start}${filter.suffix} - ${value.end}${filter.suffix}`;
                    }
                    return `$${value.start} - $${value.end}`;
                }
                return prefix + getFilterNameFromStore(value);
            } else if (!_.isNil(this.facets[facetName]) || type === "range") {
                if (_.isArray(value)) {
                    const formattedValues = [];
                    _.forEach(value, (v) => {
                        const facetData = _.get(this.facets[facetName], "data");
                        const facet = _.find(facetData, function (o) {
                            return _.toString(o.id) === _.toString(v);
                        });
                        formattedValues.push(_.get(facet, "name", v));
                    });

                    return prefix + _.join(formattedValues, ", ");
                } else if (type === "range" && value) {
                    const start = _.get(value, "start", null);
                    const end = _.get(value, "end", null);

                    if (_.isNil(start) && _.isNil(end)) {
                        return "no values";
                    } else if (_.isNil(start) && !_.isNil(end)) {
                        return `up to ${end}`;
                    } else if (!_.isNil(start) && _.isNil(end)) {
                        return `from ${start}`;
                    } else {
                        return `between ${start} and ${end}`;
                    }
                } else if (facetName === "miles" && value) {
                    return this.getMileRangeValue(value);
                } else {
                    const facetData = _.get(this.facets[facetName], "data");
                    const facet = _.find(facetData, function (o) {
                        return _.toString(o.id) === _.toString(value);
                    });
                    return prefix + _.get(facet, "name", value);
                }
            }

            if (_.isString(value)) {
                return prefix + value;
            } else if (_.isArray(value)) {
                return prefix + _.join(value, ", ");
            } else if (_.has(value, "start") && _.has(value, "end")) {
                return prefix + `${value.start} - ${value.end}`;
            }

            return value;
        },
        clearFilter(filterName) {
            if (filterName === "miles") {
                this.$store.commit("inventorySearch/SET_SELECTED_MILES", null);
            }
            return this.$store.dispatch(this.store + "/clearFilter", filterName);
        },
        getMileRangeValue(value) {
            const start = _.get(value, "start", null);
            const end = _.get(value, "end", null);
            return `between ${start} and ${end}`;
        },
    },
};
</script>
