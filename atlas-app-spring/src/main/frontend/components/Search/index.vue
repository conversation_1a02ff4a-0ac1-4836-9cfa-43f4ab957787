<template>
    <v-container fluid class="main search">
        <v-row class="search-list-row">
            <v-col cols="12">
                <slot name="pills"></slot>
                <slot name="searchForm" />
                <slot name="searchTiles" />
                <slot name="searchList" />
            </v-col>
        </v-row>
        <v-navigation-drawer v-model="drawer" :mini-variant.sync="mini" permanent fixed right width="300">
            <v-list-item class="px-2" @click.stop="mini = !mini">
                <v-list-item-avatar>
                    <v-icon>mdi-filter</v-icon>
                </v-list-item-avatar>

                <v-list-item-title>Filters</v-list-item-title>
                <v-btn icon>
                    <v-icon>mdi-chevron-right</v-icon>
                </v-btn>
            </v-list-item>

            <v-divider />

            <div v-if="hasFacetsSlot()">
                <slot name="searchFacets" />
            </div>
        </v-navigation-drawer>
    </v-container>
</template>
<script>
export default {
    name: "SearchPage",
    data: () => ({
        mini: true,
        drawer: true,
    }),
    methods: {
        hasFacetsSlot() {
            return this.$slots.searchFacets;
        },
    },
};
</script>
<style lang="scss" scoped>
.search {
    background-color: $gray-200;
    .search-list-row {
        margin-right: 45px;
    }
}
.v-navigation-drawer {
    height: 100%;
    top: 64px !important;
    z-index: 1;
    padding-bottom: 110px;
}

@import "~vuetify/src/styles/settings/_variables";
@media #{map-get($display-breakpoints, 'sm-and-down')} {
    .v-navigation-drawer {
        top: 56px !important;
    }
}
</style>
