<template>
    <div class="file-upload-wrapper d-flex flex-column mb-5">
        <div v-if="!lodashIsEmpty(label)">
            {{ label }}
        </div>
        <div class="file-upload-container">
            <div v-if="imageFound">
                <a v-if="isImage" :href="previewURL" target="_blank" rel="noopener">
                    <img :src="previewURL" alt="Preview" class="preview-image" />
                </a>
                <div class="preview-image-meta-data">
                    <p class="info-text">{{ file.name }}</p>
                    <p class="info-text">{{ formattedFileSize }}</p>
                </div>
            </div>
            <div v-else>
                <v-icon large>mdi-file-upload-outline</v-icon>
                <p class="info-text" :class="{ 'limit-width': !imageFound }">{{ uploadInstructions }}</p>
            </div>

            <input
                ref="fileUploadInputRef"
                type="file"
                :accept="acceptedFormats"
                style="display: none"
                @change="handleFileChange"
            />

            <v-btn v-if="imageFound" outlined large @click="removeFile('image', id)">
                <v-icon class="mr-2 delete-icon">mdi-delete-outline</v-icon> Remove File
            </v-btn>
            <v-btn v-else large @click="triggerFileInput">Upload File</v-btn>
        </div>
        <p v-if="moreFileSizeInfoFooter">
            {{ recommendation }}
        </p>
    </div>
</template>

<script>
import { defineComponent } from "vue";
import lodashIsEmpty from "lodash/isEmpty";
export default defineComponent({
    name: "FileUpload",
    props: {
        id: {
            type: String,
            default: "",
            required: false,
        },
        initialFile: {
            type: Object,
            default: () => ({
                url: null,
                name: null,
                size: null,
            }),
            required: false,
        },
        maxSize: {
            type: Number,
            default: 0.5 * 1024 * 1024, // Default max size is 500KB
            required: false,
        },
        acceptedFormats: {
            type: String,
            default: "image/jpeg, image/png", // Default accepted formats
            required: false,
        },
        uploadInstructions: {
            type: String,
            default: "JPG, PNG file type 500KB max file size", // Default upload instructions
            required: false,
        },
        moreFileSizeInfoFooter: {
            type: Boolean,
            default: true,
            required: false,
        },
        baseKeyPath: {
            type: String,
            default: "",
            required: false,
        },
        recommendation: {
            type: String,
            default: "",
            required: false,
        },
        label: {
            type: String,
            default: "",
            required: false,
        },
        maxWidth: {
            type: Number,
            default: null,
            required: false,
        },
        maxHeight: {
            type: Number,
            default: null,
            required: false,
        },
        aspectRatio: {
            type: String,
            default: null,
            required: false,
        },
    },
    emits: ["file-selected", "file-removed"],
    data() {
        return {
            file: null,
            previewURL: null,
            isImage: false,
        };
    },
    computed: {
        formattedFileSize() {
            if (!this.file) return "";

            // Check if file size is a string that contains "kb" (case insensitive)
            if (typeof this.file.size === "string" && this.file.size.toLowerCase().includes("kb")) {
                return this.file.size;
            }

            const fileSize = this.file.size;
            if (fileSize >= 1024 * 1024) {
                return (fileSize / (1024 * 1024)).toFixed(2) + " MB";
            } else {
                return (fileSize / 1024).toFixed(2) + " KB";
            }
        },
        imageFound() {
            if (this.file instanceof File && this.file.type.includes("image")) {
                return true;
            } else if (this.file instanceof Object && this.file.url) {
                return true;
            }
            return false;
        },
    },
    watch: {
        initialFile: {
            handler() {
                this.initFileHandler();
            },
            immediate: true,
        },
    },
    methods: {
        lodashIsEmpty,
        initFileHandler() {
            if (this.initialFile) {
                this.file = this.initialFile;

                if (this.file instanceof File && this.file.type.includes("image")) {
                    this.isImage = true;
                    this.previewURL = URL.createObjectURL(this.file);
                } else if (this.file instanceof Object && this.file.url) {
                    this.isImage = true;
                    this.previewURL = this.file.url;
                }
            }
        },
        async handleFileChange(event) {
            const selectedFile = event.target.files[0];
            // Clear the input value immediately to ensure change event fires on next upload
            event.target.value = null;

            if (selectedFile) {
                if (selectedFile.size > this.maxSize) {
                    alert(`File size exceeds ${this.maxSize / (1024 * 1024)}MB limit.`);
                    return;
                }

                if (!this.isFormatAccepted(selectedFile.type)) {
                    alert("Invalid file format.");
                    return;
                }

                const isImageCorrupt = await this.checkImageCorruption(selectedFile);

                if (isImageCorrupt.isCorrupt) {
                    alert(isImageCorrupt.message);
                    return;
                }

                // Check image dimensions if it's an image and dimension limits are specified
                if (selectedFile.type.includes("image") && (this.maxWidth || this.maxHeight || this.aspectRatio)) {
                    const dimensionCheck = await this.checkImageDimensions(selectedFile);
                    if (dimensionCheck.exceedsDimensions) {
                        alert(dimensionCheck.message);
                        return;
                    }
                }

                this.file = selectedFile;
                if (selectedFile.type.includes("image")) {
                    this.isImage = true;
                    this.previewURL = URL.createObjectURL(selectedFile);
                } else {
                    this.isImage = false;
                    this.previewURL = null;
                }
                this.$emit("file-selected", this.file);
                this.handleChange(selectedFile.name, "imageName", this.id + "image-name");
                this.handleChange(selectedFile.size, "imageSize", this.id + "image-size");
                this.handleChange(this.previewURL, "imageUrl", this.id + "image-url");
            }
        },
        handleChange(value, property, key) {
            this.$emit("change", { value, property, key });
        },
        checkImageCorruption(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function (event) {
                    const img = new Image();
                    img.onload = () => {
                        // Image loaded successfully, it's likely not corrupt in a basic sense
                        resolve({ isCorrupt: false, message: "Image is valid." });
                    };
                    img.onerror = () => {
                        // Error loading the image data, could be corrupt or unsupported format
                        resolve({ isCorrupt: true, message: "Image is corrupt or cannot be loaded." });
                    };
                    // Corrected line: Assign the loaded data URL to the image source
                    img.src = event.target.result;
                };

                reader.onerror = () => {
                    // Error during the file reading process itself
                    resolve({ isCorrupt: true, message: "Error reading the file." });
                };
                // Start reading the file as a Data URL
                reader.readAsDataURL(file);
            });
        },
        checkImageDimensions(file) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const width = img.width;
                        const height = img.height;

                        let exceedsDimensions = false;
                        let message = "";

                        if (this.maxWidth && width > this.maxWidth) {
                            exceedsDimensions = true;
                            message += `Image width (${width}px) exceeds maximum allowed width (${this.maxWidth}px). `;
                        }

                        if (this.maxHeight && height > this.maxHeight) {
                            exceedsDimensions = true;
                            message += `Image height (${height}px) exceeds maximum allowed height (${this.maxHeight}px). `;
                        }

                        // Check aspect ratio if specified
                        if (this.aspectRatio && !exceedsDimensions) {
                            const aspectRatioCheck = this.validateAspectRatio(width, height, this.aspectRatio);
                            if (!aspectRatioCheck.isValid) {
                                exceedsDimensions = true;
                                message += aspectRatioCheck.message;
                            }
                        }

                        resolve({
                            exceedsDimensions,
                            message: message.trim(),
                            width,
                            height,
                        });
                    };
                    img.onerror = () => {
                        resolve({
                            exceedsDimensions: true,
                            message: "Could not read image dimensions.",
                            width: 0,
                            height: 0,
                        });
                    };
                    img.src = event.target.result;
                };

                reader.onerror = () => {
                    resolve({
                        exceedsDimensions: true,
                        message: "Error reading file for dimension check.",
                        width: 0,
                        height: 0,
                    });
                };

                reader.readAsDataURL(file);
            });
        },
        validateAspectRatio(width, height, expectedRatio) {
            // Parse aspect ratio string (e.g., "16:9", "1:1")
            const [expectedWidth, expectedHeight] = expectedRatio.split(":").map(Number);

            if (!expectedWidth || !expectedHeight) {
                return { isValid: false, message: "Invalid aspect ratio format." };
            }

            const actualRatio = width / height;
            const expectedActualRatio = expectedWidth / expectedHeight;

            // Allow for small floating point differences
            const tolerance = 0.1;
            const isValid = Math.abs(actualRatio - expectedActualRatio) <= tolerance;

            if (!isValid) {
                return {
                    isValid: false,
                    message: `Image aspect ratio (${width}:${height}) does not match required aspect ratio (${expectedRatio}).`,
                };
            }

            return { isValid: true, message: "" };
        },
        isFormatAccepted(fileType) {
            const acceptedFormats = this.acceptedFormats.split(",").map((format) => format.trim());
            return acceptedFormats.includes(fileType);
        },
        removeFile(property, key) {
            this.file = null;
            this.previewURL = null;
            this.isImage = false;
            this.$emit("file-removed", { value: this.file, property, key });
        },
        triggerFileInput() {
            this.$refs.fileUploadInputRef.click();
        },
    },
});
</script>

<style scoped lang="scss">
.file-upload-wrapper {
    .delete-icon {
        font-size: 18px;
    }
    .label {
        font-size: px2rem(14);
        font-weight: 600;
    }
    p {
        margin: 0;
    }
    .info-text {
        font-size: px2rem(14);
        line-height: 20px;
        color: #535353;
    }
    .limit-width {
        max-width: 125px;
    }
    .file-upload-container {
        margin-top: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;

        .preview-image {
            max-width: 36px;
            max-height: 36px;
        }

        .preview-image-meta-data {
            display: flex;
            flex-direction: column;
        }

        & > div {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        & > i {
            width: 36px;
            height: 36px;
        }
    }
    & > p {
        margin-top: 16px;
        font-size: px2rem(14);
        font-style: italic;
        color: var(--grey-grey-darken-2, #616161);
    }
}
</style>
