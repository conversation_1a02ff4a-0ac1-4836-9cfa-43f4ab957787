<template>
    <div class="pagination">
        <div class="pagination-info d-none d-md-flex">
            <span> Rows per page: </span>
            <v-select
                v-model="localPagination.itemsPerPage"
                :items="rowsPerPageOptions"
                hide-details
                @change="onItemsPerPageChange"
            >
            </v-select>
        </div>
        <div>
            <span> {{ pageStart }}-{{ pageEnd }} of {{ totalItems }}</span>
        </div>

        <div class="pagination-controls">
            <v-btn icon :disabled="onFirstPage" aria-label="Previous Page" class="arrow-btn" @click="goToPreviousPage">
                <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            <v-btn icon :disabled="onLastPage" aria-label="Next Page" class="arrow-btn" @click="goToNextPage">
                <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
        </div>
    </div>
</template>

<script>
export default {
    name: "Pagination",
    props: {
        pagination: {
            type: Object,
            required: true,
            default: () => ({
                page: 1,
                itemsPerPage: 10,
                totalItems: 0,
            }),
        },
        rowsPerPageOptions: {
            type: Array,
            default: () => [5, 10, 20, 50, 100],
        },
    },
    emits: ["update:pagination"],
    data() {
        return {
            localPagination: { ...this.pagination },
        };
    },
    computed: {
        totalItems() {
            return this.pagination?.totalItems;
        },
        totalPages() {
            return Math.ceil(this.totalItems / this.localPagination.itemsPerPage) || 1;
        },
        pageStart() {
            return (this.localPagination.page - 1) * this.localPagination.itemsPerPage + 1;
        },
        pageEnd() {
            return Math.min(this.localPagination.page * this.localPagination.itemsPerPage, this.totalItems);
        },
        onFirstPage() {
            return this.localPagination.page === 1;
        },
        onLastPage() {
            return this.localPagination.page === this.totalPages;
        },
    },
    watch: {
        pagination: {
            deep: true,
            handler(newVal) {
                this.localPagination = { ...newVal };
            },
        },
        localPagination: {
            handler(newVal) {
                this.$emit("update:pagination", { ...newVal }, { local: true });
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        updatePagination() {
            this.$emit("update:pagination", { ...this.localPagination }, { local: false });
        },
        goToPreviousPage() {
            if (this.localPagination.page > 1) {
                this.localPagination.page -= 1;
                this.updatePagination();
            }
        },
        goToNextPage() {
            if (this.localPagination.page < this.totalPages) {
                this.localPagination.page += 1;
                this.updatePagination();
            }
        },
        onItemsPerPageChange() {
            this.localPagination.page = 1;
            this.updatePagination();
        },
    },
};
</script>

<style scoped lang="scss">
@import "~vuetify/src/styles/settings/_variables";

.pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 39px;
    height: 58px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        justify-content: center;
        gap: 12px;
    }
}
.pagination-info {
    display: flex;
    align-items: center;
    gap: 35px;
}
.pagination-controls {
    display: flex;
    align-items: center;
    gap: 22px;
    @media #{map-get($display-breakpoints, 'sm-and-down')} {
        gap: 6px;
    }
}
.arrow-btn {
    cursor: pointer;
    border: none;
    background-color: transparent;
    font-size: 18px;
}
.arrow-btn:disabled {
    cursor: not-allowed;
    color: gray;
}
:deep(.v-input__control) {
    max-width: 60px !important;
}

.v-text-field {
    padding-top: 0px;
    margin-top: 0px;
}
</style>
