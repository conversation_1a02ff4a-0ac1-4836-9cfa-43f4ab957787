<template>
    <div class="in-transit-flag d-flex">
        <span class="mr-1">
            {{ stockType }} In Transit ({{ dealerEstimatedTimeOfArrival | moment("MM/DD/YYYY") }})
        </span>
        <info-tooltip size="13">
            This vehicle is currently in transit to the dealership.
            <span v-if="dealerEstimatedTimeOfArrival">
                It will be available on {{ dealerEstimatedTimeOfArrival | moment("MM/DD/YYYY") }}.
            </span>
        </info-tooltip>
    </div>
</template>
<script>
import _ from "lodash";
import InfoTooltip from "Components/InfoTooltip";
export default {
    name: "InTransitFlag",
    components: { InfoTooltip },
    props: {
        vehicle: {
            type: Object,
            required: true,
        },
    },
    computed: {
        dealerEstimatedTimeOfArrival() {
            return _.get(this.vehicle, "dealerEstimatedTimeOfArrival");
        },
        stockType() {
            const stockTypeName = _.get(this.vehicle, "stockType", "");

            return stockTypeName.toLowerCase();
        },
    },
};
</script>
<style lang="scss">
.in-transit-flag {
    font-size: px2rem(12);
    line-height: px2rem(14);
    text-transform: capitalize;
    color: red;
}
</style>
