package com.carsaver.partner.digitalretailotp;

import com.carsaver.magellan.client.config.CarSaverFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(name = "digitalRetailOtpClient", url = "${digital-retail.otp-api-uri}", decode404 = true, configuration = CarSaverFeignConfig.class)
public interface DigitalRetailOtpClient {

    @PostMapping("/")
    Object createDealerToken(@RequestBody Map<String, String> payload);
}
