package com.carsaver.partner.digitalretailotp;

import com.carsaver.partner.model.digital_retail.response.Otp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@AllArgsConstructor
@RequestMapping("/api/digital-retail/logins")
@RestController
public class DigitalRetailOtpController {

    private final DigitalRetailOtpService digitalRetailOtpService;

    @PostMapping("/dealer-otps")
    public ResponseEntity<Object> createDealerToken(
        @RequestBody Map<String, String> payload
    ) {
        Otp otpToken;

        try {
            otpToken = digitalRetailOtpService.createDealerToken(payload);
            return ResponseEntity.ok(otpToken.getData());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
    }
}
