package com.carsaver.partner;

import com.carsaver.sba.annotation.EnableSpringBootAdminAmazonEcs;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
@SpringBootApplication
public class AtlasApplication {

    public static void main(String[] args) {
        SpringApplication.run(AtlasApplication.class, args);
    }

    @Component
    @Slf4j
    public static class UsernamePrinter {

        public UsernamePrinter(@Value("${spring.datasource.username:}") String username) {
            log.warn("DB Username is {}", username);
        }
    }

    @EnableSpringBootAdminAmazonEcs
    @Configuration
    @Profile("!test")
    public static class SpringBootAdminProfile {

    }

    public static CloseableHttpClient client() {
        return HttpClients
            .custom()
            .setConnectionManager(new PoolingHttpClientConnectionManager())
            .disableAutomaticRetries()
            .disableCookieManagement()
            .build();
    }
}
