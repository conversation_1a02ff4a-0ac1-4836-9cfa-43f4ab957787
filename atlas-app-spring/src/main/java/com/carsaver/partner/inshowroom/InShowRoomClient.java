package com.carsaver.partner.inshowroom;

import com.carsaver.magellan.model.connection.DealerConnectionRequest;
import com.carsaver.partner.client.oauth.OAuthClient;
import com.carsaver.partner.http.HttpRequest;
import com.carsaver.partner.http.HttpService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class InShowRoomClient {

    @Value("${lead-service.api-uri}") String leadServiceApiUri;
    private final HttpService httpService;
    private final OAuthClient oAuthClient;

    /**
     * Retrieves credit profile for a given user id.
     *
     * @param timeWindowHours
     * @param dealerId
     * @return userNotesResponse
     */
    public List<DealerConnectionRequest> getDealerConnectionsByType(Integer timeWindowHours, String dealerId, List<String>  leadTypes) {

        // create the request
        HttpRequest httpRequest = HttpRequest
            .get(leadServiceApiUri, "/v2/dealer-connections/recent")
            .queryParam("leadTypes", String.join(",",leadTypes))
            .queryParam("dealerId", dealerId)
            .queryParam("timeWindowHours", timeWindowHours)
            .bearerToken(oAuthClient.getAccessToken());

        // return the response
        return httpService.getSuccessResponse(httpRequest, new TypeReference<>() {
        });
    }




}
