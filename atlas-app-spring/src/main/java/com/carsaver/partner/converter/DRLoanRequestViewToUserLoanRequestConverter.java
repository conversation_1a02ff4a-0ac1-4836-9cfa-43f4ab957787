package com.carsaver.partner.converter;

import com.carsaver.magellan.api.finance.LoanDecisionService;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.magellan.model.finance.payload.FinanceAppRequestView;
import com.carsaver.partner.client.finance.FinanceServiceClient;
import com.carsaver.partner.model.finance.FinanceDecision;
import com.carsaver.partner.model.user.UserLoanRequestDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * TODO - change implementation to ModelConverter
 */
@Component
@RequiredArgsConstructor
public class DRLoanRequestViewToUserLoanRequestConverter implements Converter<LoanRequestView, UserLoanRequestDTO> {

    private final LoanDecisionService loanDecisionService;

    private final FinancierClient financierClient;
    private final FinanceServiceClient financeServiceClient;
    @Override
    public UserLoanRequestDTO convert(LoanRequestView loanRequest) {
        String type = loanRequest.isFinanceApp() ? "Finance" : "Lease";

        var builder = UserLoanRequestDTO.builder()
            .id(loanRequest.getId())
            .vehicle(UserLoanRequestDTO.Vehicle.from(loanRequest.getCertificate()))
            .appStatusResponse(UserLoanRequestDTO.AppStatusResponse.from(loanRequest.getResponse()))
            .requestedAmountFinanced(Optional.ofNullable(loanRequest.getPayload()).map(FinanceAppRequestView::getRequestedAmountFinanced).orElse(0.0))
            .vin(loanRequest.getVin())
            .appId(loanRequest.getAppId())
            .createdDate(loanRequest.getCreatedDate())
            .certificate(loanRequest.getCertificate())
            .type(type);

        List<FinanceDecision> financeDecisionModels = Optional.of(loanRequest)
            .map(i -> financeServiceClient.retrieveLoanResponses(i.getId()))
            .orElseGet(Collections::emptyList);
        String overallStatus = loanRequest.isComplete()
            ? determineOverallStatusFrom(financeDecisionModels)
            : "In Progress";

        return builder
            .overallStatus(overallStatus)
            .financeDecisions(financeDecisionModels)
            .build();
    }


    private String determineOverallStatusFrom(List<FinanceDecision> financeDecisionModels) {
        boolean fullyApproved = false, conditionedApproval = false;

        for(FinanceDecision fdm: financeDecisionModels) {
            if(fdm.isFullyApproved()) {
                fullyApproved = true;
                break;
            } else if(fdm.isConditionedApproval()) {
                conditionedApproval = true;
            }
        }

        return fullyApproved
            ? "Approved"
            : conditionedApproval
                ? "Conditioned"
                : "Declined";
    }

}
