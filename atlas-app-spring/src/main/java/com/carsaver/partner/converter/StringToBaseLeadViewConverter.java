package com.carsaver.partner.converter;

import com.carsaver.magellan.client.AppointmentClient;
import com.carsaver.magellan.client.ConnectionClient;
import com.carsaver.magellan.model.BaseLeadView;
import com.carsaver.magellan.model.ConnectionView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class StringToBaseLeadViewConverter implements Converter<String, BaseLeadView> {

    @Autowired
    private AppointmentClient appointmentClient;

    @Autowired
    private ConnectionClient connectionClient;

    @Override
    public BaseLeadView convert(String source) {
        ConnectionView connectionView = connectionClient.findById(source);

        if(connectionView != null) {
            return connectionView;
        }

        return appointmentClient.findById(source);
    }
}
