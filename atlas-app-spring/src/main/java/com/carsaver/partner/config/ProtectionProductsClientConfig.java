package com.carsaver.partner.config;

import com.carsaver.partner.client.nissan.ProtectionProductsClient;
import com.carsaver.partner.client.nissan.ProtectionProductsClientSigning;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
@Profile("!e2e")
public class ProtectionProductsClientConfig {
    @Bean
    @ConditionalOnMissingBean
    public ProtectionProductsClient protectionProductsClient() {
        ProtectionProductsClient client = new ProtectionProductsClient();
        return client;
    }

    @Bean
    @ConditionalOnMissingBean
    public ProtectionProductsClientSigning protectionProductsClientSigning() {
        ProtectionProductsClientSigning client = new ProtectionProductsClientSigning();
        return client;
    }
}
