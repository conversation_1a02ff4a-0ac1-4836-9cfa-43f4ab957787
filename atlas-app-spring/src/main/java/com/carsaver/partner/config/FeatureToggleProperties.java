package com.carsaver.partner.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "features-toggle")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FeatureToggleProperties {
    private boolean chatEnable;
    private boolean libertyMutualEnable;
    private boolean routeOneFinanceAndInsuranceEnable;

   }
