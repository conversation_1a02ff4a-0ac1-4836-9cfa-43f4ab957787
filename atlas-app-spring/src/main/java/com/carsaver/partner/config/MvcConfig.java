package com.carsaver.partner.config;

import com.carsaver.magellan.support.SourceCreator;
import com.carsaver.partner.interceptor.DealerSelectInterceptor;
import com.carsaver.partner.search.converter.TenantFacetConverter;
import com.carsaver.partner.service.DealerProgramService;
import com.carsaver.partner.web.support.WebPTransformer;
import com.carsaver.partner.web.support.WebpackHashResourceResolver;
import com.carsaver.search.converter.ColorFacetConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.EncodedResourceResolver;
import org.springframework.web.servlet.resource.ResourceUrlEncodingFilter;
import org.springframework.web.servlet.resource.ResourceUrlProvider;

import java.util.concurrent.TimeUnit;

@Configuration
public class MvcConfig implements WebMvcConfigurer {

    @Autowired
    private ResourceUrlProvider resourceUrlProvider;

    @Bean
    public ResourceUrlEncodingFilter resourceUrlEncodingFilter() {
        return new ResourceUrlEncodingFilter();
    }

    @Bean
    public Validator getValidator() {
        return new LocalValidatorFactoryBean();
    }

    @Bean
    public SourceCreator sourceCreator() {
        return new SourceCreator();
    }

    @Bean
    public ColorFacetConverter colorFacetConverter() {
        return new ColorFacetConverter();
    }

    @Bean
    public TenantFacetConverter facetConverter() {
        return new TenantFacetConverter();
    }

    @Bean
    public DealerProgramService dealerProgramService() {
        return new DealerProgramService();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/dist/**")
            .addResourceLocations("classpath:/static/dist/")
            .setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS).cachePublic())
            .resourceChain(true)
            .addResolver(new EncodedResourceResolver())
            .addResolver(new WebpackHashResourceResolver());

        registry.addResourceHandler("/dist/img/**")
            .addResourceLocations("classpath:/static/dist/img/")
            .setCacheControl(CacheControl.maxAge(365, TimeUnit.DAYS).cachePublic())
            .resourceChain(false)
            .addTransformer(new WebPTransformer(resourceUrlProvider));
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new DealerSelectInterceptor());
    }
}
