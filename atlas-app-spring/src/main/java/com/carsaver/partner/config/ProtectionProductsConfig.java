package com.carsaver.partner.config;

import com.carsaver.nissan.reservations.support.AwsSecretManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

@ConditionalOnProperty(prefix = "insurance-service", name = "route-one-secret-key-name")
@Slf4j
@Configuration
public class ProtectionProductsConfig {
    @Value("${insurance-service.route-one-secret-key-name}")
    private String secretKeyName;

    private String secretKeyJson;

    @Getter
    private String routeOneEncryptionKey;

    @EventListener(ApplicationReadyEvent.class)
    public void doRetrieveAWSSecrets() {
        log.info("Calling ProtectionProductsConfig.doRetrieveAWSSecrets()");
        retrieveAWSSecrets();
    }

    void retrieveAWSSecrets() {
        secretKeyJson = AwsSecretManager.getSecreteKey(secretKeyName);
        routeOneEncryptionKey = AwsSecretManager.getJsonKeyValue(secretKeyJson,"route-one-encryption-key");
        log.info("ProtectionProductsUtility AWS Secret: {}", secretKeyJson);
    }

}
