package com.carsaver.partner.config;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticProperties;
import com.carsaver.elasticsearch.service.LeadDocService;
import com.carsaver.elasticsearch.service.UserAndProspectDocService;
import com.carsaver.elasticsearch.service.UserContractDocService;
import com.carsaver.elasticsearch.service.UserDocService;
import com.carsaver.elasticsearch.service.VehicleDocService;
import com.carsaver.elasticsearch.service.VehicleSaleDocService;
import com.carsaver.magellan.client.ZipCodeClient;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.api.user.elasticsearch.CustomersDocService;
import com.carsaver.search.support.FacetParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Slf4j
@Configuration
public class ElasticsearchConfig {

    @Bean
    public FacetParser facetParser() {
        return new FacetParser();
    }

    @Primary
    @Qualifier("vega")
    @Bean(destroyMethod = "close")
    public ElasticClient elasticSearchClient(@Qualifier("es-inventory-props") ElasticProperties elasticProperties) {
        return new ElasticClient(elasticProperties);
    }

    @Qualifier("gibson")
    @Bean(destroyMethod = "close")
    public ElasticClient elasticSearchGibsonClient(@Qualifier("es-gibson-props") ElasticProperties elasticProperties) {
        return new ElasticClient(elasticProperties);
    }

    @Qualifier("nova")
    @Bean(destroyMethod = "close")
    public ElasticClient elasticSearchNovaClient(@Qualifier("es-nova-props") ElasticProperties elasticProperties) {
        return new ElasticClient(elasticProperties);
    }

    @Bean
    @Qualifier("es-gibson-props")
    @ConfigurationProperties("elasticsearch.gibson")
    public ElasticProperties elasticGibsonProperties() {
        return new ElasticProperties();
    }

    @Bean
    @Qualifier("es-nova-props")
    @ConfigurationProperties("elasticsearch.nova")
    public ElasticProperties elasticNovaProperties() {
        return new ElasticProperties();
    }

    @Bean
    @Primary
    @Qualifier("es-inventory-props")
    @ConfigurationProperties("elasticsearch.inventory")
    public ElasticProperties elasticProperties() {
        return new ElasticProperties();
    }

    @Bean
    public UserDocService userDocService(@Qualifier("gibson") ElasticClient elasticClient) {
        return new UserDocService(elasticClient, facetParser());
    }

    @Bean
    public UserAndProspectDocService userAndProspectDocService(@Qualifier("gibson") ElasticClient elasticClient) {
        return new UserAndProspectDocService(elasticClient, facetParser());
    }

    @Bean
    public CustomersDocService customersDocService(@Qualifier("gibson") ElasticClient elasticClient,
                                                   ZipCodeClient zipCodeClient,
                                                   SplitFeatureFlags splitFeatureFlags) {
        return new CustomersDocService(elasticClient, facetParser(), zipCodeClient, splitFeatureFlags);
    }

    @Bean
    public LeadDocService leadDocService(@Qualifier("gibson") ElasticClient elasticClient) {
        return new LeadDocService(elasticClient, facetParser());
    }

    @Bean
    public VehicleDocService vehicleDocService(@Qualifier("vega") ElasticClient elasticClient) {
        return new VehicleDocService(elasticClient, facetParser());
    }

    @Bean
    public UserContractDocService UserContractDocService(@Qualifier("gibson") ElasticClient elasticClient) {
        return new UserContractDocService(elasticClient, facetParser());
    }

    @Bean
    public VehicleSaleDocService VehicleSaleDocService(@Qualifier("gibson") ElasticClient elasticClient) {
        return new VehicleSaleDocService(elasticClient, facetParser());
    }
}
