package com.carsaver.partner.config;

import com.carsaver.magellan.cache.BaseCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
@EnableCaching(proxyTargetClass = true)
@Slf4j
public class CacheConfig extends BaseCacheConfig {

    @Override
    public void addCaches(Map<String, Long> cacheMap) {
        cacheMap.put("features", 30L);
    }
}
