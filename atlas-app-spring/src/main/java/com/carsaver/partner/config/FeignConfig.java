package com.carsaver.partner.config;

import com.carsaver.magellan.client.*;
import com.carsaver.magellan.client.prospect.UpgradeCampaignClient;
import com.carsaver.magellan.client.vendor.SendGridClient;
import com.carsaver.partner.client.ProgramSubscriptionsClient;
import com.carsaver.partner.client.UserProgramsClient;
import com.carsaver.partner.client.UserServiceClient;
import com.carsaver.partner.client.campaign.suppressions.CampaignModelSuppressionClient;
import com.carsaver.partner.client.leads.CustomerLeadClient;
import com.carsaver.partner.client.leads.ProspectLeadClient;
import com.carsaver.partner.configuration.ConfigurationClient;
import com.carsaver.partner.digitalretailotp.DigitalRetailOtpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.hateoas.config.EnableHypermediaSupport;

@Slf4j
@Configuration
@EnableFeignClients(
    clients = {
        BasicUserAssociationClient.class,
        DealerPermissionsClient.class,
        DealerContractClient.class,
        CampaignClient.class,
        DealerClient.class,
        DealerGroupClient.class,
        DigitalRetailOtpClient.class,
        PasswordClient.class,
        UserClient.class,
        InventoryClient.class,
        PricingClient.class,
        VehicleClient.class,
        ZipCodeClient.class,
        CertificateClient.class,
        WarrantyContractClient.class,
        UserContractClient.class,
        FinancierClient.class,
        VehicleQuoteClient.class,
        LoanClient.class,
        EvoxImageClient.class,
        UserVehicleClient.class,
        ChromeAdsClient.class,
        DealerLinkClient.class,
        EmailValidationClient.class,
        EmailClient.class,
        LeadClient.class,
        ConnectionClient.class,
        DealerFeeClient.class,
        DealerEmailClient.class,
        SendGridEventClient.class,
        SendGridClient.class,
        ProgramManagerClient.class,
        SalesManagerClient.class,
        FollowUpManagerClient.class,
        TinyUrlClient.class,
        AppointmentClient.class,
        ProgramSubscriptionClient.class,
        ProgramClient.class,
        VehicleSaleClient.class,
        SmsEnablementClient.class,
        SaleClient.class,
        TenantClient.class,
        TrainingVideoClient.class,
        TrainingCourseClient.class,
        UpgradeCampaignClient.class,
        AccessoriesServiceClient.class,
        UserProgramsClient.class,
        ProgramSubscriptionsClient.class,
        ReservationsClient.class,
        CampaignModelSuppressionClient.class,
        ProgramPermissionClient.class,
        com.carsaver.partner.client.UserClient.class,
        com.carsaver.partner.client.DealClient.class,
        OfferLogixConfigClient.class,
        com.carsaver.partner.client.financier.FinancierClient.class,
        UserServiceClient.class,
        ProspectLeadClient.class,
        CustomerLeadClient.class,
        ConfigurationClient.class,
        DealerFinancierClient.class
    })
@EnableHypermediaSupport(type = EnableHypermediaSupport.HypermediaType.HAL)
public class FeignConfig {

}
