package com.carsaver.partner.config;

import com.carsaver.magellan.api.ImageUrlService;
import com.carsaver.magellan.api.deal.DealService;
import com.carsaver.magellan.api.vehicle.VehicleInfoService;
import com.carsaver.magellan.client.EvoxImageClient;
import com.carsaver.magellan.client.converter.StringToUserContractViewConverter;
import com.carsaver.magellan.client.converter.StringToUserVehicleQuoteViewConverter;
import com.carsaver.magellan.client.converter.StringToUserVehicleViewConverter;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AutoConfigureAfter(FeignConfig.class)
public class FeignConverterConfig {
    @Bean
    public DealService dealService() {
        return new DealService();
    }

    @Bean
    public ImageUrlService imageUrlService(EvoxImageClient client) {
        return new ImageUrlService(client);
    }

    @Bean
    public VehicleInfoService vehicleInfoService() {
        return new VehicleInfoService();
    }

    @Bean
    public StringToUserVehicleQuoteViewConverter stringToUserVehicleQuoteViewConverter() {
        return new StringToUserVehicleQuoteViewConverter();
    }

    @Bean
    public StringToUserContractViewConverter stringToUserContractViewConverter() {
        return new StringToUserContractViewConverter();
    }

    @Bean
    public StringToUserVehicleViewConverter stringToUserVehicleViewConverter() {
        return new StringToUserVehicleViewConverter();
    }
}
