package com.carsaver.partner.config.rest

import org.springframework.security.oauth2.jwt.Jwt
import java.lang.annotation.Inherited

@Retention(AnnotationRetention.RUNTIME)
@Inherited
@MustBeDocumented
annotation class CurrentUser


var SERVICE_ACCOUNT_NAME = "<EMAIL>"


fun mapToCurrenUserAndCheckNotServiceAccount(jwt: Jwt): CurrentUserInfo {
    if (jwt.getClaim<Any>("user_name") == SERVICE_ACCOUNT_NAME) {
        throw ApiException.forbidden("User required rather than service account.")
    }

    return toCurrentUserInfo(jwt)
}

fun toCurrentUserInfo(jwt: Jwt): CurrentUserInfo {
    val id: String = jwt.getClaim("id")
    val userName: String = jwt.getClaim("user_name")
    val targetUserId: String? = jwt.getClaim<String>("target_user_id")
    val dealerId: String? = jwt.getClaim<String>("dealer_id")
    val tenantId: String = jwt.getClaim("tenant_id")
    val authorities: List<String>? = jwt.getClaim<List<String>>("authorities")

    var type: UserType = UserType.UNKNOWN
    var actingAsDealer = false
    if (authorities != null) {
        type = UserType.fromAuthority(authorities)
        actingAsDealer = type.canRepresentUserAsDealer() && targetUserId != null
    }
    return CurrentUserInfo(
        id,
        userName,
        actingAsDealer,
        tenantId,
        type,
        targetUserId,
        dealerId
    )
}

data class CurrentUserInfo(
    val callingUserId: String? = null,
    val callingUserName: String? = null,
    val actingAsDealer: Boolean? = null,
    val tenantId: String? = null,
    val type: UserType? = null,
    val targetUserId: String? = null,
    val dealerId: String? = null
)

enum class UserType {
    ADMIN, PROGRAM, DEALER, USER, UNKNOWN;

    companion object {
        @JvmStatic
        fun fromAuthority(list: List<String>): UserType {
            val lowercaseAuthorities = list.map { it.lowercase() }

            return entries.firstOrNull { userType: UserType ->
                "role_" + userType.name.lowercase() in lowercaseAuthorities
            } ?: UNKNOWN
        }

        private val typesThatCanRepresentUser = setOf(PROGRAM, ADMIN, DEALER)
    }

    fun canRepresentUserAsDealer(): Boolean {
        return typesThatCanRepresentUser.contains(this)
    }
}
