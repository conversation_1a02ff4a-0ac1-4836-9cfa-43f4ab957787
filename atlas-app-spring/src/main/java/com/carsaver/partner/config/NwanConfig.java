package com.carsaver.partner.config;

import com.amazonaws.services.kinesisfirehose.AmazonKinesisFirehose;
import com.carsaver.cloud.aws.feign.KinesisFirehoseLogger;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.stereotype.ProviderId;
import com.carsaver.warranty.service.NwanClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class NwanConfig extends com.carsaver.warranty.config.NwanClientConfig {

    @Value("${spring.application.name}")
    private String serviceFrom;

    @Value("${carsaver.cloud.kinesis.api-log-stream:#{null}}")
    private String apiLogDeliveryStream;

    @Bean
    @ConditionalOnBean(AmazonKinesisFirehose.class)
    @ConditionalOnProperty(prefix = "carsaver.cloud.kinesis", name = "api-log-stream")
    public NwanClient nwanClientKinesis(AmazonKinesisFirehose firehoseClient) {
        log.info("configuring NwanClient with KinesisFirehoseLogger");

        KinesisFirehoseLogger logger = KinesisFirehoseLogger.builder()
            .caller(serviceFrom)
            .firehoseClient(firehoseClient)
            .deliveryStreamName(apiLogDeliveryStream)
            .recordEnabled(true)
            .identityProvider(AuthUtils::getUserIdFromSecurityContext)
            .provider(ProviderId.NWAN)
            .build();

        return super.nwanClient(logger);
    }
}
