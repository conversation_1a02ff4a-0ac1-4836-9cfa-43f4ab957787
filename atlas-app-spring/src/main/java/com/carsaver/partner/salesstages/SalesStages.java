package com.carsaver.partner.salesstages;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum SalesStages {
    PROSPECT("Prospect"),
    ELIGIBLE_PROSPECT("Eligible Prospect"),
    <PERSON><PERSON><PERSON><PERSON>ER("Explorer"),
    <PERSON><PERSON><PERSON><PERSON>("Shopper"),
    INTE<PERSON>ER("Intender"),
    <PERSON><PERSON><PERSON><PERSON>("Buyer"),
    OUT_OF_PROCESS("Out Of Process"),
    POST_SALE("Post Sale"),
    NEEDS_DEALER("Needs Dealer"),
    LONG_TERM_FOLLOW_UP("Long Term Follow Up");

    @Getter
    private String stageTitle;

    SalesStages(String stageTitle) {
        this.stageTitle = stageTitle;
    }

    public static boolean isStageTitle(String stageTitle) {

        for (SalesStages value : values()) {
            if (value.stageTitle.equals(stageTitle)) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getNames() {
        return Arrays.stream(SalesStages.values()).map(e -> e.stageTitle).collect(Collectors.toList());
    }
}
