package com.carsaver.partner.salesstages.model.v2;

import com.carsaver.partner.elasticsearch.criteria.CustomersSearchCriteria;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class StageStatsCustomersSearchCriteria extends CustomersSearchCriteria {

    @TermQuery(field = "program.id", scope = SearchScope.QUERY)
    private List<String> programs;
}
