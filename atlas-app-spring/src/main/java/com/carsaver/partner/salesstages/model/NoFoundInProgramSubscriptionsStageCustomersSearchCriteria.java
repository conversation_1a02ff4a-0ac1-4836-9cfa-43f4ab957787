package com.carsaver.partner.salesstages.model;

import com.carsaver.partner.elasticsearch.criteria.CustomersSearchCriteria;
import com.carsaver.search.annotation.ExistsQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class NoFoundInProgramSubscriptionsStageCustomersSearchCriteria extends CustomersSearchCriteria {

    @TermQuery(
            path = "programSubscriptionStages",
            field = "dealerId",
            boolQuery = BoolQueryOccurrence.MUST_NOT,
            nested = true
    )
    private String programSubscriptionStagesDealerIdMustNotMatch;

    @TermQuery(
            path = "programSubscriptionStages",
            field = "stageName",
            boolQuery = BoolQueryOccurrence.MUST_NOT,
            nested = true
    )
    private List<String> programSubscriptionStagesStageNameMustNotMatch;

    @TermQuery(field = "stage", boolQuery = BoolQueryOccurrence.MUST)
    private List<String> stagesToMatch;

    @ExistsQuery(
            field = "programSubscriptionStages",
            boolQuery = BoolQueryOccurrence.MUST,
            path = "programSubscriptionStages",
            nested = true
    )
    private boolean programSubscriptionStagesExists;
}
