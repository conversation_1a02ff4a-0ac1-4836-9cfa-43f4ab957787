package com.carsaver.partner.salesstages.model.v2;

import com.carsaver.partner.elasticsearch.criteria.CustomersSearchCriteria;
import com.carsaver.search.annotation.CompoundQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class NoFoundInProgramSubscriptionsStageCustomersSearchCriteria extends CustomersSearchCriteria {

    @CompoundQuery(boolQuery = BoolQueryOccurrence.FILTER)
    private List<ProgramSubscriptionStagePairMustNot> programSubscriptionStagePairMustNotMatch;

    @TermQuery(field = "stage", boolQuery = BoolQueryOccurrence.MUST)
    private List<String> stagesToMatch;

    @TermQuery(field = "program.id", scope = SearchScope.QUERY)
    private List<String> programs;
}
