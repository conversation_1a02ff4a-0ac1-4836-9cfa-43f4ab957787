package com.carsaver.partner.client.oauth;

import com.github.benmanes.caffeine.cache.Expiry;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Component
public class OAuthTokenExpiry implements Expiry<OAuthType, OAuthToken> {

    /**
     * Returns the expiry time based on the token 'expiresIn' field,
     * minus a small buffer window to allow the next token to be retrieved.
     *
     * @param key
     * @param token
     * @param currentTime
     * @return long
     */
    @Override
    public long expireAfterCreate(OAuthType key, OAuthToken token, long currentTime) {
        return TimeUnit.SECONDS.toNanos(token.getExpiresIn() - Duration.ofMinutes(5).getSeconds());
    }

    @Override
    public long expireAfterUpdate(OAuthType key, OAuthToken token, long currentTime, long currentDuration) {
        return currentDuration;
    }

    @Override
    public long expireAfterRead(OAuthType key, OAuthToken token, long currentTime, long currentDuration) {
        return currentDuration;
    }

}
