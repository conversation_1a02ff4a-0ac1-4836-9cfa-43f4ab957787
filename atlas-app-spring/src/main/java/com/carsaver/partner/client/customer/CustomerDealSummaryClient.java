package com.carsaver.partner.client.customer;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.partner.client.inventory.AuthorizationClient;
import com.carsaver.partner.client.inventory.Error;
import com.carsaver.partner.model.deal.CustomerDealSummaryRequest;
import com.carsaver.partner.model.retail.DealSummaryResponse;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;

@Slf4j
@Component
public class CustomerDealSummaryClient extends AuthorizationClient {

    private final String userVehicleServiceApiUri;

    public CustomerDealSummaryClient(@Value("${user-vehicle-service.api-uri}") String userVehicleServiceApiUri, CarSaverAuthService carSaverAuthService) {
        super(carSaverAuthService);
        this.userVehicleServiceApiUri = userVehicleServiceApiUri;
    }

    public DealSummaryResponse fetchDealSummary(@NotNull String customerId) {
        String endpoint = "/api/customers/" + customerId + "/deal-summary";
        HttpResponse<DealSummaryResponse> response = runRetrieveDealSummaryService(endpoint);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = runRetrieveDealSummaryService(endpoint);
        }

        if (response.isSuccess()) {
            return response.getBody();
        }

        throw new UnirestException("Failed to retrieve deal summary for customer [" + customerId + "]");
    }



    private HttpResponse<DealSummaryResponse> runRetrieveDealSummaryService(@NotNull String endpoint) {

        HttpResponse<DealSummaryResponse> success = Unirest.get(userVehicleServiceApiUri + endpoint)
                .header("Authorization", getAuthToken())
                .asObject(DealSummaryResponse.class)
                .ifFailure(Error.class, AuthorizationClient::handleManagedErrors);
        return success;
    }

    //V2 Implementation
    public DealSummaryResponse fetchDealSummaryV2(@NotNull String customerId, CustomerDealSummaryRequest request) {
        String endpoint = "/api/customers/v2/" + customerId + "/deal-summary";
        HttpResponse<DealSummaryResponse> response = runRetrieveDealSummaryServiceV2(endpoint, request);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = runRetrieveDealSummaryServiceV2(endpoint, request);
        }

        if (response.isSuccess()) {
            return response.getBody();
        }

        throw new UnirestException("Failed to retrieve deal summary for customer [" + customerId + "]");
    }

    private HttpResponse<DealSummaryResponse> runRetrieveDealSummaryServiceV2(@NotNull String endpoint, CustomerDealSummaryRequest request) {

        HttpResponse<DealSummaryResponse> success = Unirest.post(userVehicleServiceApiUri + endpoint)
            .header("Authorization", getAuthToken())
            .header("Content-Type", "application/json")
            .body(request)
            .asObject(DealSummaryResponse.class)
            .ifFailure(Error.class, AuthorizationClient::handleManagedErrors);
        return success;
    }
}
