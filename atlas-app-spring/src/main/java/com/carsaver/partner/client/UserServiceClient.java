package com.carsaver.partner.client;

import com.carsaver.magellan.client.config.CarSaverFeignConfig;
import com.carsaver.partner.salesstages.model.ProspectStats;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.Optional;

@FeignClient(name = "userServiceClient", url = "${user-service.api-uri}", decode404 = true, configuration = CarSaverFeignConfig.class)

public interface UserServiceClient {

    @GetMapping("/stats/prospects")
    ProspectStats getProspectsCountForMAndA(@RequestParam("campaignId") String campaignId);

    @GetMapping("/dealers/{dealerId}/salesperson")
    Collection<DealerSalesPerson> getAllSalesPerson(@PathVariable("dealerId") String dealerId);

    @GetMapping("/{id}/dealers/{dealerId}/salesperson")
    Optional<DealerSalesPerson> getSalesPerson(@PathVariable("id") String id, @PathVariable("dealerId") String dealerId);

    @PostMapping("/{id}/dealers/{dealerId}/salesperson")
    void assignSalesPerson(@PathVariable("id") String id, @PathVariable("dealerId") String dealerId);

    @DeleteMapping("/{id}/dealers/{dealerId}/salesperson")
    void unassignSalesPerson(@PathVariable("id") String id, @PathVariable("dealerId") String dealerId);
}
