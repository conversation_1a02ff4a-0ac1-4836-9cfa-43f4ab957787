package com.carsaver.partner.client.leads.v2

import com.carsaver.partner.client.oauth.OAuthClient
import com.carsaver.partner.http.HttpRequest
import com.carsaver.partner.http.HttpService
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.*

@Component
class LeadClientV2(
    @param:Value("\${lead-service.api-uri}") private val serviceApiUrl: String,
    private val httpService: HttpService,
    private val oAuthClient: OAuthClient
) {
    fun getLeadById(leadId: String): LeadV2? {
        // create the request
        val httpRequest = HttpRequest
            .get(serviceApiUrl, "/leads/$leadId")
            .bearerToken(oAuthClient.accessToken)

        // return the response
        return httpService.getOptionalResponse(httpRequest, object : TypeReference<LeadV2?>() {
        })?.orElse(null)
    }
}
