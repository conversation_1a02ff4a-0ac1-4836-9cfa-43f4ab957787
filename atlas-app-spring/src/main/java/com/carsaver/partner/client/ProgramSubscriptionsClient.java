package com.carsaver.partner.client;

import com.carsaver.magellan.client.config.CarSaverFeignConfig;
import com.carsaver.partner.model.ProgramSubscription;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.hateoas.CollectionModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
    name = "programSubscriptionsClient",
    url = "${harbor-service.api-uri}",
    decode404 = true,
    configuration = {CarSaverFeignConfig.class}
)
public interface ProgramSubscriptionsClient {
    @Cacheable(
        value = {"programSubscriptions"},
        unless = "#result == null"
    )
    @GetMapping({"/subscriptions/search/byProgramId"})
    CollectionModel<ProgramSubscription> findByProgramId(@RequestParam("programId") String programId);
}
