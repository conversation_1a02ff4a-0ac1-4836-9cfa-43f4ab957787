package com.carsaver.partner.client.inventory;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.FinanceConfig;
import com.carsaver.magellan.model.financiers.FinancierConfig;
import com.carsaver.magellan.model.financiers.PaymentConfig;
import com.carsaver.partner.model.desking.VehicleModelForPrograms;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class VehicleForProgramsClient extends AuthorizationClient{
    private final String inventoryServiceUrl;

    private final CampaignClient campaignClient;

    private final FinancierClient financierClient;

    @Autowired
    public VehicleForProgramsClient(@Value("${inventory-service.api-uri}") String harborServiceUrl, CarSaverAuthService carSaverAuthService, CampaignClient campaignClient, FinancierClient financierClient) {
        super(carSaverAuthService);
        this.inventoryServiceUrl = harborServiceUrl;
        this.campaignClient = campaignClient;
        this.financierClient = financierClient;
    }

    public VehicleModelForPrograms retrieveVehicleForProgramService(@NotNull String programId,
                                                                                  String dealerId,
                                                                                  String vin,
                                                                                  String stockNumber) {
        String endpoint = "/newOrUsed/retrieve/byProgramWithActiveProgramSubscriptions";
        HttpResponse<VehicleModelForPrograms> response = runRetrieveVehicleForProgramService(endpoint, programId, dealerId, vin, stockNumber);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = runRetrieveVehicleForProgramService(endpoint, programId, dealerId, vin, stockNumber);
        }
        if (response.isSuccess()) {
            retriveAvailableDealTypes(response.getBody(),programId);
            return response.getBody();
        }
        throw new UnirestException("Active Program Subscriptions by [" + programId + "] not successful");
    }

    public void retriveAvailableDealTypes(VehicleModelForPrograms vehicleModelForPrograms, @NotNull String programId) {
        List<String> availableDealTypes = new ArrayList<>();
        CampaignView campaignView = Optional.ofNullable(campaignClient.findByProgramId(programId).getContent().stream().findFirst().orElse(null))
            .orElseThrow(com.carsaver.magellan.api.exception.NotFoundException::new);

        Optional financierId = Optional.ofNullable(campaignView).map(CampaignView::getFinanceConfig).map(FinanceConfig::getEnabledFinancier);

        Optional<FinancierView> financierOpt = financierId.isEmpty() ? Optional.empty() : financierClient.findById(Long.valueOf(financierId.get().toString()));
        if (financierOpt.map(FinancierView::getConfig)
            .filter(Objects::nonNull)
            .map(FinancierConfig::getPaymentConfig)
            .filter(Objects::nonNull)
            .map(PaymentConfig::isLeaseEnabled).orElse(false)) {
            availableDealTypes.add("LEASE");
        }
        availableDealTypes.add("FINANCE");

        vehicleModelForPrograms.setAvailableDealTypes(availableDealTypes);
    }

    private HttpResponse<VehicleModelForPrograms> runRetrieveVehicleForProgramService(@NotNull String endpoint, @NotNull String programId,
                                                                                            String dealerId,
                                                                                            String vin,
                                                                                            String stockNumber) {
        final Map<String, Object> parameters = new HashMap<>();
        parameters.put("programId", programId);
        if(StringUtils.hasText(dealerId)){
            parameters.put("dealerId", dealerId);
        }
        if(StringUtils.hasText(vin)){
            parameters.put("vin", vin);
        }
        if(StringUtils.hasText(stockNumber)){
            parameters.put("stockNumber", stockNumber);
        }

        HttpResponse<VehicleModelForPrograms> success = Unirest.get(inventoryServiceUrl + endpoint)
                .queryString(parameters)
                .header("Authorization", getAuthToken())
                .asObject(VehicleModelForPrograms.class)
                .ifFailure(Error.class, AuthorizationClient::handleManagedErrors);

        return success;
    }

}
