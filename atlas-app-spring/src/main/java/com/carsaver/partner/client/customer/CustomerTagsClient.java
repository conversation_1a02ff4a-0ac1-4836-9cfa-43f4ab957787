package com.carsaver.partner.client.customer;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.partner.client.inventory.AuthorizationClient;
import com.carsaver.partner.client.inventory.Error;
import com.carsaver.partner.model.retail.CustomerTagsResponse;
import com.carsaver.partner.model.retail.UserTags;
import kong.unirest.GenericType;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.List;

@Slf4j
@Component
public class CustomerTagsClient extends AuthorizationClient {

    private final String userServiceApiUri;

    public CustomerTagsClient(@Value("${user-service.api-uri}") String userServiceApiUri, CarSaverAuthService carSaverAuthService) {
        super(carSaverAuthService);
        this.userServiceApiUri = userServiceApiUri;
    }

    public CustomerTagsResponse fetchCustomerTags(@NotNull String userId, List<String> dealerIds) {
        String endpoint = "/api/user/" + userId + "/user-tags";
        HttpResponse<List<UserTags>> response = retrieveCustomerTags(endpoint, dealerIds);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = retrieveCustomerTags(endpoint, dealerIds);
        }

        if (response.isSuccess()) {
            return CustomerTagsResponse.builder().customerTags(response.getBody()).build();
        }

        throw new UnirestException("Failed to retrieve deal summary for customer [" + userId + "]");
    }

    private HttpResponse<List<UserTags>> retrieveCustomerTags(@NotNull String endpoint, List<String> dealerIds) {
        HttpResponse<List<UserTags>> successResponse = Unirest.post(userServiceApiUri + endpoint)
            .header("Authorization", getAuthToken())
            .header("Content-Type", "application/json")
            .body(dealerIds)
            .asObject(new GenericType<List<UserTags>>() {})
            .ifFailure(Error.class, AuthorizationClient::handleManagedErrors);
        return successResponse;
    }




}
