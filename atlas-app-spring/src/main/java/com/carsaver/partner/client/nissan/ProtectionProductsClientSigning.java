package com.carsaver.partner.client.nissan;

import com.carsaver.partner.config.ProtectionProductsConfig;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.elasticsearch.common.collect.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.TimeZone;

@Slf4j
public class ProtectionProductsClientSigning {
    public static final String HMAC_SHA_256 = "HmacSHA256";

    @Value("${insurance-service.route-one-hmac_id}")
    private String HMAC_ID;

    @Value("${insurance-service.route-one-user-id}")
    private String routeoneUserId;

    @Autowired
    private ProtectionProductsConfig config;

    @Autowired
    SplitFeatureFlags splitFeatureFlags;

    public HttpHeaders buildSignedMessageHeaders(HttpMethod httpMethod, String routeOneURI, String requestBody, String routeOneDealerId) {
        String md5 = requestBody != null ? buildMD5(requestBody) : "";
        String date = buildDate();
        String authorizationHeader = generateAuthorizationHeader(httpMethod, routeOneURI, md5, date, routeOneDealerId);
        HttpHeaders httpHeaders = buildHeaders(authorizationHeader, requestBody, md5, date, routeOneDealerId);

        return httpHeaders;
    }

    String buildDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String date = sdf.format(new Date()).toLowerCase();

        return date;
    }

    HttpHeaders buildHeaders(String authorizationHeader, String request, String md5, String date, String routeOneDealerId) {

        log.debug("FinanceAndInsuranceProviderService initiateRouting{}", this.getClass().getName());

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.put("Authorization", List.of(authorizationHeader));
        headers.put("Content-MD5", List.of(md5));
        headers.put("Content-Type", List.of(MediaType.APPLICATION_JSON_VALUE));
        headers.put("Date", List.of(date));
        headers.put("X-RouteOne-Act-As-Dealership", List.of(routeOneDealerId));
        headers.put("X-RouteOne-Act-As-Dealership-Partner-ID", List.of(routeOneDealerId));
        headers.put("X-RouteOne-User-ID", List.of(routeoneUserId));
        // Temp fix to help R1 and Horizon Test
        if (Boolean.TRUE.equals(splitFeatureFlags.isRouteOneProtectionProductStagingHostEnabled()) ) {
            headers.put("Host", List.of("testint.r1dev.com"));
        } else {
            headers.put("Host", List.of("www.routeone.net"));
        }

        // TODO: hardcoded x-api-key
        HttpHeaders httpHeaders = new HttpHeaders(headers);

        return httpHeaders;
    }

    // TODO: split this into methods
    String generateAuthorizationHeader(HttpMethod httpMethod, String routeOneURI, String md5, String date, String routeOneDealerId) {
        String stringToSign = httpMethod + "\n"
            + md5.toLowerCase() + "\n"
            + MediaType.APPLICATION_JSON_VALUE.toLowerCase() + "\n"
            + date.toLowerCase() + "\n"
            + "x-routeone-act-as-dealership:" + routeOneDealerId + "\n"
            + "x-routeone-act-as-dealership-partner-id:" + routeOneDealerId + "\n"
            + "x-routeone-user-id:" + routeoneUserId + "\n"
            + routeOneURI + "\n";
        log.info(stringToSign);

        byte[] byteResult = calcHmacSha256(
            config.getRouteOneEncryptionKey().getBytes(),
            stringToSign.getBytes(StandardCharsets.UTF_8)
        );

        String result = Base64.getEncoder().encodeToString(byteResult);
        log.info("Result {} ", result);
        // TODO: hardcoded RouteOne id
        String authHeader = "RouteOne " + HMAC_ID + ":" + result;
        log.info("Auth Header is {}", authHeader);

        return authHeader;
    }

    String buildMD5(String data) {
        String input;
        String result;
        String resultBytesTpBase64 = null;

        try {
            input = data;
            final MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(input.getBytes());
            final byte[] resultByte = messageDigest.digest();
            result = new String(Hex.encodeHex(resultByte));
            resultBytesTpBase64 = Base64.getEncoder().encodeToString(resultByte);
            log.info("Base64 MD5 of Bytes {}", resultBytesTpBase64);
            String base64Res = Base64.getEncoder().encodeToString(result.getBytes(StandardCharsets.UTF_8));
            log.info("Base64 MD5 of Hex {}", base64Res);
            return resultBytesTpBase64;
        }
        catch (NoSuchAlgorithmException e) {
            // TODO: do something better here and elsewhere
        }

        return resultBytesTpBase64;
    }

    byte[] calcHmacSha256(byte[] secretKey, byte[] message) {
        // TODO: what to init?
        byte[] result = null;

        try {
            Mac sha256_HMAC = Mac.getInstance(HMAC_SHA_256);
            sha256_HMAC.init(new SecretKeySpec(secretKey, HMAC_SHA_256));
            result = sha256_HMAC.doFinal(message);
        }
        catch (NoSuchAlgorithmException | InvalidKeyException e) {
            // TODO: do something better here and elsewhere
        }

        return result;
    }
}
