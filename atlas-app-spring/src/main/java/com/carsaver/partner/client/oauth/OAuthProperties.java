package com.carsaver.partner.client.oauth;

import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

@Getter
@Setter
@Validated
@Configuration
@ConfigurationProperties(prefix = "client.oauth")
@AllArgsConstructor
@NoArgsConstructor
public class OAuthProperties {

    @NonNull
    private Map<OAuthType, OAuthConfig> configurations;

    @Getter
    @Setter
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OAuthConfig {

        @NonNull
        private String url;

        @NonNull
        private OAuthGrant grantType;

        @NonNull
        private String authUsername;

        @NonNull
        private String authPassword;

        private String scope;

        // only for password grant types
        private String tokenUsername;
        private String tokenPassword;
    }

}
