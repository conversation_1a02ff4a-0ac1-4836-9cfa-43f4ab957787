package com.carsaver.partner.client.inventory;

import com.carsaver.magellan.NotFoundException;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.auth.TokenResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import kong.unirest.HttpResponse;
import kong.unirest.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Optional;

@Slf4j
public class AuthorizationClient {

    private final CarSaverAuthService carSaverAuthService;
    public AuthorizationClient(CarSaverAuthService carSaverAuthService) {
        this.carSaverAuthService = carSaverAuthService;
    }

    protected String getAuthToken() {
        String jwtToken = null;
        Optional<String> authJwt = AuthUtils.getJwtTokenFromSecurityContext();
        if (authJwt.isPresent()) {
            log.debug("Found token in SecurityContext");
            jwtToken = "Bearer " + (String)authJwt.get();
        }

        if (jwtToken == null) {
            log.debug("No valid token in SecurityContext, retrieving from {}", CarSaverAuthService.class);
            TokenResponse token = this.carSaverAuthService.getToken();
            jwtToken = "Bearer " + token.getAccessToken();
        }
        return jwtToken;
    }

    protected void refreshTokenFromServer(){
        TokenResponse token = this.carSaverAuthService.getToken();
        if(token != null) {
            carSaverAuthService.refreshToken(token.getRefreshToken());
        } else {
            throw new RuntimeException("Harbor Client failed to refresh Auth Token");
        }
    }

    protected static void handleManagedErrors(HttpResponse<Error> response) {
        Error error = response.getBody();
        log.info("Error response: {} status : {}", error, response.getStatus());

        if (response.getStatus() == HttpStatus.NOT_FOUND) {
            if (error != null && StringUtils.hasText(error.getMessage())) {
                throw new NotFoundException(error.getMessage());
            } else {
                throw new NotFoundException();
            }
        }

    }
}
