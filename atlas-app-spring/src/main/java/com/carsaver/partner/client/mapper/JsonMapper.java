package com.carsaver.partner.client.mapper;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import org.springframework.hateoas.mediatype.hal.Jackson2HalModule;

import static com.fasterxml.jackson.module.kotlin.ExtensionsKt.jacksonObjectMapper;


/**
 * JsonMapper
 * <p>
 * Encapsulates a global object mapper and provides convenience methods for json conversion.
 *
 * <AUTHOR>
 */
public final class JsonMapper {

    // the object mapper
    private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

    /**
     * Creates an object mapper that can be used for all conversions.
     *
     * @return ObjectMapper
     */
    private static ObjectMapper createObjectMapper() {
        return jacksonObjectMapper()
            .findAndRegisterModules()
            .registerModule(new Jackson2HalModule())
            .registerModule(new JavaTimeModule())
            .registerModule(new Jdk8Module())
            .setSerializationInclusion(Include.NON_NULL)
            .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * Returns the object mapper instance.
     *
     * @return ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    /**
     * Converts an object to json.
     *
     * @param object
     * @return String
     */
    @SneakyThrows
    public static String toJson(Object object) {
        return OBJECT_MAPPER.writeValueAsString(object);
    }

    /**
     * Converts a json string to an object.
     *
     * @param json
     * @param objectClass
     * @return T
     */
    @SneakyThrows
    public static <T> T fromJson(String json, Class<T> objectClass) {
        return json == null ? null : OBJECT_MAPPER.readValue(json, objectClass);
    }

    /**
     * Converts a json string to an object.
     *
     * @param json
     * @param typeReference
     * @return T
     */
    @SneakyThrows
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        return json == null ? null : OBJECT_MAPPER.readValue(json, typeReference);
    }

}
