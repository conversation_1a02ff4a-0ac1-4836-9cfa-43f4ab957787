package com.carsaver.partner.client;

import com.carsaver.magellan.client.config.CarSaverFeignConfig;
import com.carsaver.partner.model.user.UserView;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "userClient", url = "${dealer-service.api-uri}", decode404 = true, configuration = CarSaverFeignConfig.class)

public interface UserClient {

    @GetMapping("/users/certificateId")
    UserView searchUserByCertificateId(@RequestParam("certificateId") Integer certificateId);
}
