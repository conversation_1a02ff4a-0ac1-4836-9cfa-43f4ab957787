package com.carsaver.partner.client.financier;

import com.carsaver.magellan.client.config.CarSaverFeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "financierClient", url = "${meridian-service.api-uri}", decode404 = true, configuration = CarSaverFeignConfig.class)

public interface FinancierClient {

    @GetMapping({"/loanresponses/{id}/decision-history"})
    List<DecisionHistoryResponse> getLoanDecisionHistoryById(@PathVariable("id") Integer id);
}
