package com.carsaver.partner.client.dealer

import com.carsaver.partner.client.oauth.OAuthClient
import com.carsaver.partner.http.HttpRequest
import com.carsaver.partner.http.HttpService
import com.carsaver.partner.model.user.CreditProfile
import com.carsaver.partner.model.user.UserView
import com.fasterxml.jackson.core.type.TypeReference
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.*

@Component
class DealerClient(
    @param:Value("\${dealer-service.api-uri}") private val dealerServiceApiUri: String,
    private val httpService: HttpService,
    private val oAuthClient: OAuthClient
) {
    fun getCreditProfile(userId: String): CreditProfile? {
        // create the request

        val httpRequest = HttpRequest
            .get(dealerServiceApiUri, "/users/%s/creditProfile", userId)
            .bearerToken(oAuthClient.accessToken)

        // return the response
        return httpService.getOptionalResponse(httpRequest, object : TypeReference<CreditProfile?>() {
        })?.orElse(null)
    }

    fun getUserById(userId: String): UserView? {
        // create the request

        val httpRequest = HttpRequest
            .get(dealerServiceApiUri, "/users/%s", userId)
            .bearerToken(oAuthClient.accessToken)

        // return the response
        return httpService.getOptionalResponse(httpRequest, object : TypeReference<UserView?>() {
        }).orElse(null)
    }

    fun getCertificateById(certificateId: Long): CertificateV2? {
        // create the request

        val httpRequest = HttpRequest
            .get(dealerServiceApiUri, "/certificates/%s", certificateId)
            .bearerToken(oAuthClient.accessToken)

        // return the response
        return httpService.getOptionalResponse(httpRequest, object : TypeReference<CertificateV2?>() {
        })?.orElse(null)
    }

    fun getDealerById(dealerId: String): DealerV2? {
        // create the request

        val httpRequest = HttpRequest
            .get(dealerServiceApiUri, "/dealers/%s", dealerId)
            .bearerToken(oAuthClient.accessToken)

        // return the response
        return httpService.getOptionalResponse(httpRequest, object : TypeReference<DealerV2?>() {
        }).orElse(null)
    }
}
