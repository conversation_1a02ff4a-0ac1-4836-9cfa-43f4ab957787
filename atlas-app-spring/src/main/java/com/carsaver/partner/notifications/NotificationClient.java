package com.carsaver.partner.notifications;

import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.partner.client.inventory.AuthorizationClient;
import com.carsaver.partner.client.inventory.Error;
import com.carsaver.partner.model.Notification;
import kong.unirest.GenericType;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.List;

@Slf4j
@Component
public class NotificationClient extends AuthorizationClient {

    private final String dealerServiceApiUri;

    public NotificationClient(@Value("${dealer-service.api-uri}") String dealerServiceApiUri, CarSaverAuthService carSaverAuthService) {
        super(carSaverAuthService);
        this.dealerServiceApiUri = dealerServiceApiUri;
    }

    public List<Notification> fetchNotifications(String dealerId) {
        String endpoint = "/notifications";
        HttpResponse<List<Notification>> response = retrieveNotifications(endpoint, dealerId);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = retrieveNotifications(endpoint, dealerId);
        }

        if (response.isSuccess()) {
            return response.getBody();
        }

        throw new UnirestException("Failed to retrieve Notifications for dealer [" + dealerId + "]");
    }

    public Notification markAsDisplayed(String userId, String time) {
        HttpResponse<Notification> response = updateNotificationStatus("/notifications/displayed", userId, time);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = updateNotificationStatus("/notifications/displayed", userId, time);
        }

        if (response.isSuccess()) {
            return response.getBody();
        }

        throw new UnirestException("Failed to update Notification status for displayed");

    }

    public Notification markAsCtaClicked(String userId, String time) {
        HttpResponse<Notification> response = updateNotificationStatus("/notifications/ctaClicked", userId, time);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = updateNotificationStatus("/notifications/ctaClicked", userId, time);
        }

        if (response.isSuccess()) {
            return response.getBody();
        }

        throw new UnirestException("Failed to update Notification status for ctaClicked");

    }

    public Notification markAsDisplayExpired(String userId, String time) {
        HttpResponse<Notification> response = updateNotificationStatus("/notifications/displayExpired", userId, time);

        if (response.getStatus() == 401) {
            refreshTokenFromServer();
            response = updateNotificationStatus("/notifications/displayExpired", userId, time);
        }

        if (response.isSuccess()) {
            return response.getBody();
        }

        throw new UnirestException("Failed to update Notification status for displayedExpired");

    }

    private HttpResponse<Notification> updateNotificationStatus(String endpoint, String userId, String time) {
        HttpResponse<Notification> response = Unirest.put(dealerServiceApiUri + endpoint)
            .header("Authorization", getAuthToken()) // Assuming the token is handled correctly
            .queryString("userId", userId)
            .queryString("time", time)
            .asObject(Notification.class);

        return response;
    }


    private HttpResponse<List<Notification>> retrieveNotifications(@NotNull String endpoint, String dealerIds) {
        HttpResponse<List<Notification>> successResponse = Unirest.get(dealerServiceApiUri + endpoint)
            .header("Authorization", getAuthToken())
            .header("Content-Type", "application/json")
            .queryString("dealerId", dealerIds)
            .asObject(new GenericType<List<Notification>>() {
            })
            .ifFailure(Error.class, AuthorizationClient::handleManagedErrors);
        return successResponse;
    }


}
