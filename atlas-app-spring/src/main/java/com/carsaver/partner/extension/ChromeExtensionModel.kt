package com.carsaver.partner.extension

import com.carsaver.elasticsearch.model.UserAndProspectDoc
import com.carsaver.partner.model.CustomerLastLoginDetails
import com.carsaver.partner.model.retail.CustomerTagsResponse
import javax.validation.Valid
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotEmpty

data class ChromeExtensionModel(
    val user: UserAndProspectDoc,
    val loginDetails: CustomerLastLoginDetails,
    val tags: CustomerTagsResponse
)

data class MatchDetailsForDealerRequest(
    @field:Valid
    var names: List<@Valid Name>? = null,
    var emails: List<String>? = null,
    var phoneNumbers: List<String>? = null,
    @field:NotBlank
    var dealerId: String? = null
)

data class Name(
    @field:NotEmpty
    var firstName: String? = null,
    @field:NotEmpty
    var lastName: String? = null
)
