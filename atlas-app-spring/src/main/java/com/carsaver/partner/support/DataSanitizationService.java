package com.carsaver.partner.support;

import org.apache.commons.text.StringEscapeUtils;
import org.owasp.html.HtmlPolicyBuilder;
import org.owasp.html.PolicyFactory;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class DataSanitizationService {
    private static final PolicyFactory sanitizer = new HtmlPolicyBuilder()
        .disallowElements("*") // Disallow all HTML elements
        .toFactory();

    private static final String emailPattern = "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}";

    /**
     * Sanitizes the given HTML string by removing all HTML tags and entities.
     *
     * @param html The HTML string to sanitize.
     * @return The sanitized string with all HTML tags and entities removed.
     */
    public String sanitizeHtml(String html) {
        if (Objects.isNull(html)) {
            return null;
        }
        String email = isEmail(html);
        if(email != null){
            return email;
        }

        return StringEscapeUtils.unescapeHtml4(sanitizer.sanitize(html));
    }

    public String isEmail(String input){
        // Compile the regex pattern
        Pattern pattern = Pattern.compile(emailPattern);

        // Match the input string against the pattern
        Matcher matcher = pattern.matcher(input);

        // Check if the pattern matches any part of the input string
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

}
