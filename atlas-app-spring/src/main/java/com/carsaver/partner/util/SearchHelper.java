package com.carsaver.partner.util;

import com.carsaver.partner.web.api.user.elasticsearch.model.CriteriaAggregationRequest;
import com.carsaver.partner.web.api.user.elasticsearch.model.CriteriaFilterRequest;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchCriteria;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchRequest;
import com.carsaver.search.model.SearchMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class SearchHelper {

    private static final Map<String, String> searchFieldsVOIDealer = new HashMap<>();
    private static final Map<String, String> searchFieldsVOIGlobal = new HashMap<>();

    static {

        //Dealer
        searchFieldsVOIDealer.put("vehicleOfInterestSavedOrViewed", "dealerVehicleOfInterestSavedOrViewed");
        searchFieldsVOIDealer.put("vehicleOfInterestStockTypes", "dealerVehicleOfInterestStockTypes");
        searchFieldsVOIDealer.put("vehicleOfInterestCertified", "dealerVehicleOfInterestCertified");
        searchFieldsVOIDealer.put("vehicleOfInterestMakes", "dealerVehicleOfInterestMakes");
        searchFieldsVOIDealer.put("vehicleOfInterestYears", "dealerVehicleOfInterestYears");
        searchFieldsVOIDealer.put("vehicleOfInterestModels", "dealerVehicleOfInterestModels");

        //Global
        searchFieldsVOIGlobal.put("vehicleOfInterestSavedOrViewed", "globalVehicleOfInterestSavedOrViewed");
        searchFieldsVOIGlobal.put("vehicleOfInterestStockTypes", "globalVehicleOfInterestStockTypes");
        searchFieldsVOIGlobal.put("vehicleOfInterestCertified", "globalVehicleOfInterestCertified");
        searchFieldsVOIGlobal.put("vehicleOfInterestMakes", "globalVehicleOfInterestMakes");
        searchFieldsVOIGlobal.put("vehicleOfInterestYears", "globalVehicleOfInterestYears");
        searchFieldsVOIGlobal.put("vehicleOfInterestModels", "globalVehicleOfInterestModels");
    }

    public String getDealerActualSearchField(String field) {
        return searchFieldsVOIDealer.get(field);
    }

    public String getGlobalActualSearchField(String field) {
        return searchFieldsVOIGlobal.get(field);
    }

    public void normalizeSearchMethods(SearchCriteria searchForm, SearchRequest searchRequest, boolean isDealer) {

        if (searchForm.getSearchMethods().isEmpty()) {
            return;
        }

        Map<String, SearchMethod> searchMethods = searchRequest.getSearchMethods();
        for (Field field : SearchRequest.class.getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object value = field.get(searchRequest);
                if (value == null) {
                    continue;
                }

                String fieldName = field.getName();
                SearchMethod searchMethod = searchMethods.get(fieldName);

                String actualSearchField = isDealer ? getDealerActualSearchField(fieldName) : getGlobalActualSearchField(fieldName);
                if (actualSearchField == null || searchMethod == null) {
                    continue;
                }

                if (searchForm instanceof CriteriaFilterRequest) {
                    setSearchMethodFilter(searchForm, actualSearchField, searchMethod, value);
                } else if (searchForm instanceof CriteriaAggregationRequest) {
                    setSearchMethodAggregation(searchForm, actualSearchField, searchMethod, value);
                }

            } catch (IllegalAccessException e) {
                log.error("Error in normalizeSearchMethods while accessing field: {}", field.getName(), e);
            }
        }
    }

    private void setSearchMethodFilter(SearchCriteria searchForm, String field, SearchMethod searchMethod, Object value) {
        try {
            Field actualField = CriteriaFilterRequest.class.getDeclaredField(field);
            actualField.setAccessible(true);
            actualField.set(searchForm, value);
            searchForm.getSearchMethods().put(field, searchMethod);
        } catch (IllegalAccessException | NoSuchFieldException e) {
            log.error("Error in setSearchMethodAggregation while accessing field: ", e);
        }
    }

    private void setSearchMethodAggregation(SearchCriteria searchForm, String field, SearchMethod searchMethod, Object value) {
        try {
            Field actualField = CriteriaAggregationRequest.class.getDeclaredField(field);
            actualField.setAccessible(true);
            actualField.set(searchForm, value);
            searchForm.getSearchMethods().put(field, searchMethod);
        } catch (IllegalAccessException | NoSuchFieldException e) {
            log.error("Error in setSearchMethodAggregation while accessing field: ", e);
        }
    }
}
