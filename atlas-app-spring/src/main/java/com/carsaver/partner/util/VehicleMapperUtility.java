package com.carsaver.partner.util;

import com.carsaver.elasticsearch.model.sale.VehicleBasicDoc;
import com.carsaver.magellan.model.VehicleView;
import lombok.experimental.UtilityClass;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;

@UtilityClass
public final class VehicleMapperUtility {


    public static String getStockTypeName(VehicleBasicDoc vehicleBasicDoc) {
        if(vehicleBasicDoc == null) {
            return null;
        }

        return determineStockTypeName(vehicleBasicDoc.getCertified(), vehicleBasicDoc.getStockType().name());
    }

    public static String getStockTypeName(VehicleView vehicleView) {
        if(vehicleView == null) {
            return null;
        }

        return determineStockTypeName(vehicleView.getCertifiedByDealer(), vehicleView.getStockType().name());
    }

    @NotNull
    static String determineStockTypeName(Boolean isCertified, String stockType) {
        return Objects.equals(Boolean.TRUE, isCertified)
            ? "Certified"
            : stockType;
    }
}
