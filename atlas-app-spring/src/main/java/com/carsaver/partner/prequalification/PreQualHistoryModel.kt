package com.carsaver.partner.prequalification

import com.fasterxml.jackson.annotation.JsonValue
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey
import java.time.OffsetDateTime

data class PreQualRecord(
    val customerId: String? = null,
    val requestDate: OffsetDateTime? = null,
    val responseStatus: CreditResponseStatusDTO? = null,
    val failureReason: PreQualSoftPullFailureReason? = null,
    val source: String? = null,
    val bureau: String? = null,
    val financeProgram: String? = null,
    val creditTier: String? = null,
    val expirationDate: OffsetDateTime? = null,
    val maxPayment: Int? = null,
) {
    companion object {
        @JvmStatic
        fun fromDynamoRecord(record: PreQualHistoryDynamoRecord): PreQualRecord {
            val expirationDate = if (record.responseStatus == CreditResponseStatus.SUCCESS) record.expirationDate else null
            return PreQualRecord(
                customerId = record.customerId,
                requestDate = record.requestDate,
                responseStatus = mapStatusToDtoWithExpirationDate(record.responseStatus, record.expirationDate),
                failureReason = record.failureReason,
                source = record.source,
                bureau = record.bureau,
                financeProgram = record.financeProgram,
                creditTier = record.creditTier,
                expirationDate = expirationDate,
                maxPayment = record.maxPayment
            )
        }

        @JvmStatic
        fun mapStatusToDtoWithExpirationDate(
            originalStatus: CreditResponseStatus?,
            expirationDate: OffsetDateTime?
        ): CreditResponseStatusDTO? {
            return when {
                originalStatus == CreditResponseStatus.SUCCESS && expirationDate?.isBefore(OffsetDateTime.now()) == true ->
                    CreditResponseStatusDTO.EXPIRED

                originalStatus == CreditResponseStatus.SUCCESS ->
                    CreditResponseStatusDTO.PASSED

                originalStatus == CreditResponseStatus.FAILURE ->
                    CreditResponseStatusDTO.FAIL

                originalStatus == CreditResponseStatus.NOT_FOUND ->
                    CreditResponseStatusDTO.FAIL

                originalStatus == CreditResponseStatus.LOCKED ->
                    CreditResponseStatusDTO.LOCKED

                else -> null
            }
        }

        @JvmStatic
        fun fromDynamoRecordList(records: List<PreQualHistoryDynamoRecord>): List<PreQualRecord> {
            return records.map { fromDynamoRecord(it) }
        }
    }
}

@DynamoDbBean
data class PreQualHistoryDynamoRecord(
    @get:DynamoDbPartitionKey @get:DynamoDbAttribute("CustomerId") var customerId: String? = null,
    @get:DynamoDbSortKey @get:DynamoDbAttribute("TimestampCustomerId") var timestampCustomerId: String? = null,
    @get:DynamoDbAttribute("RequestDate") var requestDate: OffsetDateTime? = null,
    @get:DynamoDbAttribute("ResponseStatus") var responseStatus: CreditResponseStatus? = null,
    @get:DynamoDbAttribute("FailureReason") var failureReason: PreQualSoftPullFailureReason? = null,
    @get:DynamoDbAttribute("Source") var source: String? = null,
    @get:DynamoDbAttribute("Bureau") var bureau: String? = null,
    @get:DynamoDbAttribute("FinanceProgram") var financeProgram: String? = null,
    @get:DynamoDbAttribute("CreditTier") var creditTier: String? = null,
    @get:DynamoDbAttribute("ExpirationDate") var expirationDate: OffsetDateTime? = null,
    @get:DynamoDbAttribute("MaxPayment") var maxPayment: Int? = null
) {}

enum class CreditResponseStatus {
    SUCCESS,
    NOT_FOUND,
    FAILURE,
    LOCKED,
}

enum class CreditResponseStatusDTO(@JsonValue val value: String) {
    PASSED("Passed"), // Response Status = SUCCESS. Pre-qual request was successful
    FAIL("Fail"), // Response Status = FAILURE
    LOCKED("Locked"), // Response Status = LOCKED
    EXPIRED("Expired") // = Pre-qual request was successful but expiration date is in the past.
}

enum class PreQualSoftPullFailureReason {
    MAXIMUM_ATTEMPTS_EXCEEDED,
    REPORT_NOT_FOUND,
    REPORT_LOCKED_BY_CONSUMER,
    REPORT_FROZEN_BY_FED,
    INSUFFICIENT_TRADE_LINE,//legacy compatibility
    REPORT_INSUFFICIENT_TRADELINE,
    INVALID_USER,
    INVALID_SCORE,
    REPORT_MINOR_INFORMATION,
    REPORT_SUPPRESSED,
    INVALID_DEALER_CODE,
    VALIDATION_ERROR,
    INVALID_ZIP_FORMAT,
    INVALID_RISK_MODEL,
    INVALID_DOB,
    REPORT_INSUFFICIENT_CREDIT,
    REPORT_SUBJECT_DECEASED,
    REPORT_DUPLICATE_INQUIRIES,
    ADDRESS_MISSING,
    SURNAME_MISSING,
    FIRST_NAME_MISSING,
    REMOTE_SERVER_ERROR,
    CURRENT_DELINQUENCY_RULE,
    MONTHLY_OBLIGATION_RULE,
    MONTHLY_INCOME_RULE,
    NUMBER_OF_TRADELINES_RULE,
    PRIOR_BANKRUPTCY_RULE,
    CURRENT_BANKRUPTCY_RULE,

    UNKNOWN;
}
