package com.carsaver.partner.configuration.leadpreferences

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.builder.LocalBuilderDefinitionService
import org.slf4j.LoggerFactory
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

private const val PAGE_PATH = "Lead Preferences"
@RestController
class LeadPreferencesController(
    private val localBuilderDefinitionService: LocalBuilderDefinitionService,
    private val leadPreferencesService: LeadPreferencesService,
) {

    private val logger = LoggerFactory.getLogger(LeadPreferencesController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=${PAGE_PATH}"])
    fun getBuilderConfiguration(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
        @RequestParam("page") page: String?,
        @RequestParam(value = "userId", required = false) userId: String?
    ): Any {
        logger.info("Fetching builder definition for page: $page")
        return localBuilderDefinitionService.getBuilderDefinition(page)
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=${PAGE_PATH}"])
    fun getConfiguration(@RequestParam("dealerId") dealerId: String): Any {
        return leadPreferencesService.getLenderPreferenceConfiguration(dealerId, null)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=${PAGE_PATH}"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    fun saveLeadsConfigurationForCDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestBody request: Map<String, Any?>,
    ): ResponseEntity<Any> {
        logger.info("Saving overlay settings for dealerId: $dealerId, page: $page")
        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }

        leadPreferencesService.saveLenderPreferenceConfiguration(
            dealerId = dealerId,
            userId = loggedInUser.id,
            domain = null,
            request = request,
        )
        return ResponseEntity.ok().build()
    }
}
