package com.carsaver.partner.configuration.ctaconfig

import com.fasterxml.jackson.annotation.JsonInclude
import java.util.Optional

@JsonInclude(JsonInclude.Include.NON_NULL)
data class CtaConfigResponse(
    var vdp: Optional<CtaPage>? = null,
    var listings: Optional<CtaPage>? = null,
    var integrationMethod: Optional<String>? = null,
    var displayType: Optional<String>? = null,
) {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class CtaPage(
        var primaryButton: Optional<ButtonOptions>? = null,
        var secondButton: Optional<ButtonOptions>? = null,
        var thirdButton: Optional<ButtonOptions>? = null,
        var fourthButton: Optional<ButtonOptions>? = null
    )


    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class ButtonOptions(

        // Formatting options
        var width: Optional<String>? = null,
        var height: Optional<String>? = null,
        var font: Optional<String>? = null,
        var fontSize: Optional<String>? = null,
        var fontWeight: Optional<String>? = null,
        var textAlign: Optional<String>? = null,
        var padding: Optional<String>? = null,
        var margin: Optional<String>? = null,
        var borderRadius: Optional<String>? = null,

        // Button options
        var destination: Optional<String>? = null,
        var color: Optional<String>? = null,
        var hoverColor: Optional<String>? = null,
        var hoverBackgroundColor: Optional<String>? = null,
        var backgroundColor: Optional<String>? = null,
        var name: Optional<String>? = null,//button text
        var active: Optional<String>? = null,
        var renderType: Optional<String>? = null,

        // Image options
        var imageDisplayName: Optional<String>? = null,
        var imageName: Optional<String>? = null,
        var imageSize: Optional<String>? = null,
        var imageBackgroundColor : Optional<String>? = null,
        var imageUrl: Optional<String>? = null,
    )
}
