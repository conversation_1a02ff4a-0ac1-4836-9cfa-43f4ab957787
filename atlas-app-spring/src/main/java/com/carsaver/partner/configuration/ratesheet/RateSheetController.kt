package com.carsaver.partner.configuration.ratesheet

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.builder.LocalBuilderDefinitionService
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*


private const val PAGE_PATH = "Rate Sheet Configuration"

@RestController
class RateSheetController (
    private val localBuilderDefinitionService: LocalBuilderDefinitionService,
    private val rateSheetService: RateSheetService
) {
    private val logger = LoggerFactory.getLogger(RateSheetController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=$PAGE_PATH"])
    fun getBuilderConfiguration(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
        @RequestParam("page") page: String?,
        @RequestParam(value = "userId", required = false) userId: String?
    ): Any {
        logger.info("Fetching builder definition for page: $page")
        return localBuilderDefinitionService.getBuilderDefinition(page)
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=$PAGE_PATH"])
    fun getConfiguration(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
    ): Map<String, Any?> {
        return rateSheetService.getRateSheetConfiguration(dealerId)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=$PAGE_PATH"])
    fun saveClientThemingJustForDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestBody request: FallbackRateSheet,
    ): ResponseEntity<Any> {
        logger.info("Saving config for dealerId: $dealerId, page: $page")
        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }

        rateSheetService.saveRateSheetConfiguration(dealerId, loggedInUser.id, null, request)
        return ResponseEntity.ok().build()
    }
}
