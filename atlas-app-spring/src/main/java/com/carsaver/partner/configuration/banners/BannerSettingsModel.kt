package com.carsaver.partner.configuration.banners

data class BannerSettingsModel(
    val placements: List<PlacementModel>? = null,
    val contents: List<BannerModel>? = null
)

data class PlacementModel (
    var id: String? = null,
    var enabled: Boolean? = null,
    var name: String? = null,
    var selectedBanner: String? = null,
    var script: String? = null
)

data class BannerModel (
    var id: String? = null,
    var imageSelected: String? = null,
    var destination: String? = null,
    var preTitle: String? = null,
    var title: String? = null,
    var subText: String? = null,
    var dealerLogo: Boolean? = null,
    var ctaText: String? = null,
    var ctaColor: String? = null,
    var ctaBackgroundColor: String? = null,
    var html: String? = null,
    var type: String? = null,
    var location: String? = null
)

