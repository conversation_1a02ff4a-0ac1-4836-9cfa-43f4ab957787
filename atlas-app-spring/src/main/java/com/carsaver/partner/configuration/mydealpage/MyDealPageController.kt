package com.carsaver.partner.configuration.mydealpage

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.builder.LocalBuilderDefinitionService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*


private const val PAGE_PATH = "My Deal Page"

@RestController
class MyDealPageController (
    private val localBuilderDefinitionService: LocalBuilderDefinitionService,
    private val myDealPageConfigService: MyDealPageConfigService
) {
    private val logger = org.slf4j.LoggerFactory.getLogger(MyDealPageController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=$PAGE_PATH"])
    fun getBuilderConfiguration(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
        @RequestParam("page") page: String?,
        @RequestParam(value = "userId", required = false) userId: String?
    ): Any {
        logger.info("Fetching builder definition for page: $page")
        return localBuilderDefinitionService.getBuilderDefinition(page)
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=$PAGE_PATH"])
    fun getMyDealPageConfiguration(
        @RequestParam("dealerId") dealerId: String
    ): Any {
        return myDealPageConfigService.getMyDealPageConfiguration(dealerId, null)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=$PAGE_PATH"])
    fun saveMyDealPageConfigurationJustForDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestBody request: Map<String, Any?>,
    ): ResponseEntity<Any> {
        logger.info("Saving config for dealerId: $dealerId, page: $page")
        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }

        myDealPageConfigService.saveMyDealPageConfiguration(dealerId, loggedInUser.id, null, request)
        return ResponseEntity.ok().build()
    }

}
