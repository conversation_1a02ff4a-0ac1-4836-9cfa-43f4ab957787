package com.carsaver.partner.configuration.theming

import com.carsaver.configuration.v2.api.theme.ThemingModel
import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.configuration.utils.FlatMapUtils
import org.springframework.stereotype.Service

@Service
class ThemingConfigService (
    val configServiceClient: ConfigServiceClient,
    val mapper: ThemingResponseMapper,
){

    fun getThemingConfiguration(
        dealerId: String?,
        domain: String?
    ): Map<String, Any?> {
        //get the response from config service
        val response = configServiceClient.getThemingValues(dealerId, domain)
        //convert response to ThemingModel
        val model = mapper.toModel(response)
        //convert model to map for FE
        val flatMap = FlatMapUtils.modelToMap(model)
        return flatMap
    }


    fun saveThemingConfiguration(
        dealerId: String?,
        userId: String,
        domain: String?,
        request: Map<String, String?>
    ) {

        //convert map to ThemingModel -> similar to toJson java class
        val themeModel = FlatMapUtils.mapToModel(request, ThemingModel())
        //convert model to Request for service
        val theme = mapper.toRequest(themeModel as ThemingModel?)
        //send to config service
        configServiceClient.saveThemingValues(dealerId,userId, domain, theme)
    }



}
