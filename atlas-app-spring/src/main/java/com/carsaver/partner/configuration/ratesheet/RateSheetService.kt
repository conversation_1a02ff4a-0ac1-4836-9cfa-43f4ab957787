package com.carsaver.partner.configuration.ratesheet

import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.configuration.ratesheet.FallbackRateSheet.AgeRange
import com.carsaver.partner.configuration.utils.FlatMapUtils
import org.springframework.stereotype.Service

@Service
class RateSheetService(
    private val configServiceClient: ConfigServiceClient,
) {

    fun getRateSheetConfiguration(
        dealerId: String?
    ): Map<String, Any?> {
        //get the response from config service
        val response = configServiceClient.getRateSheet(dealerId, null)

        response.terms?.forEach { term ->
            term.rates?.forEach { rate ->
                if (rate.stockTypeNew == true){
                    val ageRange = rate.ageRange ?: AgeRange()
                    ageRange.min = "New"
                    ageRange.max = "New"
                    rate.ageRange = ageRange
                }
            }
        }

        //convert model to map for FE
        val flatMap = FlatMapUtils.modelToMap(response)
        return flatMap
    }

    fun saveRateSheetConfiguration(
        dealerId: String?,
        userId: String,
        domain: String?,
        request: FallbackRateSheet
    ) {
        request.terms?.forEach { term ->
            term.rates?.forEach { rate ->
                rate.stockTypeNew = rate.ageRange?.min == "New" || rate.ageRange?.max == "New"
            }
        }
        configServiceClient.saveRateSheet(dealerId, userId, domain, request)
    }
}
