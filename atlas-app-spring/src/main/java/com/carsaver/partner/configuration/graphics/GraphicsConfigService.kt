package com.carsaver.partner.configuration.graphics

import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.client.mapper.JsonMapper
import com.carsaver.partner.configuration.ConfigurationClient
import com.carsaver.partner.configuration.graphics.mappers.GraphicsConfigFileMapper
import com.carsaver.partner.configuration.graphics.mappers.GraphicsConfigPayloadMapper
import com.carsaver.partner.configuration.ctaconfig.CtaImageValidator
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream

@Service
class GraphicsConfigService(
    private val configServiceClient: ConfigServiceClient,
    private val configurationClient: ConfigurationClient,
) {
    private val objectMapper: ObjectMapper = JsonMapper.getObjectMapper()
    private val logger = LoggerFactory.getLogger(GraphicsConfigService::class.java)

    fun getGraphicsConfigByDealer(
        dealerId: String,
        domain: String?,
    ):GraphicsConfigResponse {
        val graphicsConfig = configServiceClient.getGraphicsConfigByDealerId(dealerId, domain)

        return graphicsConfig
    }

    fun saveGraphicsConfigByDealer(
        dealerId: String,
        userId: String,
        domain: String?,
        payload: Map<String, String?>,
        files: MutableList<MultipartFile?>
    ) {
        val graphicsConfig: GraphicsConfigRequest = GraphicsConfigPayloadMapper.mapToConfig(payload)
        val notNullFiles: Array<MultipartFile> = GraphicsConfigFileMapper.injectFiles(dealerId, graphicsConfig, files).toTypedArray()

        notNullFiles.forEach {
            CtaImageValidator.validateImageType(ByteArrayInputStream(it.bytes), it.contentType)
        }

        val request = objectMapper.writeValueAsString(graphicsConfig)
        configurationClient.saveGraphicsConfig(dealerId, userId, domain, request, notNullFiles)
    }

}
