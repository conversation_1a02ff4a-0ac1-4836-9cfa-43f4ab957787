package com.carsaver.partner.configuration.mydealpage

data class MyDealPageResponse (
    var paymentOptions: PaymentOptionsResponse? = null,
    var rebatesAndIncentives: RebatesAndIncentivesResponse? = null,
    var tradeIn: TradeInResponse? = null,
    var protectionProducts: ProtectionProductsResponse? = null,
    var accessories: AccessoriesResponse? = null,
    var financing: FinancingResponse? = null,
    var contractingAndDelivery: ContractingAndDeliveryResponse? = null,
    var uploadDocuments: UploadDocumentsResponse? = null,
    var appointment: AppointmentResponse? = null,
    var insuranceQuotes: InsuranceQuotesResponse? = null,
    var dealGuide: DealGuideResponse? = null,
)

data class PaymentOptionsResponse (
    var sectionTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null,
    var paymentMethodsDisplayed: List<String>? = null
)

data class RebatesAndIncentivesResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var rebateCategories: List<String>? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class TradeInResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class ProtectionProductsResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class AccessoriesResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class FinancingResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class ContractingAndDeliveryResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class UploadDocumentsResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class AppointmentResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class InsuranceQuotesResponse (
    var displaySection: Boolean? = null,
    var sectionTitle: String? = null,
    var headerTitle: String? = null,
    var headerSubTitle: String? = null,
    var displayTimeToComplete: Boolean? = null,
    var timeToComplete: String? = null
)

data class DealGuideResponse (
    var secondaryCtaButton: CtaButtonResponse? = null,
    var tertiaryCtaButton: CtaButtonResponse? = null,
)

data class CtaButtonResponse (
    var active: Boolean? = null,
    var buttonText: String? = null,
    var linkDestination: String? = null,
)
