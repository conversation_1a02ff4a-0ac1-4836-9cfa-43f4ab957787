package com.carsaver.partner.configuration.ctaconfig.mappers

import org.springframework.web.multipart.MultipartFile

const val FILE_PATH = "websiteConfigurations/ctaButtons/customizePlugins/location/%s"

class CtaConfigMultipartExtractorMapper {

    companion object {
        fun extractFiles(page: String, request: Map<String, Any?>): MutableList<MultipartFile?> {
            val basePath = String.format(FILE_PATH, page)

            val files = mutableListOf<MultipartFile?>()
            listOf(
                "$basePath/primaryButton/image",
                "$basePath/secondButton/image",
                "$basePath/thirdButton/image",
                "$basePath/fourthButton/image"
            ).forEach { file ->
                files.add(request[file]?.let {
                    if (it is MultipartFile) it
                    else throw IllegalArgumentException("Invalid file type. Key: $file")
                })
            }
            return files
        }

        fun removeNotStringValues(request: Map<String, Any?>): Map<String, String> {
            return request.filter {  it.value == null ||  it.value is String }
                .mapValues { it.value as String }
        }
    }
}
