package com.carsaver.partner.configuration.theming

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.builder.LocalBuilderDefinitionService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*


private const val PAGE_PATH = "Client Theming"

@RestController
class ThemingController (
    private val localBuilderDefinitionService: LocalBuilderDefinitionService,
    private val themingService: ThemingConfigService
) {
    private val logger = org.slf4j.LoggerFactory.getLogger(ThemingController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=$PAGE_PATH"])
    fun getBuilderConfiguration(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
        @RequestParam("page") page: String?,
        @RequestParam(value = "userId", required = false) userId: String?
    ): Any {
        logger.info("Fetching builder definition for page: $page")
        return localBuilderDefinitionService.getBuilderDefinition(page)
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=$PAGE_PATH"])
    fun getThemingConfiguration(
        @RequestParam("dealerId") dealerId: String
    ): Any {
        return themingService.getThemingConfiguration(dealerId, null)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=$PAGE_PATH"])
    fun saveClientThemingJustForDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestBody request: Map<String, String?>,
    ): ResponseEntity<Any> {
        logger.info("Saving config for dealerId: $dealerId, page: $page")
        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }

        themingService.saveThemingConfiguration(dealerId, loggedInUser.id, null, request)
        return ResponseEntity.ok().build()
    }


}
