package com.carsaver.partner.configuration.overlaysettings

import com.carsaver.partner.client.mapper.JsonMapper
import com.carsaver.partner.configuration.ConfigurationClient
import com.carsaver.partner.configuration.ctaconfig.CtaConfigRequest
import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigPayloadMapper
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Service

@Service
class OverlaySettingsService(
    private val configurationClient: ConfigurationClient,
) {
    private val objectMapper: ObjectMapper = JsonMapper.getObjectMapper()

    fun saveOverlaySettingByDealer(
        dealerId: String,
        userId: String,
        payload: Map<String, String?>,
    ) {

        val ctaConfig: CtaConfigRequest = CtaConfigPayloadMapper.mapToConfig(payload)

        val request = objectMapper.writeValueAsString(ctaConfig)
        configurationClient.saveCtaConfig(dealerId, userId, null, request, null)
    }

    fun getOverlaySettingsByDealer(
        dealerId: String,
        userId: String,
        domain: String?,
    ):OverlaySettingsResponse {
        val ctaConfig = configurationClient.getCtaConfig(dealerId, userId, domain)
        return OverlaySettingsResponse(
            integrationMethod = ctaConfig.integrationMethod?.orElse("") ?: "",
            displayType = ctaConfig.displayType?.orElse("") ?: ""
        )
    }

}
