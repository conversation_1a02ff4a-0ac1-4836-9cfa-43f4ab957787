package com.carsaver.partner.configuration.ctaconfig.mappers

class CtaMapperUtils {

    companion object{

        fun booleanToRenderType(value: Boolean): String{
            return when (value) {
                true -> "IMAGE"
                else -> "HTML"
            }
        }

        fun renderTypeToBoolean(value: String): Boolean {
            return when (value) {
                "IMAGE" -> true
                else -> false
            }
        }
    }
}
