package com.carsaver.partner.configuration.ctaconfig.mappers

import com.carsaver.partner.configuration.ctaconfig.CtaConfigResponse
import java.util.*

private const val DEFAULT_RENDER_TYPE = "HTML"

class CtaConfigBeanMapper {

    companion object {
        fun configToMap(ctaConfigResponse: CtaConfigResponse): Map<String, String?> {
            val result = mutableMapOf<String, String?>()

            // Map VDP configurations
            mapPageToResult(ctaConfigResponse.vdp, "/vehicleDetailsPage", result)

            // Map SRP (listings) configurations
            mapPageToResult(ctaConfigResponse.listings, "/listingsPage", result)

            return result
        }

        private fun mapPageToResult(
            pageOpt: Optional<CtaConfigResponse.CtaPage>?,
            pageLocation: String,
            result: MutableMap<String, String?>
        ) {
            val page = pageOpt?.orElse(null)

            // Map formatting options
            // The formatting options are the same by page for all buttons
            // We can use the primary button as a reference
            val formattingOptions = page?.primaryButton?.orElse(null)
            addOptionalValueWidth("$BASE_KEY$pageLocation/formattingOptions/width", formattingOptions?.width, result)
            addOptionalValue("$BASE_KEY$pageLocation/formattingOptions/height", formattingOptions?.height, result)
            addOptionalValue(
                "$BASE_KEY$pageLocation/formattingOptions/font/font-family",
                formattingOptions?.font,
                result
            )
            addOptionalValue(
                "$BASE_KEY$pageLocation/formattingOptions/font/font-size",
                formattingOptions?.fontSize,
                result
            )
            addOptionalValue(
                "$BASE_KEY$pageLocation/formattingOptions/font/font-weight",
                formattingOptions?.fontWeight,
                result
            )
            addOptionalValue(
                "$BASE_KEY$pageLocation/formattingOptions/font/align-content",
                formattingOptions?.textAlign,
                result
            )
            addOptionalValue("$BASE_KEY$pageLocation/formattingOptions/padding", formattingOptions?.padding, result)
            addOptionalValue("$BASE_KEY$pageLocation/formattingOptions/margin", formattingOptions?.margin, result)
            addOptionalValue("$BASE_KEY$pageLocation/formattingOptions/radius", formattingOptions?.borderRadius, result)

            // Map each button
            mapButtonToResult(page?.primaryButton, "$BASE_KEY$pageLocation/primaryButton", result)
            mapButtonToResult(page?.secondButton, "$BASE_KEY$pageLocation/secondButton", result)
            mapButtonToResult(page?.thirdButton, "$BASE_KEY$pageLocation/thirdButton", result)
            mapButtonToResult(page?.fourthButton, "$BASE_KEY$pageLocation/fourthButton", result)
        }

        private fun mapButtonToResult(
            buttonOpt: Optional<CtaConfigResponse.ButtonOptions>?,
            buttonPath: String,
            result: MutableMap<String, String?>
        ) {
            val button = buttonOpt?.orElse(null)
            addOptionalValue("$buttonPath/link-destination", button?.destination, result)
            addOptionalValue("$buttonPath/text-color", button?.color, result)
            addOptionalValue("$buttonPath/font-weight", button?.fontWeight, result)
            addOptionalValue("$buttonPath/hover-text-color", button?.hoverColor, result)
            addOptionalValue("$buttonPath/hover-background-color", button?.hoverBackgroundColor, result)
            addOptionalValue("$buttonPath/background-color", button?.backgroundColor, result)
            addOptionalValue("$buttonPath/button-text", button?.name, result)
            addOptionalValue("$buttonPath/display", button?.active, result)
            addOptionalValueRenderType("$buttonPath/enable-image-cta", button?.renderType, result)

            addOptionalValue("$buttonPath/image-display-name", button?.imageDisplayName, result)
            addOptionalValue("$buttonPath/image-name", button?.imageName, result)
            addOptionalValue("$buttonPath/image-size", button?.imageSize, result)
            addOptionalValue("$buttonPath/image-background-color", button?.imageBackgroundColor, result)
            addOptionalValue("$buttonPath/image-url", button?.imageUrl, result)
        }

        private fun addOptionalValue(
            key: String,
            optionalValue: Optional<String>?,
            result: MutableMap<String, String?>
        ) {
            result[key] = optionalValue?.orElse(null)
        }

        private fun addOptionalValueWidth(
            key: String,
            optionalValue: Optional<String>?,
            result: MutableMap<String, String?>
        ) {
            result[key] = optionalValue?.orElse(null)
                ?.replace("100%", "")
        }

        private fun addOptionalValueRenderType(
            path: String,
            renderType: Optional<String>?,
            result: MutableMap<String, String?>
        ) {
            val checkedRenderType = renderType ?: Optional.empty() // it is possible for render type to be null

            val value = CtaMapperUtils.renderTypeToBoolean(checkedRenderType.orElse(DEFAULT_RENDER_TYPE)!!).toString()
            addOptionalValue(path, Optional.of(value), result)
        }
    }
}
