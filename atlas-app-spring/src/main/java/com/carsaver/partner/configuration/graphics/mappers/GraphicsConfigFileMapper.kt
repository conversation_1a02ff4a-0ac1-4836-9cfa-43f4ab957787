package com.carsaver.partner.configuration.graphics.mappers

import com.carsaver.partner.configuration.graphics.GraphicsConfigRequest
import org.springframework.web.multipart.MultipartFile
import java.util.*

class GraphicsConfigFileMapper {

    companion object {

        /**
         * Processes uploaded image files for Graphics configuration (logo, favicon, dealership images).
         *
         * This function:
         * 1. Validates that files are provided for graphics configuration
         * 2. Associates each file with its corresponding property in the graphics configuration
         * 3. Sets the appropriate name properties with the original filename
         * 4. Returns the original files without modification
         *
         * @param dealerId The dealer identifier
         * @param graphicsConfig The graphics configuration object to be updated with file information
         * @param files List of files: [logo, favicon, dealership1, dealership2]
         * @return A flattened list of all non-null MultipartFile objects
         */
        fun injectFiles(
            dealerId: String,
            graphicsConfig: GraphicsConfigRequest,
            files: MutableList<MultipartFile?>
        ): List<MultipartFile> {
            if (files.size != 4) {
                throw IllegalArgumentException("Bad request we expect 4 files for graphics config")
            }

            val processedFiles = mutableListOf<MultipartFile>()

            for (i in files.indices) {
                val multipartFile = files[i]

                if (multipartFile != null) {
                    processedFiles.add(multipartFile)

                    // Set the appropriate properties in the graphics config using original filename
                    when (i) {
                        0 -> { // logo
                            graphicsConfig.logoName = multipartFile.originalFilename?.let { Optional.of(it) }
                        }
                        1 -> { // favicon
                            graphicsConfig.favIconName = multipartFile.originalFilename?.let { Optional.of(it) }
                        }
                        2 -> { // dealership mobile
                            if (graphicsConfig.dealershipImages == null) {
                                graphicsConfig.dealershipImages = Optional.of(GraphicsConfigRequest.DealershipImages())
                            }
                            graphicsConfig.dealershipImages?.get()?.mobileName = multipartFile.originalFilename?.let { Optional.of(it) }
                        }
                        3 -> { // dealership desktop
                            if (graphicsConfig.dealershipImages == null) {
                                graphicsConfig.dealershipImages = Optional.of(GraphicsConfigRequest.DealershipImages())
                            }
                            graphicsConfig.dealershipImages?.get()?.desktopName = multipartFile.originalFilename?.let { Optional.of(it) }
                        }
                    }
                }
            }

            return processedFiles
        }
    }
}
