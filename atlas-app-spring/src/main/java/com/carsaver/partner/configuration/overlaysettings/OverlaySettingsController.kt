package com.carsaver.partner.configuration.ctaconfig

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigMultipartExtractorMapper
import com.carsaver.partner.configuration.overlaysettings.OverlaySettingsService
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


@RestController
class OverlaySettingsController(
    private val overlaySettingsService: OverlaySettingsService
) {
    private val logger = org.slf4j.LoggerFactory.getLogger(OverlaySettingsController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=Overlay Settings"])
    fun getBuilderForOverlaySettings(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
        @RequestParam("page") page: String?,
        @RequestParam(value = "userId", required = false) userId: String?
    ): Any {
        val resource = this::class.java.classLoader.getResource("configurationBuilderDefinition/websiteConfigurations_overlaySettings.json")
        return resource?.readText() ?: throw IllegalStateException("Resource not found")
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=Overlay Settings"])
    fun getOverlaySettings(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam(value = "userId", required = false) userId: String,
    ): Any {
        return overlaySettingsService.getOverlaySettingsByDealer(dealerId, userId, null)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=Overlay Settings"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    fun saveOverlaySettingsForDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestBody request: Map<String, String?>,
    ): ResponseEntity<Any> {
        logger.info("Saving overlay settings for dealerId: $dealerId, page: $page")
        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }
        val payload = CtaConfigMultipartExtractorMapper.removeNotStringValues(request)

        overlaySettingsService.saveOverlaySettingByDealer(dealerId, loggedInUser.id, payload)
        return ResponseEntity.ok().build()
    }
}
