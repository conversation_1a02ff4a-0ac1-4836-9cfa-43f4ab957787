package com.carsaver.partner.configuration.ctaconfig

import com.carsaver.magellan.auth.SessionUtils
import com.carsaver.partner.configuration.builder.LocalBuilderDefinitionService
import com.carsaver.partner.configuration.ctaconfig.mappers.CtaConfigMultipartExtractorMapper
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile


@RestController
class CtaConfigController(
    private val localBuilderDefinitionService: LocalBuilderDefinitionService,
    private val ctaConfigService: CtaConfigService
) {
    private val logger = org.slf4j.LoggerFactory.getLogger(CtaConfigController::class.java)

    @GetMapping(value = ["/api/configuration/builder"], params = ["page=CTA Buttons"])
    fun getBuilderForCta(
        @RequestParam(value = "dealerId", required = false) dealerId: String?,
        @RequestParam("page") page: String?,
        @RequestParam(value = "userId", required = false) userId: String?
    ): Any {
        return localBuilderDefinitionService.getBuilderDefinition(page)
    }

    @GetMapping(value = ["/api/configuration"], params = ["dealerId", "page=CTA Buttons"])
    fun getBuilderConfiguration(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam(value = "userId", required = false) userId: String
    ): Any {
        return ctaConfigService.getCtaConfigByDealer(dealerId, userId, null)
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=CTA Buttons"], consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun saveCtaConfigJustForDealer(
        @RequestParam("dealerId") dealerId: String,
        @RequestParam("page") page: String,
        @RequestParam request: Map<String, String?>,
        @RequestParam files: Map<String, MultipartFile?>,
    ): ResponseEntity<Any> {
        logger.info("Saving CTA config for dealerId: $dealerId, page: $page")
        val loggedInUser = SessionUtils.getLoggedInUser()
            .orElseThrow { RuntimeException("logged-in user required") }

        val vdpFiles = CtaConfigMultipartExtractorMapper.extractFiles("vehicleDetailsPage", files)
        val vlpFiles = CtaConfigMultipartExtractorMapper.extractFiles("listingsPage", files)
        val payload = CtaConfigMultipartExtractorMapper.removeNotStringValues(request)
        ctaConfigService.saveCtaConfigByDealer(dealerId, loggedInUser.id, payload, vdpFiles, vlpFiles)
        return ResponseEntity.ok().build()
    }

    @PostMapping(value = ["/api/configuration"], params = ["dealerId", "page=CTA Buttons"], consumes = [MediaType.APPLICATION_JSON_VALUE])
    fun saveCtaConfigJustForDealerUnsupportedMediaType(): ResponseEntity<Any> {
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).build()
    }
}
