package com.carsaver.partner.configuration.mydealpage

import com.carsaver.partner.client.configuration.ConfigServiceClient
import com.carsaver.partner.configuration.utils.FlatMapUtils
import com.carsaver.partner.configuration.utils.ModelMapperUtils
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Service

@Service
class MyDealPageConfigService (
    val configServiceClient: ConfigServiceClient,
    val mapper: MyDealPageResponseMapper,
    val objectMapper: ObjectMapper = ObjectMapper()
){

    fun getMyDealPageConfiguration(
        dealerId: String?,
        domain: String?
    ): Map<String, Any?> {
        //get the response from config service
        val response = configServiceClient.getMyDealPageValues(dealerId, domain)
        //convert response to MyDealPageModel
        val model = mapper.toModel(response)
        //convert model to map for FE
        val flatMap = FlatMapUtils.modelToMap(model)
        return flatMap
    }


    fun saveMyDealPageConfiguration(
        dealerId: String?,
        userId: String,
        domain: String?,
        request: Map<String, Any?>
    ) {
        // Pre-process the request map to handle arrays
        val processedRequest = request.mapValues { (key, value) ->
            when (value) {
                is List<*> -> objectMapper.writeValueAsString(value)
                else -> value?.toString()
            }
        }

        val myDealPageModel = ModelMapperUtils.mapToModel(processedRequest, MyDealPageModel())
        //convert model to Request for service
        val myDealPage = mapper.toRequest(myDealPageModel as MyDealPageModel?)
        //send to config service
        configServiceClient.saveMyDealPageValues(dealerId,userId, domain, myDealPage)
    }

}
