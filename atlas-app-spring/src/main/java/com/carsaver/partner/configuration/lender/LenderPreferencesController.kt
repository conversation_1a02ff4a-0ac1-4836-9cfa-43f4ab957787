package com.carsaver.partner.configuration.lender

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class LenderPreferencesController(
    private val service: LenderPreferencesService,
) {

    @GetMapping("/api/lms-options")
    fun getLmsOptions(
        @RequestParam dealerId: String): ResponseEntity<Any> {
        return service.getLmsOptions(dealerId)
    }

}
