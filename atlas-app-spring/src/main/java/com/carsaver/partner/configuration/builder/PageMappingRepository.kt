package com.carsaver.partner.configuration.builder

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.core.io.Resource
import org.springframework.core.io.ResourceLoader
import org.springframework.core.io.support.ResourcePatternUtils
import org.springframework.stereotype.Repository

@Repository
open class PageMappingRepository(
    resourceLoader: ResourceLoader
) {
    private val mapper = jacksonObjectMapper().findAndRegisterModules()

    init {
        val resources: Array<Resource> =
            ResourcePatternUtils.getResourcePatternResolver(resourceLoader).getResources("classpath:configurationBuilderDefinition/*")
        resources.forEach { file ->
            mapper.readValue<MutableMap<String,Any>>(file.file).let { it -> pathDefinitionMap[it["page"]as String] = it }
        }
    }

    fun getPagePaths(key: String): MutableMap<String, Any> {
        return pathDefinitionMap[key] ?: throw IllegalArgumentException("Page not found")
    }

    internal fun getAll(): MutableMap<String,  MutableMap<String, Any>> {
        return pathDefinitionMap
    }

    companion object {
        private val pathDefinitionMap: MutableMap<String, MutableMap<String ,Any>> = mutableMapOf()
    }
}
