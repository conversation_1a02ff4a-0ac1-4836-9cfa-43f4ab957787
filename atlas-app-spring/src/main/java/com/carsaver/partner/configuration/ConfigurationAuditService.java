package com.carsaver.partner.configuration;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.configuration.AuditDataFields;
import com.carsaver.partner.model.configuration.AuditResponseData;
import com.carsaver.partner.model.configuration.QrCodeTransformAuditResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Service
@Slf4j
public class ConfigurationAuditService {

    private ConfigurationClient configurationClient;
    private UserClient userClient;
    private final ObjectMapper mapper = new ObjectMapper()
        .configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);

    public QrCodeTransformAuditResponse fetchPagedQRCodeAuditLogs(String dealerId, int page, int size, String pageName) {
        try {
            var auditDataObj = configurationClient.getQRCodeAuditLog(dealerId, page, size, pageName);
            AuditResponseData auditResponseData = mapper.convertValue(auditDataObj, AuditResponseData.class);

            QrCodeTransformAuditResponse transformAuditResponse = new QrCodeTransformAuditResponse();
            transformAuditResponse.setSize(auditResponseData.getSize());
            transformAuditResponse.setPage(auditResponseData.getPage());
            transformAuditResponse.setTotalCount(auditResponseData.getTotalCount());

            String userName = auditResponseData.getData().stream()
                .findFirst()
                .map(AuditDataFields::getUserId)
                .filter(userId -> !userId.isEmpty())
                .map(userClient::findById)
                .map(UserView::getFullName)
                .orElse("");

            Map<String, Map<String, Object>> groupedEntries = new LinkedHashMap<>();
            auditResponseData.getData().forEach(item -> processDataItem(item, groupedEntries, userName));

            transformAuditResponse.setData(new ArrayList<>(groupedEntries.values()));
            return transformAuditResponse;
        } catch (Exception e) {
            throw new RuntimeException("Error transforming audit data", e);
        }
    }

    private void processDataItem(AuditDataFields item, Map<String, Map<String, Object>> groupedEntries, String userName) {
        String domainName = item.getDomainName();
        String date = Instant.ofEpochMilli(Long.parseLong(item.getTime())).toString();
        String key = domainName + date;

        Map<String, Object> userEntry = groupedEntries.computeIfAbsent(key, k -> new LinkedHashMap<>(Map.of(
            "date", date,
            "userName", userName,
            "fields", new ArrayList<Map<String, Object>>()
        )));

        List<Map<String, Object>> fieldsArray = (List<Map<String, Object>>) userEntry.get("fields");

        item.getFields().forEach((fieldName, value) -> {
            if (fieldName.startsWith("fieldName")) {
                String index = extractIndex(fieldName);
                String oldValueKey = "oldValue" + index;
                String newValueKey = "newValue" + index;
                String labelValueKey = "label" + index;

                String labelName = item.getFields().getOrDefault(labelValueKey, "").toString().trim();
                if (!labelName.isEmpty()) {
                    Map<String, Object> fieldEntry = new LinkedHashMap<>();
                    fieldEntry.put("fieldName", labelName);
                    fieldEntry.put("oldValue", item.getFields().getOrDefault(oldValueKey, ""));
                    fieldEntry.put("newValue", item.getFields().getOrDefault(newValueKey, ""));
                    fieldsArray.add(fieldEntry);
                }
            }
        });
    }

    private String extractIndex(String fieldName) {
        return fieldName.substring("fieldName".length());
    }
}
