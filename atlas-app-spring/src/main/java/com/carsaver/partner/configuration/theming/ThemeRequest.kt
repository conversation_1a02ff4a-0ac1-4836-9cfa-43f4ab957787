package com.carsaver.partner.configuration.theming


data class ThemeRequest (
     var topNavigationBar: TopNavigationBar? = null,
     var accentColor: AccentColor? = null,
     var primaryButton: CtaButton? = null,
     var secondaryButton: CtaButton? = null,
     var tertiaryButton: CtaButton? = null
)

data class AccentColor (
     var active: Boolean? = null,
     var color: String? = null,
)

data class TopNavigationBar (
     var active: Boolean? = null,
     var backgroundColor: String? = null,
     var textColor: String? = null,
     var cornerRadius: String? = null,
)

data class ButtonAndSectionHeaders (
      var active: Boolean? = null,
      var cornerRadius: String? = null,
      var type: String? = null
)

data class CtaButton (
     var active: Boolean? = null,
     var backgroundColor: String? = null,
     var borderColor: String? = null,
     var textColor: String? = null,
     var highlightRolloverBackgroundColor: String? = null,
     var highlightRolloverBorderColor: String? = null,
     var highlightRolloverTextColor: String? = null,
)
