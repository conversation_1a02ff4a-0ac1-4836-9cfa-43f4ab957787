package com.carsaver.partner.configuration.mydealpage;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
public interface MyDealPageResponseMapper {

    @Mappings({
        @Mapping(target = "paymentOptionsSectionTitle", source = "paymentOptions.sectionTitle"),
        @Mapping(target = "paymentOptionsDisplayTimeToComplete", source = "paymentOptions.displayTimeToComplete"),
        @Mapping(target = "paymentOptionsTimeToComplete", source = "paymentOptions.timeToComplete"),
        @Mapping(target = "paymentOptionsPaymentMethodsDisplayed", source = "paymentOptions.paymentMethodsDisplayed"),

        @Mapping(target = "rebatesAndIncentivesDisplaySection", source = "rebatesAndIncentives.displaySection"),
        @Mapping(target = "rebatesAndIncentivesSectionTitle", source = "rebatesAndIncentives.sectionTitle"),
        @Mapping(target = "rebatesAndIncentivesHeaderTitle", source = "rebatesAndIncentives.headerTitle"),
        @Mapping(target = "rebatesAndIncentivesHeaderSubTitle", source = "rebatesAndIncentives.headerSubTitle"),
        @Mapping(target = "rebatesAndIncentivesDisplayTimeToComplete", source = "rebatesAndIncentives.displayTimeToComplete"),
        @Mapping(target = "rebatesAndIncentivesTimeToComplete", source = "rebatesAndIncentives.timeToComplete"),
        @Mapping(target = "rebatesAndIncentivesRebateCategories", source = "rebatesAndIncentives.rebateCategories"),

        @Mapping(target = "tradeInDisplaySection", source = "tradeIn.displaySection"),
        @Mapping(target = "tradeInSectionTitle", source = "tradeIn.sectionTitle"),
        @Mapping(target = "tradeInHeaderTitle", source = "tradeIn.headerTitle"),
        @Mapping(target = "tradeInHeaderSubTitle", source = "tradeIn.headerSubTitle"),
        @Mapping(target = "tradeInDisplayTimeToComplete", source = "tradeIn.displayTimeToComplete"),
        @Mapping(target = "tradeInTimeToComplete", source = "tradeIn.timeToComplete"),

        @Mapping(target = "protectionProductsDisplaySection", source = "protectionProducts.displaySection"),
        @Mapping(target = "protectionProductsSectionTitle", source = "protectionProducts.sectionTitle"),
        @Mapping(target = "protectionProductsHeaderTitle", source = "protectionProducts.headerTitle"),
        @Mapping(target = "protectionProductsHeaderSubTitle", source = "protectionProducts.headerSubTitle"),
        @Mapping(target = "protectionProductsDisplayTimeToComplete", source = "protectionProducts.displayTimeToComplete"),
        @Mapping(target = "protectionProductsTimeToComplete", source = "protectionProducts.timeToComplete"),

        @Mapping(target = "accessoriesDisplaySection", source = "accessories.displaySection"),
        @Mapping(target = "accessoriesSectionTitle", source = "accessories.sectionTitle"),
        @Mapping(target = "accessoriesHeaderTitle", source = "accessories.headerTitle"),
        @Mapping(target = "accessoriesHeaderSubTitle", source = "accessories.headerSubTitle"),
        @Mapping(target = "accessoriesDisplayTimeToComplete", source = "accessories.displayTimeToComplete"),
        @Mapping(target = "accessoriesTimeToComplete", source = "accessories.timeToComplete"),

        @Mapping(target = "financingDisplaySection", source = "financing.displaySection"),
        @Mapping(target = "financingSectionTitle", source = "financing.sectionTitle"),
        @Mapping(target = "financingHeaderTitle", source = "financing.headerTitle"),
        @Mapping(target = "financingHeaderSubTitle", source = "financing.headerSubTitle"),
        @Mapping(target = "financingDisplayTimeToComplete", source = "financing.displayTimeToComplete"),
        @Mapping(target = "financingTimeToComplete", source = "financing.timeToComplete"),

        @Mapping(target = "contractingAndDeliveryDisplaySection", source = "contractingAndDelivery.displaySection"),
        @Mapping(target = "contractingAndDeliverySectionTitle", source = "contractingAndDelivery.sectionTitle"),
        @Mapping(target = "contractingAndDeliveryHeaderTitle", source = "contractingAndDelivery.headerTitle"),
        @Mapping(target = "contractingAndDeliveryHeaderSubTitle", source = "contractingAndDelivery.headerSubTitle"),
        @Mapping(target = "contractingAndDeliveryDisplayTimeToComplete", source = "contractingAndDelivery.displayTimeToComplete"),
        @Mapping(target = "contractingAndDeliveryTimeToComplete", source = "contractingAndDelivery.timeToComplete"),

        @Mapping(target = "uploadDocumentsDisplaySection", source = "uploadDocuments.displaySection"),
        @Mapping(target = "uploadDocumentsSectionTitle", source = "uploadDocuments.sectionTitle"),
        @Mapping(target = "uploadDocumentsHeaderTitle", source = "uploadDocuments.headerTitle"),
        @Mapping(target = "uploadDocumentsHeaderSubTitle", source = "uploadDocuments.headerSubTitle"),
        @Mapping(target = "uploadDocumentsDisplayTimeToComplete", source = "uploadDocuments.displayTimeToComplete"),
        @Mapping(target = "uploadDocumentsTimeToComplete", source = "uploadDocuments.timeToComplete"),

        @Mapping(target = "appointmentDisplaySection", source = "appointment.displaySection"),
        @Mapping(target = "appointmentSectionTitle", source = "appointment.sectionTitle"),
        @Mapping(target = "appointmentHeaderTitle", source = "appointment.headerTitle"),
        @Mapping(target = "appointmentHeaderSubTitle", source = "appointment.headerSubTitle"),
        @Mapping(target = "appointmentDisplayTimeToComplete", source = "appointment.displayTimeToComplete"),
        @Mapping(target = "appointmentTimeToComplete", source = "appointment.timeToComplete"),

        @Mapping(target = "insuranceQuotesDisplaySection", source = "insuranceQuotes.displaySection"),
        @Mapping(target = "insuranceQuotesSectionTitle", source = "insuranceQuotes.sectionTitle"),
        @Mapping(target = "insuranceQuotesHeaderTitle", source = "insuranceQuotes.headerTitle"),
        @Mapping(target = "insuranceQuotesHeaderSubTitle", source = "insuranceQuotes.headerSubTitle"),
        @Mapping(target = "insuranceQuotesDisplayTimeToComplete", source = "insuranceQuotes.displayTimeToComplete"),
        @Mapping(target = "insuranceQuotesTimeToComplete", source = "insuranceQuotes.timeToComplete"),

        @Mapping(target = "dealGuideSecondaryCtaButtonActive", source = "dealGuide.secondaryCtaButton.active"),
        @Mapping(target = "dealGuideSecondaryCtaButtonText", source = "dealGuide.secondaryCtaButton.buttonText"),
        @Mapping(target = "dealGuideSecondaryCtaButtonLinkDestination", source = "dealGuide.secondaryCtaButton.linkDestination"),

        @Mapping(target = "dealGuideTertiaryCtaButtonActive", source = "dealGuide.tertiaryCtaButton.active"),
        @Mapping(target = "dealGuideTertiaryCtaButtonText", source = "dealGuide.tertiaryCtaButton.buttonText"),
        @Mapping(target = "dealGuideTertiaryCtaButtonLinkDestination", source = "dealGuide.tertiaryCtaButton.linkDestination"),
    })
    MyDealPageModel toModel(MyDealPageResponse response);

    @Mappings({
        @Mapping(source = "paymentOptionsSectionTitle", target = "paymentOptions.sectionTitle"),
        @Mapping(source = "paymentOptionsDisplayTimeToComplete", target = "paymentOptions.displayTimeToComplete"),
        @Mapping(source = "paymentOptionsTimeToComplete", target = "paymentOptions.timeToComplete"),
        @Mapping(source = "paymentOptionsPaymentMethodsDisplayed", target = "paymentOptions.paymentMethodsDisplayed"),

        @Mapping(source = "rebatesAndIncentivesDisplaySection", target = "rebatesAndIncentives.displaySection"),
        @Mapping(source = "rebatesAndIncentivesSectionTitle", target = "rebatesAndIncentives.sectionTitle"),
        @Mapping(source = "rebatesAndIncentivesHeaderTitle", target = "rebatesAndIncentives.headerTitle"),
        @Mapping(source = "rebatesAndIncentivesHeaderSubTitle", target = "rebatesAndIncentives.headerSubTitle"),
        @Mapping(source = "rebatesAndIncentivesDisplayTimeToComplete", target = "rebatesAndIncentives.displayTimeToComplete"),
        @Mapping(source = "rebatesAndIncentivesTimeToComplete", target = "rebatesAndIncentives.timeToComplete"),
        @Mapping(source = "rebatesAndIncentivesRebateCategories", target = "rebatesAndIncentives.rebateCategories"),

        @Mapping(source = "tradeInDisplaySection", target = "tradeIn.displaySection"),
        @Mapping(source = "tradeInSectionTitle", target = "tradeIn.sectionTitle"),
        @Mapping(source = "tradeInHeaderTitle", target = "tradeIn.headerTitle"),
        @Mapping(source = "tradeInHeaderSubTitle", target = "tradeIn.headerSubTitle"),
        @Mapping(source = "tradeInDisplayTimeToComplete", target = "tradeIn.displayTimeToComplete"),
        @Mapping(source = "tradeInTimeToComplete", target = "tradeIn.timeToComplete"),

        @Mapping(source = "protectionProductsDisplaySection", target = "protectionProducts.displaySection"),
        @Mapping(source = "protectionProductsSectionTitle", target = "protectionProducts.sectionTitle"),
        @Mapping(source = "protectionProductsHeaderTitle", target = "protectionProducts.headerTitle"),
        @Mapping(source = "protectionProductsHeaderSubTitle", target = "protectionProducts.headerSubTitle"),
        @Mapping(source = "protectionProductsDisplayTimeToComplete", target = "protectionProducts.displayTimeToComplete"),
        @Mapping(source = "protectionProductsTimeToComplete", target = "protectionProducts.timeToComplete"),

        @Mapping(source = "accessoriesDisplaySection", target = "accessories.displaySection"),
        @Mapping(source = "accessoriesSectionTitle", target = "accessories.sectionTitle"),
        @Mapping(source = "accessoriesHeaderTitle", target = "accessories.headerTitle"),
        @Mapping(source = "accessoriesHeaderSubTitle", target = "accessories.headerSubTitle"),
        @Mapping(source = "accessoriesDisplayTimeToComplete", target = "accessories.displayTimeToComplete"),
        @Mapping(source = "accessoriesTimeToComplete", target = "accessories.timeToComplete"),

        @Mapping(source = "financingDisplaySection", target = "financing.displaySection"),
        @Mapping(source = "financingSectionTitle", target = "financing.sectionTitle"),
        @Mapping(source = "financingHeaderTitle", target = "financing.headerTitle"),
        @Mapping(source = "financingHeaderSubTitle", target = "financing.headerSubTitle"),
        @Mapping(source = "financingDisplayTimeToComplete", target = "financing.displayTimeToComplete"),
        @Mapping(source = "financingTimeToComplete", target = "financing.timeToComplete"),

        @Mapping(source = "contractingAndDeliveryDisplaySection", target = "contractingAndDelivery.displaySection"),
        @Mapping(source = "contractingAndDeliverySectionTitle", target = "contractingAndDelivery.sectionTitle"),
        @Mapping(source = "contractingAndDeliveryHeaderTitle", target = "contractingAndDelivery.headerTitle"),
        @Mapping(source = "contractingAndDeliveryHeaderSubTitle", target = "contractingAndDelivery.headerSubTitle"),
        @Mapping(source = "contractingAndDeliveryDisplayTimeToComplete", target = "contractingAndDelivery.displayTimeToComplete"),
        @Mapping(source = "contractingAndDeliveryTimeToComplete", target = "contractingAndDelivery.timeToComplete"),

        @Mapping(source = "uploadDocumentsDisplaySection", target = "uploadDocuments.displaySection"),
        @Mapping(source = "uploadDocumentsSectionTitle", target = "uploadDocuments.sectionTitle"),
        @Mapping(source = "uploadDocumentsHeaderTitle", target = "uploadDocuments.headerTitle"),
        @Mapping(source = "uploadDocumentsHeaderSubTitle", target = "uploadDocuments.headerSubTitle"),
        @Mapping(source = "uploadDocumentsDisplayTimeToComplete", target = "uploadDocuments.displayTimeToComplete"),
        @Mapping(source = "uploadDocumentsTimeToComplete", target = "uploadDocuments.timeToComplete"),

        @Mapping(source = "appointmentDisplaySection", target = "appointment.displaySection"),
        @Mapping(source = "appointmentSectionTitle", target = "appointment.sectionTitle"),
        @Mapping(source = "appointmentHeaderTitle", target = "appointment.headerTitle"),
        @Mapping(source = "appointmentHeaderSubTitle", target = "appointment.headerSubTitle"),
        @Mapping(source = "appointmentDisplayTimeToComplete", target = "appointment.displayTimeToComplete"),
        @Mapping(source = "appointmentTimeToComplete", target = "appointment.timeToComplete"),

        @Mapping(source = "insuranceQuotesDisplaySection", target = "insuranceQuotes.displaySection"),
        @Mapping(source = "insuranceQuotesSectionTitle", target = "insuranceQuotes.sectionTitle"),
        @Mapping(source = "insuranceQuotesHeaderTitle", target = "insuranceQuotes.headerTitle"),
        @Mapping(source = "insuranceQuotesHeaderSubTitle", target = "insuranceQuotes.headerSubTitle"),
        @Mapping(source = "insuranceQuotesDisplayTimeToComplete", target = "insuranceQuotes.displayTimeToComplete"),
        @Mapping(source = "insuranceQuotesTimeToComplete", target = "insuranceQuotes.timeToComplete"),

        @Mapping(source = "dealGuideSecondaryCtaButtonActive", target = "dealGuide.secondaryCtaButton.active"),
        @Mapping(source = "dealGuideSecondaryCtaButtonText", target = "dealGuide.secondaryCtaButton.buttonText"),
        @Mapping(source = "dealGuideSecondaryCtaButtonLinkDestination", target = "dealGuide.secondaryCtaButton.linkDestination"),
        @Mapping(source = "dealGuideTertiaryCtaButtonActive", target = "dealGuide.tertiaryCtaButton.active"),
        @Mapping(source = "dealGuideTertiaryCtaButtonText", target = "dealGuide.tertiaryCtaButton.buttonText"),
        @Mapping(source = "dealGuideTertiaryCtaButtonLinkDestination", target = "dealGuide.tertiaryCtaButton.linkDestination")
    })
    MyDealPageRequest toRequest(MyDealPageModel dto);
}
