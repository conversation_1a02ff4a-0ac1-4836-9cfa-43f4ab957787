package com.carsaver.partner.search.facets;

import com.carsaver.partner.search.converter.DMATermFacetConverter;
import com.carsaver.partner.search.converter.DealerGroupTermFacetConverter;
import com.carsaver.partner.search.converter.DealerTermFacetConverter;
import com.carsaver.partner.search.converter.ProgramTermFacetConverter;
import com.carsaver.search.annotation.AggConverter;
import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.annotation.NestedAggregate;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DealerDocFacets implements DocFacets {

    @Aggregate(field = "id")
    private List<TermFacet> dealerIds;

    @Aggregate(field = "id")
    @AggConverter(DealerTermFacetConverter.class)
    private List<TermFacet> dealers;

    @Aggregate(field = "group.id")
    @AggConverter(DealerGroupTermFacetConverter.class)
    private List<TermFacet> dealerGroups;

    @NestedAggregate(path = "subscriptions", field = "program.id")
    @AggConverter(ProgramTermFacetConverter.class)
    private List<TermFacet> programs;

    @Aggregate(field = "address.stateCode")
    private List<TermFacet> states;

    @Aggregate(field = "address.dmaCode")
    @AggConverter(DMATermFacetConverter.class)
    private List<TermFacet> dmas;

    @Aggregate(field = "makes")
    private List<TermFacet> makes;

    @Aggregate(field = "inventoryPricingMetrics.percentageNewPriced")
    private List<TermFacet> percentageNewPriced;

    @Aggregate(field = "inventoryPricingMetrics.percentageUsedPriced")
    private List<TermFacet> percentageUsedPriced;

}
