package com.carsaver.partner.search.converter;

import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.search.converter.FacetConverter;
import com.carsaver.search.facet.TermFacet;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class ProgramTermFacetConverter implements FacetConverter<TermFacet, Terms.Bucket> {

    @Autowired
    private ProgramClient programClient;

    public TermFacet convert(Terms.Bucket bucket) {
        String bucketKey = bucket.getKeyAsString();
        Optional<ProgramView> programOpt = programClient.findById(bucketKey);
        if(programOpt.isPresent()) {
            ProgramView program = programOpt.get();
            return TermFacet.builder()
                .id(program.getId())
                .name(program.getName())
                .count(bucket.getDocCount())
                .build();
        }

        return TermFacet.builder()
            .id(bucketKey)
            .name(bucketKey)
            .count(bucket.getDocCount())
            .build();
    }
}
