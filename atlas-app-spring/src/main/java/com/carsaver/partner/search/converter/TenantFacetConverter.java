package com.carsaver.partner.search.converter;

import com.carsaver.magellan.client.TenantClient;
import com.carsaver.magellan.model.TenantView;
import com.carsaver.search.converter.TermFacetConverter;
import com.carsaver.search.facet.TermFacet;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TenantFacetConverter implements TermFacetConverter {

    @Autowired
    private TenantClient tenantClient;

    public TermFacet convert(Terms.Bucket bucket) {
        String bucketKey = bucket.getKeyAsString();
        TenantView tenant = tenantClient.findById(bucketKey);
        if(tenant != null) {
            return TermFacet.builder()
                .id(tenant.getId())
                .name(tenant.getName())
                .count(bucket.getDocCount())
                .build();
        }

        return TermFacet.builder()
            .id(bucketKey)
            .name(bucketKey)
            .count(bucket.getDocCount())
            .build();
    }
}
