package com.carsaver.partner.search.converter;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.search.converter.TermFacetConverter;
import com.carsaver.search.facet.TermFacet;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserTermFacetConverter implements TermFacetConverter {

    @Autowired
    private UserClient userClient;

    public TermFacet convert(Terms.Bucket bucket) {
        var bucketKey = bucket.getKeyAsString();

        if (bucketKey.equalsIgnoreCase("None")) {
            return TermFacet.builder()
                .id(bucketKey)
                .name(bucketKey)
                .count(bucket.getDocCount())
                .build();
        }

        UserView user = userClient.findById(bucketKey);
        if(user != null) {
            return TermFacet.builder()
                .id(user.getId())
                .name(user.getFullName())
                .count(bucket.getDocCount())
                .build();
        }

        return TermFacet.builder()
            .id(bucketKey)
            .name(bucketKey)
            .count(bucket.getDocCount())
            .build();
    }
}
