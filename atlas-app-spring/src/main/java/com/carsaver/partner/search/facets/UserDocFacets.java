package com.carsaver.partner.search.facets;

import com.carsaver.partner.search.converter.DMATermFacetConverter;
import com.carsaver.partner.search.converter.DealerTermFacetConverter;
import com.carsaver.partner.search.converter.ProgramTermFacetConverter;
import com.carsaver.search.annotation.AggConverter;
import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.annotation.MaxAggregate;
import com.carsaver.search.annotation.MinAggregate;
import com.carsaver.search.annotation.NestedAggregate;
import com.carsaver.search.facet.NumberTermFacet;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.GREATER_THAN_12_MONTHS;
import static com.carsaver.partner.web.api.user.elasticsearch.CommonHelper.GREATER_THAN_90_DAYS;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDocFacets implements DocFacets {

    public static final Integer NEXT_12_MONTHS = 365;
    public static final Integer LAST_90_DAYS = 90;

    @Aggregate(field = "program.id")
    @AggConverter(ProgramTermFacetConverter.class)
    private List<TermFacet> programs;

    @Aggregate(field = "tags")
    private List<TermFacet> tags;

    @Aggregate(field = "repeatBuyer")
    private List<TermFacet> repeatBuyer;

    @Aggregate(field = "heatScore")
    private List<TermFacet> heatScores;

    @NestedAggregate(path = "programSubscriptionStages", field = "stageName")
    private List<TermFacet> programSubscriptionStage;

    @Aggregate(field = "stage")
    private List<TermFacet> globalStage;

    @Aggregate(field = "type")
    private List<TermFacet> types;

    @Aggregate(field = "address.stateCode")
    private List<TermFacet> states;

    @Aggregate(field = "source.hostname")
    private List<TermFacet> sourceHosts;

    @Aggregate(field = "source.utmSource")
    private List<TermFacet> utmSources;

    @Aggregate(field = "source.utmMedium")
    private List<TermFacet> utmMediums;

    @Aggregate(field = "source.utmCampaign")
    private List<TermFacet> utmCampaigns;

    @Aggregate(field = "source.utmTerm")
    private List<TermFacet> utmTerms;

    @Aggregate(field = "source.utmContent")
    private List<TermFacet> utmContents;

    @Aggregate(field = "enabled")
    private List<TermFacet> enabledUsers;

    @Aggregate(field = "giftCardStatuses")
    private List<TermFacet> giftCardStatuses;

    @Builder.Default
    private List<TermFacet> topDmas = Arrays.asList(
        TermFacet.builder().id("20").name("Top 20 DMAs").build(),
        TermFacet.builder().id("50").name("Top 50 DMAs").build()
    );

    @Aggregate(field = "address.dmaCode")
    @AggConverter(DMATermFacetConverter.class)
    private List<NumberTermFacet> dmas;

    @Aggregate(field = "signUpVehicle.stockType")
    private List<TermFacet> vehicleStockTypes;

    @Aggregate(field = "signUpVehicle.certified")
    private List<TermFacet> vehicleCertified;

    @Aggregate(field = "signUpVehicle.year")
    private List<NumberTermFacet> vehicleYears;

    @Aggregate(field = "signUpVehicle.make")
    private List<TermFacet> vehicleMakes;

    @Aggregate(field = "signUpVehicle.model")
    private List<TermFacet> vehicleModels;

    @NestedAggregate(path = "dealerLinks", field = "status")
    private List<TermFacet> statusNested;

    @Aggregate(field = "dealerLinks.status.keyword")
    private List<TermFacet> status;

    @NestedAggregate(path = "userLeads", field = "type")
    private List<TermFacet> userLeads;

    @Aggregate(field = "dealerLinks.dealerId")
    @AggConverter(DealerTermFacetConverter.class)
    private List<TermFacet> dealerLinks;

    @Aggregate(field = "tradeMake.keyword")
    private List<TermFacet> tradeMakes;

    @Aggregate(field = "tradeModel.keyword")
    private List<TermFacet> tradeModels;

    @Aggregate(field = "tradeYear")
    private List<TermFacet> tradeYears;

    @Aggregate(field = "clientAdvisor.keyword")
    private List<TermFacet> originalSalespersons;

    @MinAggregate(field = "tradeMileage")
    private Double tradeMileageMin;

    @MaxAggregate(field = "tradeMileage")
    private Double tradeMileageMax;

    @MinAggregate(field = "tradeValue")
    private Double tradeValuesMin;

    @MaxAggregate(field = "tradeValue")
    private Double tradeValuesMax;

    @MinAggregate(field = "tradeTerm")
    private Double tradeTermMin;

    @MaxAggregate(field = "tradeTerm")
    private Double tradeTermMax;

    @Builder.Default
    private List<TermFacet> maturityDate = Arrays.asList(
        TermFacet.builder().id("7").name("This Week").build(),
        TermFacet.builder().id("30").name("Next 30 Days").build(),
        TermFacet.builder().id("60").name("Next 60 Days").build(),
        TermFacet.builder().id("90").name("Next 90 Days").build(),
        TermFacet.builder().id("180").name("Next 180 Days").build(),
        TermFacet.builder().id(NEXT_12_MONTHS.toString()).name("Next 12 Months").build(),
        TermFacet.builder().id(GREATER_THAN_12_MONTHS).name("12+ Months").build()
    );

    @Aggregate(field = "tradePaymentType.keyword")
    private List<TermFacet> tradePaymentTypes;

    @MinAggregate(field = "tradePayment")
    private Double tradePaymentMin;

    @MaxAggregate(field = "tradePayment")
    private Double tradePaymentMax;

    @MinAggregate(field = "tradeEquity")
    private Double tradeEquityMin;

    @MaxAggregate(field = "tradeEquity")
    private Double tradeEquityMax;

    @MinAggregate(field = "remainingPayments")
    private Double remainingPaymentsMin;

    @MaxAggregate(field = "remainingPayments")
    private Double remainingPaymentsMax;

    @Builder.Default
    private List<TermFacet> lastActives = Arrays.asList(
        TermFacet.builder().id("1").name("Last 1 Day").build(),
        TermFacet.builder().id("3").name("Last 3 Days").build(),
        TermFacet.builder().id("7").name("Last 7 Days").build(),
        TermFacet.builder().id("30").name("Last 30 Days").build(),
        TermFacet.builder().id("60").name("Last 60 Days").build(),
        TermFacet.builder().id(LAST_90_DAYS.toString()).name("Last 90 Days").build(),
        TermFacet.builder().id(GREATER_THAN_90_DAYS).name("90+ Days").build()
    );

    @Builder.Default
    private List<TermFacet> createdDate = Arrays.asList(
        TermFacet.builder().id("1").name("Last 1 Day").build(),
        TermFacet.builder().id("3").name("Last 3 Days").build(),
        TermFacet.builder().id("7").name("Last 7 Days").build(),
        TermFacet.builder().id("30").name("Last 30 Days").build(),
        TermFacet.builder().id("60").name("Last 60 Days").build(),
        TermFacet.builder().id(LAST_90_DAYS.toString()).name("Last 90 Days").build(),
        TermFacet.builder().id(GREATER_THAN_90_DAYS).name("90+ Days").build()
    );

    @NestedAggregate(path = "traits", field = "tradeInsCount")
    private List<TermFacet> tradeInsCounts;

    @NestedAggregate(path = "globalInventoryVehicles", field = "savedOrViewed", cardinalityField = "_id")
    private List<TermFacet> globalCertificateSaved;

    @NestedAggregate(path = "dealerInventoryVehicles", field = "savedOrViewed", cardinalityField = "_id")
    private List<TermFacet> dealerCertificateSaved;

    @NestedAggregate(path = "globalInventoryVehicles", field = "make", cardinalityField = "_id")
    private List<TermFacet> globalCertificateMakes;

    @NestedAggregate(path = "dealerInventoryVehicles", field = "make", cardinalityField = "_id")
    private List<TermFacet> dealerCertificateMakes;

    @NestedAggregate(path = "globalInventoryVehicles", field = "stockType", cardinalityField = "_id")
    private List<TermFacet> globalCertificateStockType;

    @NestedAggregate(path = "dealerInventoryVehicles", field = "stockType", cardinalityField = "_id")
    private List<TermFacet> dealerCertificateStockType;

    @NestedAggregate(path = "globalInventoryVehicles", field = "model", cardinalityField = "_id")
    private List<TermFacet> globalCertificateModels;

    @NestedAggregate(path = "dealerInventoryVehicles", field = "model", cardinalityField = "_id")
    private List<TermFacet> dealerCertificateModels;

    @NestedAggregate(path = "globalInventoryVehicles", field = "year", cardinalityField = "_id")
    private List<TermFacet> globalCertificateYear;

    @NestedAggregate(path = "dealerInventoryVehicles", field = "year", cardinalityField = "_id")
    private List<TermFacet> dealerCertificateYear;

    @NestedAggregate(path = "globalInventoryVehicles", field = "certified", cardinalityField = "_id")
    private List<TermFacet> globalCertificateCertified;

    @NestedAggregate(path = "dealerInventoryVehicles", field = "certified", cardinalityField = "_id")
    private List<TermFacet> dealerCertificateCertified;

    @Aggregate(field = "locale")
    private List<TermFacet> language;
}
