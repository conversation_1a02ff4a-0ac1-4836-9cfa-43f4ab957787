package com.carsaver.partner.model.return_policy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReturnPolicyRequest {
    private String dealerId;
    private String programId;
    private Boolean isActive;
    private Boolean isActiveDisclosureLink;
    private String disclosureLink;
    private VehicleType vehicleType;
    private Integer days;
    private String policyName;
    private String policySubTitle;
    private String policyDescription;

}
