package com.carsaver.partner.model.warranty;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EnrollCustomerModelRequest {
    private Integer scheduleId;
    private Integer termMonths;
    private Integer termMiles;
    private Integer currentPlusModelYears;
    private Integer year;
    private String make;
    private String model;
    private String driveType;
    private String fuelType;
    private Integer odometer;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date origMfgInServiceDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date purchaseDate;
    private List<String> dealerIds;

    public Integer getVehicleYear() {
        return year;
    }

    public String getVehicleMake() {
        return make;
    }

    public String getVehicleModel() {
        return model;
    }

    //NOT REQUIRED
    private String engineAspiration;

    public com.carsaver.warranty.model.FindSurchargesRequest toNwanFindChargesRequest() {
        com.carsaver.warranty.model.FindSurchargesRequest nwanFindChargesRequest = new com.carsaver.warranty.model.FindSurchargesRequest();
        BeanUtils.copyProperties(this, nwanFindChargesRequest);

        return nwanFindChargesRequest;
    }
}
