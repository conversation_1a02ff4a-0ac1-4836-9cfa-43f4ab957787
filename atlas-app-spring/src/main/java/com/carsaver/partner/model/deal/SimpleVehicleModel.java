package com.carsaver.partner.model.deal;

import com.carsaver.core.StockType;
import com.carsaver.magellan.model.VehicleView;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.Optional;

@Data
@Builder
public class SimpleVehicleModel {

    private boolean active;
    private String id;
    private StockType stockType;
    private Integer year;
    private String make;
    private String model;
    private String trim;
    private String vin;
    private String stockNumber;
    private Boolean inTransit;
    private LocalDate dealerEstimatedTimeOfArrival;
    private Integer msrp;
    private Integer miles;
    private String exteriorColor;
    private String interiorColor;
    private String imageUrl;

    public static SimpleVehicleModel from(VehicleView vehicle) {
        if(vehicle == null) {
            return null;
        }

        return SimpleVehicleModel.builder()
            .active(vehicle.getActive())
            .id(vehicle.getId())
            .stockNumber(vehicle.getStockNumber())
            .stockType(vehicle.getStockType())
            .year(vehicle.getYear())
            .make(vehicle.getMake())
            .model(vehicle.getModel())
            .trim(vehicle.getTrim())
            .vin(vehicle.getVin())
            .inTransit(vehicle.getInTransit())
            .dealerEstimatedTimeOfArrival(vehicle.getDealerEstimatedTimeOfArrival())
            .msrp(vehicle.getMsrp())
            .exteriorColor(vehicle.getExteriorColor())
            .interiorColor(vehicle.getInteriorColor())
            .imageUrl(Optional.ofNullable(vehicle.getImageUrl()).map(i -> i[0]).orElse(null))
            .miles(vehicle.getMiles())
            .build();
    }

}
