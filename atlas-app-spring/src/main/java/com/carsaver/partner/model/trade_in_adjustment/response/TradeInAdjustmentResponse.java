package com.carsaver.partner.model.trade_in_adjustment.response;

import com.carsaver.partner.model.return_policy.VehicleType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.time.ZonedDateTime;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradeInAdjustmentResponse {
    private List<Pair<String, Integer>> makes;
    private List<String> models;
    private List<Integer> years;

//    private TradeInAdjustmentPrimary tradeInAdjustmentPrimary;

}
