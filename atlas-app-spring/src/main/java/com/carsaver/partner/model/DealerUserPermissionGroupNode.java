package com.carsaver.partner.model;

import com.carsaver.magellan.model.security.DealerPermissionView;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class DealerUserPermissionGroupNode {
    private int id;
    private boolean locked;
    private String description;
    private List<Integer> impliedPermissions;
    private List<DealerUserPermissionGroupNode> children = new ArrayList<>();

    public DealerUserPermissionGroupNode(DealerPermissionView dealerPermissionView, boolean locked) {
        this.id = dealerPermissionView.getId();
        this.description = dealerPermissionView.getDescription();
        this.impliedPermissions = dealerPermissionView.getImpliedPermissions();
        this.locked = locked;
    }

    public DealerUserPermissionGroupNode(int id, String description) {
        this.id = id;
        this.description = description;
    }
}
