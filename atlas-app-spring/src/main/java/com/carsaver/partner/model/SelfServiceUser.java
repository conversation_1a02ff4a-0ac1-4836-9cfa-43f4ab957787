package com.carsaver.partner.model;

import com.carsaver.magellan.model.JobTitleView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.twilio.PhoneNumber;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelfServiceUser {
    private String id;
    private String firstName;
    private String lastName;
    private String type;
    private String phoneNumber;
    private String phoneNumberExt;
    private PhoneNumber phoneNumberInfo;
    private String email;
    private String jobTitle;
    private Boolean smsEnabled;
    private Boolean smsVerified;
    private Boolean internalSms;
    private Integer jobTitleId;

    public static SelfServiceUser from(UserView userView){
        return SelfServiceUser.builder()
            .id(userView.getUserId())
            .email(userView.getEmail())
            .firstName(userView.getFirstName())
            .lastName(userView.getLastName())
            .jobTitleId(userView.getJobTitleId())
            .jobTitle(Optional.ofNullable(userView.getJobTitle()).map(JobTitleView::getTitle).orElse(null))
            .phoneNumber(userView.getPhoneNumber())
            .phoneNumberExt(userView.getPhoneNumberExt())
            .phoneNumberInfo(userView.getPhoneNumberInfo())
            .internalSms(userView.getInternalSms())
            .smsEnabled(userView.isDealerSmsEnabled())
            .smsVerified(userView.getSmsVerified())
            .build();
    }
}
