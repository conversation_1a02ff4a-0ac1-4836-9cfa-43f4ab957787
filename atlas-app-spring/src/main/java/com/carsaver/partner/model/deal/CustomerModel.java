package com.carsaver.partner.model.deal;

import com.carsaver.magellan.model.UserView;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CustomerModel {
    private String firstName;
    private String lastName;
    private String email;
    private String fullAddress;
    private String phoneNumber;

    public String getName() {
        return String.format("%s %s", firstName, lastName).trim();
    }

    public static CustomerModel from(UserView user) {
        if(user == null) {
            return null;
        }

        return CustomerModel.builder()
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .email(user.getEmail())
            .fullAddress(user.getFullAddress())
            .phoneNumber(user.getPhoneNumber())
            .build();
    }
}
