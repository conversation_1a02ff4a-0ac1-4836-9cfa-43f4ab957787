package com.carsaver.partner.model;

import com.carsaver.core.DealerStatus;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DealerProgram {

    private String name;
    private DealerStatus status;
    private String programId;

    private String supportEmail;
    private String supportPhone;

    private UserModel successManager;
    private UserModel accountManager;

    private Boolean isChatFeatureEnabled;
    private Boolean isChatEnabled;
    private DealerChatConfig dealerChatConfig;

    private Boolean isLibertyMutualFeatureEnabled;
    private Boolean isLibertyMutualEnabled;

    private Boolean isRouteOneFAndIFeatureEnabled;
    private Boolean isRouteOneFAndIEnabled;

    private Boolean isSellAtHomeFeatureEnabled;
    private Boolean isSellAtHomeEnabled;

    private Boolean isNissanBuyAtHome;
    private Boolean isBoostProgram;

    private Boolean isNesnaFAndIEnabled;
    private Boolean isNesnaConfigured;

    private Boolean isGarageAlertsFeatureEnabled;
    private Boolean isGarageAlertsEnabled;

    private Boolean isInAppAlertsFeatureEnabled;
    private Boolean isInAppAlertsEnabled;

    private Boolean isSmsAlertsFeatureEnabled;
    private Boolean isSmsAlertsEnabled;

    private Boolean isEmailAlertsFeatureEnabled;
    private Boolean isEmailAlertsEnabled;

    private Boolean isSpanishTranslationFeatureEnabled;
    private Boolean isSpanishTranslationEnabled;

    private Boolean isCarsaverFAndIFeatureEnabled;
    private Boolean isCarsaverFAndIEnabled;

    private Boolean isDealerTrackEnabled;
    private Boolean isBoostFeaturesFeatureEnabled;
    private Boolean isBoostFeaturesEnabled;
}
