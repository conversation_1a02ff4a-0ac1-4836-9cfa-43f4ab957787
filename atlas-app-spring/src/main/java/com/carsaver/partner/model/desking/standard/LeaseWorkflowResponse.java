package com.carsaver.partner.model.desking.standard;


import com.carsaver.magellan.model.QuoteView;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class LeaseWorkflowResponse {
    private List<QuoteView> quotes;
    private LeaseStepBreakDownDTO calculatedValues;
    private TaxView taxView;
}

