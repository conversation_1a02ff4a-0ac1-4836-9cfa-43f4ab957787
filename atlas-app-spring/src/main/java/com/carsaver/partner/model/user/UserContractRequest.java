package com.carsaver.partner.model.user;

import com.carsaver.core.StockType;
import com.carsaver.elasticsearch.model.AddressDoc;
import com.carsaver.elasticsearch.model.UserContractDoc;
import com.carsaver.magellan.model.Source;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static java.util.Optional.ofNullable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserContractRequest {

    private List<ContractRequest> contractRequests;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContractRequest {
        private String id;
        private ZonedDateTime createdDate;
        private String dealerId;
        private String userId;
        private String contractId;
        private String certificateId;
        private String campaignId;
        private String vehicle;
        private String vin;
        private StockType stockType;
        private String vehicleDeliveryMethod;
        private String deliveryLocation;

        public static ContractRequest from(UserContractDoc doc) {
            ContractRequestBuilder builder = ContractRequest.builder()
                .id(doc.getId())
                .dealerId(doc.getDealerId())
                .certificateId(doc.getCertificateId())
                .campaignId(Optional.ofNullable(doc.getSource()).map(Source::getCampaignId).orElse(null))
                .contractId(doc.getId())
                .deliveryLocation(Optional.ofNullable(doc.getDeliveryLocation()).map(AddressDoc::getFullAddress).orElse(null))
                .vehicleDeliveryMethod(doc.getVehicleDeliveryMethod())
                .createdDate(doc.getCreatedDate());

            ofNullable(doc.getVehicle()).ifPresent(vehicle -> {
                builder.vehicle(
                    vehicle.getYear()+" "
                        +vehicle.getMake()+" "
                        +vehicle.getModel());

                builder.stockType(vehicle.getStockType());
                builder.vin(vehicle.getVin());
            });


            return builder.build();
        }
    }
}
