package com.carsaver.partner.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeliveryFee {
    private String id;

    @NotBlank(message = "Min range is required")
    private Double min;

    @NotBlank(message = "Max range is required")
    private Double max;

    @Max(value = 9999, message = "Max amount is $9,999")
    @NotNull(message = "Fee amount is required")
    private Double amount;
}
