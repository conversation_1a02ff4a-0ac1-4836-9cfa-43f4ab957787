package com.carsaver.partner.model.protection_products.deal_jacket_response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrimaryBuyer {
	private String zipCode;
	private String lastName;
	private String residenceTypeCode;
	private Integer otherIncomeAmount;
	private String employmentTitle;
	private String city;
	private String displayName;
	private String customerTypeCode;
	private String county;
	private String employerName;
	private Integer previousMonthsOnJob;
	private String suffix;
	private String previousEmployerName;
	private String otherIncomeSourceCode;
	private String ssn;
	private Integer monthsAtAddress;
	private Integer monthsOnJob;
	private String educationLevelCode;
	private String countryCode;
	private String addressLine1;
	private String addressLine2;
	private String id;
	private String state;
	private String employerPhone;
	private String email;
	private String employmentTypeCode;
	private String otherIncomeIntervalCode;
	private Integer rentMortgagePaymentAmount;
	private String phoneType;
	private String otherIncomeSourceDescription;
	private String dealerId;
	private Integer incomeAmount;
	private String dateOfBirth;
	private String incomeIntervalCode;
	private String altPhoneType;
	private String firstName;
	private String employmentStatusCode;
	private String phone;
	private String altPhone;
	private String middleName;
}
