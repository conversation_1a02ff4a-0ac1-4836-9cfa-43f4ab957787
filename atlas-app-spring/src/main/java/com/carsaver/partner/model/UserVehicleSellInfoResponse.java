package com.carsaver.partner.model;

import com.carsaver.core.PaymentType;
import com.carsaver.magellan.model.vehicle.VehicleInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class UserVehicleSellInfoResponse {

    private String programId;
    private List<UserVehicleSellInfo> content;
    private Pageable pageable;

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class UserVehicleSellInfo {

        private String id;
        private String firstName;
        private String lastName;
        private String userId;
        private String color;
        private Integer miles;
        private String vin;
        private String provider;
        private Boolean deleted;
        private PaymentType paymentType;
        private Double payment;
        private String vehicleCondition;
        private String dealerId;
        private String userVehicleId;
        private String financierName;
        private Boolean expired;
        private String status;
        private VehicleInfo vehicleInfo;
        private String expirationDate;
        private ZonedDateTime createdDate;
        protected ZonedDateTime lastModifiedDate;

        public String getStatus() {
            String value = "approved";
            if (status == null) {
                if (expired) {
                    value = "expired";
                } else {
                    value = "pending";
                }
            }
            return value;
        }

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Pageable {
        private Integer pageSize;
        private Integer pageNumber;
        private Long totalElements;

    }

}
