package com.carsaver.partner.model.warranty;

import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.web.api.warranty.WarrantyControllerHelper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerModel {
    private String id;
    private String firstName;
    private String lastName;
    private String middleName;
    private String email;
    private String streetAddress;
    private String city;
    private String state;
    private String zipCode;
    private String fullAddress;
    private String phoneNumber;
    private boolean walmartEmployee;


    public static CustomerModel from(UserView user) {
        if(user == null) {
            return null;
        }

        return CustomerModel.builder()
            .id(user.getId())
            .firstName(user.getFirstName())
            .lastName(user.getLastName())
            .email(user.getEmail())
            .streetAddress(user.getAddress())
            .city(user.getCity())
            .state(user.getStateCode())
            .zipCode(user.getZipCode())
            .fullAddress(user.getFullAddress())
            .phoneNumber(user.getPhoneNumber())
            .walmartEmployee(user.tagExists(WarrantyControllerHelper.USER_TAGS_WALMART_ASSOCIATE))
            .build();
    }
}
