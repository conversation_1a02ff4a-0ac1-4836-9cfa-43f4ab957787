package com.carsaver.partner.model.source;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Source {
    private String hostname;
    private String referrer;
    private String ambassadorPin;
    private String agentUserId;
    private String prospectId;
    private String whispProspectId;
    private String campaignId;
    private String channel;
    private String dealerId;
    private String vehicleId;
    private String utmSource;
    private String utmMedium;
    private String utmCampaign;
    private String utmTerm;
    private String utmContent;
    private String campaignGroup;
    private String memberType;

    public String getTier() {
        return this.utmSource;
    }
}
