package com.carsaver.partner.model.protection_products.deal_jacket_response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaxFeesItem {
	private Double amount;
	private String name;
	private String id;
	private Boolean paidUpfrontIndicator;
	private String dealJacketId;
	private String typeCode;
	private String paidTo;
	private String paidFor;
}
