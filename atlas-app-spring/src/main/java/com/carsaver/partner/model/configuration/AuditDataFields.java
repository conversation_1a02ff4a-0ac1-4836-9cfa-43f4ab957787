package com.carsaver.partner.model.configuration;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditDataFields {
    private String domainName;
    private String time;
    private String userId;

    // Initialize fields map to ensure it is ready to store dynamic fields
    private Map<String, Object> fields = new HashMap<>();

    // Use @JsonAnySetter to capture any additional fields into the fields map
    @JsonAnySetter
    public void addDynamicField(String key, Object value) {
        fields.put(key, value);
    }
}
