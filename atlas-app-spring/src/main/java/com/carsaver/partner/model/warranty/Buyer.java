package com.carsaver.partner.model.warranty;

import com.carsaver.magellan.model.UserView;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Buyer {

    @NotBlank(message="Buyer last name is required.")
    private String lastName;
    private String firstName = "";
    private String middleName = "";

    @NotBlank(message="Buyer street address is required.")
    private String streetAddress;

    @NotBlank(message="Buyer city is required.")
    private String city;

    @NotBlank(message="Buyer state is required.")
    @Size(min = 2, max = 2)
    private String state;

    @NotBlank(message="Buyer zip code is required.")
    private String zip;

    @NotBlank(message="Buyer email address is required.")
    private String emailAddress;

    @NotBlank(message="Buyer primary phone is required.")
    @Pattern(regexp = "(^[0-9]{10})", message = "Buyer primary phone must be 10 digits.")
    private String primaryPhone = "";

    @Pattern(regexp = "(^[0-9]{10}|^$)", message = "Buyer alternate phone must be 10 digits.")
    private String alternatePhone = "";

    public static Buyer from(UserView user) {
        Buyer buyer = new Buyer();
        BeanUtils.copyProperties(user, buyer);

        buyer.setStreetAddress(user.getAddress());
        buyer.setEmailAddress(user.getEmail());
        buyer.setPrimaryPhone(user.getPhoneNumber());
        buyer.setZip(user.getZipCode());
        buyer.setState(user.getStateCode());

        return buyer;
    }

    public com.carsaver.warranty.model.Buyer toNwanBuyer() {
        com.carsaver.warranty.model.Buyer buyer = new com.carsaver.warranty.model.Buyer();
        BeanUtils.copyProperties(this, buyer);

        return buyer;
    }
}
