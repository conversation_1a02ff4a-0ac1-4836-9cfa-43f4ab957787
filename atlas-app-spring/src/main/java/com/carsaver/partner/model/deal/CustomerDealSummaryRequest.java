package com.carsaver.partner.model.deal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDealSummaryRequest {
    @NotEmpty(message = "dealerIds cannot be null or empty")
    private List<String> dealerIds;
}
