package com.carsaver.partner.model.user;

import com.carsaver.elasticsearch.model.UserStageDoc;
import com.carsaver.elasticsearch.model.program.ProgramDoc;
import com.carsaver.magellan.model.UserView;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExtendedUserView extends UserView {
    private String clientAdvisor;
    private String stageName;
    private List<UserStageDoc> programSubscriptionStages;
    private ProgramDoc program;
}
