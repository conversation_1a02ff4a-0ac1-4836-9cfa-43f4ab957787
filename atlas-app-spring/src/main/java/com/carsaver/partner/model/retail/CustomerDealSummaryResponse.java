package com.carsaver.partner.model.retail;

import com.carsaver.magellan.model.user.CreditRange;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerDealSummaryResponse {
    private CreditRange range;
    private String creditRatingSource;
    private Double currentPayment;
    private Double maxPayment;
    private TradeDetails trade;
    private Double tradeEquity;
    private String paymentPreference;
    private String purchaseType;
    private Double fixedDownPayment;
    private Double minDownPayment;
    private Double maxDownPayment;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TradeDetails {
        private String make;
        private String model;
        private Integer year;
    }
}

