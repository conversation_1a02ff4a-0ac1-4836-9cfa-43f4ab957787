package com.carsaver.partner.model.desking;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ClonedDealResponse {
    private Long id;
    private String dealerId;
    private String userId;
    private Integer certificateId;
    private Long originalCertificateId;
    private String dealName;
    private String dealType;
    private String editType;
    private VehicleDetail vehicleDetail;
    private FinanceDetails financeDetails;
    private DueAtSigning dueAtSigning;
    private Double dueAtSigningTotal;
    private Rebates rebates;
    private CustomerRebates customerRebates;
    private Double rebatesTotal;
    private TradeAllowance tradeAllowance;
    private Double tradeAllowanceTotal;
    private GrossCapitalCost grossCapitalCost;
    private Double grossCapitalCostTotal;
    private Inceptions inceptions;
    private Double inceptionsTotal;
    private CapCostReduction capCostReduction;
    private Double capCostReductionTotal;
    private MonthlyPayment monthlyPayment;
    private Double monthlyPaymentTotal;
    private String clonedBy;
    private AddOns addons;
    private TaxesAndFees taxesAndFees;
    private Double addonsTotal;
    private Double taxesAndFeesTotal;
    private ZonedDateTime createdDate;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TaxesAndFees {
        private Double salesTax;
        private Double titleAndRegistration;
        private List<LineItem> lineItems;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AddOns {
        private List<LineItem> lineItems;
        private List<LineItem> accessories;
        private List<LineItem> protectionProduct;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class TradeAllowance {
        private String model;
        private String trim;
        private String appraisalType;
        private String allowance;
        private Double payoff;
        private Double netTrade;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class VehicleDetail {
        private String inventoryId;
        private String vehicleType;
        private String stockNumber;
        private String vin;
        private Double msrp;
        private Double invoice;
        private Double sellingPrice;
        private Integer year;
        private String make;
        private String model;
        private String exteriorColor;
        private String interiorColor;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class CapCostReduction {
        private List<LineItem> lineItems;
        private Double capCostReduction;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class DueAtSigning {
        private Double positiveTradeEquity;
        private String consumerCash;
        private List<LineItem> lineItems;
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class FinanceDetails {
        private String financeCompany;
        private Integer term;
        private Integer milesPerYear;
        private String tier;
        private Double baseMoneyFactor;
        private Double bps;
        private String moneyFactoryWithBps;
        private String residualPercentage;
        private Double residualAmount;
        private Double acquisitionFee;
        private String leaseTerminationFee;
        private Double apr;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class GrossCapitalCost {
        private Double sellingPrice;
        private String tradeBalance;
        private String acquisitionFee;
        private Double dealerFee;
        private List<LineItem> lineItems;
        private List<CloneDealRequest.LineItem> accessories;
        private List<CloneDealRequest.LineItem> protectionProduct;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Inceptions {
        private Double firstMonthlyPayment;
        private Double titleAndRegistration;
        private List<LineItem> lineItems;
        private List<LineItem> staticLineItems;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class LineItem {
        private String name;
        private Double amount;
        private Boolean taxable;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class MonthlyPayment {
        private Double firstMonthlyPayment;
        private Double monthlyTax;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class Rebates {
        private List<LineItem> lineItems;
        private List<LineItem> staticLineItems;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    public static class CustomerRebates {
        private List<LineItem> lineItems;
    }

}
