package com.carsaver.partner.model.desking;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class VehicleUpdateModel {
    private String vehicleType;
    private String stockNumber;
    private String vin;
    private Double msrp;
    private Double invoice;
    private Double sellingPrice;
    private String trim;
    private Integer year;
    private String make;
    private String model;
    private String exteriorColor;
    private String interiorColor;
    private String imageUrl;
    private String inventoryId;
    private List<String> availableDealTypes;
}
