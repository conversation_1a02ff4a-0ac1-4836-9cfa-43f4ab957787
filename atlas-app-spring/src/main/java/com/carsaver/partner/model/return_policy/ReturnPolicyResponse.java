package com.carsaver.partner.model.return_policy;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReturnPolicyResponse {
    // TODO: due to @Builder, did not see an easy way to extend Request to prevent DRY.
    private String id;
    private String dealerId;
    private String programId;
    private Boolean isActive;
    private Boolean isActiveDisclosureLink;
    private String disclosureLink;
    private VehicleType vehicleType;
    private Integer days;
    private String policyName;
    private String policySubTitle;
    private String policyDescription;
    private ZonedDateTime createdAt;
    private ZonedDateTime updatedAt;

}
