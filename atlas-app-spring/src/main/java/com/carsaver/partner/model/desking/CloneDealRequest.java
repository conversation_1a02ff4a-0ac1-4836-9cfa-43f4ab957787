package com.carsaver.partner.model.desking;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CloneDealRequest {

    private Long id;
    private String dealerId;
    private String userId;
    private Integer certificateId;
    private String dealName;
    private String editType;
    private Long originalCertificateId;
    private VehicleDetail vehicleDetail;
    private FinanceDetails financeDetails;
    private DueAtSigning dueAtSigning;
    private Double dueAtSigningTotal;
    private Rebates rebates;
    private Double rebatesTotal;
    private TradeAllowance tradeAllowance;
    private Double tradeAllowanceTotal;
    private GrossCapitalCost grossCapitalCost;
    private Double grossCapitalCostTotal;
    private Inceptions inceptions;
    private Double inceptionsTotal;
    private CapCostReduction capCostReduction;
    private Double capCostReductionTotal;
    private MonthlyPayment monthlyPayment;
    private Double monthlyPaymentTotal;
    private String dealType;
    private AddOns addons;
    private TaxesAndFees taxesAndFees;
    private Double addonsTotal;
    private Double taxesAndFeesTotal;
    private ZonedDateTime createdDate;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class TaxesAndFees {
        private Double salesTax;
        private Double titleAndRegistration;
        private List<LineItem> lineItems;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AddOns {
        private List<LineItem> lineItems;
        private List<LineItem> accessories;
        private List<LineItem> protectionProduct;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CapCostReduction {
        private List<LineItem> lineItems;
        private Double capCostReduction;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DueAtSigning {
        private Double positiveTradeEquity;
        private String consumerCash;
        private List<LineItem> lineItems;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FinanceDetails {
        private String financeCompany;
        private int term;
        private int milesPerYear;
        private String tier;
        private Double baseMoneyFactor;
        private Double bps;
        private String moneyFactoryWithBps;
        private String residualPercentage;
        private Double residualAmount;
        private Double acquisitionFee;
        private String leaseTerminationFee;
        private Double apr;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GrossCapitalCost {
        private Double sellingPrice;
        private String tradeBalance;
        private String acquisitionFee;
        private Double dealerFee;
        private List<LineItem> lineItems;
        private List<LineItem> accessories;
        private List<LineItem> protectionProduct;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Inceptions {
        private Double firstMonthlyPayment;
        private Double titleAndRegistration;
        private List<LineItem> lineItems;
        private List<LineItem> staticLineItems;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LineItem {
        private String name;
        private Double amount;
        private Boolean taxable;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MonthlyPayment {
        private Double firstMonthlyPayment;
        private Double monthlyTax;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Rebates {
        private List<LineItem> lineItems;
        private List<LineItem> staticLineItems;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TradeAllowance {
        private String model;
        private String trim;
        private String appraisalType;
        private String allowance;
        private Double payoff;
        private Double netTrade;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VehicleDetail {
        private String inventoryId;
        private String vehicleType;
        private String stockNumber;
        private String vin;
        private Double msrp;
        private Double invoice;
        private Double sellingPrice;
        private String interiorColor;
        private String exteriorColor;
        private Integer year;
        private String make;
        private String model;
    }

}
