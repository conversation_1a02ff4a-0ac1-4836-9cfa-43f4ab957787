package com.carsaver.partner.model.pricing;

import com.carsaver.core.StockType;
import com.carsaver.magellan.model.PriceAdjustmentView;
import com.carsaver.magellan.model.StockTypePriceAdjustmentView;
import com.carsaver.magellan.search.PriceAdjustmentSearchCriteria;
import lombok.Data;
import org.springframework.data.domain.Page;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

@Data
public class UsedVehiclePricingForm {
    private PriceAdjustmentSearchCriteria searchForm = new PriceAdjustmentSearchCriteria();

    @Valid
    private StockTypePriceAdjustmentView stockTypePriceAdjustment = new StockTypePriceAdjustmentView();

    @Valid
    private List<PricedCarRow> pricedCarRows = Collections.emptyList();

    private Page<PricedCarRow> pricedCarRowsPage = Page.empty();
    private boolean enforcePricingRules;

    private String vehicleSearchFailureMessage;
    private boolean vehicleSearchFoundVehicle;
    private boolean vehicleSearchAttempted;
    private List<String> dealerIds;

    public void addStockTypePriceAdjustmentWithDefaults(StockTypePriceAdjustmentView stockTypePriceAdjustment, String dealerId) {
        if(stockTypePriceAdjustment != null) {
            this.stockTypePriceAdjustment = stockTypePriceAdjustment;
        } else {
            StockTypePriceAdjustmentView stockTypePriceAdjustmentWithDefaults = new StockTypePriceAdjustmentView();

            stockTypePriceAdjustmentWithDefaults.setDealerId(dealerId);
            stockTypePriceAdjustmentWithDefaults.setStockType(StockType.USED);
            PriceAdjustmentView adjustment = new PriceAdjustmentView();
            adjustment.setAmount(0d);
            adjustment.setType(PriceAdjustmentView.Type.DOLLAR);

            stockTypePriceAdjustmentWithDefaults.setAdjustment(adjustment);

            this.stockTypePriceAdjustment = stockTypePriceAdjustmentWithDefaults;
        }
    }
}
