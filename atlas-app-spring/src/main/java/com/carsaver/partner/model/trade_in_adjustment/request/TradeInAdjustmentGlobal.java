package com.carsaver.partner.model.trade_in_adjustment.request;

import com.carsaver.partner.model.trade_in_adjustment.TradeInAdjustmentType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TradeInAdjustmentGlobal {
//    private String id;
//    private String primaryId;
    private Boolean isDeleted;
    private TradeInAdjustmentType adjustmentType;
    private Double adjustmentAmount;

}
