package com.carsaver.partner.model.user;

import com.carsaver.core.StockType;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.finance.ApplicationCallbackView;
import com.carsaver.partner.model.finance.FinanceDecision;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.StringUtils;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@Builder(toBuilder = true)
public class UserLoanRequestDTO {
    private String id;
    private Vehicle vehicle;
    private AppStatusResponse appStatusResponse;
    private List<FinanceDecision> financeDecisions;
    private String overallStatus;
    private String vin;
    private Integer appId;
    private Double requestedAmountFinanced;
    private ZonedDateTime createdDate;
    private String type;
    private Double leaseMarkup;
    private Double financeMarkup;
    private CertificateView certificate;

    public String getVehicleDescription() {
        return vehicle != null ? vehicle.getDescription() : null;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Vehicle {
        private String id;
        private StockType stockType;
        private Integer year;
        private String make;
        private String model;
        private String trim;
        private String stockNumber;

        public String getDescription() {
            if(ObjectUtils.allNotNull(year, make, model)) {
                return year + " " + make + " " + model + (StringUtils.hasLength(trim) ? " " + trim : "");
            }
            return null;
        }

        public static Vehicle from(CertificateView certificate) {

            if (certificate == null || certificate.getVehicle() == null) {
                return null;
            }
            VehicleView vehicle = certificate.getVehicle();

            return Vehicle.builder()
                .id(vehicle.getId())
                .stockType(vehicle.getStockType())
                .year(vehicle.getYear())
                .make(vehicle.getMake())
                .model(vehicle.getModel())
                .trim(vehicle.getTrim())
                .stockNumber(vehicle.getStockNumber())
                .build();
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AppStatusResponse {
        private String appStatus;

        public static AppStatusResponse from(ApplicationCallbackView applicationCallBack) {

            if(applicationCallBack == null) {
                return null;
            }

            return AppStatusResponse.builder()
                .appStatus(applicationCallBack.getAppStatus())
                .build();
        }

    }

}
