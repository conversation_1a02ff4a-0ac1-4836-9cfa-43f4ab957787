package com.carsaver.partner.model.trade_in_adjustment.request;

import com.carsaver.partner.model.trade_in_adjustment.request.TradeInAdjustmentGlobal;
import com.carsaver.partner.model.trade_in_adjustment.request.TradeInAdjustmentModelSpecific;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TradeInAdjustmentPrimary {
    private String dealerId;
    private String programId;
    private String dataType;
    private Boolean isDeleted;
    private Boolean isGlobalPricingEnabled;
    private TradeInAdjustmentGlobal globalAdjustment;
    private TradeInAdjustmentModelSpecific modelSpecificAdjustment;

}
