package com.carsaver.partner.model.paas;

import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.protection_products.deal_jacket_response.DealJacketResponse;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class InputValues {
    private String transaction;
    private RequestType requestType;
    private BigDecimal downPayment;
    private BigDecimal sellingPrice;
    private BigDecimal msrp;
    private BigDecimal inceptionFeeNonTaxable;
    private BigDecimal inceptionFeeTaxable;
    private BigDecimal capCostFeeNonTaxable;
    private BigDecimal capCostFeeTaxable;
    private BigDecimal dealerFeeNoneTaxable;
    private BigDecimal dealerFeeTaxable;
    private BigDecimal addOnsTaxable;
    private BigDecimal addOnsNonTaxable;
    private BigDecimal rebates;
    private Integer term;
    private Integer mileage;
    private Integer creditScore;
    private DealJacketResponse editedProtectionProducts;
    private CloneDealRequest cloneDealRequest;

    public enum RequestType {
        ALL,
        LOWEST
    }
}
