package com.carsaver.partner.model.protection_products.deal_jacket_response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Finance {
	private Double vehicleCashPrice;
	private Double annualPercentageRate;
	private Double nonTaxableAccessories;
	private Double incentiveAmount;
	private Double taxableAccessories;
	private String priorCreditOrLeaseBalancePaidTo;
	private String transactionType;
	private String deferredDownPaymentDueDate1;
	private String deferredDownPaymentDueDate2;
	private Double amountFinanced;
	private String deferredDownPaymentDueDate3;
	private Double totalSalePrice;
	private Double monthlyPayment;
	private Double cashDownPayment;
	private String priorCreditOrLeasePaidTo;
	private Double financeCharge;
	private Integer term;
	private Double deferredDownPayment1;
	private Double totalOfPayments;
	private String dealJacketId;
	private String vehicleDeliveryDate;
	private Double totalDueAtSigning;
}
