package com.carsaver.partner.model.document_upload;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDocuments {
    private List<Documents> documents;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Documents {
        private String fileId;
        private String dealerId;
        private String userId;
        private String entityType;
        private LocalDateTime createdDate;
        private Integer fileSize;
        private String name;
        private String description;
        private LocalDateTime expirationDate;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DealerUser {
        private String dealerId;
        private String userId;
        private LocalDateTime createdDate;
        private String dealerName;
    }

}


