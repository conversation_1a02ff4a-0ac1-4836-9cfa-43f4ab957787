package com.carsaver.partner.model.user;

import com.carsaver.core.StockType;
import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.elasticsearch.model.LeadDoc;
import com.carsaver.elasticsearch.model.sale.VehicleBasicDoc;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.QuoteView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.certificate.DealPreferences;
import com.carsaver.magellan.model.certificate.DealType;
import com.carsaver.magellan.model.color.ExteriorColor;
import com.carsaver.magellan.model.pricing.PricesView;
import com.carsaver.partner.util.VehicleMapperUtility;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserLead {

    private List<Lead> leads;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Lead {
        private String id;
        private LocalDateTime visitTime;
        private String type;
        private String leadType;
        private StockType stockType;
        private String stockTypeName;
        private String vehicle;
        private ZonedDateTime createdDate;
        private Dealer dealer;
        private Certificate certificate;
        private Boolean inTransit;
        private String timeZone;
        private String adfPayload;
        private String vin;
        private Boolean vehicleActive;



        public static Lead from(LeadDoc leadDoc, CertificateView certificateView, DealJacket dealJacket, String adf) {
            return Lead.builder()
                .id(leadDoc.getId())
                .visitTime(leadDoc.getVisitTime())
                .type(leadDoc.getType())
                .leadType(leadDoc.getLeadType())
                .stockType(ofNullable(leadDoc.getVehicle()).map(VehicleBasicDoc::getStockType).orElse(null))
                .stockTypeName(VehicleMapperUtility.getStockTypeName(leadDoc.getVehicle()))
                .vehicle(ofNullable(leadDoc.getVehicle()).map(VehicleBasicDoc::getFullName).orElse(null))
                .createdDate(leadDoc.getCreatedDate())
                .dealer(Dealer.from(leadDoc.getDealer()))
                .certificate(Certificate.from(certificateView, dealJacket))
                .inTransit(ofNullable(dealJacket).map(DealJacket::getVehicle).map(VehicleView::getInTransit).orElse(Boolean.FALSE))
                .vehicleActive(ofNullable(dealJacket).map(DealJacket::getVehicle).map(VehicleView::getActive).orElse(Boolean.FALSE))
                .timeZone(leadDoc.getDealer().getTimeZone())
                .adfPayload(adf)
                .vin(Optional.ofNullable(leadDoc.getVehicle()).map(VehicleBasicDoc::getVin).orElse(null))
                .build();
        }

        public static Lead from(LeadDoc leadDoc, String adf) {
            Certificate certificate = Certificate.builder()
                .exteriorColor("")
                .msrp(0)
                .dealType(null)
                .payment(0D)
                .paymentType("")
                .build();

            return Lead.builder()
                .id(leadDoc.getId())
                .visitTime(leadDoc.getVisitTime())
                .type(leadDoc.getType())
                .leadType(leadDoc.getLeadType())
                .createdDate(leadDoc.getCreatedDate())
                .dealer(Dealer.from(leadDoc.getDealer()))
                .timeZone(leadDoc.getDealer().getTimeZone())
                .vehicle(Optional.ofNullable(leadDoc.getVehicle()).map(VehicleBasicDoc::getFullName).orElse(null))
                .certificate(certificate)
                .adfPayload(adf)
                .vin(Optional.ofNullable(leadDoc.getVehicle()).map(VehicleBasicDoc::getVin).orElse(null))
                .build();
        }



    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Dealer {
        private String id;
        private String name;

        public static Dealer from(DealerDoc dealerDoc) {
            if (dealerDoc == null) {
                return null;
            }

            return Dealer.builder()
                .id(dealerDoc.getId())
                .name(dealerDoc.getName())
                .build();
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Certificate {
        private Integer id;
        private String exteriorColor;
        private String exteriorColorHex;
        private Double payment;
        private String paymentType;
        private Integer salePrice;
        private Integer msrp;
        private String trim;
        private DealType dealType;
        private String inventoryId;
        private Boolean deleted;

        public static Certificate from(CertificateView certificateView, DealJacket dealJacket) {
            if (certificateView == null) {
                return null;
            }

            Optional<Integer> purchasePrice = ofNullable(dealJacket).map(DealJacket::getPurchasePrice);
            Optional<Integer> carsaverPrice = Optional.of(certificateView).map(CertificateView::getVehiclePrices).map(PricesView::getCarsaverPrice);
            Optional<Integer> internetPrice = Optional.of(certificateView).map(CertificateView::getVehicle).map(VehicleView::getInternetPrice);

            Integer salePrice;
            Integer msrp;
            boolean isDigitalRetail = Optional.ofNullable(certificateView.getSummaryDetails()).isPresent();
            if (isDigitalRetail) {
                var prices = certificateView.getPrices().getPrices();
                salePrice = Optional.ofNullable(prices).map(PricesView::getPrice).orElse(carsaverPrice.orElse(null));
                msrp = Optional.ofNullable(prices).map(PricesView::getMsrp).orElse(null);
            } else {
                salePrice = Stream.of(purchasePrice, carsaverPrice, internetPrice)
                    .filter(Optional::isPresent)
                    .findFirst()
                    .map(Optional::get)
                    .orElse(null);
                msrp = Optional.ofNullable(dealJacket).map(DealJacket::getMsrp).orElse(null);
            }


            VehicleView vehicle = certificateView.getVehicle();
            Optional<VehicleView> optVehicle = Optional.ofNullable(vehicle);

            String trim = optVehicle.map(VehicleView::getTrim).orElse(null);

            var dealType = ofNullable(certificateView.getDealPreferences())
                .map(DealPreferences::getDealType)
                .orElse(null);

            return Certificate.builder()
                .id(certificateView.getId())
                .exteriorColor(optVehicle.map(VehicleView::getExteriorColor).orElseGet(() -> Optional.of(certificateView).map(CertificateView::getExteriorColor).map(ExteriorColor::getName).orElse(null)))
                .exteriorColorHex(optVehicle.map(VehicleView::getExteriorColorHexCode).orElseGet(() -> Optional.of(certificateView).map(CertificateView::getExteriorColor).map(ExteriorColor::getHex).orElse(null)))
                .dealType(dealType)
                .paymentType(ofNullable(certificateView.getQuote()).map(QuoteView::financeOrLease).orElse(null))
                .payment(ofNullable(dealJacket).map(DealJacket::getMonthlyPayments).map(DealJacket.MonthlyPayments::getWithFeesAndTaxes).orElse(null))
                .salePrice(salePrice)
                .msrp(msrp)
                .trim(trim)
                .inventoryId(certificateView.getInventoryId())
                .deleted(certificateView.getDeleted())
                .build();
        }
    }

}
