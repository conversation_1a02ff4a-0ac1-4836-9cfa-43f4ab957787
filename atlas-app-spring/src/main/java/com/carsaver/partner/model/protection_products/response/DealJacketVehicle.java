package com.carsaver.partner.model.protection_products.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DealJacketVehicle {
    private String id;
    private String dealJacketId;
    private Boolean tradeInVehicleIndicator;
    private Boolean licensePlateVehicleIndicator;
    private Boolean garageIndicator;
    private Boolean vehicleSoldAsIsIndicator;
    private Boolean primaryIndicator;
    private List<AddOnEquipment> addOnEquipments;
    private List<DealerProduct> dealerProducts;
    private Vehicle vehicle;
}
