package com.carsaver.partner.model.nensa;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DealerNesnaProducts {
    private String id;
    private String dealerId;
    private String masterNesnaProductId;
    private Boolean enabled;
    private String name;
    private String adjustedValue;
    private String adjustmentType;
    private Integer markupAmount;
    private List<PriceLevel> levels;
    private String productTypeCode;
    private String productTypeDescription;
    private String coverageGroupCode;
    private String coverageGroupDescription;
    private String description;
    private String icon;
    private String brochureThumbnails;
    private String videoLink;
    private String stateSpecificDetails;
}
