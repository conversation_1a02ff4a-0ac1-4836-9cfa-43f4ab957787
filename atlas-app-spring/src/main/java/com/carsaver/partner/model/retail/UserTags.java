package com.carsaver.partner.model.retail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserTags {
    private String userId;
    private Integer tagId;
    private String name;
    private String category;
    private String dealerId;
    private String status;
    private ZonedDateTime createdDate;
    private String createdBy;

}

