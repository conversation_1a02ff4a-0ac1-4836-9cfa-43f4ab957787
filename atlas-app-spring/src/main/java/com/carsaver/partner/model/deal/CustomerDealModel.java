package com.carsaver.partner.model.deal;

import com.carsaver.core.PaymentType;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.QuoteView;
import com.carsaver.magellan.model.certificate.DealPreferences;
import com.carsaver.magellan.model.certificate.DealType;
import com.carsaver.magellan.model.deal.DealCreditStatus;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.deal.Summary;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.magellan.model.financiers.CreditTier;
import com.carsaver.magellan.model.financiers.FinancierConfig;
import com.carsaver.magellan.model.pricing.PricesView;
import com.carsaver.magellan.model.user.CreditEvaluator;
import com.carsaver.magellan.model.user.CreditProfile;
import com.carsaver.magellan.model.user.CreditRange;
import com.carsaver.magellan.model.user.SoftPullFailureReason;
import com.carsaver.magellan.model.user.UserVehicleDiscrepancy;
import com.carsaver.partner.service.DealerFeesService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static java.util.Optional.ofNullable;

@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDealModel {
    private String source;
    private UserInfoModel assignee;
    private CustomerModel customer;
    private SimpleVehicleModel vehicle;
    private DealModel deal;
    private Trade trade;
    private String inventoryId;
    private OrderStatus orderStatus;
    private RemoteDelivery remoteDelivery;
    private Dealer dealer;


    public boolean isTradeExists() {
        return trade != null;
    }

    @Data
    @Builder
    public static class RemoteDelivery {
        private String address;
        private Double amount;
        private String street1;
        private String street2;
        private String city;
        private String state;
        private String zipcode;
    }


    @Builder
    @Data
    public static class Trade {
        private String userVehicleId;
        private UserVehicleDiscrepancy discrepancy;
        private String vehicleQuoteId;
        private String yearMakeModel;
        private Integer mileage;
        private String vin;
        private PaymentType purchaseType;
        private Boolean leaseInspectionScheduled;
        private String financeCompany;
        private BigDecimal loanAmount;
        private BigDecimal valueOfVehicle;
        private BigDecimal netCashOffer;
        private BigDecimal monthlyPayments;
        private BigDecimal dispositionFee;
        private Integer remainingPayments;
        private String make;
        private String model;
        private String trim;
        private Integer year;
        private String externalColor;
        private String type;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DealModel {
        private Long orderId;
        private Integer certificateId;
        private String financierName;
        private String dealName;
        private DealCreditStatus dealCreditStatus;
        private String status;
        private String financeType;
        private String lenderTier;
        private CreditRange selfSelectedCreditTier;
        private CreditEvaluator creditEvaluator;
        private SoftPullFailureReason softPullFailureReason;
        private ZonedDateTime preQualificationDate;
        private ZonedDateTime applicationDate;
        private ZonedDateTime createdAt;
        private ZonedDateTime preQualifiedExpirationDate;
        private String applicationNumber;
        private String applicationType;
        private Integer msrp;
        private Integer rebates;
        private Integer discount;
        private Integer salePrice;
        private Double registration;
        private Double dealerFee;
        private Collection<DealerFeeView> dealerFees;
        private Double netCashOffer;
        private Double outOfPocket;
        private Double totalAmountFinance;
        private String termLength;
        private Double interestRate;
        private Double totalPayments;
        private Double totalDueAtSigning;
        private Double securityDeposit;
        private Double dispositionFee;
        private Double acquisitionFee;
        private Double endOfTermPurchaseOption;
        private String annualMileageAllowance;
        private Double mileagePenalty;
        private Double tax;
        private Double monthlyPayment;
        private Double monthlyPaymentWithFeesAndTaxes;
        private Double monthlyTaxes;
        private Double cashBack;
        private String dealType;
        private ExternalOffersModel externalOffers;
        private Double downPayment;
        private Integer purchasePrice;
        private List<DealSheet.RebateLineItem> dealerRebates;
        private Boolean modifiedByDealer;
        private String dealURL;
        private PreApprovalModel preApproval;
        private BigDecimal reservationDeposit;
        private Summary summaryDetail;

        private BigDecimal moneyFactor;
        private BigDecimal residualPct;
        private BigDecimal taxOnInception;
        private BigDecimal taxOnCapCost;
        private BigDecimal inceptionsTotal;
        private BigDecimal inceptionDealerFeeTotal;
        private BigDecimal capCostDealerFeeTotal;
        private BigDecimal grossCapCost;
        private Collection<DealerFeeView> inceptionDealerFees;
        private Collection<DealerFeeView> capCostDealerFees;

        public Double getTotal() {
            int price = Optional.ofNullable(getSalePrice()).orElse(0);
            int reb = Optional.ofNullable(getRebates()).orElse(0);
            double reg = Optional.ofNullable(getRegistration()).orElse(0.0);
            double fee = Optional.ofNullable(getDealerFee()).orElse(0.0);
            double tx = Optional.ofNullable(getTax()).orElse(0.0);
            double cashOffer = Optional.ofNullable(getNetCashOffer()).orElse(0.0);
            BigDecimal reservationDeposit = Optional.ofNullable(getReservationDeposit()).orElse(BigDecimal.valueOf(0));

            return price - reb + reg + fee + tx - cashOffer - reservationDeposit.doubleValue();
        }

        public static DealModel from(CertificateView certificate, DealJacket dealJacket, List<DealerFeeView> dealerFeesList) {
            if(certificate == null || dealJacket == null) {
                return null;
            }

            PricesView prices = Optional.ofNullable(certificate.getPrices()).map(PricesView.PriceWrapper::getPrices).orElse(null);
            if(prices == null) {
                return null;
            }

            DealType dealType = getDealType(certificate);

            var dealSheet = dealJacket.getDealSheet();
            var customerRebates = dealSheet.getTotalCustomerRebates(convertDealType(dealType));
            var dealerRebates = dealSheet.getTotalDealerRebates();
            var priceRebates = prices.getRebates() !=null? prices.getRebates():0;
            Integer totalRebates = customerRebates.intValue() + dealerRebates.intValue() + priceRebates;
            Integer purchasePrice = dealJacket.getSalePrice() != null ? dealJacket.getSalePrice()- totalRebates: 0;

            Double dealerFeeTotal = DealerFeesService.getTotalFees(dealerFeesList);

            return DealModel.builder()
                .certificateId(certificate.getId())
                .dealName(certificate.getDealName())
                .dealType(ofNullable(dealType).map(Objects::toString).orElse(null))
                .netCashOffer(dealJacket.getTradeEquity())
                .msrp(dealJacket.getMsrp())
                .rebates(totalRebates)
                .dealerRebates(ofNullable(dealSheet.getDealerRebates()).orElse(Collections.emptyList()))
                .discount(dealJacket.getDealerDiscount())
                .salePrice(dealJacket.getSalePrice())
                .purchasePrice(purchasePrice)
                .dealerFee(dealerFeeTotal)
                .dealerFees(dealerFeesList)
                .tax(prices.getSalesTax())
                .modifiedByDealer(certificate.getGeneratedByDealer())
                .dealURL(generateDealURL(certificate))
                .registration(prices.getTitleLicenseFee())
                .externalOffers(ofNullable(certificate.getExternalOffers()).map(ExternalOffersModel::new).orElse(null))
                .createdAt(certificate.getCreatedDate())
                .build();
        }

        private static com.carsaver.magellan.model.dealer.DealType convertDealType(DealType dealType) {
            switch (dealType) {
                case CASH: return com.carsaver.magellan.model.dealer.DealType.CASH;
                case FINANCE: return com.carsaver.magellan.model.dealer.DealType.FINANCE;
                case LEASE: return com.carsaver.magellan.model.dealer.DealType.LEASE;
                default: return null;
            }
        }

        private static String getCreditStatusDescription(DealCreditStatus creditStatus){
            if(creditStatus.isSelfSelectedCredit()) {
                return "Self-Selected Credit";
            } else if(creditStatus.isPreApproved()){
                return "Pre-Approved";
            } else if(creditStatus.isPreQualified()) {
                return "Pre-Qualified";
            } else {
                return null;
            }
        }

        private static String generateDealURL(CertificateView certificate){
            try {
                return certificate.generateDealURL().get();
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            return null;
        }

        public static DealModel from(CertificateView certificate, DealCreditStatus creditStatus, String financierName, DealJacket dealJacket, List<DealerFeeView> dealerFeeViewList) {
            if(certificate == null || dealJacket == null) {
                return null;
            }

            QuoteView quote = certificate.getQuote();
            PricesView prices = Optional.ofNullable(certificate.getPrices())
                .map(PricesView.PriceWrapper::getPrices)
                .orElse(null);

            var dealSheet = dealJacket.getDealSheet();

            if (quote == null) {
                return DealModel.builder()
                    .certificateId(certificate.getId())
                    .msrp(dealJacket.getMsrp())
                    .dealerFee(dealSheet.getTotalDealerFees())
                    .dealerFees(dealerFeeViewList)
                    .discount(dealJacket.getDealerDiscount())
                    .rebates(ofNullable(prices).map(PricesView::getRebates).orElse(null))
                    .dealerRebates(ofNullable(dealSheet.getDealerRebates()).orElse(Collections.emptyList()))
                    .salePrice(dealJacket.getSalePrice())
                    .purchasePrice(dealJacket.getPurchasePrice())
                    .modifiedByDealer(certificate.getGeneratedByDealer())
                    .dealURL(generateDealURL(certificate))
                    .externalOffers(Optional.ofNullable(certificate.getExternalOffers()).map(ExternalOffersModel::new).orElse(null))
                    .build();
            }

            String dealType = ofNullable(getDealType(certificate)).map(Objects::toString).orElse(null);
            String financeType = quote.isLease() ? PaymentType.LEASE.toProperCaseStr() : PaymentType.FINANCE.toProperCaseStr();
            String lenderTier = getLenderTier(certificate, quote);
            String dealStatus = getCreditStatusDescription(creditStatus);

            var user = certificate.getUser();
            var creditProfile = user.getCreditProfile();
            var preQualDate = creditProfile.map(CreditProfile::getScoreDate).orElse(null);
            var selfSelectedCreditTier = creditProfile.map(CreditProfile::getCreditRange).orElse(null);
            var creditEvaluator = creditProfile.map(CreditProfile::getCreditEvaluator).orElse(null);
            var softPullFailureReason = creditProfile.map(CreditProfile::getSoftPullFailureReason).orElse(null);

            Double dealerFeeTotal = DealerFeesService.getTotalFees(dealerFeeViewList);
            Double purchasePriceOption = getPurchasePriceOption(certificate, quote);

            return DealModel.builder()
                .certificateId(certificate.getId())
                .dealName(certificate.getDealName())
                .dealType(dealType)
                .netCashOffer(dealJacket.getTradeEquity())
                .status(dealStatus)
                .dealCreditStatus(creditStatus)
                .lenderTier(lenderTier)
                .selfSelectedCreditTier(selfSelectedCreditTier)
                .creditEvaluator(creditEvaluator)
                .softPullFailureReason(softPullFailureReason)
                .preQualificationDate(preQualDate)
                .financierName(financierName)
                .financeType(financeType)
                .applicationNumber("N/A")
                .outOfPocket(dealJacket.getOutOfPocket())
                .downPayment(dealJacket.getDownPayment())
                .cashBack(dealJacket.getCashBack())
                .totalAmountFinance(dealJacket.getTotalAmountFinanced())
                .registration(quote.getLicenseRegistration())
                .tax(quote.getTotalTaxes())
                .rebates(quote.getRebates())
                .modifiedByDealer(certificate.getGeneratedByDealer())
                .dealURL(generateDealURL(certificate))
                .dealerRebates(ofNullable(dealSheet.getDealerRebates()).orElse(Collections.emptyList()))
                .monthlyPayment(quote.getMonthlyPayment())
                .monthlyPaymentWithFeesAndTaxes(ofNullable(dealJacket.getMonthlyPayments()).map(DealJacket.MonthlyPayments::getWithFeesAndTaxes).orElse(null))
                .monthlyTaxes(quote.getMonthlyTaxes())
                .totalPayments(quote.getTotalPayments())
                .termLength(String.format("%s", quote.getTerm()))
                .interestRate(quote.getInterestRate())
                .totalDueAtSigning(quote.getDueAtSigning())
                .annualMileageAllowance(String.format("%s",quote.getMileageAllowed()))
                .mileagePenalty(quote.getMilePenalty())
                .endOfTermPurchaseOption(purchasePriceOption)
                .acquisitionFee(quote.getAcquisitionFee())
                .dispositionFee(quote.getDispositionFee())
                .securityDeposit(quote.getSecurityDeposit())
                .salePrice(dealJacket.getSalePrice())
                .purchasePrice(dealJacket.getPurchasePrice())
                .msrp(dealJacket.getMsrp())
                .discount(dealJacket.getDealerDiscount())
                .dealerFee(dealerFeeTotal)
                .dealerFees(dealerFeeViewList)
                .externalOffers(ofNullable(certificate.getExternalOffers()).map(ExternalOffersModel::new).orElse(null))
                .preApproval(ofNullable(certificate.getDealPreferences())
                        .map(DealPreferences::getAppliedPreApproval)
                        .map(preApproval -> new PreApprovalModel(preApproval, financierName)).orElse(null))
                .createdAt(certificate.getCreatedDate())
                .build();
        }

        @NotNull
        private static DealType getDealType(CertificateView certificate) {
            return ofNullable(certificate.getDealPreferences())
                .map(DealPreferences::getDealType)
                .orElseGet(() ->
                    (Optional.of(certificate)
                        .map(CertificateView::getQuote)
                        .map(QuoteView::getType)
                        .map(it -> "Loan".equals(it) ? DealType.FINANCE : DealType.LEASE)
                        .orElse(DealType.CASH))
                );
        }

        @Nullable
        private static String getLenderTier(CertificateView certificate, QuoteView quote) {
            return Optional.ofNullable(certificate.getFinancier())
                .map(FinancierView::getConfig)
                .map(FinancierConfig::getCreditConfig)
                .flatMap(creditConfig -> creditConfig.findCreditTier(quote.getBeacon()))
                .map(CreditTier::getName)
                .orElse(null);
        }
    }

    private static double getPurchasePriceOption(CertificateView certificate, QuoteView quoteView) {
        double purchasePriceOption = Optional.ofNullable(quoteView.getPurchaseOption()).orElse(0D);
//        boolean isLeafElectricVehicle = Optional.ofNullable(certificate.getVehicle()).map(VehicleView::getFullName).map(String::toLowerCase).map(i-> i.contains("leaf") || i.contains("ariya")).orElse(false);
//        if (isLeafElectricVehicle) {
//            purchasePriceOption = 0.0;
//        }
        return purchasePriceOption;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Dealer {
        private String id;
        private String name;
        private String address;
        private String city;
        private String state;
        private String zipCode;
        private String phone;
        private Double lat;
        private Double lng;
        private List<DealerEmployee> employees;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DealerEmployee {
        private String id;
        private String firstName;
        private String lastName;
        private String type;
        private String phoneNumber;
        private String email;
        private String jobTitle;
        private Boolean programManager;
        private Boolean followupManager;
        private Boolean salesManager;
    }
}
