package com.carsaver.partner.model.warranty;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.warranty.model.Agreement;
import com.carsaver.warranty.model.ApplicantRegistration;
import com.carsaver.warranty.model.Buyer;
import com.carsaver.warranty.model.GetVehicleResponse;
import com.carsaver.warranty.model.Premium;
import com.carsaver.warranty.model.PremiumsRequest;
import com.carsaver.warranty.model.Vehicle;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;

@Data
@Slf4j
public class ApplicantRegistrationForm {

    private GetVehicleResponse vehicleResponse;
    private PremiumsRequest premiumsRequest;
    private Premium selectedPremium;
    private Integer selectedPremiumIndex;
    private UserView customer;
    private ApplicantRegistration applicantRegistration = new ApplicantRegistration();
    private String usersSearchQuery = "";
    private boolean registrationProcessStarted;
    private boolean applicantRegistrationPrepared;
    private boolean upgradeProcessStarted;
    private LocalDate dealerEffectiveEnrollmentLocalDate;

    public void setStarted() {
        reset();
        this.registrationProcessStarted = true;
    }

    public void startUpgradeProcess(UserView customer) {
        reset();
        this.upgradeProcessStarted = true;
        this.customer = customer;
    }

    public void setComplete() {
        reset();
        this.registrationProcessStarted = false;
    }

    public void setSelectedPremium(Premium selectedPremium, Integer selectedPremiumIndex) {
        setSelectedPremium(selectedPremium);
        setSelectedPremiumIndex(selectedPremiumIndex);
    }

    public ApplicantRegistration getApplicantRegistration() {
        return !applicantRegistrationPrepared && !upgradeProcessStarted ? prepareApplicantRegistration() : applicantRegistration;
    }

    public ApplicantRegistration prepareApplicantRegistration() {

        applicantRegistration.setAgreement(new Agreement(premiumsRequest.getPurchaseDate()));
        applicantRegistration.setCarSaverTransaction(premiumsRequest.getCarSaverTransaction());

        Vehicle applicantRegistrationVehicle = new Vehicle(vehicleResponse);
        applicantRegistration.setVehicle(applicantRegistrationVehicle);

        applicantRegistration.applySelectedPremium(selectedPremium);
        applicantRegistration.setBuyer(newBuyerFrom(customer));

        applicantRegistrationPrepared = true;
        return applicantRegistration;
    }

    public WarrantyContractView toWarrantyContractView(DealerView dealer) {
        return toWarrantyContractView(dealer, customer.getId());
    }

    public WarrantyContractView toWarrantyContractView(DealerView dealer, String userId) {
        WarrantyContractView contractView = new WarrantyContractView();
        contractView.setApplicantRegistration(applicantRegistration);
        contractView.setDealerId(dealer.getId());

        String vin = applicantRegistration.getVehicle() != null ? applicantRegistration.getVehicle().getVin() : null;
        contractView.setVin(vin);

        contractView.setUserId(userId);

        contractView.setSelectedPremium(selectedPremium);

        return contractView;
    }

    public boolean isStartedAndCustomerSelected() {
        return isRegistrationProcessStarted() && getCustomer() != null;
    }

    private void reset() {
        this.vehicleResponse = null;
        this.premiumsRequest = null;
        this.selectedPremium = null;
        this.selectedPremiumIndex = null;
        this.customer = null;
        this.applicantRegistration = new ApplicantRegistration();
        this.applicantRegistrationPrepared = false;
        this.registrationProcessStarted = false;
        this.upgradeProcessStarted = false;
    }

    private Buyer newBuyerFrom(UserView customer) {
        if(customer == null) {
            return new Buyer();
        }
        Buyer buyer = new Buyer();
        buyer.setFirstName(customer.getFirstName());
        buyer.setLastName(customer.getLastName());
        buyer.setStreetAddress(customer.getAddress());
        buyer.setState(customer.getState());
        buyer.setPrimaryPhone(customer.getPhoneNumber());
        buyer.setCity(customer.getCity());
        buyer.setEmailAddress(customer.getEmail());
        buyer.setZip(customer.getZipCode());

        return buyer;
    }
}
