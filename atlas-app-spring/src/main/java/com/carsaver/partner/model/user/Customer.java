package com.carsaver.partner.model.user;

import com.carsaver.magellan.model.AddressView;
import com.fasterxml.jackson.annotation.JsonIgnore;

public interface Customer extends Sourceable {

    String getId();
    String getFirstName();
    String getLastName();
    String getEmail();
    String getPhoneNumber();
    String getAddress();
    String getAddress2();
    String getCity();
    String getState();
    String getStateCode();
    String getZipCode();
    String getCounty();

    @JsonIgnore
    default String getFullAddress() {
        return getAddressView().getFullAddress();
    }

    default AddressView getAddressView() {
        return AddressView.builder()
                .street1(getAddress())
                .street2(getAddress2())
                .city(getCity())
                .state(getStateCode())
                .zipCode(getZipCode())
                .county(getCounty())
                .build();
    }
}
