package com.carsaver.partner.model.warranty;

import com.carsaver.magellan.model.VehicleView;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateCustomerVehicle {
    private String imageUrl;
    private Integer year;
    private String make;
    private String model;
    private String trim;
    private String dealerId;
    private String vin;
    private String stockNumber;

    public static CreateCustomerVehicle from(VehicleView vehicleView, String imageUrl) {
        return CreateCustomerVehicle.builder()
            .imageUrl(imageUrl)
            .year(vehicleView.getYear())
            .make(vehicleView.getMake())
            .model(vehicleView.getModel())
            .trim(vehicleView.getTrim())
            .vin(vehicleView.getVin())
            .stockNumber(vehicleView.getStockNumber())
            .dealerId(vehicleView.getDealerId())
            .build();
    }
}
