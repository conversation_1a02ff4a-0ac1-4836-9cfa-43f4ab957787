package com.carsaver.partner.model.mapper;

import com.carsaver.magellan.api.finance.FinanceDecisionModel;
import com.carsaver.partner.model.finance.FinanceDecision;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

@Mapper(componentModel = "spring")
public interface FinanceDecisionMapper {

    @Mappings({
        @Mapping(target = "lmsName", ignore = true),
        @Mapping(target = "lmsId", ignore = true),
        @Mapping(target = "expirationDate", ignore = true),
        @Mapping(target = "maxLoanAmount", ignore = true),
    })
    FinanceDecision toModel(FinanceDecisionModel financeDecisionModel);
}
