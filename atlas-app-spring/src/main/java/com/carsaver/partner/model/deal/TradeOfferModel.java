package com.carsaver.partner.model.deal;

import com.carsaver.core.PaymentType;
import com.carsaver.magellan.model.user.UserVehicleDiscrepancy;
import com.carsaver.magellan.model.vehicle.VehicleInfo;
import com.carsaver.magellan.model.vehicle.quote.Question;
import com.carsaver.magellan.model.vehicle.quote.VehicleAccessory;
import com.carsaver.magellan.model.vehicle.quote.VehicleCondition;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public final class TradeOfferModel {
    private String id;
    private Integer totalTradeValue;
    private Integer adjustedTradeValue;
    private Integer accessoriesTradeValue;
    private String vin;
    private Integer mileage;
    private List<SelectedFeatures> selectedFeatures;
    private String color;
    private String transmission;
    private List<Question> questions;
    private String physicalCondition;
    private LocalDate expirationDate;
    private boolean expired;
    private VehicleInfo vehicle;
    private PayoffQuote payoffQuote;
    private UserVehicleDiscrepancy discrepancy;
    private String userVehicleId;
    private Integer guaranteedOffer;
    private String userId;
    private PaymentType purchaseType;
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private String tradeType;
    @JsonInclude(JsonInclude.Include.ALWAYS)
    private Boolean isLeasePayoffEnabled;


    @NoArgsConstructor
    @Data
    public static class SelectedFeatures {
        private String description;
        private Integer tradeInValue;

        public SelectedFeatures(VehicleAccessory vehicleAccessory, VehicleCondition vehicleCondition) {
            this.description = vehicleAccessory.getName();
            this.tradeInValue = vehicleAccessory.getTradeInValue(vehicleCondition);
        }
    }

    @Data
    public static class PayoffQuote {
        private Integer financierId;
        private String financeCompany;
        private PaymentType financeType;
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
        private LocalDate payoffGoodThrough;
        private Double monthlyPayment;
        private Double payoffAmount;
        private Integer paymentsRemaining;
        private String payoffMethod;
        private String payoffReferenceId;
    }

}
