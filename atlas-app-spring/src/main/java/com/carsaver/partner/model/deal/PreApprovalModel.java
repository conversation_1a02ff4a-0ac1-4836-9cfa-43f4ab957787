package com.carsaver.partner.model.deal;

import com.carsaver.magellan.model.ModelUtils;
import com.carsaver.magellan.model.user.CreditPreApproval;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@NoArgsConstructor
@Data
public class PreApprovalModel {

    private Integer financierId;

    @Getter(AccessLevel.NONE)
    private String financierName;

    @Getter(AccessLevel.NONE)
    private String tierBump;

    private Double monthlyPayment;

    private LocalDate preApprovalDate;

    private boolean isValid;

    private String approvalNumber;

    public PreApprovalModel(CreditPreApproval creditPreApproval, String financierName){
        financierId = creditPreApproval.getFinancierId();
        tierBump = creditPreApproval.getTierBump();
        monthlyPayment = creditPreApproval.getPreApprovalPaymentAmount();
        preApprovalDate = creditPreApproval.getPreApprovalDate();
        isValid = creditPreApproval.isValid();
        approvalNumber = creditPreApproval.getApprovalNumber();
        this.financierName = financierName;
    }

    public String getTierBump(){
        return ModelUtils.getStrippedTierBump(tierBump);
    }

    public String getFinancierName(){

        return financierName;
    }
}
