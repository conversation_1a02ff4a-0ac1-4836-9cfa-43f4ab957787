package com.carsaver.partner.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerLastLoginDetails {

    private ZonedDateTime lastLogin;
    private Integer webSiteVisit;
    private boolean online;

    public static CustomerLastLoginDetails from(ZonedDateTime lastLogin, Integer webSiteVisit, boolean online) {
        return new CustomerLastLoginDetails(lastLogin, webSiteVisit, online);
    }
}
