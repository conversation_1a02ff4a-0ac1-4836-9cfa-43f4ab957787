package com.carsaver.partner.model.mapper;

import com.carsaver.accessories.api.DealAccessory;
import com.carsaver.partner.model.deal.AccessoryItemModel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;


@Mapper(componentModel = "spring")
public interface AccessoryItemModelMapper {

    @Mappings({
        @Mapping(target = "monthlyPayment", source = "monthlyPmt"),
    })
    AccessoryItemModel toAccessoryItemModel(DealAccessory dealAccessory);
}
