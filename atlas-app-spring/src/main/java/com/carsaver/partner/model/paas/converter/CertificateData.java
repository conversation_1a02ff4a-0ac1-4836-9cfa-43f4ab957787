package com.carsaver.partner.model.paas.converter;

import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.chrome.StyleView;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class CertificateData {

    private List<DealSheet.DealerFeeItem> leaseDealerFees;
    private List<DealSheet.DealerFeeItem> financeDealerFees;
    private UserVehicleQuoteView tradeQuote;
    private UserVehicleView tradeVehicle;
    private StyleView style;
    private VehicleView vehicle;

}
