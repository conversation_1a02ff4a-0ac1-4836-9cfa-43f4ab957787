package com.carsaver.partner.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@JsonSerialize
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class PortalAnnouncement {

    private Type type;
    private String message;

    public static PortalAnnouncement success(String actionMessage){
        return new PortalAnnouncement(Type.SUCCESS, actionMessage);
    }

    public static PortalAnnouncement info(String actionMessage){
        return new PortalAnnouncement(Type.INFO, actionMessage);
    }

    public static PortalAnnouncement warning(String actionMessage){
        return new PortalAnnouncement(Type.WARNING, actionMessage);
    }

    public static PortalAnnouncement error(String actionMessage){
        return new PortalAnnouncement(Type.ERROR, actionMessage);
    }

    private enum Type {
        /* must be a valid toastr message type when lowered */
        SUCCESS,
        INFO,
        WARNING,
        ERROR;
    }
}
