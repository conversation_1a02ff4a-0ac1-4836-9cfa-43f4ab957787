package com.carsaver.partner.model.finance;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class FinanceDecision {
    private Integer id;
    private String applicationId;
    private String financeCompany;
    private Double sellRateApr;
    private Double buyRateApr;
    private Double monthlyPayment;
    private boolean lease;
    private Integer term; //replaces months
    private String tier;
    private boolean approved;
    private boolean fullyApproved;
    private boolean conditionedApproval;
    private Double dealerFinanceMarkUpRate;
    private Double dealerLeaseMarkUpRate;
    private List<String> stipulations;
    private String lmsName;
    private String lmsId;
    private String expirationDate;
    private Double maxLoanAmount;

    @Deprecated //use getTerm()
    public Integer getMonths() {
        return term;
    }

    /**
     * for backwards compatibility with old logic in LoanDecisionService
     * e.g. for useQuoteDetails=false and !isLease it was appending the dealerFinanceMarkUpRate to the apr
     *
     * code should be refactored to leverage
     * getSellRateApr() or getBuyRateApr() accordingly
     * @return
     */
    @Deprecated
    public Double getApr() {
        if(!isLease()) {
            if(dealerFinanceMarkUpRate != null) {
                return sellRateApr;
            }
        }
        return buyRateApr;
    }

    public String getStatus() {
        return isFullyApproved()
            ? "Approved"
            : isConditionedApproval()
                ? "Conditioned"
                : "Declined";
    }
}
