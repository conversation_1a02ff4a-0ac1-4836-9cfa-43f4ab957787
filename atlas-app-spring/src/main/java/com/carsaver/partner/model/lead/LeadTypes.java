package com.carsaver.partner.model.lead;

import java.util.Arrays;
import java.util.List;

public class LeadTypes {

    public static final List<String> CONNECTION_LEAD_TYPES = Arrays.asList(
        "contact",
        "Financing",
        "My Trade In",
        "Reserving This Vehicle",
        "Other",
        "pricing-request",
        "Pricing",
        "other-type",
        "order-request",
        "financing-type",
        "send-to-crm",
        "Vehicle Availability",
        "availability-type",
        "financing-type",
        "trade-type",
        "reserve-type",
        "other-type",
        "protection-product-type",
        "pre-qual",
        "pre-qual-standalone"
    );

    public static final List<String> OTHERS_LEAD_TYPES = Arrays.asList(
        "appointment",
        "atlas-manual",
        "finance-app",
        "saved-to-garage",
        "SAVED_TO_GARAGE",
        "save-a-deal",
        "sell-at-home",
        "sell-at-home lead",
        "sell-at-home-contact",
        "session-ended",
        "stand-alone-trade",
        "stand-alone-trade-contact",
        "stand-alone-trade-inspection",
        "stand-alone-trade-sale",
        "t1-sell-at-home",
        "t1-sell-at-home-appointment",
        "t1-sell-at-home-contact",
        "t1-stand-alone-trade",
        "t1-stand-alone-trade-contact",
        "t1-stand-alone-trade-inspection",
        "Trade Request",
        "In-store-check-in",
        "Send to CRM Atlas"
    );

}
