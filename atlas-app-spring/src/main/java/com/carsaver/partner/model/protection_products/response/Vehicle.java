package com.carsaver.partner.model.protection_products.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Vehicle {
    private String id;
    private String vin;
    private String make;
    private String model;
    private Double year;
    private String style;
    private String color;
    private String condition;
    private String stockNumber;
    private Double mileage;
    private Double msrp;
}
