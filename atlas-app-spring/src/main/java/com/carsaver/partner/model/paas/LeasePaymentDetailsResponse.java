package com.carsaver.partner.model.paas;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LeasePaymentDetailsResponse {

    private LeasePaymentResponse leasePaymentResponse;

    private List<Fees> fees;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LeasePaymentResponse {

        LocalDateTime effectiveDate;
        LeaseDealStructure dealStructure;
        LeasePayment payment;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class LeaseDealStructure {
            Integer financierId;
            BigDecimal msrp;
            BigDecimal sellingPrice;
            Integer term;
            BigDecimal netTradeInValue;
            BigDecimal moneyFactor;
            BigDecimal residualPct;
            BigDecimal downPayment;
            BigDecimal rebates;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class LeasePayment {
            BigDecimal totalMonthlyPayment;
            BigDecimal preTaxMonthlyPayment;
            BigDecimal monthlyTax;
            BigDecimal basePayment;
            BigDecimal residualAmount;
            BigDecimal monthlyRentCharge;
            BigDecimal grossCapitalizedCost;
            BigDecimal capitalizedCostReduction;
            BigDecimal adjustedCapitalizedCost;
            BigDecimal cashCapReduction;
            BigDecimal totalCapCostReduction;
            BigDecimal taxOnTotalCapCostReduction;
            BigDecimal taxOnInceptionFees;
            BigDecimal totalSalesTax;
            BigDecimal taxRate;
            BigDecimal licenseAndRegistration;
            LeaseDueAtStart amountDueAtStart;
        }

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class LeaseDueAtStart {
            BigDecimal totalInceptions;
            BigDecimal firstMonthlyPayment;
            BigDecimal dealerInceptionFees;
            BigDecimal customerCash;
            BigDecimal positiveTradeInValue;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Fees {
        private String feeValue;
        private String ruleName;
    }

}
