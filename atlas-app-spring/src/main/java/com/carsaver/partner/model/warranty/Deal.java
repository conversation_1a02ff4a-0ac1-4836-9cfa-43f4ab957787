package com.carsaver.partner.model.warranty;

import com.carsaver.warranty.model.Premium;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
public class Deal {

    @NotNull(message="Schedule Id is required.")
    private Integer scheduleId;
    private String dealerFAndINumber = "";
    private String dealerContactName = "";
    private String dealerStockNumber = "";
    private String dealerCustomerNumber = "";

    public Deal(Premium premium) {
        this.setScheduleId(premium.getScheduleId());
    }

    public com.carsaver.warranty.model.Deal toNwanDeal() {
        com.carsaver.warranty.model.Deal deal = new com.carsaver.warranty.model.Deal();
        BeanUtils.copyProperties(this, deal);

        return deal;
    }
}
