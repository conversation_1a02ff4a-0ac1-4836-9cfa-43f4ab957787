package com.carsaver.partner.model.warranty;

import com.carsaver.warranty.model.PremiumsRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
public class PremiumsRequestModel {

    @NotNull(message = "Vin must be 17 characters")
    @Size(min=17, max=17, message = "Vin must be 17 characters")
    private String vin;

    @NotNull(message = "Odometer is required")
    private Integer odometer;

    @NotNull(message = "purchaseDate is required")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date purchaseDate;

    private boolean carSaverTransaction;

    private List<String> dealerIds;

    public static PremiumsRequestModel from(PremiumsRequest premiumsRequest) {
        PremiumsRequestModel premiumsRequestModel = new PremiumsRequestModel();
        BeanUtils.copyProperties(premiumsRequest, premiumsRequestModel);
        premiumsRequestModel.setCarSaverTransaction(Boolean.TRUE.equals(premiumsRequest.getCarSaverTransaction()));

        return premiumsRequestModel;
    }


}
