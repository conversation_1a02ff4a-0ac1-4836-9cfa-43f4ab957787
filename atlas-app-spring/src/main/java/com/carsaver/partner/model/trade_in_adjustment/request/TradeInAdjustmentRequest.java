package com.carsaver.partner.model.trade_in_adjustment.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TradeInAdjustmentRequest {
    private TradeInAdjustmentPrimary tradeInAdjustmentPrimary;
}
