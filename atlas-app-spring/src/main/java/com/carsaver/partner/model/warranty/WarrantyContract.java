package com.carsaver.partner.model.warranty;

import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.warranty.model.Agreement;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarrantyContract {
    private String id;
    private String dealerId;
    private String userId;
    private String vin;
    private Vehicle vehicle;
    private Premium selectedPremium;
    private boolean paymentRequired;
    private boolean paid;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private Date purchaseDate;

    private ApplicantRegistration applicantRegistration;

    public static WarrantyContract from(WarrantyContractView warrantyContractView) {
        WarrantyContract existingWarrantyContract = new WarrantyContract();

        existingWarrantyContract.setId(warrantyContractView.getId());
        existingWarrantyContract.setDealerId(warrantyContractView.getDealerId());
        existingWarrantyContract.setPaymentRequired(warrantyContractView.isPaymentRequired());
        existingWarrantyContract.setUserId(warrantyContractView.getUserId());
        existingWarrantyContract.setVin(warrantyContractView.getVin());
        existingWarrantyContract.setPaid(warrantyContractView.isPaid());

        Optional.ofNullable(warrantyContractView.getVehicle())
            .ifPresent(vehicle ->  {
                existingWarrantyContract.setVehicle(Vehicle.from(vehicle));

                if(warrantyContractView.getApplicantRegistration() != null && warrantyContractView.getApplicantRegistration().getVehicle() != null) {
                    existingWarrantyContract.getVehicle().setEngineAspiration(
                        warrantyContractView.getApplicantRegistration().getVehicle().getEngineAspiration());

                    if(warrantyContractView.getApplicantRegistration().getVehicle().getConditionCode() != null) {
                        existingWarrantyContract.getVehicle().setConditionCode(
                            warrantyContractView.getApplicantRegistration().getVehicle().getConditionCode().getCode());
                    }

                }
            });

        Optional.ofNullable(warrantyContractView.getApplicantRegistration()).ifPresent(nwanApplicantRegistration -> {
            ApplicantRegistration applicantRegistration = new ApplicantRegistration();
            if(nwanApplicantRegistration.getLienholder() != null) {
                Lienholder lienholder = new Lienholder();
                lienholder.setName(nwanApplicantRegistration.getLienholder().getName());
                applicantRegistration.setLienholder(lienholder);
            }
            existingWarrantyContract.setApplicantRegistration(applicantRegistration);
        });

        existingWarrantyContract.setSelectedPremium(Premium.from(warrantyContractView.getSelectedPremium(), 0));

        Optional.ofNullable(warrantyContractView.getApplicantRegistration())
            .map(com.carsaver.warranty.model.ApplicantRegistration::getAgreement)
            .map(Agreement::getPurchaseDate)
            .ifPresent(existingWarrantyContract::setPurchaseDate);

        return existingWarrantyContract;
    }
}
