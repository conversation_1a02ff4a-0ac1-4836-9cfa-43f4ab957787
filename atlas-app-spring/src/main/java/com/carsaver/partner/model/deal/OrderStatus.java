package com.carsaver.partner.model.deal;

import com.carsaver.magellan.model.user.UserContractView;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatus {

    private boolean contractRequested;
    private String signingOption;
    private String receiveMethod;

    public static OrderStatus from(UserContractView userContract) {
        OrderStatus orderStatus = new OrderStatus();

        if(userContract != null) {
            orderStatus.setContractRequested(true);
            orderStatus.setReceiveMethod(userContract.getVehicleDeliveryMethod());
            orderStatus.setSigningOption(userContract.getSignatureCaptureMethod());
        }

        return orderStatus;
    }
}
