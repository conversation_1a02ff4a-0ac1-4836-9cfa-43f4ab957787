package com.carsaver.partner.model.trade_in_adjustment.response;

import com.carsaver.partner.model.trade_in_adjustment.TradeInAdjustmentType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class TradeInAdjustmentModelSpecific {
    private String id;
    private Boolean isDeleted;
    private TradeInAdjustmentType adjustmentType;
    private Double adjustmentAmount;
    private Integer year;
    private String make;
    private String model;
    private Boolean isYearRange;
    private Integer startYear;
    private Boolean isStartYearLimit;
    private Integer endYear;
    private Boolean isEndYearLimit;
}
