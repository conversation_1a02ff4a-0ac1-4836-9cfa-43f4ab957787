package com.carsaver.partner.model.protection_products.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrimaryBuyer {
    private String id;
    private String dealerId;
    private String customerTypeCode;
    private String firstName;
    private String lastName;
    private String displayName;
}
