package com.carsaver.partner.validation;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.web.form.UserForm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class UniqueEmailValidator implements ConstraintValidator<UniqueEmail, UserForm> {

    @Value("${tenant.portal-id}")
    private String portalTenantId;

    @Autowired
    private UserClient userClient;

    @Override
    public boolean isValid(UserForm userForm, ConstraintValidatorContext context) {
        if(userForm == null) {
            return true;
        }

        UserView foundUser = userClient.findByTenantAndEmail(portalTenantId, userForm.getEmail()).orElse(null);

        /*
            if we didn't find a user associated with the email, then it's NOT a duplicate
            however, if we found a user associated with the email AND it doesn't belong to the user currently being updated then it IS a duplicate
         */
        if (foundUser != null && !foundUser.getId().equals(userForm.getUserId())) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
                .addPropertyNode("email")
                .addConstraintViolation();
            return false;
        }

        return true;

    }
}
