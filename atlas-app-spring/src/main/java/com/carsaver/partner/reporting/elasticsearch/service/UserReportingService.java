package com.carsaver.partner.reporting.elasticsearch.service;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.UserDoc;
import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.elasticsearch.criteria.ReportUserSearchCriteria;
import com.carsaver.partner.search.facets.UserDocFacets;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.FacetParser;
import de.cronn.reflection.util.PropertyUtils;
import de.cronn.reflection.util.TypedPropertyGetter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserReportingService extends CriteriaSearchHandler<UserDoc> {

    private static final String[] USERS_INDEX = {"users"};

    @Autowired
    private DateRangeHelper dateRangeHelper;

    @Autowired
    public UserReportingService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    public FacetInfoResult getFacet(String programId, List<String> dealerIds, TypedPropertyGetter<UserDocFacets, ?> propertyGetter) {
        var criteria = new ReportUserSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);

        // type safe way to get the field name we want
        String facetName = PropertyUtils.getPropertyName(UserDocFacets.class, propertyGetter);
        return this.facets(criteria, UserDocFacets.class, facetName);
    }

    public long getCount(String programId, List<String> dealerIds, ZonedDateRange dateRange) {
        var criteria = new ReportUserSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setCreatedDate(dateRange);

        return this.count(criteria);
    }

    public double getMonthOverMonthPercent(String programId, List<String> dealerIds) {
        var pastMonthCriteria = new ReportUserSearchCriteria();
        pastMonthCriteria.setDealerIds(dealerIds);
        pastMonthCriteria.setProgramId(programId);
        pastMonthCriteria.setCreatedDate(dateRangeHelper.getLast30DaysRange());

        var pastMonthCount = this.count(pastMonthCriteria);

        var previousMonthCriteria = new ReportUserSearchCriteria();
        previousMonthCriteria.setDealerIds(dealerIds);
        previousMonthCriteria.setProgramId(programId);
        previousMonthCriteria.setCreatedDate(dateRangeHelper.getPreviousMonthRange());

        var previousMonthCount = this.count(previousMonthCriteria);

        double rate;
        if(previousMonthCount == 0) {
            rate = (pastMonthCount - (double) previousMonthCount) * 100;
        } else {
            rate = (pastMonthCount - previousMonthCount) / (double) previousMonthCount * 100;
        }

        return rate;
    }

    public double getYearOverYearPercent(String programId, List<String> dealerIds) {
        var pastYearCriteria = new ReportUserSearchCriteria();
        pastYearCriteria.setDealerIds(dealerIds);
        pastYearCriteria.setProgramId(programId);
        pastYearCriteria.setCreatedDate(dateRangeHelper.getPastYearRange());

        var pastYearCount = this.count(pastYearCriteria);

        var previousYearCriteria = new ReportUserSearchCriteria();
        previousYearCriteria.setDealerIds(dealerIds);
        previousYearCriteria.setProgramId(programId);
        previousYearCriteria.setCreatedDate(dateRangeHelper.getPreviousYearRange());

        var previousYearCount = this.count(previousYearCriteria);

        double rate;
        if(previousYearCount == 0) {
            rate = (pastYearCount - (double) previousYearCount) * 100;
        } else {
            rate = (pastYearCount - previousYearCount) / (double) previousYearCount * 100;
        }

        return rate;
    }

    @Override
    protected String[] getSearchIndex() {
        return USERS_INDEX;
    }

}
