package com.carsaver.partner.reporting.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
public class LenderBreakdownReport {
    private List<NnmacHistogram> nmacHistogram;

    private List<LenderReport> lenderReportList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NnmacHistogram {
        private LocalDate date;

        private Double percentNmacApplications;

        private Long leasedCount;

        private Long financedCount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LenderReport {
        private String lenderName;

        private Integer applications;

        private Long approvals;

        private Double percentApproved;

        private Double percentFinanced;

        private Double averageRequestedAmountFinanced;

        private List<Integer> creditScoreList;

        private List<String> rateList;

        private List<String> termList;
    }
}
