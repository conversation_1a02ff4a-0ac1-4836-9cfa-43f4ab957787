package com.carsaver.partner.reporting.elasticsearch.criteria;

import com.carsaver.core.DealerStatus;
import com.carsaver.elasticsearch.criteria.DealerSearchCriteria;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.SearchScope;
import lombok.Data;

import java.util.List;

@Data
public class ReportDealerSearchCriteria extends DealerSearchCriteria {

    /**
     * This field is used to limit the dealers returned based solely on the access that the user has
     */
    @TermQuery(field = "id", scope = SearchScope.QUERY)
    private List<String> dealerAccessList;

    @TermQuery(field = "id", scope = SearchScope.POST_FILTER)
    private List<String> ids;

    @TermQuery(nested = true, path = "subscriptions", field = "program.id", scope = SearchScope.QUERY)
    private List<String> subscriptionProgramIds;

    @TermQuery(nested = true, path = "subscriptions", field = "status", scope = SearchScope.QUERY)
    private List<DealerStatus> programStatuses;

}
