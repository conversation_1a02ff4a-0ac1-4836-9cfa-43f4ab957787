package com.carsaver.partner.reporting.elasticsearch.facets;

import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.annotation.aggs.DateHistogramAgg;
import com.carsaver.search.facet.HistogramFacet;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DealDocReportingFacets implements DocFacets {

    @DateHistogramAgg(field="createdDate", expression = "1d")
    private List<HistogramFacet> dailyDealsCount;

    @Aggregate(field = "dealPreferences.dealType", uniqueField = "certificateId")
    private List<TermFacet> dealType;

    //TODO: is this enough for ATS-256, or do I need to grab just the vehicle.StockType
    @Aggregate(field = "type", uniqueField = "certificateId")
    private List<TermFacet> dealStockType;

    @Aggregate(field = "dealPreferences.financePreferences.term", uniqueField = "certificateId")
    private List<TermFacet> dealTerms;

}
