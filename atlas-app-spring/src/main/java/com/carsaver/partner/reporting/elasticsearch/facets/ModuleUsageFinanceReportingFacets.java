package com.carsaver.partner.reporting.elasticsearch.facets;

import com.carsaver.search.annotation.CardinalityAgg;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModuleUsageFinanceReportingFacets  implements DocFacets {

    @CardinalityAgg(field = "user.id")
    private Long uniqueUserCreditHardPullCount;
}
