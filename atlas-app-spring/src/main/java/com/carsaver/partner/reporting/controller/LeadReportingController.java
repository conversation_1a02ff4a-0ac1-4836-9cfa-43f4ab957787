package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.reporting.elasticsearch.service.LeadReportingService;
import com.carsaver.partner.reporting.elasticsearch.service.VehicleSaleReportingService;
import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.model.ReportingCount;
import com.carsaver.partner.reporting.model.ReportingRequest;
import com.carsaver.partner.reporting.elasticsearch.facets.LeadDocReportingFacets;
import com.carsaver.partner.reporting.elasticsearch.facets.VehicleSaleDocReportingFacets;
import com.carsaver.search.support.FacetInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/reporting")
public class LeadReportingController {

    @Autowired
    private LeadReportingService leadReportingService;

    @Autowired
    private VehicleSaleReportingService vehicleSaleReportingService;

    @Autowired
    private DateRangeHelper dateRangeHelper;

    @PostMapping("/{programId}/leads/stocktype")
    public FacetInfoResult getLeadsByStockType(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var leadsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        return leadReportingService.getLeadDocReportingFacet(programId, reportingRequest.getDealerIds(), LeadDocReportingFacets::getStockTypes, leadsDateRange);
    }

    @PostMapping("/{programId}/leads/counts")
    public ReportingCount getLeadCounts(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var searchDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getPastYearRange();

        var result = leadReportingService.getLeadDocReportingFacet(programId, reportingRequest.getDealerIds(), LeadDocReportingFacets::getUniqueUserLeadCount, searchDateRange);
        var uniqueCount = (long) result.getResults();
        var count = leadReportingService.getCount(programId, reportingRequest.getDealerIds(), searchDateRange);

        return ReportingCount.builder()
            .count(count)
            .uniqueCount(uniqueCount)
            .build();
    }

    @PostMapping("/{programId}/leads/close_rate")
    public double getCloseRate(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var searchDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getPastYearRange();

        var closeRate = leadReportingService.getCloseRate(programId, reportingRequest.getDealerIds(), searchDateRange);
        return closeRate;
    }

    @PostMapping("/{programId}/leads/softpullPrequal")
    public FacetInfoResult getLeadsBySoftPullPrequal(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {
        var leadsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        return leadReportingService.getLeadDocReportingFacet(programId, reportingRequest.getDealerIds(), LeadDocReportingFacets::getCreditProfileSelfSelected, leadsDateRange);
    }

}
