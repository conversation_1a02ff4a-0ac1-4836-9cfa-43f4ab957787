package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.reporting.elasticsearch.service.ModuleUsageReportingService;
import com.carsaver.partner.reporting.model.ModuleUsage;
import com.carsaver.partner.reporting.model.ReportingRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/reporting")
public class ModuleUsageReportingController {

    @Autowired
    private ModuleUsageReportingService moduleUsageReportingService;

    @PostMapping("/{programId}/module-usage")
    public ModuleUsage getModuleUsageReport(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest){
        return moduleUsageReportingService.getModuleUsageReport(programId, reportingRequest);
    }

}
