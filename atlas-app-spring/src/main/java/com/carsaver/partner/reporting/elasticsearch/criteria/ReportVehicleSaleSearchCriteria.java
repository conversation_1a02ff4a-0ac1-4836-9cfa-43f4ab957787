package com.carsaver.partner.reporting.elasticsearch.criteria;

import com.carsaver.magellan.model.lead.SaleStatus;
import com.carsaver.search.annotation.RangeQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReportVehicleSaleSearchCriteria extends AbstractSearchCriteria {

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    @RangeQuery
    private ZonedDateRange deliveryDate;

    @TermQuery(field = "dealer.id")
    private List<String> dealerIds;

    @TermQuery(field = "user.program.id")
    private String programId;

    @TermQuery(field = "user.id")
    private List<String> userIds;

    @TermQuery(field = "status", scope = SearchScope.POST_FILTER)
    private SaleStatus status;

    @TermQuery(field = "createdSource")
    private String createdSource;
}
