package com.carsaver.partner.reporting.elasticsearch.facets;

import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.annotation.AvgAggregate;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradeReportingFacets implements DocFacets {
    @Aggregate(field = "tradeVehicle.make")
    private List<TermFacet> makes;

    @AvgAggregate(field = "dealSheet.tradeValue")
    private Double averageTradeValue;
}
