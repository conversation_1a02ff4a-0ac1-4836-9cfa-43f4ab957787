package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.reporting.model.ActiveUsers;
import com.carsaver.partner.reporting.service.ActiveUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/reporting")
public class ActiveUsersReportingController {

    @Autowired
    private ActiveUserService activeUserService;

    @PostMapping("/{programId}/active_users")
    public List<ActiveUsers> getActiveUsers(@PathVariable String programId, @RequestBody List<String> dealerIds) {
        return activeUserService.getActiveUsers(dealerIds, programId);
    }

}
