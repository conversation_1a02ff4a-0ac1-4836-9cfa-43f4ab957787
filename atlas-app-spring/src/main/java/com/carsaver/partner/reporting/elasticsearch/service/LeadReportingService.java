package com.carsaver.partner.reporting.elasticsearch.service;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticFacetResponse;
import com.carsaver.elasticsearch.model.LeadDoc;
import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.model.warranty.Vehicle;
import com.carsaver.partner.reporting.elasticsearch.facets.LeadDocReportingFacets;
import com.carsaver.partner.reporting.elasticsearch.criteria.ReportLeadSearchCriteria;
import com.carsaver.partner.reporting.elasticsearch.facets.TradeReportingFacets;
import com.carsaver.partner.search.facets.LeadDocFacets;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.FacetParser;
import de.cronn.reflection.util.PropertyUtils;
import de.cronn.reflection.util.TypedPropertyGetter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LeadReportingService extends CriteriaSearchHandler<LeadDoc> {

    private static final String[] LEADS_INDEX = {"leads"};

    @Autowired
    private DateRangeHelper dateRangeHelper;

    @Autowired
    private VehicleSaleReportingService vehicleSaleReportingService;

    @Autowired
    public LeadReportingService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    public FacetInfoResult getLeadDocReportingFacet(String programId, List<String> dealerIds, TypedPropertyGetter<LeadDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        var criteria = new ReportLeadSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setCreatedDate(dateRange);

        // type safe way to get the field name we want
        String facetName = PropertyUtils.getPropertyName(LeadDocReportingFacets.class, propertyGetter);
        return this.facets(criteria, LeadDocReportingFacets.class, facetName);
    }

    public long getCount(String programId, List<String> dealerIds, ZonedDateRange dateRange) {
        var criteria = new ReportLeadSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setCreatedDate(dateRange);
        return this.count(criteria);
    }

    public Double getCloseRate(String programId, List<String> dealerIds, ZonedDateRange dateRange){

        var criteria = new ReportLeadSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setCreatedDate(dateRange);
        ElasticFacetResponse<LeadDocFacets> response = searchWithResponse(criteria, LeadDocFacets.class);

        List<String> userIds = response.getFacets().getUserIds().stream().map(TermFacet::getId).collect(Collectors.toList());
        long uniqueUserLeadCount = response.getFacets().getUniqueUserLeadCount();
        log.info("UserIds from leads {} , Unique UserLeads {}", userIds, uniqueUserLeadCount);
        var uniqueSales = vehicleSaleReportingService.getCountByUserIds(programId, dealerIds, userIds);
        return ((double) uniqueSales / uniqueUserLeadCount) * 100;
    }

    public double getMonthOverMonthPercent(String programId, List<String> dealerIds) {
        var pastMonthCriteria = new ReportLeadSearchCriteria();
        pastMonthCriteria.setDealerIds(dealerIds);
        pastMonthCriteria.setProgramId(programId);
        pastMonthCriteria.setCreatedDate(dateRangeHelper.getLast30DaysRange());

        var pastMonthCount = this.count(pastMonthCriteria);

        var previousMonthCriteria = new ReportLeadSearchCriteria();
        previousMonthCriteria.setDealerIds(dealerIds);
        previousMonthCriteria.setProgramId(programId);
        previousMonthCriteria.setCreatedDate(dateRangeHelper.getPreviousMonthRange());

        var previousMonthCount = this.count(previousMonthCriteria);

        double rate;
        if(previousMonthCount == 0) {
            rate = (pastMonthCount - (double) previousMonthCount) * 100;
        } else {
            rate = (pastMonthCount - previousMonthCount) / (double) previousMonthCount * 100;
        }

        return rate;
    }

    public double getYearOverYearPercent(String programId, List<String> dealerIds) {
        var pastYearCriteria = new ReportLeadSearchCriteria();
        pastYearCriteria.setDealerIds(dealerIds);
        pastYearCriteria.setProgramId(programId);
        pastYearCriteria.setCreatedDate(dateRangeHelper.getPastYearRange());

        var pastYearCount = this.count(pastYearCriteria);

        var previousYearCriteria = new ReportLeadSearchCriteria();
        previousYearCriteria.setDealerIds(dealerIds);
        previousYearCriteria.setProgramId(programId);
        previousYearCriteria.setCreatedDate(dateRangeHelper.getPreviousYearRange());

        var previousYearCount = this.count(previousYearCriteria);

        double rate;
        if(previousYearCount == 0) {
            rate = (pastYearCount - (double) previousYearCount) * 100;
        } else {
            rate = (pastYearCount - previousYearCount) / (double) previousYearCount * 100;
        }

        return rate;
    }

    @Override
    protected String[] getSearchIndex() {
        return LEADS_INDEX;
    }

}
