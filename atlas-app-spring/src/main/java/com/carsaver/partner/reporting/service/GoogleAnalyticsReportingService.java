package com.carsaver.partner.reporting.service;

import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.google.api.services.analyticsreporting.v4.AnalyticsReporting;
import com.google.api.services.analyticsreporting.v4.model.DateRange;
import com.google.api.services.analyticsreporting.v4.model.DateRangeValues;
import com.google.api.services.analyticsreporting.v4.model.DimensionFilter;
import com.google.api.services.analyticsreporting.v4.model.DimensionFilterClause;
import com.google.api.services.analyticsreporting.v4.model.GetReportsRequest;
import com.google.api.services.analyticsreporting.v4.model.GetReportsResponse;
import com.google.api.services.analyticsreporting.v4.model.Metric;
import com.google.api.services.analyticsreporting.v4.model.Report;
import com.google.api.services.analyticsreporting.v4.model.ReportData;
import com.google.api.services.analyticsreporting.v4.model.ReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoogleAnalyticsReportingService {

    private static final String ALL_SITE_DATA_VIEW_ID_BETA = "248180736";
    private static final String ALL_SITE_DATA_VIEW_ID_PROD = "167426634";
    private static final String PROFILE_GET_STARTED_URI = "/profile/get-started";
    private static final String GARAGE_URI = "/garage";

    @Autowired
    private AnalyticsReporting analyticsReporting;

    @Autowired
    private CampaignClient campaignClient;

    @Autowired
    private Environment environment;

    public double getUniquePageviewsYearOverYear(List<String> dealerIds, String programId) {
        var pastYear = createDateRange(LocalDate.now().minusYears(1), LocalDate.now());
        var previousYear = createDateRange(LocalDate.now().minusYears(2), LocalDate.now().minusYears(1));
        var hostnames = getHostnames(programId);
        var users = createUniqueUsersMetric();
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var pageFilter = createPagePathFilter(List.of(PROFILE_GET_STARTED_URI));
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, pageFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(pastYear, previousYear), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long pastYearCount = 0L;
        long previousYearCount = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            pastYearCount = parseResponseValueByIndex(response, 0);
            previousYearCount = parseResponseValueByIndex(response, 1);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return calculateRate(previousYearCount, pastYearCount);
    }

    public double getUniquePageviewsMonthOverMonth(List<String> dealerIds, String programId) {
        var last30DateRange = createDateRange(LocalDate.now().minusDays(30), LocalDate.now());
        var previous30DateRange = createDateRange(LocalDate.now().minusDays(60), LocalDate.now().minusDays(30));
        var hostnames = getHostnames(programId);
        var users = createUniqueUsersMetric();
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var pageFilter = createPagePathFilter(List.of(PROFILE_GET_STARTED_URI));
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, pageFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(last30DateRange, previous30DateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long last30Count = 0L;
        long previous30Count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            last30Count = parseResponseValueByIndex(response, 0);
            previous30Count = parseResponseValueByIndex(response, 1);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return calculateRate(previous30Count, last30Count);
    }

    public long getUniquePageviewsLast30Days(List<String> dealerIds, String programId) {
        var dateRange = createDateRange(LocalDate.now().minusDays(30), LocalDate.now());
        var hostnames = getHostnames(programId);
        var users = createUniqueUsersMetric();
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var pageFilter = createPagePathFilter(List.of(PROFILE_GET_STARTED_URI));
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, pageFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(dateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            count = parseResponseValueByIndex(response, 0);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return count;
    }

    public long getUniquePageviews(List<String> dealerIds, LocalDate startDate, LocalDate endDate, String programId, String path) {
        var dateRange = createDateRange(startDate, endDate);
        var hostnames = getHostnames(programId);
        var users = createUniqueUsersMetric();
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var pageFilter = createPagePathFilter(List.of(path));
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, pageFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(dateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            count = parseResponseValueByIndex(response, 0);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return count;
    }

    public long getUniqueSignInsLast30Days(List<String> dealerIds, String programId) {
        var dateRange = createDateRange(LocalDate.now().minusDays(30), LocalDate.now());
        var hostnames = getHostnames(programId);
        var users = createUniqueUsersMetric();
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var pageFilter = createPagePathFilter(List.of(GARAGE_URI));
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, pageFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(dateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            count = parseResponseValueByIndex(response, 0);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return count;
    }

    public double getUniqueSignInsMonthOverMonth(List<String> dealerIds, String programId) {
        var last30DateRange = createDateRange(LocalDate.now().minusDays(30), LocalDate.now());
        var previous30DateRange = createDateRange(LocalDate.now().minusDays(60), LocalDate.now().minusDays(30));
        var hostnames = getHostnames(programId);
        var users = createUniqueUsersMetric();
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var pageFilter = createPagePathFilter(List.of(GARAGE_URI));
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, pageFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(last30DateRange, previous30DateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long last30Count = 0L;
        long previous30Count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            last30Count = parseResponseValueByIndex(response, 0);
            previous30Count = parseResponseValueByIndex(response, 1);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return calculateRate(previous30Count, last30Count);
    }

    public double getUniqueSignInsYearOverYear(List<String> dealerIds, String programId) {
        var pastYear = createDateRange(LocalDate.now().minusYears(1), LocalDate.now());
        var previousYear = createDateRange(LocalDate.now().minusYears(2), LocalDate.now().minusYears(1));
        var hostnames = getHostnames(programId);
        var users = createUniqueUsersMetric();
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var pageFilter = createPagePathFilter(List.of(GARAGE_URI));
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, pageFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(pastYear, previousYear), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long pastYearCount = 0L;
        long previousYearCount = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            pastYearCount = parseResponseValueByIndex(response, 0);
            previousYearCount = parseResponseValueByIndex(response, 1);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return calculateRate(previousYearCount, pastYearCount);
    }

    public long getTodayActiveUsers(List<String> dealerIds, String programId) {
        var dateRange = createDateRange(LocalDate.now(), LocalDate.now());
        var hostnames = getHostnames(programId);
        var users = createActiveUsersMetric("active users today");
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(dateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            count = parseResponseValueByIndex(response, 0);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return count;
    }

    public long getThirtyDayActiveUsers(List<String> dealerIds, String programId) {
        var dateRange = createDateRange(LocalDate.now().minusDays(30), LocalDate.now());
        var hostnames = getHostnames(programId);
        var users = createActiveUsersMetric("active users last 30 days");
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(dateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            count = parseResponseValueByIndex(response, 0);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return count;
    }

    public long getSixtyDayActiveUsers(List<String> dealerIds, String programId) {
        var dateRange = createDateRange(LocalDate.now().minusDays(60), LocalDate.now());
        var hostnames = getHostnames(programId);
        var users = createActiveUsersMetric("active users last 60 days");
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(dateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            count = parseResponseValueByIndex(response, 0);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return count;
    }

    public long getNinetyDayActiveUsers(List<String> dealerIds, String programId) {
        var dateRange = createDateRange(LocalDate.now().minusDays(90), LocalDate.now());
        var hostnames = getHostnames(programId);
        var users = createActiveUsersMetric("active users last 90 days");
        var dealerIdFilter = getDealerIdsFilter(dealerIds);
        var hostnamesFilter = getHostnamesFilter(hostnames);
        var dimensionFilterClause = createDimensionFilterClause(List.of(dealerIdFilter, hostnamesFilter), "AND");
        var reportRequest = createReportRequest(List.of(dateRange), List.of(dimensionFilterClause), List.of(users));
        var getReportsRequest = createGetReportsRequest(List.of(reportRequest));

        long count = 0L;
        try {
            var response = analyticsReporting.reports().batchGet(getReportsRequest).execute();
            count = parseResponseValueByIndex(response, 0);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return count;
    }

    private List<String> getHostnames(String programId) {
        return campaignClient.findByProgramId(programId).getContent().stream()
            .map(CampaignView::getDomain)
            .collect(Collectors.toList());
    }

    private String getViewId() {
        if(environment.acceptsProfiles(Profiles.of("prod"))) {
            return ALL_SITE_DATA_VIEW_ID_PROD;
        } else {
            return ALL_SITE_DATA_VIEW_ID_BETA;
        }
    }

    private DateRange createDateRange(LocalDate start, LocalDate end) {
        return new DateRange()
            .setStartDate(start.toString())
            .setEndDate(end.toString());
    }

    private Metric createActiveUsersMetric(String alias) {
        return new Metric()
            .setExpression("ga:users")
            .setAlias(alias);
    }

    private Metric createUniqueUsersMetric() {
        return new Metric()
            .setExpression("ga:uniquePageviews")
            .setAlias("unique page views");
    }

    private DimensionFilter createPagePathFilter(List<String> pagePaths) {
        return new DimensionFilter()
            .setDimensionName("ga:pagePath")
            .setOperator("BEGINS_WITH")
            .setExpressions(pagePaths);
    }

    private DimensionFilter getDealerIdsFilter(List<String> dealerIds) {
        return new DimensionFilter()
            .setDimensionName("ga:dimension1")
            .setOperator("IN_LIST")
            .setExpressions(dealerIds);
    }

    private DimensionFilter getHostnamesFilter(List<String> hostnames) {
        return new DimensionFilter()
            .setDimensionName("ga:hostname")
            .setOperator("IN_LIST")
            .setExpressions(hostnames);
    }

    private DimensionFilterClause createDimensionFilterClause(List<DimensionFilter> filters, String operator) {
        return new DimensionFilterClause()
            .setFilters(filters)
            .setOperator(operator);
    }

    private GetReportsRequest createGetReportsRequest(List<ReportRequest> requests) {
        return new GetReportsRequest()
            .setReportRequests(requests);
    }

    private double calculateRate(long previousYearCount, long pastYearCount) {
        if(previousYearCount == 0) {
            return (pastYearCount - (double) previousYearCount) * 100;
        } else {
            return (pastYearCount - previousYearCount) / (double) previousYearCount * 100;
        }
    }

    private ReportRequest createReportRequest(List<DateRange> dateRanges, List<DimensionFilterClause> filters, List<Metric> metrics) {
        return new ReportRequest()
            .setViewId(getViewId())
            .setDateRanges(dateRanges)
            .setDimensionFilterClauses(filters)
            .setMetrics(metrics);
    }

    private long parseResponseValueByIndex(GetReportsResponse response, int totalsIndex) {
        return Optional.of(response)
            .map(GetReportsResponse::getReports)
            .map(reports -> reports.get(0))
            .map(Report::getData)
            .map(ReportData::getTotals)
            .map(totals -> totals.get(totalsIndex))
            .map(DateRangeValues::getValues)
            .map(values -> values.get(0))
            .map(Long::parseLong)
            .orElse(0L);
    }
}
