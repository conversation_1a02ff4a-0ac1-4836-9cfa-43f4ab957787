package com.carsaver.partner.reporting.service;

import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.elasticsearch.service.LeadReportingService;
import com.carsaver.partner.reporting.elasticsearch.service.UserReportingService;
import com.carsaver.partner.reporting.elasticsearch.service.VehicleSaleReportingService;
import com.carsaver.partner.reporting.model.KeyMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class KeyMetricsService {

    @Autowired
    private UserReportingService userReportingService;

    @Autowired
    private LeadReportingService leadReportingService;

    @Autowired
    private VehicleSaleReportingService vehicleSaleReportingService;

    @Autowired
    private DateRangeHelper dateRangeHelper;

    public List<KeyMetrics> getKeyMetrics(List<String> dealerIds, String programId) {
        List<KeyMetrics> keyMetrics = new ArrayList<>();
        keyMetrics.add(getCreatedAccounts(dealerIds, programId));
        keyMetrics.add(getLeads(dealerIds, programId));
        keyMetrics.add(getSales(dealerIds, programId));
        return keyMetrics;
    }

    private KeyMetrics getCreatedAccounts(List<String> dealerIds, String programId) {
        var countToday = userReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(1));
        var last30Days = userReportingService.getCount(programId, dealerIds, dateRangeHelper.getLast30DaysRange());
        var last60Days = userReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(60));
        var last90Days = userReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(90));

        return KeyMetrics.builder()
            .metric("Accounts Created")
            .todayCount(countToday)
            .last30DayCount(last30Days)
            .last60DayCount(last60Days)
            .last90DayCount(last90Days)
            .build();
    }

    private KeyMetrics getLeads(List<String> dealerIds, String programId) {
        var countToday = leadReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(1));
        var last30Days = leadReportingService.getCount(programId, dealerIds, dateRangeHelper.getLast30DaysRange());
        var last60Days = leadReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(60));
        var last90Days = leadReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(90));

        return KeyMetrics.builder()
            .metric("Leads")
            .todayCount(countToday)
            .last30DayCount(last30Days)
            .last60DayCount(last60Days)
            .last90DayCount(last90Days)
            .build();
    }

    private KeyMetrics getSales(List<String> dealerIds, String programId) {
        var countToday = vehicleSaleReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(1));
        var last30Days = vehicleSaleReportingService.getCount(programId, dealerIds, dateRangeHelper.getLast30DaysRange());
        var last60Days = vehicleSaleReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(60));
        var last90Days = vehicleSaleReportingService.getCount(programId, dealerIds, dateRangeHelper.getLastNDaysRange(90));

        return KeyMetrics.builder()
            .metric("Sales")
            .todayCount(countToday)
            .last30DayCount(last30Days)
            .last60DayCount(last60Days)
            .last90DayCount(last90Days)
            .build();
    }

}
