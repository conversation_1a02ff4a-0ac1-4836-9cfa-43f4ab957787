package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.reporting.elasticsearch.service.UserReportingService;
import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.model.CustomerConversion;
import com.carsaver.partner.reporting.model.ReportingRequest;
import com.carsaver.partner.reporting.service.GoogleAnalyticsReportingService;
import com.carsaver.partner.search.facets.UserDocFacets;
import com.carsaver.search.model.LocalDateRange;
import com.carsaver.search.support.FacetInfoResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/reporting")
public class CustomerReportingController {

    private static final String PROFILE_GET_STARTED_URI = "/profile/get-started";

    @Autowired
    private UserReportingService userReportingService;

    @Autowired
    private GoogleAnalyticsReportingService googleAnalyticsReportingService;

    @Autowired
    private DateRangeHelper dateRangeHelper;

    @PostMapping("/{programId}/customer_conversion")
    public CustomerConversion getTotalCustomers(@PathVariable String programId, @RequestBody ReportingRequest reportingRequest) {

        var signUpsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : dateRangeHelper.getAllTimeRange();

        var pageViewsDateRange = dateRangeHelper.isDateRangeValid(reportingRequest.getDateRange())
                ? reportingRequest.getDateRange()
                : new LocalDateRange(dateRangeHelper.getBuyAtHomeStartDate(), LocalDate.now());



        var signups = userReportingService.getCount(programId, reportingRequest.getDealerIds(), signUpsDateRange);
        var pageViews = googleAnalyticsReportingService.getUniquePageviews(reportingRequest.getDealerIds(), pageViewsDateRange.getStart(), pageViewsDateRange.getEnd(), programId, PROFILE_GET_STARTED_URI);
        double rate = 0D;
        if (pageViews > 0) {
            rate = (signups / (double) pageViews * 100);
        }

        return CustomerConversion.builder()
            .totalSignups(signups)
            .uniquePageviews(pageViews)
            .conversionRate(rate)
            .build();
    }

    @PostMapping("/{programId}/customers/utm_campaign")
    public FacetInfoResult getCustomerUtmCampaigns(@PathVariable String programId, @RequestBody List<String> dealerIds) {
        return userReportingService.getFacet(programId, dealerIds, UserDocFacets::getUtmCampaigns);
    }

    @PostMapping("/{programId}/customers/dmas")
    public FacetInfoResult getCustomerDmas(@PathVariable String programId, @RequestBody List<String> dealerIds) {
        return userReportingService.getFacet(programId, dealerIds, UserDocFacets::getDmas);
    }

}
