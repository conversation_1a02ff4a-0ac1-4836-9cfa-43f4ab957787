package com.carsaver.partner.reporting.elasticsearch.service;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.CertificateDoc;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.pricing.LimitedPricesView;
import com.carsaver.magellan.model.pricing.PricesView;
import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.elasticsearch.facets.DealDocReportingFacets;
import com.carsaver.partner.reporting.elasticsearch.criteria.DealReportingSearchCriteria;
import com.carsaver.partner.reporting.model.AverageSaleOverTimeResponse;
import com.carsaver.partner.reporting.model.DealsOverTimeResponse;
import com.carsaver.search.facet.HistogramFacet;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.FacetParser;
import de.cronn.reflection.util.PropertyUtils;
import de.cronn.reflection.util.TypedPropertyGetter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class DealReportingService extends CriteriaSearchHandler<CertificateDoc> {

    @Autowired
    private DateRangeHelper dateRangeHelper;

    @Autowired
    public DealReportingService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    private FacetInfoResult getDealDocReportingFacet(String programId, List<String> dealerIds, TypedPropertyGetter<DealDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        var criteria = DealReportingSearchCriteria.builder()
                .dealerIds(dealerIds)
                .programId(programId)
                .createdDate(dateRange)
                .deleted(false)
                .build();

        return getDealDocReportingFacet(criteria, propertyGetter);
    }

    private FacetInfoResult getDealDocReportingFacet(DealReportingSearchCriteria criteria, TypedPropertyGetter<DealDocReportingFacets, ?> propertyGetter) {
        var facetName = PropertyUtils.getPropertyName(DealDocReportingFacets.class, propertyGetter);
        return this.facets(criteria, DealDocReportingFacets.class, facetName);
    }

    public DealsOverTimeResponse getCountByMonthWithinDateRange(String programId, List<String> dealerIds, TypedPropertyGetter<DealDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        var criteria = DealReportingSearchCriteria.builder()
                .dealerIds(dealerIds)
                .programId(programId)
                .createdDate(dateRange)
                .build();

        var facetResults = getDealDocReportingFacet(criteria,propertyGetter);
        var timeValuePairs = new ArrayList<Map.Entry<Long, Long>>();

        if (facetResults.getResults() instanceof ArrayList){
            var facetLists = (ArrayList<HistogramFacet>)facetResults.getResults();
            facetLists.forEach(element -> {
                var parsedDate = ZonedDateTime.parse(element.getName());
                var elementDate = LocalDate.from(parsedDate);
                var timestamp = Timestamp.valueOf(elementDate.atStartOfDay());
                timeValuePairs.add(new AbstractMap.SimpleEntry<>(timestamp.getTime(),element.getCount()));
            });
        }

        return new DealsOverTimeResponse(timeValuePairs);
    }

    public AverageSaleOverTimeResponse getSalesAverageByMonthWithinDateRange(String programId, List<String> dealerIds, ZonedDateRange dateRange) {
        var criteria = DealReportingSearchCriteria.builder()
                .dealerIds(dealerIds)
                .programId(programId)
                .createdDate(dateRange)
                .deleted(false)
                .build();
        var pageable = PageRequestUtils.maxSizeRequest(Sort.by("createdDate"));
        var result = search(criteria, pageable);
        var lineChartPairs = new ArrayList<Map.Entry<Long, Double>>();
        if(result.getContent().isEmpty()){
            return new AverageSaleOverTimeResponse(lineChartPairs);
        }

        var averageCounter = new AtomicInteger();
        var salePriceSum = new AtomicReference<>(BigDecimal.ZERO);

        var searchResults = result.getContent().toArray();
        for (int i = 0; i < searchResults.length; i++) {
            CertificateDoc element = (CertificateDoc) searchResults[i];
            var elementDate = LocalDate.from(element.getCreatedDate());

            Double salePrice = getSalePrice(element);
            if (salePrice != null) {
                salePriceSum.set(salePriceSum.get().add(BigDecimal.valueOf(salePrice)));
                averageCounter.set(averageCounter.incrementAndGet());
            }

            boolean isLastElement = false;
            boolean dayCalculationHasFinish = false;
            if(i+1 != searchResults.length){
                var nextElement = (CertificateDoc) searchResults[i+1];
                var nextElementDate = LocalDate.from(nextElement.getCreatedDate());
                dayCalculationHasFinish = !elementDate.isEqual(nextElementDate);
            }else{
                isLastElement = true;
            }
            
            if(isLastElement || dayCalculationHasFinish){
                try{
                    var lineChart = createLineChart(elementDate, averageCounter, salePriceSum, element);
                    lineChartPairs.add(lineChart);
                }catch (ArithmeticException ae){
                    log.debug("Certificate skipped due to pricing error, id:{}",element.getCertificateId());
                }
                averageCounter.set(0);
                salePriceSum.set(BigDecimal.ZERO);
            }


        }

        return new AverageSaleOverTimeResponse(lineChartPairs);
    }

    private AbstractMap.SimpleEntry<Long, Double> createLineChart(LocalDate currentDate, AtomicInteger averageCounter, AtomicReference<BigDecimal> salePriceSum, CertificateDoc element) {
        var timestamp = Timestamp.valueOf(currentDate.atStartOfDay());

        if(averageCounter.get()==0){
            Double salePrice = getSalePrice(element);
            if (salePrice != null){
                salePriceSum.set(salePriceSum.get().add(BigDecimal.valueOf(salePrice)));
                averageCounter.set(averageCounter.incrementAndGet());
            }
        }

        var avg = salePriceSum.get().doubleValue() / averageCounter.get();
        if(Double.isNaN(avg)){
            throw new ArithmeticException("Average value is NaN");
        }

        return new AbstractMap.SimpleEntry<>(timestamp.getTime(),avg);
    }

    private Double getSalePrice(CertificateDoc element) {
        var salePrice = Optional.ofNullable(element.getDealSheet())
                .map(DealSheet::getSalePrice)
                .orElse(null);

        if (salePrice == null){
            var carsaverPrice = Optional.ofNullable(element.getPrices())
                    .map(LimitedPricesView.PriceWrapper::getPrices)
                    .map(PricesView::getCarsaverPrice);
            if(carsaverPrice.isPresent()){
                salePrice = Double.valueOf(carsaverPrice.get());
            }
        }else{
            log.debug("SalePrice fetched from DealSheet");
        }
        return salePrice;
    }

    public FacetInfoResult getCountByDealTypeWithinDateRange(String programId, List<String> dealerIds, TypedPropertyGetter<DealDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        return getDealDocReportingFacet(programId, dealerIds, propertyGetter, dateRange);
    }

    public FacetInfoResult getCountByDealStockTypeWithinDateRange(String programId, List<String> dealerIds, TypedPropertyGetter<DealDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        var facetResults = getDealDocReportingFacet(programId, dealerIds, propertyGetter, dateRange);
        if (facetResults.getResults() instanceof ArrayList){
            var facetLists = (ArrayList<TermFacet>)facetResults.getResults();
            facetLists.stream().filter(element -> element.getId().equals("inventory"))
                    .forEach(element -> {
                        element.setId("new");
                        element.setName("new");
            });
        }

        return facetResults;
    }

    public FacetInfoResult getCountByDealTermWithinDateRange(String programId, List<String> dealerIds, TypedPropertyGetter<DealDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        return getDealDocReportingFacet(programId, dealerIds, propertyGetter, dateRange);
    }

    @Override
    protected String[] getSearchIndex() {
        return new String[]{"certificates"};
    }

}
