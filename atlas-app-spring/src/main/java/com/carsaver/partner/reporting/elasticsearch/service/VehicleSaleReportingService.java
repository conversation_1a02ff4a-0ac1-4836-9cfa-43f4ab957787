package com.carsaver.partner.reporting.elasticsearch.service;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.sale.VehicleSaleDoc;
import com.carsaver.magellan.model.lead.SaleStatus;
import com.carsaver.partner.elasticsearch.util.DateRangeHelper;
import com.carsaver.partner.reporting.elasticsearch.criteria.ReportLeadSearchCriteria;
import com.carsaver.partner.reporting.elasticsearch.criteria.ReportVehicleSaleSearchCriteria;
import com.carsaver.partner.reporting.elasticsearch.facets.VehicleSaleDocReportingFacets;
import com.carsaver.search.model.ZonedDateRange;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.FacetParser;
import de.cronn.reflection.util.PropertyUtils;
import de.cronn.reflection.util.TypedPropertyGetter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VehicleSaleReportingService extends CriteriaSearchHandler<VehicleSaleDoc> {

    private static final String[] VEHICLE_SALES_INDEX = {"vehicle-sales"};
    private static final String MARITZ_CREATE_SOURCE = "MARITZ";

    @Autowired
    private DateRangeHelper dateRangeHelper;

    @Autowired
    public VehicleSaleReportingService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    public FacetInfoResult getVehicleSaleDocReportingFacet(String programId, List<String> dealerIds, TypedPropertyGetter<VehicleSaleDocReportingFacets, ?> propertyGetter, ZonedDateRange dateRange) {
        var criteria = new ReportVehicleSaleSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setDeliveryDate(dateRange);
        criteria.setStatus(SaleStatus.VERIFIED);
        criteria.setCreatedSource(MARITZ_CREATE_SOURCE);

        // type safe way to get the field name we want
        String facetName = PropertyUtils.getPropertyName(VehicleSaleDocReportingFacets.class, propertyGetter);
        return this.facets(criteria, VehicleSaleDocReportingFacets.class, facetName);
    }

    public long getCount(String programId, List<String> dealerIds, ZonedDateRange dateRange) {
        var criteria = new ReportVehicleSaleSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setDeliveryDate(dateRange);
        criteria.setCreatedSource(MARITZ_CREATE_SOURCE);
        criteria.setStatus(SaleStatus.VERIFIED);
        return this.count(criteria);
    }

    public long getCountByUserIds(String programId, List<String> dealerIds, List<String> userIds) {
        var criteria = new ReportVehicleSaleSearchCriteria();
        criteria.setDealerIds(dealerIds);
        criteria.setProgramId(programId);
        criteria.setUserIds(userIds);
        criteria.setCreatedSource(MARITZ_CREATE_SOURCE);
        criteria.setStatus(SaleStatus.VERIFIED);
        return this.count(criteria);
    }

    public double getMonthOverMonthPercent(String programId, List<String> dealerIds) {
        var pastMonthCriteria = new ReportVehicleSaleSearchCriteria();
        pastMonthCriteria.setDealerIds(dealerIds);
        pastMonthCriteria.setProgramId(programId);
        pastMonthCriteria.setDeliveryDate(dateRangeHelper.getLast30DaysRange());

        var pastMonthCount = this.count(pastMonthCriteria);

        var previousMonthCriteria = new ReportVehicleSaleSearchCriteria();
        previousMonthCriteria.setDealerIds(dealerIds);
        previousMonthCriteria.setProgramId(programId);
        previousMonthCriteria.setDeliveryDate(dateRangeHelper.getPreviousMonthRange());

        var previousMonthCount = this.count(previousMonthCriteria);

        double rate;
        if(previousMonthCount == 0) {
            rate = (pastMonthCount - (double) previousMonthCount) * 100;
        } else {
            rate = (pastMonthCount - previousMonthCount) / (double) previousMonthCount * 100;
        }

        return rate;
    }

    // TODO: left this here because we'll need to show in the future
    public double getYearOverYearPercent(String programId, List<String> dealerIds) {
        var pastYearCriteria = new ReportVehicleSaleSearchCriteria();
        pastYearCriteria.setDealerIds(dealerIds);
        pastYearCriteria.setProgramId(programId);
        pastYearCriteria.setDeliveryDate(dateRangeHelper.getPastYearRange());

        var pastYearCount = this.count(pastYearCriteria);

        var previousYearCriteria = new ReportVehicleSaleSearchCriteria();
        previousYearCriteria.setDealerIds(dealerIds);
        previousYearCriteria.setProgramId(programId);
        previousYearCriteria.setDeliveryDate(dateRangeHelper.getPreviousYearRange());

        var previousYearCount = this.count(previousYearCriteria);

        double rate;
        if(previousYearCount == 0) {
            rate = (pastYearCount - (double) previousYearCount) * 100;
        } else {
            rate = (pastYearCount - previousYearCount) / (double) previousYearCount * 100;
        }

        return rate;
    }

    @Override
    protected String[] getSearchIndex() {
        return VEHICLE_SALES_INDEX;
    }
}
