package com.carsaver.partner.reporting.elasticsearch.facets;

import com.carsaver.search.annotation.Aggregate;
import com.carsaver.search.annotation.CardinalityAgg;
import com.carsaver.search.facet.TermFacet;
import com.carsaver.search.support.DocFacets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleSaleDocReportingFacets implements DocFacets {

    @Aggregate(field = "vehicle.stockType", uniqueField = "user.id")
    private List<TermFacet> stockTypes;

    @CardinalityAgg(field = "user.id")
    private Long uniqueUserVehicleSaleCount;

    @Aggregate(field = "sale.saleType")
    private List<TermFacet> saleTypes;

    @Aggregate(field = "sale.lenderName")
    private List<TermFacet> lenders;

    @Aggregate(field = "vehicle.model")
    private List<TermFacet> models;

    @Aggregate(field = "vehicle.model")
    private List<TermFacet> salesCount;

}
