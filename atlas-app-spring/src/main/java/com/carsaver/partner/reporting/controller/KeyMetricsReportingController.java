package com.carsaver.partner.reporting.controller;

import com.carsaver.partner.reporting.model.KeyMetrics;
import com.carsaver.partner.reporting.service.KeyMetricsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/reporting")
public class KeyMetricsReportingController {

    @Autowired
    private KeyMetricsService keyMetricsService;

    @PostMapping("/{programId}/keyMetrics")
    public List<KeyMetrics> getGrowthRates(@PathVariable String programId, @RequestBody List<String> dealerIds) {
        return keyMetricsService.getKeyMetrics(dealerIds, programId);
    }

}
