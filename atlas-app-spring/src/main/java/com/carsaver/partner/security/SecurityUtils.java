package com.carsaver.partner.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

@Slf4j
public abstract class SecurityUtils {

    public static boolean isDealerUser() {
        return hasRole("ROLE_DEALER");
    }

    public static boolean isProgramUser() {
        return hasRole("ROLE_PROGRAM");
    }

    public static boolean isAdminUser() {
        return hasRole("ROLE_ADMIN");
    }

    private static boolean hasRole(String role) {
        log.info("Checking is user in role: {}", role);
        // get security context from thread local
        SecurityContext context = SecurityContextHolder.getContext();
        if (context == null) {
            return false;
        }

        Authentication authentication = context.getAuthentication();
        if (authentication == null) {
            return false;
        }

        for (GrantedAuthority auth : authentication.getAuthorities()) {
            log.debug("Checking GrantedAuthority: {}", auth);
            if (role.equals(auth.getAuthority())) {
                return true;
            }
        }

        return false;
    }

}
