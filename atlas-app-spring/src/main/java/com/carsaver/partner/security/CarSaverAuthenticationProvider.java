package com.carsaver.partner.security;

import com.carsaver.magellan.api.ActivityService;
import com.carsaver.magellan.auth.CarSaverAuthService;
import com.carsaver.magellan.auth.CarSaverJWTToken;
import com.carsaver.magellan.auth.CarSaverUserDetails;
import com.carsaver.magellan.auth.PrincipleService;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.DealerContractClient;
import com.carsaver.magellan.client.DealerPermissionsClient;
import com.carsaver.magellan.client.ProgramPermissionClient;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.model.ActivityEventView;
import com.carsaver.magellan.model.security.DealerPermissionView;
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation;
import com.carsaver.magellan.model.user.ProgramRoleUserAssociation;
import com.carsaver.partner.reporting.service.ProgramService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.hateoas.CollectionModel;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CarSaverAuthenticationProvider implements AuthenticationProvider {

    @Value("${tenant.portal-id}")
    private String portalTenantId;

    @Value("${spring.security.oauth2.client.provider.carsaver.jwk-set-uri}")
    private String jwkSetUri;

    private final SimpleGrantedAuthority ROLE_PROGRAM = new SimpleGrantedAuthority("ROLE_PROGRAM");

    @Autowired
    private CarSaverAuthService carSaverAuthService;

    @Autowired
    private BasicUserAssociationClient basicUserAssociationClient;

    @Autowired()
    private DealerPermissionsClient dealerPermissionsClient;

    private NimbusJwtDecoder decoder;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private DealerContractClient dealerContractClient;

    @Autowired
    private ProgramSubscriptionClient programSubscriptionClient;

    @Autowired
    private PrincipleService principleService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramPermissionClient programPermissionClient;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String email = authentication.getName();
        String password = (String) authentication.getCredentials();

        List<SimpleGrantedAuthority> grantedAuthorities;
        TokenResponse tokenResponse;
        try {
            tokenResponse = carSaverAuthService.login(portalTenantId, email, password);

            Jwt jwt = getDecoder().decode(tokenResponse.getAccessToken());

            ArrayList<String> authorities = (ArrayList<String>) jwt.getClaims().get("authorities");
            grantedAuthorities = authorities.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
        } catch (Exception e) {
            throw new BadCredentialsException("1000");
        }

        ProgramRoleUserAssociation programPermissions = programPermissionClient.findByUserId(tokenResponse.getId());
        List<Integer> finalProgramPermission = null;
        if (programPermissions != null) {
            finalProgramPermission = programPermissions.getPermissions();
        }

        Collection<BasicDealerUserRoleAssociation> privileges = getDealersPrivileges(grantedAuthorities, tokenResponse, finalProgramPermission);

        CarSaverJWTToken token = new CarSaverJWTToken(tokenResponse, grantedAuthorities);
        CarSaverUserDetails userDetails = new CarSaverUserDetails(tokenResponse.getId(), privileges, grantedAuthorities);

        if (grantedAuthorities.contains(ROLE_PROGRAM)) {
            userDetails.setProgramPermissions(setupProgramRolePrivileges(programPermissions));
        }

        token.setPrincipal(userDetails);
        token.setAuthenticated(true);

        trackLoginIfAdmin(tokenResponse, grantedAuthorities);

        return token;
    }

    private Map<String, List<String>> setupProgramRolePrivileges(ProgramRoleUserAssociation programPermissions) {
        Map<String, List<String>> permissions = new HashMap<>();
        if (programPermissions != null && programPermissions.getPermissions() != null) {
            CollectionModel<DealerPermissionView> permissionViews = dealerPermissionsClient.findPermissions(programPermissions.getPermissions());
            if (permissionViews != null) {
                List<String> permissionNameList = new ArrayList<>();
                permissionViews.forEach(permission -> permissionNameList.add(permission.getName()));
                permissions.put(programPermissions.getUserId(), permissionNameList);
            }
        }
        return permissions;
    }

    private Collection<BasicDealerUserRoleAssociation> getDealersPrivileges(List<SimpleGrantedAuthority> grantedAuthorities, TokenResponse tokenResponse, List<Integer> finalProgramPermissions) {
        if (grantedAuthorities.contains(ROLE_PROGRAM)) {
            return getProgramPrivileges(tokenResponse, finalProgramPermissions);
        }

        CollectionModel<BasicDealerUserRoleAssociation> userPrivileges = basicUserAssociationClient.findByUserId(tokenResponse.getId());
        return Optional.ofNullable(userPrivileges).map(CollectionModel::getContent).orElse(Collections.emptyList());
    }

    private Collection<BasicDealerUserRoleAssociation> getProgramPrivileges(TokenResponse tokenResponse, List<Integer> finalProgramPermissions) {

        var dealerIds = programService.getDealerIdsFromProgramUserId(tokenResponse.getId());

        Collection<BasicDealerUserRoleAssociation> programPrivileges = new ArrayList<>();
        dealerIds.forEach(id -> {
            var dealer = new BasicDealerUserRoleAssociation();
            dealer.setDealerId(id);
            dealer.setPermissions(finalProgramPermissions != null ? finalProgramPermissions : new ArrayList<>());
            dealer.setUserId(tokenResponse.getId());
            dealer.setDealerClient(dealerClient);
            dealer.setDealerContractClient(dealerContractClient);
            dealer.setDealerPermissionsClient(dealerPermissionsClient);
            dealer.setPrincipleService(principleService);
            dealer.setProgramSubscriptionClient(programSubscriptionClient);
            programPrivileges.add(dealer);
        });

        return programPrivileges;
    }

    // admin users should be logging into portal via auto/sso login flow and thus
    // shouldn't be authenticated via this provider (username,password), however, it's still technically possible for them to do so
    // for the time being track these occasions
    private void trackLoginIfAdmin(TokenResponse tokenResponse, List<SimpleGrantedAuthority> grantedAuthorities) {
        try {
            if (grantedAuthorities != null && grantedAuthorities.stream().anyMatch(sga -> sga.getAuthority() != null && sga.getAuthority().equalsIgnoreCase("ROLE_ADMIN"))) {
                ActivityEventView activityEvent = new ActivityEventView();
                activityEvent.setUserId(tokenResponse.getId());
                activityEvent.setEventType("login");
                activityEvent.setEventName("atlas-direct-admin-login");
                activityEvent.setDescription("Event which tracks when admins directly login to atlas");
                activityEvent.setEventTime(ZonedDateTime.now());

                activityService.postActivityEvent(activityEvent);
            }
        } catch (Exception e) {
            log.error("error tracking login", e);
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.isAssignableFrom(UsernamePasswordAuthenticationToken.class);
    }

    private NimbusJwtDecoder getDecoder() {
        if (this.decoder == null) {
            this.decoder = NimbusJwtDecoder.withJwkSetUri(jwkSetUri).build();
        }

        return this.decoder;
    }
}
