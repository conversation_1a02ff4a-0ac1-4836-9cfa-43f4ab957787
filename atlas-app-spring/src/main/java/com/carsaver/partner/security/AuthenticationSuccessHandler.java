package com.carsaver.partner.security;

import com.carsaver.magellan.api.ActivityService;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.request.user.LastLoginUpdateRequest;
import com.carsaver.magellan.model.ActivityEventView;
import com.carsaver.magellan.model.UserView;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.DefaultSavedRequest;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class AuthenticationSuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

    static final String SAVED_REQUEST = "SPRING_SECURITY_SAVED_REQUEST";
    final UserClient userClient;
    private final ActivityService activityService;

    public AuthenticationSuccessHandler(UserClient userClient, ActivityService activityService) {
        this.userClient = userClient;
        this.activityService = activityService;
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        Optional<UserView> loggedUser = getLoggedUser();
        loggedUser.ifPresent(userView -> {
            ZonedDateTime currentTime = ZonedDateTime.now();

            // updating last login date for user
            userClient.updateLastLoginAt(userView.getId(), LastLoginUpdateRequest.builder().lastLoginAt(currentTime).build());

            // post login activity event
            ActivityEventView activityEvent = new ActivityEventView();
            activityEvent.setUserId(loggedUser.get().getId());
            activityEvent.setEventType("login");
            activityEvent.setEventName("login-success");
            activityEvent.setDescription("Atlas User log in success");
            activityEvent.setMetadata(createEventMetadata(userView, currentTime));
            activityService.postActivityEvent(activityEvent);
        });

        // gets the cache URL, determines if a user should be redirected to their intended destination after logging in
        Object savedRequest = request.getSession().getAttribute(SAVED_REQUEST);
        if (savedRequest != null) {
            DefaultSavedRequest defaultSavedRequest = (DefaultSavedRequest) savedRequest;
            response.sendRedirect(defaultSavedRequest.getRedirectUrl());
        }

        super.onAuthenticationSuccess(request, response, authentication);
    }


    private Optional<UserView> getLoggedUser() {
        Optional<String> userId = AuthUtils.getUserIdFromSecurityContext();
        if (userId.isEmpty()){
            return Optional.empty();
        }

        UserView user = userClient.findById(userId.get());
        return Optional.ofNullable(user);
    }

    private Map<String, Object> createEventMetadata(UserView userView, ZonedDateTime currentTime) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("fullName", userView.getFullName());
        metadata.put("email", userView.getEmail());
        metadata.put("role", userView.getType());
        metadata.put("lastLoginAt", currentTime.withZoneSameInstant(ZoneId.of("UTC")).toString());

        ZonedDateTime lastLoginAt = Objects.requireNonNullElse(userView.getLastLoginAt(), currentTime);
        metadata.put("hoursSinceLastLogin", Duration.between(lastLoginAt, currentTime).toHours());
        return metadata;
    }
}
