package com.carsaver.partner.elasticsearch;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.sale.VehicleSaleDoc;
import com.carsaver.search.support.FacetParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class VehicleSaleDocService extends CriteriaSearchHandler<VehicleSaleDoc> {

    private static final String[] SEARCH_INDEX = {"vehicle-sales"};

    @Autowired
    public VehicleSaleDocService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser) {
        super(elasticClient, facetParser);
    }

    @Override
    protected String[] getSearchIndex() {
        return SEARCH_INDEX;
    }

}
