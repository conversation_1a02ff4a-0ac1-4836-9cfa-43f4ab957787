package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class VehicleDealerProgramPair extends AbstractSearchCriteria {

    @TermQuery(field = "dealer.id")
    private String dealerId;

    @TermQuery(field = "programIds")
    private Set<String> onlyLivePrograms;
}
