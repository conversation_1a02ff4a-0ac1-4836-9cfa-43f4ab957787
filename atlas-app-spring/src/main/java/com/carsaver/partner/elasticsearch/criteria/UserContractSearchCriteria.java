package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class UserContractSearchCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "userId", scope = SearchScope.POST_FILTER)
    private String userId;

    @TermQuery(field = "email", scope = SearchScope.POST_FILTER)
    private String email;

    @TermQuery(field = "dealerId", scope = SearchScope.POST_FILTER)
    private List<String> dealerIds;

    @TermQuery(field = "certificateId", scope = SearchScope.POST_FILTER)
    private String certificateId;

    @TermQuery(field = "vehicle.vin", scope = SearchScope.POST_FILTER)
    private String vin;

    @TermQuery(field = "id", scope = SearchScope.POST_FILTER)
    private String id;
}
