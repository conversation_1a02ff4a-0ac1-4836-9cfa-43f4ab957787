package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.elasticsearch.criteria.UserSearchCriteria;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserLiveProgramSearchCriteria extends UserSearchCriteria {

    @TermQuery(field = "program.id", scope = SearchScope.POST_FILTER)
    private List<String> programs;

    /**
     * Forced check to ensure we only list customers for programs that the dealer is live on now.
     */
    @TermQuery(field = "program.id")
    private List<String> onlyLivePrograms;

    /**
     * Always force test tags to be excluded. Doing it this way allows for us to still filter on
     * tags apart from this forced exclusion if desired.
     */
    @Setter(AccessLevel.NONE)
    @TermQuery(field = "tags", boolQuery = BoolQueryOccurrence.MUST_NOT, scope = SearchScope.QUERY)
    private String excludeTestTag = "test";
}
