package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.search.annotation.BoolConfig;
import com.carsaver.search.annotation.BoolQuery;
import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@BoolConfig(minimumShouldMatch = 1)
@ToString(callSuper = true)
public class DealerUserCustomerSearchCriteria extends CustomersSearchCriteria {

    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private EligibleProspectByDealerSearchCriteria eligibleProspectByDealerSearchCriteria;

    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private DealerLinkCustomerSearchCriteria dealerLinkCustomerSearchCriteria;

    @Data
    @ToString(callSuper = true)
    public static class DealerLinkCustomerSearchCriteria extends AbstractSearchCriteria {
        @TermQuery(field = "dealerLinks.dealerId", scope = SearchScope.QUERY)
        private List<String> dealerLinks;
    }
}
