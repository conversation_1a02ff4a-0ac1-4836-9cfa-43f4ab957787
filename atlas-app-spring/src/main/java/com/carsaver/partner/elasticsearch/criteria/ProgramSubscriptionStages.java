package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor()
public class ProgramSubscriptionStages extends AbstractSearchCriteria {

    @TermQuery(
            field = "programSubscriptionStages.stageName"
    )
    private String programSubscriptionStagesStageName;

    @TermQuery(
            field = "programSubscriptionStages.dealerId"
    )
    private String programSubscriptionStagesDealerId;
}
