package com.carsaver.partner.elasticsearch.criteria;


import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class UserDealerProgramPair extends AbstractSearchCriteria {
    @TermQuery(field = "program.id")
    private Set<String> onlyLivePrograms;
}
