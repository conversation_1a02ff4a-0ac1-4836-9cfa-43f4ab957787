package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.elasticsearch.criteria.UserSearchCriteria;
import com.carsaver.search.annotation.BoolQuery;
import com.carsaver.search.query.BoolQueryOccurrence;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProgramUserByDealerCustomerSearchCriteria extends UserSearchCriteria {

    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private EligibleProspectByDealerSearchCriteria eligibleProspectByDealerSearchCriteria;

    @BoolQuery(boolQuery = BoolQueryOccurrence.SHOULD)
    private CustomersSearchCriteria customersSearchCriteria;

}
