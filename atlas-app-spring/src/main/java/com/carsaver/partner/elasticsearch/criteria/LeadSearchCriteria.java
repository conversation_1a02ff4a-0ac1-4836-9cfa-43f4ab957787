package com.carsaver.partner.elasticsearch.criteria;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.SearchScope;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class LeadSearchCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "user.id", scope = SearchScope.POST_FILTER)
    private String userId;

    @TermQuery(field = "dealer.id", scope = SearchScope.POST_FILTER)
    private List<String> dealerIds;

    @TermQuery(field = "docType.keyword", scope = SearchScope.POST_FILTER)
    private String docType;
}
