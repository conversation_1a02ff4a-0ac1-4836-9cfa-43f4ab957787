package com.carsaver.partner.web.api.training.dealer.model.byUser;

import com.carsaver.magellan.model.trainingvideo.TrainingCourseView;
import com.carsaver.magellan.model.trainingvideo.TrainingVideoRecordView;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Data
@Builder
class TrainingCourse {

    private String id;
    private String title;
    private int sort;
    private List<TrainingVideo> videos;

    public static TrainingCourse from(String userId, TrainingCourseView course, Collection<TrainingVideoRecordView> userRecords) {
        var builder = TrainingCourse.builder()
                .id(course.getId())
                .sort(course.getSort())
                .title(course.getTitle());

        List<TrainingVideo> trainingVideos = new LinkedList<>();
        course.getTrainingVideos().forEach((video) ->{
            var completedAt = userRecords.stream()
                    .filter((userRecord) -> Objects.equals(userId,userRecord.getUserId()) && Objects.equals(video.getId(),userRecord.getVideoId()))
                    .findFirst()
                    .map(TrainingVideoRecordView::getCompletedAt);

            var trainingVideo = TrainingVideo.builder()
                    .videoId(video.getId())
                    .sort(video.getSort())
                    .title(video.getTitle())
                    .completedAt(completedAt.orElse(null));

            trainingVideos.add(trainingVideo.build());
        });

        builder.videos(trainingVideos);
        return builder.build();
    }
}
