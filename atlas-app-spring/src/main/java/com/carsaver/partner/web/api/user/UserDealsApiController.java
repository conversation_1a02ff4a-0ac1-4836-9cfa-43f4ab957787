package com.carsaver.partner.web.api.user;

import com.carsaver.magellan.BadRequestException;
import com.carsaver.magellan.api.deal.CertificateService;
import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.client.CampaignClient;
import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.partner.client.inventory.VehicleForProgramsClient;
import com.carsaver.partner.exception.DealNotFoundException;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.ClonedDealResponse;
import com.carsaver.partner.model.desking.VehicleModelForPrograms;
import com.carsaver.partner.model.desking.VehicleUpdateModel;
import com.carsaver.partner.model.user.UserVehicle;
import com.carsaver.partner.service.FinancierUtilsService;
import com.carsaver.partner.service.UserVehicleService;
import com.carsaver.partner.service.desking.CloneDealService;
import com.carsaver.partner.service.desking.CopyDealService;
import com.carsaver.partner.service.desking.standard.UpdateDealService;
import com.carsaver.partner.web.api.CustomerBaseController;
import com.carsaver.partner.web.api.SecurityHelperService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
public class UserDealsApiController implements CustomerBaseController {

    @Autowired
    private UserVehicleService userVehicleService;

    @Autowired
    private SecurityHelperService securityHelperService;

    @Autowired
    private CloneDealService cloneDealService;

    @Autowired
    FinancierUtilsService financierUtilsService;

    @Autowired
    private CampaignClient campaignClient;

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private CopyDealService copyDealService;

    @Autowired
    private CertificateClient certificateClient;

    @Autowired
    CertificateService certificateService;

    @Autowired
    UpdateDealService updateDealService;

    @Autowired
    private VehicleForProgramsClient vehicleForProgramsClient;

    @GetMapping("/api/users/{userId}/deals")
    public Page<UserVehicle> getDeals(@RequestParam(value = "dealerIds", required = false) List<String> dealerIds,
                                      @RequestParam(value = "programIds", required = false) List<String> programIds,
                                      @PathVariable String userId,
                                      HttpSession httpSession,
                                      @PageableDefault(sort = "createdAt", direction = Sort.Direction.DESC) Pageable pageable) {
        if (!CollectionUtils.isEmpty(programIds) && CollectionUtils.isEmpty(dealerIds)) {
            return userVehicleService.getDealsByProgram(userId, programIds, pageable);
        }
        final var finalDealerIds = getFinalListOfDealerAndValidateCustomerPermissions(dealerIds, httpSession, securityHelperService);
        return userVehicleService.getDeals(userId, finalDealerIds, pageable);
    }

    /**
     *  Old deal editing tool.
     *  Only used for Cash deals
     */
    @PostMapping("/api/dealer/{dealer}/users/{userId}/deals/{deal}/copy")
    public UserVehicle copyDeal(@PathVariable DealerView dealer, @PathVariable String userId, @PathVariable CertificateView deal,
                                @RequestParam("newDealName") String newDealName) throws Exception {

        UserVehicle userVehicle =  copyDealService.copyDeal(dealer, userId, deal, newDealName);
        return userVehicle;
    }

    /**
     *  New deal editing tool.
     *  Will copy the existing deal and add it to the clone table
     */
    @PostMapping("/api/dealer/{dealer}/users/{userId}/deals/{deal}/save")
    public ResponseEntity<UserVehicle> saveDeal(@PathVariable DealerView dealer, @PathVariable String userId, @PathVariable CertificateView deal
            , @RequestParam("newDealName") String newDealName, @RequestParam("newDealNote") String newDealNote
            , @RequestParam("newDealDisplayNote") Boolean newDealDisplayNote) throws Exception {

        ResponseEntity<UserVehicle> response;
        try {
            UserVehicle userVehicle = copyDealService.saveCopyDeal(dealer, userId, deal, newDealName, newDealNote, newDealDisplayNote);
            response = ResponseEntity.ok(userVehicle);
        }
        catch (Exception e) {
            response = ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }

        return response;
    }

    @GetMapping(value = "/api/dealer/clone/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ClonedDealResponse> retrieveClonedDealById(@PathVariable Long id){
        ClonedDealResponse result = cloneDealService.retrieveClonedDealById(id);
        ResponseEntity<ClonedDealResponse> response = ResponseEntity.ok(result);
        return response;
    }

    @GetMapping(value = "/api/dealer/clone/certificate/{certificateId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ClonedDealResponse> retrieveClonedDealByCertificateId(@PathVariable Integer certificateId){
        ClonedDealResponse result = cloneDealService.retrieveClonedDealByCertificateId(certificateId);
        if (result == null) {
            log.error("Cloned Deal Not Found for certificateId {}", certificateId);
            throw new DealNotFoundException("Cloned Deal Not Found for certificateId " + certificateId);
        }
        ResponseEntity<ClonedDealResponse> response = ResponseEntity.ok(result);
        return response;
    }

    @PostMapping(value = "/api/dealer/clone/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> upsertDeal(@RequestBody CloneDealRequest dealDeskingRequest) {
        ResponseEntity<?> responseEntity;
        try {
            ClonedDealResponse dealResponse = updateDealService.updateEditedDeal(dealDeskingRequest);
            responseEntity = ResponseEntity.ok(dealResponse);
        } catch (Exception ex) {
            log.error("Clone Deal Update Exception when saving updated data for deal: {}, error: {} ", dealDeskingRequest.getId(), ex.getMessage());
            responseEntity = ResponseEntity.badRequest().body("An error has occurred");
        }

        return responseEntity;
    }

    @PostMapping(value = "/api/dealer/clone/lenders")
    public List<String> fetchLendersByCampaignDealer(@Valid @RequestParam Integer certificateId, @Valid @RequestParam String dealerId) {
        CertificateView certificate = certificateClient.findById(certificateId);
        Source source = certificate.getSource();
        String campaignId = source.getCampaignId();
        CampaignView campaign = campaignClient.findById(campaignId);
        DealerView dealer = dealerClient.findById(dealerId);
        List<String> financiers = financierUtilsService.getFinanciersByDealer(campaign, dealer);
        return financiers;
    }


    @GetMapping(value = "/api/vehicle/retrieve", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> retrieveVehicle(@RequestParam String dealerId, @RequestParam(required = false) String stockNumber, @RequestParam(required = false) String vin,@RequestParam(required = false) String userId) {
        try {
            VehicleView vehicleView = cloneDealService.retrieveVehicle(dealerId, stockNumber, vin);

            VehicleUpdateModel vehicleUpdateModel = cloneDealService.buildVehicleUpdateModel(vehicleView,userId);

            return ResponseEntity.ok(vehicleUpdateModel);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Error: " + e.getMessage());
        }
    }

    @SneakyThrows
    @GetMapping(value = "/api/vehicle/retrieve/byProgramWithActiveProgramSubscriptions", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<VehicleModelForPrograms> retrieveVehicleForProgramUser(@RequestParam String programId,
                                                                                           @RequestParam(required =false) String dealerId,
                                                                                           @RequestParam(required = false) String stockNumber,
                                                                                           @RequestParam(required = false) String vin) {
        if(StringUtils.isEmpty(vin) && StringUtils.isEmpty(stockNumber)){
            throw new BadRequestException("Please provide Vin or StockNumber");
        }

        return ResponseEntity.ok(vehicleForProgramsClient.retrieveVehicleForProgramService(programId, dealerId, vin, stockNumber));
    }

    @PostMapping(value = "/api/dealer/clone/reCalculate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> reCalculateDeal(@RequestBody CloneDealRequest dealDeskingRequest) {
        ResponseEntity<?> responseEntity;

        try {
            ClonedDealResponse dealResponse = updateDealService.reCalculateLowestQuotes(dealDeskingRequest);
            responseEntity = ResponseEntity.ok(dealResponse);
        }
        catch (NotFoundException ex) {
            responseEntity = ResponseEntity.status(HttpStatus.NOT_FOUND).body("No quote found");
        }
        catch (Exception ex) {
            log.error("Clone Deal Exception when recalculating quote for deal: {}, error: {} ", dealDeskingRequest.getId(), ex);
            responseEntity = ResponseEntity.badRequest().body("An error has occurred. Please check logs");
        }

        return responseEntity;
    }
}
