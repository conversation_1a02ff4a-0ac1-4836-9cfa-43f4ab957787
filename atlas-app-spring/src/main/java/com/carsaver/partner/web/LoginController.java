package com.carsaver.partner.web;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.user.UserPreferences;
import com.carsaver.partner.exception.BadRequestException;
import com.carsaver.partner.filter.DealerUserAccessFilter;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.MixpanelClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpSession;
import java.security.Principal;
import java.util.List;
import java.util.Optional;

@Slf4j
@Controller
public class LoginController {

    @Autowired
    UserClient userClient;

    @Autowired
    private MixpanelClient mixpanelClient;

    @GetMapping({"/login", "/forgot", "/update-password"})
    public String login() {
        return "login";
    }

    @GetMapping(value = "/login-success")
    public String redirect(Principal principal, HttpSession session) {
        if(principal == null) {
            log.error("Principal was not found");
            return "redirect:/login?error";
        }
        log.info("Got: {}", principal);
        final Optional<UserView> loggedUserOpt = getLoggedUser();
        if(loggedUserOpt.isEmpty()){
            return "redirect:/";
        }

        mixpanelClient.updateUser(loggedUserOpt.get());
        mixpanelClient.track("Login");

        if(SecurityUtils.isProgramUser()){
            return redirectProgramUser();
        }

        if(SecurityUtils.isDealerUser()){
            List<DealerView> dealers = (List<DealerView>)Optional.ofNullable(session.getAttribute(DealerUserAccessFilter.DEALER_LIST))
                    .orElseThrow(()-> new BadRequestException("Dealer list should be set If the logged in user has the role DEALER"));
            return redirectDealerUser(dealers, loggedUserOpt);
        }

        return "redirect:/";
    }

    private String redirectDealerUser(List<DealerView> dealers, Optional<UserView> loggedUserOpt) {
        Optional<String> previousSelectedDealerId = getPreviousSelectedDealerId(loggedUserOpt);
        if (previousSelectedDealerId.isPresent()
                && canUserAccessDealer(previousSelectedDealerId.get(), dealers)) {
            return String.format("redirect:/dealer/details?dealerIds=%s", previousSelectedDealerId.get());
        }

        return "redirect:/";
    }

    private String redirectProgramUser() {
        return "redirect:/";
    }

    private Optional<UserView> getLoggedUser() {
        Optional<String> userId = AuthUtils.getUserIdFromSecurityContext();
        if (userId.isEmpty()){
            return Optional.empty();
        }

        UserView user = userClient.findById(userId.get());
        return Optional.ofNullable(user);
    }

    private Optional<String> getPreviousSelectedDealerId(Optional<UserView> user) {
        return user
            .map(UserView::getUserPreferences)
            .map(UserPreferences::getAtlasPreferences)
            .map(UserPreferences.AtlasPreferences::getLastSelectedDealerId);
    }

    private boolean canUserAccessDealer(String dealerId, List<DealerView> dealers){
        return dealers.stream().anyMatch(dealer -> dealer.getId().equals(dealerId));
    }

}
