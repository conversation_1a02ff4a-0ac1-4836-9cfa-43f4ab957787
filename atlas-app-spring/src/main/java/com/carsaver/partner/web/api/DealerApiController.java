package com.carsaver.partner.web.api;

import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.DealerContractClient;
import com.carsaver.magellan.client.DealerEmailClient;
import com.carsaver.magellan.client.DealerFeeClient;
import com.carsaver.magellan.client.DealerFinancierClient;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.client.SendGridEventClient;
import com.carsaver.magellan.client.vendor.SendGridClient;
import com.carsaver.magellan.client.vendor.SendGridDeleteRequest;
import com.carsaver.magellan.client.vendor.SendGridSuppressionResponse;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.SendGridEventView;
import com.carsaver.magellan.model.contract.DealerContractView;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.magellan.model.dealer.DealerFinancierModel;
import com.carsaver.magellan.model.dealer.DealerFinancierModelView;
import com.carsaver.magellan.model.financiers.DealerFinanciersRequest;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.magellan.model.preferences.DealerPreferences;
import com.carsaver.partner.model.DealerEmail;
import com.carsaver.partner.model.DealerFeeModel;
import com.carsaver.partner.model.DealerHours;
import com.carsaver.partner.model.DealerInfo;
import com.carsaver.partner.model.metrics.DealerMetrics;
import com.carsaver.partner.service.DealerMetricsService;
import com.carsaver.partner.service.LenderDeskService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.support.SanitizeInput;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.hateoas.CollectionModel;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/dealer")
@RequiredArgsConstructor
public class DealerApiController implements BaseListController {

    private final DealerFeeClient dealerFeeClient;

    private final DealerClient dealerClient;

    private final SendGridEventClient sendGridEventClient;

    private final DealerEmailClient dealerEmailClient;

    private final SendGridClient sendGridClient;

    private final DealerMetricsService dealerMetricsService;

    private final DealerContractClient dealerContractClient;

    private final ProgramSubscriptionClient programSubscriptionClient;

    private final SecurityHelperService securityHelperService;

    private final HttpSession session;

    private final SplitFeatureFlags splitFeatureFlags;

    private final DealerFinancierClient dealerFinancierClient;

    private final LenderDeskService lenderDeskService;


    @PreAuthorize("hasPermission(#dealer, 'dealer:read')")
    @GetMapping("/{dealer}/dealer-info")
    public ResponseEntity<DealerInfo> getDealerInfo(@PathVariable DealerView dealer) {
        return ResponseEntity.of(Optional.ofNullable(dealer).map(DealerInfo::from));
    }

    // get dealer hours of operation
    @PreAuthorize("hasPermission(#dealer, 'dealer:read')")
    @GetMapping("/{dealer}/dealer-hours")
    public ResponseEntity<DealerHours> getDealerHours(@PathVariable DealerView dealer) {
        return ResponseEntity.of(Optional.ofNullable(dealer).map(DealerHours::from));
    }

    @GetMapping("/{dealerId}/dealer-metrics")
    public DealerMetrics fetchDealerMetrics(@PathVariable String dealerId) {
        return dealerMetricsService.fetchDealerMetrics(dealerId);
    }

    @PreAuthorize("hasPermission(#dealer, 'dealer:read')")
    @GetMapping("/{dealer}/dealer-fees")
    public Collection<DealerFeeView> fetchDealerFees(@PathVariable DealerView dealer) {
        return Optional.ofNullable(dealerFeeClient.findByDealerId(dealer.getId())).map(CollectionModel::getContent).orElse(null);
    }

    @PreAuthorize("hasPermission(#dealer, 'dealer:read')")
    @GetMapping("/{dealer}/dealer-lenders")
    public Collection<DealerFinancierModel> fetchDealerLenders(@PathVariable DealerView dealer) {
        return Optional.ofNullable(dealerClient.findDealerFinanciersByDealer(dealer.getId())).orElse(Collections.emptyList());
    }

    @SanitizeInput
    @PreAuthorize("hasPermission(#dealer, 'dealer:edit') or hasPermission(#dealer, 'inventory:new-pricing:edit')  or hasPermission(#dealer, 'inventory:used-pricing:edit')")
    @PostMapping("/{dealer}/dealer-fees")
    public DealerFeeView saveDealerFee(@NonNull @PathVariable DealerView dealer, @RequestBody @Valid DealerFeeModel dealerFee) {
        dealerFee.setDealerId(dealer.getId());
        return dealerFeeClient.save(dealerFee.toDealerFeeView());
    }

    @PreAuthorize("hasPermission(#dealer, 'dealer:edit') or hasPermission(#dealer, 'inventory:new-pricing:edit')  or hasPermission(#dealer, 'inventory:used-pricing:edit')")
    @DeleteMapping("/{dealer}/dealer-fees/{dealerFeeId}")
    public void deleteDealerFee(@NonNull @PathVariable DealerView dealer, @PathVariable String dealerFeeId) {
        dealerFeeClient.deleteById(dealerFeeId);
    }

    @PreAuthorize("hasPermission(#dealer, 'dealer:read')")
    @GetMapping("/{dealer}/email-addresses")
    public List<DealerEmail> getEmailAddresses(@NonNull @PathVariable DealerView dealer) {
        List<DealerEmail> dealerEmails = dealerEmailClient.findDealerEmails(dealer.getId()).getContent()
            .stream()
            .map(DealerEmail::from)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // Add status and bounces
        dealerEmails.forEach(dealerEmail -> {
            dealerEmail.setSendGridStatus(getEventStatus(dealerEmail.getEmail()));
            dealerEmail.setBounceReason(checkBouncesFor(dealerEmail.getEmail()));
        });

        return dealerEmails;
    }

    @PreAuthorize("hasPermission(#dealer, 'dealer:read')")
    @DeleteMapping("/{dealer}/emails/{email}/bounce")
    public ResponseEntity<String> removeEmailFromBounceList(@PathVariable DealerView dealer, @PathVariable String email) {
        SendGridDeleteRequest sendGridDeleteRequest = new SendGridDeleteRequest();
        sendGridDeleteRequest.setEmails(new String[] {email});
        sendGridClient.removeBounceFor(sendGridDeleteRequest);

        return ResponseEntity.ok().build();
    }

    @GetMapping("/save-a-deal-enabled")
    public ResponseEntity<Boolean> saveADealEnabled(@RequestParam("dealerIds") List<String> dealerIdsParam) {
        var dealerIds = getDealerIdsAndCheckPermissions(dealerIdsParam, session, "dealer:read");

        for(String dealerId : dealerIds){
            boolean isSaveDealEnabled = programSubscriptionClient.findByDealer(dealerId).getContent().stream()
                    .filter(ProgramSubscriptionView::isActive)
                    .map(ProgramSubscriptionView::getContractId)
                    .map(dealerContractClient::findById)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(DealerContractView::getExpressWarrantyAmount)
                    .anyMatch(price -> price > 0);

            if (!isSaveDealEnabled){
                ResponseEntity.ok(false);
            }
        }

        return ResponseEntity.ok(true);
    }

    @PreAuthorize("hasPermission(#dealer, 'dealer:edit')")
    @PatchMapping("/{dealer}/preferences/{lmsPreference}")
    public ResponseEntity<DealerPreferences> updateDealerPreferencesLMSPreference(@NonNull @PathVariable DealerView dealer, @NonNull @PathVariable String lmsPreference) {
        ResponseEntity<DealerPreferences> result;
        String dealerId = dealer.getId();

        // Preferences will be null for a dealer that doesn't have any set yet.
        DealerPreferences preferences = Optional.ofNullable(dealer).map(DealerView::getPreferences).orElse(new DealerPreferences());
        preferences.setLmsPreference(lmsPreference);

        // This is essentially a copy preferences operation.
        // Update the preferences for this dealer by creating/updating the lmsPreference setting.
        DealerUpdatePreferencesRequest dealerUpdatePreferencesRequest = new DealerUpdatePreferencesRequest(preferences);
        DealerView updated = dealerClient.update(dealerId, dealerUpdatePreferencesRequest);
        DealerPreferences updatedPreferences = Optional.ofNullable(updated).map(DealerView::getPreferences).orElse(null);

        // Let caller know of an error situation if there is no data returned on the update.
        result = updatedPreferences != null ? ResponseEntity.ok(updatedPreferences) : ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);

        return result;
    }

    private List<String> getDealerIdsAndCheckPermissions(List<String> dealerIdsParam, HttpSession session, String permission) {
        final List<String> dealerIds = dealerIdsParam == null || dealerIdsParam.isEmpty()
                ? getDealerIds(session)
                : dealerIdsParam;
        securityHelperService.checkPermission(SecurityContextHolder.getContext().getAuthentication(), dealerIds, permission);
        return dealerIds;
    }

    private String getEventStatus(String email) {
        if (email == null) {
            return null;
        }

        try {
            Optional<SendGridEventView> opt = sendGridEventClient.findMostRecentByEmail(email);
            return opt.map(SendGridEventView::getEvent).orElse(null);
        } catch(Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    private String checkBouncesFor(String email) {
        if (email == null) {
            return null;
        }

        try {
            List<SendGridSuppressionResponse> responses = sendGridClient.getBouncesFor(email);
            if (!responses.isEmpty()) {
                return responses.get(0).getReason();
            }
        } catch(Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return null;
    }

    // Split.io feature flag must be removed once digital retail is fully launched
    // Update Apr 29th, 2024: We can't remove split feature flag because I can not update the V1 record in DB to point to V2 URL
    @GetMapping("/{dealerId}/split/enabledForDigitalRetail")
    public Boolean isDealerEnabledForDigitalRetail(@PathVariable("dealerId") String dealerUUID) {
        final DealerView dealer = dealerClient.findById(dealerUUID);
        final boolean isNissanDealer = StringUtils.hasText(dealer.getNnaDealerId());
        if (!isNissanDealer) {
            return false;
        }
        return splitFeatureFlags.isDigitalRetailEnabledForDealer(dealerUUID, null);
    }

    @GetMapping("/{dealerId}/financiers")
    public DealerFinancierModelView getDealerFinanciersByDealerId(@PathVariable String dealerId){
        DealerFinancierModelView dalerFinancier = lenderDeskService.getDealerFinanciersByDealerId(dealerId);
        return dalerFinancier;
    }

    @PostMapping("/{dealerId}/financiers/refresh")
    public List<DealerFinancierModel> refreshDealerFinanciersByDealerId(@PathVariable String dealerId){
        return lenderDeskService.refreshDealerFinanciersByDealerId(dealerId);
    }

    @PostMapping("/{dealerId}/financiers")
    public ResponseEntity<?> upsertFinanciers(@Valid @RequestBody DealerFinanciersRequest dealerFinanciersRequest, @PathVariable String dealerId){
        return ResponseEntity.ok().body(dealerClient.upsertDealerFinanciers(dealerFinanciersRequest,dealerId).getBody());
    }
}
