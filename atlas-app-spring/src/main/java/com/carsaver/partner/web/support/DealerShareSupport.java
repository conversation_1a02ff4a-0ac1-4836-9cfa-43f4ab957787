package com.carsaver.partner.web.support;

import com.carsaver.elasticsearch.criteria.DealerSearchCriteria;
import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.partner.elasticsearch.DealerDocService;
import com.carsaver.search.support.SearchResults;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service
public class DealerShareSupport {

    private final DealerDocService dealerDocService;

    private final DealerClient dealerClient;

    public List<String> getSharedGroupDealerId(Set<String> dealerIds) {
        Set<String> dealerGroupIds = new HashSet<>();
        dealerIds.forEach(dealerId -> Optional.ofNullable(dealerClient.findById(dealerId))
                .map(DealerView::getDealerGroupId)
                .ifPresent(dealerGroupId -> dealerGroupIds.add(dealerGroupId)));

        // TODO should use a data sharing mechanic instead here
        if(!dealerGroupIds.isEmpty()) {
            DealerSearchCriteria criteria = new DealerSearchCriteria();
            criteria.setDealerGroupIds(new LinkedList<>(dealerGroupIds));
            SearchResults<DealerDoc> searchResults = dealerDocService.scroll(criteria);
            Set<String> dealerIdsAggregatedByDealerGroup = searchResults.getContent().stream()
                .map(DealerDoc::getId)
                .collect(Collectors.toSet());

            dealerIds.addAll(dealerIdsAggregatedByDealerGroup);
        }

        return new LinkedList<>(dealerIds);
    }
}
