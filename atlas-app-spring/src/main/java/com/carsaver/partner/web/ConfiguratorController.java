package com.carsaver.partner.web;

import com.carsaver.partner.filter.DealerUserAccessFilter;
import com.carsaver.partner.model.ProgramModel;
import com.carsaver.partner.model.user.UserView;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.DealerEntryPointsService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.SessionAttribute;

import java.util.List;
import java.util.Objects;

@Slf4j
@Controller
public class ConfiguratorController {
    @GetMapping("/settings/**")
    public String settings() { return "settings"; }

    @GetMapping("/payment-calculations/**")
    public String paymentCalculations() {
        return "paymentCalculations";
    }

    @GetMapping(path = {"/website-components/**","/website-configuration/**","/website-theming/**", "/marketing-settings/**"})
    public String websiteConfiguration() {
        return "configurationManager";
    }
}
