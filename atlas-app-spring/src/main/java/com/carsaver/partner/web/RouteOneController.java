package com.carsaver.partner.web;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.finance.LoanRequestView;
import com.carsaver.magellan.model.finance.LoanResponseView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.magellan.model.user.UserContractView;
import com.carsaver.partner.service.routeone.OtpRequest;
import com.carsaver.partner.service.routeone.RouteOneOTPService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Optional;

@Slf4j
@Controller
public class RouteOneController {

    public static final Integer UPGRADE_PRODUCT_ID = 103;

    @Autowired
    private UserClient userClient;

    @Autowired
    private RouteOneOTPService routeOneOTPService;

    @Autowired
    private ProgramClient programClient;

    @Value("${features-toggle.routeone-prefix-enable}")
    private boolean isRouteOnePrefixEnabled;

    @PreAuthorize("hasPermission(#dealer, 'customer:read')")
    @GetMapping("/routeone/{userOrder}/sso")
    public String baseLeadDetails(@PathVariable UserContractView userOrder) {
        Optional<String> otpLogin = getRouteOneOTP(userOrder);
        if(otpLogin.isEmpty()) {
            return "redirect:/routeone/sso-failure";
        }

        return "redirect:" + otpLogin.get();
    }

    //DealerId is used by the permission evaluator and it needs to be received on the method signature
    @PreAuthorize("hasPermission(#dealerId, 'customer:read')")
    @GetMapping("/routeone/{dealerId}/finance-app/{loanRequest}/sso")
    public String baseLeadDetails(@PathVariable String dealerId, @PathVariable LoanRequestView loanRequest) {
        Optional<String> otpLogin = getRouteOneOTP(loanRequest);
        if(otpLogin.isEmpty()) {
            return "redirect:/routeone/sso-failure";
        }
        log.debug("routeone loadRequest sso url: {}",otpLogin.get());
        return "redirect:" + otpLogin.get();
    }

    @GetMapping("/routeone/sso-failure")
    public String getSsoFailure(@RequestParam(required = false, name = "faultreason") Optional<String> faultReason){
        String reason = faultReason.orElse("Unknown");

        log.warn("Route One SSO failure. Reason was {}", reason);

        return "routeOneSsoFailure";
    }

    private Optional<String> getRouteOneOTP(LoanRequestView loanRequest) {
        if(loanRequest == null) {
            log.error("No LoanRequest provided");
            return Optional.empty();
        }

        CertificateView certificate = loanRequest.getCertificate();
        if(certificate == null) {
            log.error("No certificate found for: {}", loanRequest);
            return Optional.empty();
        }

        return getRouteOneOTP(certificate, loanRequest);
    }

    private Optional<String> getRouteOneOTP(UserContractView userOrder) {
        if(userOrder == null) {
            log.error("No UserOrder provided");
            return Optional.empty();
        }

        CertificateView certificate = userOrder.getCertificate();
        if(certificate == null) {
            log.error("No certificate found for: {}", userOrder);
            return Optional.empty();
        }

        LoanResponseView loanResponse = userOrder.getLoanResponse();
        if(loanResponse == null) {
            return Optional.empty();
        }

        return getRouteOneOTP(certificate, loanResponse.getLoanRequest());
    }

    private Optional<String> getRouteOneOTP(CertificateView certificate, LoanRequestView loanRequest) {
        UserView user = AuthUtils.getUserIdFromSecurityContext()
            .map(userClient::findById)
            .orElse(null);

        if(user == null || loanRequest == null) {
            return Optional.empty();
        }

        String routeOneDealerId = Optional.ofNullable(certificate)
            .map(CertificateView::getDealer)
            .map(DealerView::getRouteOneDealerId)
            .orElse(null);

        String conversationId = getRouteOneConversationId(Optional.ofNullable(certificate), loanRequest);
        OtpRequest otpRequest = OtpRequest.builder()
            .conversationId(conversationId)
            .dealerId(routeOneDealerId)
            .pageAccess(OtpRequest.PageAccess.readAndEditCreditApp)
            .build();
        log.info("Attempting to create OTP for User: {} and ConversationId:{}", user.getEmail(), otpRequest);
        try {
            return Optional.ofNullable(routeOneOTPService.generateOtpUrl(user.getEmail(), otpRequest));
        } catch(Exception ex) {
            log.error(ex.getMessage(), ex);
        }

        return Optional.empty();
    }

    private String getRouteOneConversationId(Optional<CertificateView> certificate, LoanRequestView loanRequest) {
        if(certificate.isPresent()){
            CampaignView campaign = certificate.get().getCampaign();
            if (isRouteOnePrefixEnabled && campaign != null) {
                Optional<ProgramView> programView = programClient.findById(campaign.getProgramId());
                if (programView.isPresent() && programView.get().getProduct() != null) {
                    ProductView productView = programView.get().getProduct();
                    if (productView.getId().equals(UPGRADE_PRODUCT_ID)) {
                        return String.format("CSU-1-%s", loanRequest.getAppId());
                    }
                }
            }
        }
        return String.format("CSH-1-%s", loanRequest.getAppId());
    }
}
