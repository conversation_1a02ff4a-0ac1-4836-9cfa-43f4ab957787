package com.carsaver.partner.web;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.PasswordClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.client.request.UpdateRequest;
import com.carsaver.magellan.client.request.UpdateRequestFactory;
import com.carsaver.magellan.model.JobTitleView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.security.PasswordUpdateRequest;
import com.carsaver.partner.model.SelfServiceUser;
import com.carsaver.partner.model.UserAPIAccountFormDataModel;
import com.carsaver.partner.web.form.UserForm;
import com.carsaver.partner.web.form.UserUpdateForm;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.Comparator;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/account/profile")
public class UserSelfServiceController {

    private UserClient userClient;
    private PasswordClient passwordClient;

    public UserSelfServiceController(UserClient userClient, PasswordClient passwordClient) {
        this.userClient = userClient;
        this.passwordClient = passwordClient;
    }

    @GetMapping("form-data")
    public ResponseEntity<UserAPIAccountFormDataModel> getFormData() {
        Collection<JobTitleView> jobTitles = userClient.getDealerJobTitles().getContent()
            .stream().sorted(Comparator.comparing(JobTitleView::getTitle))
            .collect(Collectors.toList());

        return ResponseEntity.of(Optional.ofNullable(new UserAPIAccountFormDataModel(jobTitles)));
    }

    @GetMapping("logged-in-user")
    public ResponseEntity<SelfServiceUser> loggedInUserForSelfService(){
        Optional<UserView> loggedInUser = getProfileUser();
        if(loggedInUser.isEmpty()){
            return ResponseEntity.notFound().build();
        }
        var user = SelfServiceUser.from(loggedInUser.get());
        return ResponseEntity.of(Optional.ofNullable(user));
    }

    @PutMapping("update-password")
    public ResponseEntity updateLoggedInUserPassword(@RequestBody @Valid ChangePasswordForm form){
        Optional<UserView> loggedInUser = getProfileUser();
        if(loggedInUser.isEmpty()){
            return ResponseEntity.notFound().build();
        }
        if (!form.getPassword().equals(form.getPasswordConfirm())) {
            return ResponseEntity.badRequest().build();
        }

        passwordClient.updatePassword(loggedInUser.get().getUserId(), PasswordUpdateRequest.builder().password(form.getPassword()).userId(loggedInUser.get().getUserId()).build());

        return ResponseEntity.ok().build();
    }

    @PatchMapping("update-logged-in-user")
    public ResponseEntity<SelfServiceUser> loggedInUserUpdate(@RequestBody @Valid UserUpdateForm userForm){
        Optional<UserView> loggedInUser = getProfileUser();
        if(loggedInUser.isEmpty()){
            return ResponseEntity.notFound().build();
        }

        UserView userView = userClient.update(loggedInUser.get().getUserId(), userForm);

        var user = SelfServiceUser.from(userView);
        return ResponseEntity.of(Optional.ofNullable(user));
    }

    private Optional<UserView> getProfileUser(){
        return AuthUtils.getUserIdFromSecurityContext()
            .map(userClient::findById);
    }

    @Data
    private static class ChangePasswordForm {
        @NotEmpty
        private String password;

        @NotEmpty
        private String passwordConfirm;

    }
}
