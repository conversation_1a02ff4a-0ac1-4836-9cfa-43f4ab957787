package com.carsaver.partner.web.api.user.elasticsearch.model;

import com.carsaver.search.model.IntegerRange;
import com.carsaver.search.model.LocalDateRange;
import com.carsaver.search.model.SearchMethod;
import com.carsaver.search.model.ZonedDateRange;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public interface SearchCriteria {

    @JsonIgnore
    default boolean isZipCodeFiltered() {
        return StringUtils.isNotEmpty(getZipCode());
    }

    @JsonIgnore
    default boolean isDistanceFiltered() {
        return getDistance() != null && getDistance() > 0;
    }

    @JsonIgnore
     default boolean isPurchaseTimeFrameFiltered() {
        return getPurchaseTimeFrame() != null;
    }

    String getZipCode();
    Integer getDistance();
    LocalDateRange getPurchaseTimeFrame();

    void setExcludeTestTag(String testTag);
    void setExcludeStages(List<String> stages);
    void setEligibleProspectCriteria(EligibleProspectCriteria criteria);
    void setDealerLinkCriteria(DealerLinkCriteria dealerLinkCriteria);
    void setDealerAcls(List<String> dealerACLs);
    void setProgramSubscriptionStages(List<String> programSubscriptionStages);
    void setProgramCriteria(ProgramCriteria programCriteria);
    void setGlobalStage(List<String> globalStage);
    void setNestedSortFilterValue(String nestedSortFilterValue);
    void setStageDealerId(String stageDealerId);
    void setMaturityDate(LocalDateRange maturityDate);
    void setLastActive(LocalDateRange lastActive);
    void setCreatedDate(ZonedDateRange createdDate);
    void setHasTradeIns(IntegerRange hasTradeIns);
    void setStatus(List<String> status);
    void setConnectionLeads(List<String> userLeads);
    void setOtherLeads(List<String> userLeads);
    void setLanguage(List<String> languages);

    Map<String, SearchMethod> getSearchMethods();

    void setDealerInventoryVehiclesId(String dealerInventoryVehiclesId);
    void setDealerVehicleOfInterestSavedOrViewed(List<String> dealerVehicleOfInterestSavedOrViewed);
    void setGlobalVehicleOfInterestSavedOrViewed(List<String> dealerVehicleOfInterestSavedOrViewed);
    void setDealerVehicleOfInterestStockTypes(List<String> dealerVehicleOfInterestStockTypes);
    void setGlobalVehicleOfInterestStockTypes(List<String> globalVehicleOfInterestStockTypes);
    void setDealerVehicleOfInterestCertified(List<String> dealerVehicleOfInterestCertified);
    void setGlobalVehicleOfInterestCertified(List<String> globalVehicleOfInterestCertified);
    void setDealerVehicleOfInterestYears(List<String> dealerVehicleOfInterestYears);
    void setGlobalVehicleOfInterestYears(List<String> globalVehicleOfInterestYears);
    void setDealerVehicleOfInterestMakes(List<String> dealerVehicleOfInterestMakes);
    void setGlobalVehicleOfInterestMakes(List<String> globalVehicleOfInterestMakes);
    void setDealerVehicleOfInterestModels(List<String> dealerVehicleOfInterestModels);
    void setGlobalVehicleOfInterestModels(List<String> globalVehicleOfInterestModels);
}
