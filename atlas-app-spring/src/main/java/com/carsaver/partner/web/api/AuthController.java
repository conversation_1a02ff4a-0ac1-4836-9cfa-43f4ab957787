package com.carsaver.partner.web.api;

import com.carsaver.magellan.client.PasswordClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.security.PasswordResetRequest;
import com.carsaver.magellan.model.security.PasswordUpdateRequest;
import com.carsaver.magellan.model.security.PasswordlessLoginView;
import com.carsaver.partner.service.MailService;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.time.ZonedDateTime;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private PasswordClient passwordClient;

    @Autowired
    private UserClient userClient;

    @Value("${tenant.portal-id}")
    private String portalTenantId;

    @Autowired
    private MailService mailService;

    @PostMapping("/password-reset")
    public ResponseEntity passwordResetForm(@RequestBody @Valid AuthController.ForgotPasswordForm form, HttpServletRequest req) {
        Optional<UserView> user = userClient.findByTenantAndEmail(portalTenantId, form.getEmail());

        if (user.isPresent() && !user.get().isRegularUser()) {
            String token = passwordClient.generateResetPasswordToken(PasswordResetRequest.builder()
                .userId(user.get().getId())
                .expirationTime(ZonedDateTime.now().plusDays(1))
                .build());

            Email from = new Email("<EMAIL>", "CarSaver at Walmart");
            Email to = new Email(form.getEmail(), user.get().getFullName());

            Mail mail = new Mail();
            mail.setFrom(from);
            mail.setTemplateId("d-dd8c1df599d24b5d9681806084b37954");

            Personalization personalization = new Personalization();
            personalization.addDynamicTemplateData("resetUrl", String.format("%s://%s/update-password?token=%s", req.getScheme(), req.getServerName(), token));
            personalization.addTo(to);
            mail.addPersonalization(personalization);

            mailService.sendEmail(mail);
        } else {
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok().build();
    }

    @GetMapping(value = "/verify-token", params = {"token"})
    public ResponseEntity<ChangePasswordForm> verifyToken(@RequestParam String token) {
        Optional<PasswordlessLoginView> responseEntity = passwordClient.verifyToken(token);

        if (responseEntity.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        ChangePasswordForm form = new ChangePasswordForm();
        form.setToken(token);
        form.setUserId(responseEntity.get().getUserId());

        return ResponseEntity.ok(form);
    }

    @PostMapping("/update-password")
    public ResponseEntity updatePassword(@RequestBody @Valid ChangePasswordForm form) {
        Optional<PasswordlessLoginView> responseEntity = passwordClient.verifyToken(form.getToken());

        if (responseEntity.isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        if (!form.getPassword().equals(form.getPasswordConfirm())) {
            return ResponseEntity.badRequest().build();
        }

        passwordClient.updatePassword(responseEntity.get().getUserId(), PasswordUpdateRequest.builder().password(form.getPassword()).userId(responseEntity.get().getUserId()).build());

        return ResponseEntity.ok().build();
    }

    @Data
    private static class ChangePasswordForm {
        @NotEmpty
        private String userId;

        @NotEmpty
        private String password;

        @NotEmpty
        private String passwordConfirm;

        @NotEmpty
        private String token;
    }

    @Data
    public static class ForgotPasswordForm {

        @javax.validation.constraints.Email
        @NotBlank
        private String email;
    }
}
