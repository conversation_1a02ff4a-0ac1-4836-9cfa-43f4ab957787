package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.partner.model.DeliveryFee;
import com.carsaver.partner.model.DeliveryFees;
import com.carsaver.partner.service.DealerDeliveryFeeService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.support.SanitizeInput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
public class DealerDeliveryFeeApiController {

    private final DealerDeliveryFeeService dealerDeliveryFeeService;
    private final SplitFeatureFlags splitFeatureFlags;

    public DealerDeliveryFeeApiController(DealerDeliveryFeeService dealerDeliveryFeeService, SplitFeatureFlags splitFeatureFlags) {
        this.dealerDeliveryFeeService = dealerDeliveryFeeService;
        this.splitFeatureFlags = splitFeatureFlags;
    }

    @PreAuthorize("hasPermission(#dealer, 'dealer:read')")
    @GetMapping("/api/dealer/{dealer}/dealer-delivery-fees")
    public List<DeliveryFee> fetchDealerDeliveryFees(@PathVariable DealerView dealer) {
        List<DeliveryFee> listOfDealerDeliveryFees = new ArrayList<>();

        if ( !Objects.isNull(dealer)) {
            listOfDealerDeliveryFees = dealerDeliveryFeeService.getDealerDeliveryFees(dealer.getId());
        }

        return listOfDealerDeliveryFees;
    }

    @SanitizeInput
    @PostMapping(value = "/api/dealer/{dealerId}/dealer-delivery-fee", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DeliveryFees> createOrUpdateDeliveryFee(@PathVariable String dealerId, @RequestBody DeliveryFee deliveryFee) {
        DeliveryFees result = dealerDeliveryFeeService.createOrUpdateDeliveryFees(dealerId, deliveryFee);
        ResponseEntity<DeliveryFees> response = ResponseEntity.ok(result);
        return response;
    }

    @SanitizeInput
    @DeleteMapping(value = "/api/dealer/{dealerId}/dealer-delivery-fee/{deliveryFeeId}" , produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DeliveryFees> deleteDeliveryFee(@PathVariable String dealerId, @PathVariable String deliveryFeeId) {
        DeliveryFees result = dealerDeliveryFeeService.deleteDeliveryFee(dealerId, deliveryFeeId);
        ResponseEntity<DeliveryFees> response = ResponseEntity.ok(result);
        return response;
    }
}
