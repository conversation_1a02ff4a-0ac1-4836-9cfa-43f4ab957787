package com.carsaver.partner.web.api.training.dealer;

import com.carsaver.core.DealerStatus;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.client.TrainingCourseClient;
import com.carsaver.magellan.client.TrainingVideoClient;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.magellan.model.trainingvideo.TrainingCourseView;
import com.carsaver.magellan.model.trainingvideo.TrainingVideoRecordView;
import com.carsaver.partner.model.DealerUser;
import com.carsaver.partner.service.DealerUserService;
import com.carsaver.partner.web.api.training.dealer.model.byCourse.TrainingCourse;
import com.carsaver.partner.web.api.training.dealer.model.byUser.TrainingUser;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@Slf4j
@RequestMapping("/api/training/dealers")
public class DealerTrainingCourseApiController {

    @Autowired
    private ProgramSubscriptionClient programSubscriptionClient;

    @Autowired
    private TrainingVideoClient trainingVideoClient;

    @Autowired
    private TrainingCourseClient trainingCourseClient;

    @Autowired
    private DealerUserService dealerUserService;

    @GetMapping("/{dealerId}/courses")
    public DealerTrainingCourseView getTrainingCoursesByDealer(@PathVariable("dealerId") String dealerId){
        Collection<TrainingCourseView> courses = new LinkedList<>();
        programSubscriptionClient.findByDealer(dealerId).getContent()
                .stream()
                .filter(p -> p.getStatus() != DealerStatus.CANCELLED)
                .forEach(program-> {
                    var coursesByProgram = trainingCourseClient.findByProgramId(PageRequestUtils.maxSizeRequest(),program.getProgramId())
                            .getContent()
                            .stream()
                            .collect(Collectors.toList());

                    courses.addAll(coursesByProgram);
                });

        Map<DealerUser, Collection<TrainingVideoRecordView>> usersRecords = new HashMap<>();
        List<DealerUser> dealerUsers = dealerUserService.getDealerUsers(dealerId);
        dealerUsers.forEach(user -> {
            Collection<TrainingVideoRecordView> videoRecords = Optional.ofNullable(trainingVideoClient.findRecordsByUserId(user.getId()))
                    .map(CollectionModel::getContent)
                    .orElse(Collections.emptyList());
            usersRecords.put(user,videoRecords);
        });

        var byCourses = courses
                .stream()
                .map(course -> TrainingCourse.from(course,usersRecords))
                .collect(Collectors.toList());

        var byUsers = dealerUsers
                .stream()
                .map(user -> TrainingUser.from(user,courses,usersRecords.get(user)))
                .collect(Collectors.toList());

        return DealerTrainingCourseView.builder()
                .byCourse(byCourses)
                .byUser(byUsers)
                .build();
    }

    @Data
    @Builder
    static class DealerTrainingCourseView {
        List<TrainingCourse> byCourse;
        List<TrainingUser> byUser;
    }
}
