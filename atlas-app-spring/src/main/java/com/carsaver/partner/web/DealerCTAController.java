package com.carsaver.partner.web;

import com.carsaver.partner.model.dealercta.AtlasCTAReqRes;
import com.carsaver.partner.service.DealerCTAService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/api/dealer-cta")
public class DealerCTAController {
    final DealerCTAService dealerCTAService;

    public DealerCTAController(DealerCTAService dealerCTAService) {
        this.dealerCTAService = dealerCTAService;
    }

    @PostMapping
    public ResponseEntity<AtlasCTAReqRes> upsertDeal(@RequestBody AtlasCTAReqRes dealerCTA) {
        AtlasCTAReqRes result = null;
        ResponseEntity<AtlasCTAReqRes> response;
        try {
            result = dealerCTAService.upsertDealerCTA(dealerCTA);
            response = ResponseEntity.ok(result);
        } catch (Exception e) {
            response = ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
        }
        return response;
    }

    @GetMapping("/{dealerId}")
    public ResponseEntity<?> getDealerCTA(@PathVariable String dealerId) {
        AtlasCTAReqRes result;
        ResponseEntity<?> response;

        try {
            result = dealerCTAService.getDealerCTA(dealerId);
            response = ResponseEntity.ok(result);
        } catch (Exception e) {
            response = ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        }

        return response;
    }

}

