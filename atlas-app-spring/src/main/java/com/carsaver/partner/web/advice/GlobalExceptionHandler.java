package com.carsaver.partner.web.advice;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Order(2)
@ControllerAdvice(annotations = Controller.class)
public class GlobalExceptionHandler {
    @ExceptionHandler(AccessDeniedException.class)
    public String accessDeniedException(HttpServletRequest request, AccessDeniedException e) {
        log.error("ACCESS DENIED: path={}, redirecting to {}", request.getRequestURI(), "/access-denied", e);

        return "redirect:/access-denied";
    }
}
