package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.model.DealerLinkDoc;
import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.elasticsearch.model.UserDoc;
import com.carsaver.elasticsearch.model.UserStageDoc;
import com.carsaver.elasticsearch.model.lead.UserLead;
import com.carsaver.magellan.export.BufferProcessor;
import com.carsaver.magellan.export.CsvWriter;
import com.carsaver.magellan.export.DataStreamProvider;
import com.carsaver.magellan.export.service.CsvExportService;
import com.carsaver.magellan.model.user.traits.GlobalUserTraits;
import com.carsaver.magellan.model.user.traits.UserTraits;
import com.carsaver.partner.util.ExportHelper;
import com.carsaver.partner.web.api.user.elasticsearch.model.CriteriaFilterRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ExportService {

    private final CustomersDocService customersDocService;
    private final ExportHelper exportHelper;
    private final LocaleService localeService;
    private final TraitsService traitsService;

    private final int pageSize;

    public ExportService(
        CustomersDocService customersDocService, ExportHelper exportHelper, LocaleService localeService,
        TraitsService traitsService,
        @Value("${export.customer.page-size}") int pageSize) {
        this.customersDocService = customersDocService;
        this.exportHelper = exportHelper;
        this.localeService = localeService;
        this.traitsService = traitsService;
        this.pageSize = pageSize;
    }

    public void exportUserToCsv(
        HttpServletResponse response,
        List<String> columns,
        List<String> labels,
        CriteriaFilterRequest searchForm,
        String timezone
    ) throws Exception {
        exportHelper.validateLabels(columns, labels);

        final CsvWriter csvWriter = prepareCsvWriter(columns, labels, response).build();

        final Set<String> uniqueDealerIds = CollectionUtils.isEmpty(searchForm.getDealerAcls())
            ? null
            : new HashSet<>(searchForm.getDealerAcls());

        var processor = new CustomerBufferProcessor(traitsService, uniqueDealerIds, timezone);

        try (com.carsaver.magellan.export.service.ExportService<UserAndProspectDoc> exportService = new CsvExportService<>(csvWriter, processor)) {

            DataStreamProvider<UserAndProspectDoc> dataStreamProvider = () -> customersDocService.scrollStream(searchForm, TimeValue.timeValueMinutes(1L), pageSize);

            exportService.export(dataStreamProvider);
        }
    }


    class CustomerBufferProcessor implements BufferProcessor<List<UserAndProspectDoc>, List<UserAndProspectDoc>> {

        private final TraitsService traitsService;
        private final Set<String> uniqueDealerIds;
        private final String timezone;

        CustomerBufferProcessor(
            TraitsService traitsService, Set<String> uniqueDealerIds, String timezone) {
            this.traitsService = traitsService;
            this.uniqueDealerIds = uniqueDealerIds;
            this.timezone = timezone;
        }

        @Override
        public List<UserAndProspectDoc> convert(List<UserAndProspectDoc> val) {
            List<String> userIds = val.stream().map(UserAndProspectDoc::getId).collect(Collectors.toList());
            var byUserId = traitsService.fetchUserTraits(userIds, uniqueDealerIds).stream()
                .collect(Collectors.toMap(UserTraits::getUserId, Function.identity()));

            val.forEach(userAndProspectDoc -> enrichSingle(userAndProspectDoc, uniqueDealerIds, byUserId, timezone));

            return val;
        }
    }


    private void enrichSingle(UserAndProspectDoc doc, Set<String> uniqueDealerIds, Map<String, UserTraits> byUserId, String timezone) {
        defaultTraitsForExport(doc);
        setStageTileForExport(doc, uniqueDealerIds);
        setDealerLinksCustomerStatus(doc, uniqueDealerIds);
        setStageTransitionDate(doc, uniqueDealerIds);
        setLeadTypeOnDoc(doc);
        localeService.setLocaleOnDoc(doc);
        doc.setTraits(byUserId.get(doc.getId()));
        doc.setSignUpDate(exportHelper.convertDateToSpecificTimezone(doc.getCreatedDate(), timezone));
    }

    private CsvWriter.Builder prepareCsvWriter(List<String> columns, List<String> labels, HttpServletResponse response) {
        Collections.replaceAll(columns, "programSubscriptionStages", "stage");

        var rankedStageIndex = columns.indexOf("rankedStage");
        if (rankedStageIndex != -1) {
            columns.set(rankedStageIndex, labels.get(rankedStageIndex));
        }

        CsvWriter.Builder builder = exportHelper.prepareCsvWriter(response, columns, labels);

        if (rankedStageIndex != -1) {
            builder.addRowPostProcessor("Stage", (data) -> {
                UserDoc userDoc = (UserDoc) data;
                return userDoc.getRankedStage() + " - " + userDoc.getStage();
            });
        }
        return builder;
    }

    private UserAndProspectDoc defaultTraitsForExport(UserAndProspectDoc userAndProspectDoc) {
        if (userAndProspectDoc.getPreApprovalCount() == null) {
            userAndProspectDoc.setPreApprovalCount(0);
        }

        if (userAndProspectDoc.getTraits() == null) {
            GlobalUserTraits globalTraits = GlobalUserTraits.builder().build();
            UserTraits traits = UserTraits.builder().globalUserTraits(globalTraits).build();
            userAndProspectDoc.setTraits(traits);
        }

        return userAndProspectDoc;
    }

    private UserAndProspectDoc setStageTileForExport(UserAndProspectDoc userAndProspectDoc, Set<String> uniqueDealerIds) {
        if (!CollectionUtils.isEmpty(uniqueDealerIds) && uniqueDealerIds.size() == 1) {
            final String dealerId = uniqueDealerIds.stream().findFirst().get();
            Optional.of(userAndProspectDoc)
                .map(UserAndProspectDoc::getProgramSubscriptionStages)
                .orElse(List.of())
                .stream()
                .filter(it -> Objects.equals(dealerId, it.getDealerId()))
                .findFirst()
                .ifPresent(dealerStage -> userAndProspectDoc.setStage(dealerStage.getStageName()));
        }
        return userAndProspectDoc;
    }

    private UserAndProspectDoc setStageTransitionDate(UserAndProspectDoc userAndProspectDoc, Set<String> uniqueDealerIds) {
        if (!CollectionUtils.isEmpty(uniqueDealerIds) && uniqueDealerIds.size() == 1) {
            final String dealerId = uniqueDealerIds.iterator().next();

            ZonedDateTime stageTransitionDate = Optional.of(userAndProspectDoc)
                .map(UserAndProspectDoc::getProgramSubscriptionStages)
                .orElse(List.of())
                .stream()
                .filter(it -> Objects.equals(dealerId, it.getDealerId()))
                .map(UserStageDoc::getLastModifiedDate)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(userAndProspectDoc.getLastModifiedDate());

            userAndProspectDoc.setStageTransition(stageTransitionDate);
        } else {
            userAndProspectDoc.setStageTransition(userAndProspectDoc.getLastModifiedDate());
        }
        return userAndProspectDoc;
    }


    private UserAndProspectDoc setDealerLinksCustomerStatus(UserAndProspectDoc userAndProspectDoc, Set<String> uniqueDealerIds) {
        if (!CollectionUtils.isEmpty(uniqueDealerIds) && uniqueDealerIds.size() == 1) {
            final String dealerId = uniqueDealerIds.stream().findFirst().get();
            Optional.of(userAndProspectDoc)
                .map(UserAndProspectDoc::getDealerLinks)
                .orElse(List.of())
                .stream()
                .filter(it -> Objects.equals(dealerId, it.getDealerId()))
                .findFirst()
                .map(DealerLinkDoc::getStatus)
                .ifPresent(dealerLinksStatus -> userAndProspectDoc.setStatus(dealerLinksStatus.name()));
        }
        return userAndProspectDoc;
    }

    private UserAndProspectDoc setLeadTypeOnDoc(UserAndProspectDoc doc) {
        if (doc.getUserLeads() != null && !doc.getUserLeads().isEmpty()) {
            String leadTypes = doc.getUserLeads().stream()
                .map(UserLead::getType)
                .filter(Objects::nonNull)
                .map(type -> type.replace("-", " ")) // Replace dashes with spaces
                .map(type -> Arrays.stream(type.split(" "))
                    .map(word -> Character.toUpperCase(word.charAt(0)) + word.substring(1).toLowerCase())
                    .collect(Collectors.joining(" "))) // Convert to Title Case
                .distinct()
                .collect(Collectors.joining(", ")); // Join into a single string
            doc.setLeadType(leadTypes);
        }
        return doc;
    }

    private UserAndProspectDoc setUserTraits(UserAndProspectDoc userAndProspectDoc, Set<String> uniqueDealerIds) {
        final List<UserTraits> userTraitsList = traitsService.fetchUserTraits(userAndProspectDoc.getId(), uniqueDealerIds);
        userTraitsList.stream()
            .filter(userTraits -> userTraits.getUserId().equalsIgnoreCase(userAndProspectDoc.getId()))
            .findFirst()
            .ifPresent(userAndProspectDoc::setTraits);
        return userAndProspectDoc;
    }
}
