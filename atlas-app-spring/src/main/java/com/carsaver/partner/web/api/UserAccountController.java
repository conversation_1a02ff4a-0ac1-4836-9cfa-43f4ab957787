package com.carsaver.partner.web.api;

import com.carsaver.magellan.auth.SessionUtils;
import com.carsaver.magellan.client.DealerPermissionsClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.JobTitleView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.security.DealerPermissionView;
import com.carsaver.magellan.validation.EmailDomain;
import com.carsaver.partner.model.DealerUser;
import com.carsaver.partner.model.DealerUserPermissionGroupNode;
import com.carsaver.partner.model.UserAccountFormDataModel;
import com.carsaver.partner.service.DealerUserPermissionService;
import com.carsaver.partner.service.DealerUserService;
import com.carsaver.partner.support.SanitizeInput;
import com.carsaver.partner.web.form.DealerPermissionsForm;
import com.carsaver.partner.web.form.UserForm;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class UserAccountController {

    @Value("${tenant.portal-id}")
    private String portalTenantId;

    @Autowired
    private DealerUserService dealerUserService;

    @Autowired
    private UserClient userClient;

    @Autowired
    private DealerPermissionsClient dealerPermissionsClient;

    @GetMapping(value = "/api/dealer/{dealerId}/users", params = {"email"})
    public ResponseEntity<DealerUser> findExistingUser(@ModelAttribute @Valid EmailRequest emailRequest, @PathVariable("dealerId") String dealerId) {
        Optional<UserView> dealerUserOpt = userClient.findByTenantAndEmail(portalTenantId, emailRequest.getEmail());
        var dealerUser = dealerUserService.convert(dealerId, dealerUserOpt);
        return ResponseEntity.of(Optional.ofNullable(dealerUser));
    }

    @PreAuthorize("hasPermission(#dealerId, 'dealer:read')")
    @GetMapping("/api/dealer/{dealerId}/users")
    public List<DealerUser> getUsers(@PathVariable String dealerId) {
        return dealerUserService.getDealerUsers(dealerId);
    }

    @PreAuthorize("hasPermission(#dealerId, 'dealer-user:edit')")
    @PostMapping(value = "/api/dealer/{dealerId}/users")
    @SanitizeInput
    public ResponseEntity<DealerUser> createDealerUserAndAssociate(@RequestBody @Valid UserForm userForm, @PathVariable String dealerId, @RequestHeader(HttpHeaders.HOST) String host) {
        Optional<DealerUser> dealerUser = dealerUserService.createUser(dealerId,userForm, host, portalTenantId);

        if(dealerUser.isEmpty()) {
            return ResponseEntity.badRequest().build();
        }
        return ResponseEntity.of(dealerUser);
    }

    @PreAuthorize("hasPermission(#dealerId, 'dealer-user:edit')")
    @PostMapping(value = "/api/dealer/{dealerId}/users/{userId}")
    public ResponseEntity<DealerUser> associateExisting(@PathVariable("dealerId") String dealerId, @PathVariable String userId, @RequestBody DealerPermissionsForm form) {
        dealerUserService.associateUserToDealer(form, dealerId, userId, form.getPermissions(), SessionUtils.getLoggedInUser());
        UserView user = userClient.findById(userId);
        var dealerUser = dealerUserService.convert(dealerId, Optional.ofNullable(user));
        return ResponseEntity.of(Optional.ofNullable(dealerUser));
    }

    @PreAuthorize("hasPermission(#dealerId, 'dealer-user:edit')")
    @GetMapping("/api/dealer/{dealerId}/users/form-data")
    public UserAccountFormDataModel getFormData(@PathVariable("dealerId") String dealerId) {
        Collection<DealerPermissionView> permissions = dealerPermissionsClient.getPermissionsIncludeNew().getContent()
            .stream()
            .filter(dealerUserService.getPermissionFilter()) //TODO - remove when permissions migration is complete
            .collect(Collectors.toList());

        Collection<JobTitleView> jobTitles = userClient.getDealerJobTitles().getContent()
            .stream().sorted(Comparator.comparing(JobTitleView::getTitle))
            .collect(Collectors.toList());
        List<DealerUserPermissionGroupNode> dealerUserPermissionGroupNodes = DealerUserPermissionService.convertToDealerPermissionModels(permissions);

        return new UserAccountFormDataModel(dealerUserPermissionGroupNodes, jobTitles);
    }

    @PreAuthorize("hasPermission(#dealerId, 'dealer-user:edit')")
    @DeleteMapping(value = "/api/dealer/{dealerId}/users/{userId}")
    public void removeUser(@PathVariable String dealerId, @PathVariable String userId) {
        dealerUserService.removeDealerUserAssociation(dealerId,userId);
    }

    @Data
    public static class EmailRequest {
        @NotNull
        @EmailDomain
        private String email;
    }
}
