package com.carsaver.partner.web.api.user;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.carsaver.magellan.model.CertificateView;
import com.carsaver.partner.model.desking.CreateDealRequest;
import com.carsaver.partner.service.desking.CreateDealService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class CreateDealController {

    @Autowired
    private CreateDealService createDealService;

    @PostMapping("/api/create-deal")
    public ResponseEntity<?> createDeal(@RequestBody CreateDealRequest request)  {
        log.info("Creating a deal for User {}, Dealer {}, Vin {}", request.getUserId(), request.getDealerId(), request.getVin());

        Optional<CertificateView> certificate = createDealService.createDeal(request); 
        
        if(certificate.isEmpty()) {
            String error = String.format("Could not create deal for User %s, Dealer %s, Vin %s ", 
                    request.getUserId(), request.getDealerId(), request.getVin());
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
        }
        
        return ResponseEntity.ok(certificate.get());
    }
    
}
