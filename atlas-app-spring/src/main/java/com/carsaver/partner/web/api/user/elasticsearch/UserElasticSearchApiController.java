package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.partner.model.CustomerLastLoginDetails;
import com.carsaver.partner.salesstages.model.SalesStageStats;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.OnlineUsersService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.api.BaseListController;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchRequest;
import com.carsaver.search.model.SearchMethod;
import com.carsaver.search.support.FacetInfoResult;
import com.carsaver.search.support.SearchResults;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@RestController
public class UserElasticSearchApiController implements BaseListController {

    private final DealerUserElasticSearchService dealerUserElasticSearchService;
    private final ProgramUserElasticSearchService programUserElasticSearchService;
    private final DealerAndProgramAccessHelper dealerAndProgramAccessHelper;
    private final SaleStagesStatsService saleStagesStatsService;
    private final TraitsService traitsService;
    private final SplitFeatureFlags splitFeatureFlags;
    private final OnlineUsersService onlineUsersService;
    private final LocaleService localeService;

    @Autowired
    private UserClient userClient;

    @PostMapping("/api/users/v2/search")
    public SearchResults<UserAndProspectDoc> find(@RequestBody SearchRequest request, @SortDefault(value = "createdDate") Pageable pageable, HttpSession session) {

        if (SecurityUtils.isDealerUser() || SecurityUtils.isAdminUser()) {
            SearchResults<UserAndProspectDoc> results = dealerUserElasticSearchService.search(request, pageable);
            traitsService.setUserAndProspectDocTraitsOn(results, Set.of(request.getDealerIds()));
            localeService.setUserAndProspectDocLanguage(results);
            return results;
        }

        if (SecurityUtils.isProgramUser()) {
            final List<String> dealerIds = dealerAndProgramAccessHelper.getDealerIdsAndCheckPermissions(request, session);
            final List<String> programIds = dealerAndProgramAccessHelper.getProgramIdsAndCheckPermissions(request, session, dealerIds);

            SearchResults<UserAndProspectDoc> results = programUserElasticSearchService.search(request, dealerIds, programIds, pageable);
            traitsService.setUserAndProspectDocTraitsOn(results, new HashSet<>(dealerIds));
            localeService.setUserAndProspectDocLanguage(results);
            return results;
        }

        throw new IllegalArgumentException("User type not supported");
    }

    @PostMapping(value = "/api/users/v2/facet_info", params = "id")
    public FacetInfoResult findFacet(@RequestBody SearchRequest request, @RequestParam("id") String facet, HttpSession session) {
        if (SecurityUtils.isDealerUser() || SecurityUtils.isAdminUser()) {
            return dealerUserElasticSearchService.findFacetInfo(request, facet);
        }

        if (SecurityUtils.isProgramUser()) {
            final List<String> dealerIds = dealerAndProgramAccessHelper.getDealerIdsAndCheckPermissions(request, session);
            final List<String> programIds = dealerAndProgramAccessHelper.getProgramIdsAndCheckPermissions(request, session, dealerIds);
            return programUserElasticSearchService.findFacetInfo(request, facet, dealerIds, programIds);
        }

        throw new IllegalArgumentException("User type not supported");
    }

    @PostMapping("/api/users/v2/sales/stages-stats")
    public SalesStageStats getSalesStagesStats(@RequestBody SearchRequest request, HttpSession session) {
        final FacetInfoResult facetInfoResult = findFacet(request, "stages", session);

        if (SecurityUtils.isDealerUser() || SecurityUtils.isAdminUser()) {
            return saleStagesStatsService.DealerSalesStageStats(facetInfoResult);
        }

        if (SecurityUtils.isProgramUser()) {
            final List<String> dealerIds = dealerAndProgramAccessHelper.getDealerIdsAndCheckPermissions(request, session);
            final List<String> programIds = dealerAndProgramAccessHelper.getProgramIdsAndCheckPermissions(request, session, dealerIds);
            return saleStagesStatsService.ProgramSalesStageStats(facetInfoResult, programIds);
        }

        throw new IllegalArgumentException("User type not supported");
    }

    @GetMapping(value = "/customers/customer_report.csv", produces = "text/csv")
    public void exportUserToCsv(
        @RequestParam List<String> columns,
        @RequestParam List<String> labels,
        @ModelAttribute("searchForm") SearchRequest searchForm,
        @RequestParam String negativeSearchColumns,
        @RequestParam(required = false) String stageTile,
        @RequestParam String timezone,
        HttpServletResponse response,
        HttpSession session
    ) throws Exception {
        columns = columns.stream().map(ColumnMappings::getFullColumnName).collect(Collectors.toList());
        labels = labels.stream().map(ColumnMappings::getFullLabelName).collect(Collectors.toList());
        normalizeSearchMethods(searchForm, negativeSearchColumns);
        if (SecurityUtils.isDealerUser() || SecurityUtils.isAdminUser()) {
            dealerUserElasticSearchService.exportUserToCsv(
                response,
                columns,
                labels,
                searchForm,
                timezone
            );
            return;
        }

        if (SecurityUtils.isProgramUser()) {
            final List<String> dealerIds = dealerAndProgramAccessHelper.getDealerIdsAndCheckPermissions(searchForm, session);
            final List<String> programIds = dealerAndProgramAccessHelper.getProgramIdsAndCheckPermissions(searchForm, session, dealerIds);

            programUserElasticSearchService.exportUserToCsv(
                response,
                columns,
                labels,
                searchForm,
                dealerIds,
                programIds,
                timezone
            );
            return;
        }

        throw new IllegalArgumentException("User type not supported");
    }

    private void normalizeSearchMethods(SearchRequest searchForm, String negativeSearchColumns) {
        final List<String> negativeSearchColumnsList = List.of(negativeSearchColumns.split(","));
        final Map<String, SearchMethod> searchMethods = new HashMap<>();

        for (Field field : SearchRequest.class.getDeclaredFields()) {
            field.setAccessible(true);
            try {
                final Object value = field.get(searchForm);
                if (value == null) {
                    continue;
                }

                SearchMethod searchMethod = SearchMethod.POSITIVE;
                if (negativeSearchColumnsList.contains(field.getName())) {
                    searchMethod = SearchMethod.NEGATIVE;
                }

                searchMethods.put(field.getName(), searchMethod);
            } catch (IllegalAccessException e) {
                log.error("Error while normalizing search methods", e);
            }
        }

        searchForm.setSearchMethods(searchMethods);
    }

    @GetMapping(value = "/api/users/{userId}/login-details")
    public CustomerLastLoginDetails userLoginDetails(@RequestParam(value = "dealerId") String dealerId, @PathVariable String userId){
        UserAndProspectDoc userAndProspectDoc = dealerUserElasticSearchService.search(userId)
            .orElseThrow(() -> new NotFoundException("No data found for requested user"));

        return onlineUsersService.buildCustomerLastLoginDetails(userAndProspectDoc);
    }

}
