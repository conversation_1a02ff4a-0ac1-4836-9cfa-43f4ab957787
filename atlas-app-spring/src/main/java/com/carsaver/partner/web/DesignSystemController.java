package com.carsaver.partner.web;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class DesignSystemController {
    //design guide page
    @GetMapping(value = "/design-guide/**")
    public String designGuide() {
        return "designGuide";
    }
}
