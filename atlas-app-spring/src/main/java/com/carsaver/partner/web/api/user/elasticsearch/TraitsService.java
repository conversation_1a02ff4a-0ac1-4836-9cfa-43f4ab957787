package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.user.traits.UserTraits;
import com.carsaver.magellan.model.user.traits.UserTraitsSearchCriteria;
import com.carsaver.search.support.SearchResults;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@AllArgsConstructor
@Service
public class TraitsService {

    private final UserClient userClient;

    public SearchResults<UserAndProspectDoc> setUserAndProspectDocTraitsOn(SearchResults<UserAndProspectDoc> results, Set<String> dealerIds) {
        List<String> userIds = results.getContent().stream().map(UserAndProspectDoc::getId).collect(Collectors.toList());

        UserTraitsSearchCriteria criteria = new UserTraitsSearchCriteria();

        criteria.setUserIds(userIds);
        criteria.setDealerIds(dealerIds);

        List<UserTraits> userTraitsList = this.getUserTraits(criteria);

        results.getContent().forEach(UserAndProspectDoc -> matchAndSetMatchingUserAndProspectDocTraits(UserAndProspectDoc, userTraitsList));

        return results;
    }

    public List<UserTraits> fetchUserTraits(String userIds, Set<String> dealerIds) {
        return fetchUserTraits(List.of(userIds), dealerIds);
    }

    public List<UserTraits> fetchUserTraits(List<String> userIds, Set<String> dealerIds) {
        UserTraitsSearchCriteria criteria = new UserTraitsSearchCriteria();
        criteria.setUserIds(userIds);
        criteria.setDealerIds(dealerIds);
        return getUserTraits(criteria);
    }

    public void matchAndSetMatchingUserAndProspectDocTraits(UserAndProspectDoc UserAndProspectDoc, List<UserTraits> userTraitsList) {
        userTraitsList.stream()
            .filter(userTraits -> userTraits.getUserId().equalsIgnoreCase(UserAndProspectDoc.getId()))
            .findFirst()
            .ifPresent(UserAndProspectDoc::setTraits);
    }

    private List<UserTraits> getUserTraits(UserTraitsSearchCriteria criteria) {
        StopWatch sw = new StopWatch();
        sw.start();

        List<UserTraits> userTraits = userClient.getUserTraits(criteria);

        sw.stop();
        log.debug("getUserTraits roundtrip time = {}", sw.getTotalTimeMillis());

        return userTraits;
    }
}
