package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.service.FastPassService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller to handle Fast Pass API requests.
 */
@Slf4j
@RestController
public class FastPassApiController {

    private final FastPassService fastPassService;

    public FastPassApiController(FastPassService fastPassService) {
        this.fastPassService = fastPassService;
    }

    /**
     * Triggers a Fast Pass event and lead for a given user and dealer.
     *
     * @param user the user
     * @param dealer the dealer
     * @return a ResponseEntity indicating the result of the operation
     */
    @PostMapping(path = "/api/fast-pass/users/{user}/dealers/{dealer}")
    public ResponseEntity<String> triggerFastPassLeadAndEvent (@PathVariable UserView user, @PathVariable DealerView dealer) {
        if (user == null || dealer == null) {
            return ResponseEntity.status(400).body("Invalid User or Dealer ID");
        }

        try {
            fastPassService.triggerFastPassLeadAndEvent(user, dealer);
            return ResponseEntity.ok("Fast Pass Lead and Event triggered successfully.");
        } catch (Exception e) {
            log.error("Error triggering Fast Pass Event and Lead", e);
            return ResponseEntity.status(500).body("Error triggering Fast Pass Event and Lead");
        }
    }
}
