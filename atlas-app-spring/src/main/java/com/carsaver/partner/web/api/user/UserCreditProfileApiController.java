package com.carsaver.partner.web.api.user;

import com.carsaver.partner.model.user.CreditProfile;
import com.carsaver.partner.service.user.CreditProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController("userCreditProfileApiController")
public class UserCreditProfileApiController {

    private final CreditProfileService userCreditProfileService;

    public UserCreditProfileApiController(CreditProfileService userCreditProfileService) {
        this.userCreditProfileService = userCreditProfileService;
    }

    @GetMapping("/api/users/{userId}/credit-profile")
    public List<CreditProfile> getUserCreditProfile(@PathVariable String userId) {
        CreditProfile creditProfile = userCreditProfileService.getCreditProfile(userId);
        return creditProfile != null && Objects.equals(creditProfile.getCreditEvaluator(), com.carsaver.partner.model.user.CreditProfile.CreditEvaluator.BUREAU)
            ? Collections.singletonList(creditProfile)
            : Collections.emptyList();
    }
}
