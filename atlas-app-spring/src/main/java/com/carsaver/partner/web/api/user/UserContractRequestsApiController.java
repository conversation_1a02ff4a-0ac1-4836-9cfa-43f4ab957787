package com.carsaver.partner.web.api.user;

import com.carsaver.elasticsearch.model.UserContractDoc;
import com.carsaver.elasticsearch.service.UserContractDocService;
import com.carsaver.magellan.http.PageRequestUtils;
import com.carsaver.partner.elasticsearch.criteria.UserContractSearchCriteria;
import com.carsaver.partner.model.user.UserContractRequest;
import com.carsaver.partner.web.api.CustomerBaseController;
import com.carsaver.partner.web.api.SecurityHelperService;
import com.carsaver.search.support.SearchResults;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@AllArgsConstructor
@RestController("userContractRequestApiController")
public class UserContractRequestsApiController implements CustomerBaseController {

    private final UserContractDocService userContractDocService;
    private final SecurityHelperService securityHelperService;

    @GetMapping("/api/users/{userId}/contractRequests")
    public UserContractRequest getUserContractRequests(@RequestParam(value = "dealerIds", required = false) List<String> dealerIds,
                                                       @PathVariable String userId,
                                                       @SortDefault(value = "createdDate", direction = Sort.Direction.DESC) Pageable pageable,
                                                       HttpSession session) {
        Objects.requireNonNull(userId);
        final var finalDealerIds = getFinalListOfDealerAndValidateCustomerPermissions(dealerIds, session, securityHelperService);

        UserContractSearchCriteria criteria = new UserContractSearchCriteria();
        criteria.setUserId(userId);
        criteria.setDealerIds(finalDealerIds);
        SearchResults<UserContractDoc> searchResponse = userContractDocService.search(criteria, PageRequestUtils.asOneBasedPage(pageable));

        List<UserContractRequest.ContractRequest> contractRequestsView = searchResponse.getContent()
            .stream()
            .map(UserContractRequest.ContractRequest::from)
            .collect(Collectors.toList());

        return UserContractRequest.builder().contractRequests(contractRequestsView).build();
    }
}
