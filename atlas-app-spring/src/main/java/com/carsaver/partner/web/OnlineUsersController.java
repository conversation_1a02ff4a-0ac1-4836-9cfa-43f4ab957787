package com.carsaver.partner.web;

import com.carsaver.partner.model.user.OnlineUser;
import com.carsaver.partner.repository.DigitalRetailSession;
import com.carsaver.partner.service.OnlineUsersService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/online-users")
@RequiredArgsConstructor
public class OnlineUsersController {
    private final OnlineUsersService onlineUsersService;

    @GetMapping
    public ResponseEntity<List<OnlineUser>> getOnlineUsers() {
        List<OnlineUser> digitalRetailSessions = onlineUsersService.getOnlineUsers();
        return ResponseEntity.ok(digitalRetailSessions);
    }

    @GetMapping("/dealers/{dealerId}")
    public ResponseEntity<List<OnlineUser>> getOnlineUsersByDealer(@PathVariable String dealerId) {
        List<OnlineUser> digitalRetailSessions = onlineUsersService.getOnlineUsersByDealer(dealerId);
        return ResponseEntity.ok(digitalRetailSessions);
    }
}
