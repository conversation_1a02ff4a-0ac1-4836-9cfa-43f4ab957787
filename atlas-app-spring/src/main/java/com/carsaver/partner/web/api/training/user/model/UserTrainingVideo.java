package com.carsaver.partner.web.api.training.user.model;

import com.carsaver.magellan.model.trainingvideo.TrainingVideoRecordView;
import com.carsaver.magellan.model.trainingvideo.TrainingVideoView;
import com.carsaver.magellan.model.trainingvideo.VideoResources;
import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Data
@Builder
class UserTrainingVideo{

    private String videoId;
    private String title;
    private String description;
    private Integer sort;
    private String url;
    private String userId;
    private ZonedDateTime completedAt;
    private List<VideoResources> resources;

    public static UserTrainingVideo from(TrainingVideoView video, Collection<TrainingVideoRecordView> userRecord) {

        var builder = UserTrainingVideo.builder()
                .videoId(video.getId())
                .title(video.getTitle())
                .description(video.getDescription())
                .url(video.getUrl())
                .sort(video.getSort())
                .resources(video.getResources());

        userRecord.stream()
                .filter(uRecord -> Objects.equals(uRecord.getVideoId(),video.getId()))
                .findFirst()
                .ifPresent(uRecord-> {
                    builder.userId(uRecord.getUserId());
                    builder.completedAt(uRecord.getCompletedAt());
                });

        return builder.build();
    }
}
