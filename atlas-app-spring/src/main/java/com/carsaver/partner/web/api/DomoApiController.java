package com.carsaver.partner.web.api;

import com.carsaver.partner.model.domo.DomoRequest;
import com.carsaver.partner.model.domo.DomoResponse;
import com.carsaver.partner.service.DomoReportService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/domo")
public class DomoApiController {

    private final DomoReportService domoReportService;

    public DomoApiController(DomoReportService domoReportService) {
        this.domoReportService = domoReportService;
    }

    @PostMapping("/generate-token")
    public ResponseEntity<DomoResponse> getDomoData(@RequestBody DomoRequest domoRequest) {
        DomoResponse domoResponse = domoReportService.domoEmbedTokenGeneration(domoRequest.getDealerId(), domoRequest.getProgramId(), domoRequest.getDashboardId());
        return ResponseEntity.ok(domoResponse);
    }
}
