package com.carsaver.partner.web.api.user.elasticsearch.model;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
public class EligibleProspectCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "stage")
    private String eligibleProspectStage;
    @TermQuery(field = "dealerId.keyword")
    private String upgradeProspectDealerId;
    @TermQuery(field = "dealerAcls")
    private List<String> dealerACLs;
    @TermQuery(field = "program.id")
    private List<String> programs;
}
