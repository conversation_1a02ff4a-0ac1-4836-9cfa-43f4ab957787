package com.carsaver.partner.web.api.user;

import com.carsaver.elasticsearch.model.sale.VehicleSaleDoc;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.client.VehicleSaleClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.lead.CreatedSource;
import com.carsaver.magellan.model.lead.SaleStatus;
import com.carsaver.magellan.model.lead.VehicleSaleView;
import com.carsaver.partner.elasticsearch.VehicleSaleDocService;
import com.carsaver.partner.elasticsearch.criteria.VehicleSaleSearchCriteria;
import com.carsaver.partner.model.user.UserVehicleSale;
import com.carsaver.partner.web.api.CustomerBaseController;
import com.carsaver.partner.web.api.SecurityHelperService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
//@PreAuthorize("hasPermission(#dealer, 'customer:read') or hasAuthority('read:user')")
public class UserVehicleSaleApiController implements CustomerBaseController {

    private final VehicleSaleDocService vehicleSaleDocService;
    private final VehicleSaleClient vehicleSaleClient;
    private final SecurityHelperService securityHelperService;
    private final DealerLinkClient dealerLinkClient;

    @Autowired
    public UserVehicleSaleApiController(VehicleSaleDocService vehicleSaleDocService, VehicleSaleClient vehicleSaleClient, SecurityHelperService securityHelperService, DealerLinkClient dealerLinkClient) {
        this.vehicleSaleDocService = vehicleSaleDocService;
        this.vehicleSaleClient = vehicleSaleClient;
        this.securityHelperService = securityHelperService;
        this.dealerLinkClient = dealerLinkClient;
    }

    @GetMapping("/api/users/{userId}/vehicles/sales")
    public UserVehicleSale getUserVehicleSaleData(@RequestParam(value = "dealerIds", required = false) List<String> dealerIds,
                                                  @PathVariable String userId,
                                                  @SortDefault(value = "createdDate", direction = Sort.Direction.DESC) Pageable pageable,
                                                  HttpSession session) {
        Objects.requireNonNull(userId);
        List<String> validateListOfDealerIds = getFinalListOfDealerAndValidateCustomerPermissions(dealerIds, session, securityHelperService);

        VehicleSaleSearchCriteria criteria = new VehicleSaleSearchCriteria();
        criteria.setUserId(userId);
        criteria.setDealerIds(validateListOfDealerIds);
        criteria.setStatuses(SaleStatus.VERIFIED);
        Collection<VehicleSaleDoc> saleDocs = vehicleSaleDocService.search(criteria, pageable).getContent();

        if (saleDocs.isEmpty()) {
            log.info("No Vehicle Sales found for User {} & Dealer {} with a status of {}", userId , validateListOfDealerIds.stream().collect(Collectors.joining(",")) , SaleStatus.VERIFIED);
            return null;
        }

        List<UserVehicleSale.VehicleSale> vehicleSales = saleDocs
            .stream()
            .map(UserVehicleSale.VehicleSale::from)
            .collect(Collectors.toList());

        return UserVehicleSale.builder().vehicleSales(vehicleSales).build();
    }

    @PostMapping("/api/dealer/{dealer}/users/{userId}/vehicles/sales")
    public ResponseEntity<VehicleSaleView> createSale(@PathVariable DealerView dealer, @RequestBody VehicleSaleRequest vehicleSaleRequest){
        VehicleSaleView savedVehicleSale = vehicleSaleClient.save(vehicleSaleRequest.toVehicleSaleView());
        return ResponseEntity.of(Optional.ofNullable(savedVehicleSale));
    }

    @Data
    private static class VehicleSaleRequest {
        private String userId;
        private String dealerId;
        private String vin;
        private LocalDate deliveryDate;

        public VehicleSaleView toVehicleSaleView(){
            VehicleSaleView vehicleSaleView = new VehicleSaleView();
            vehicleSaleView.setUserId(this.getUserId());
            vehicleSaleView.setDealerId(this.getDealerId());
            vehicleSaleView.setDeliveryDate(this.getDeliveryDate());
            vehicleSaleView.setVin(this.getVin());
            vehicleSaleView.setCreatedSource(CreatedSource.STATUS_SOLD);

            return vehicleSaleView;
        }
    }


}
