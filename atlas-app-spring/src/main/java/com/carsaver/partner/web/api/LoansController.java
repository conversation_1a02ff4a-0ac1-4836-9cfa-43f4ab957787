package com.carsaver.partner.web.api;

import com.carsaver.partner.client.financier.DecisionHistoryResponse;
import com.carsaver.partner.client.financier.FinancierClient;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class LoansController {
    private final FinancierClient financierClient;

    @GetMapping({"/api/loanresponses/{id}/changes"})
    public List<DecisionHistoryResponse> getRevisionsById(@PathVariable("id") Integer id) {
        return financierClient.getLoanDecisionHistoryById(id);
    }
}
