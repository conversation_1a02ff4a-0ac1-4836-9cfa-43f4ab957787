package com.carsaver.partner.web.api;

import com.carsaver.magellan.security.DealerPermissionEvaluator;
import com.carsaver.partner.exception.ForbiddenException;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

@Service
public class SecurityHelperService {

    public void checkPermission(Authentication authentication, List<String> dealerIds, String permission) {
        if (!StringUtils.hasText(permission)) {
            return;
        }

        boolean isAuthorized = DealerPermissionEvaluator.hasDealerPrivilege(authentication, dealerIds, permission);
        if (!isAuthorized) {
            throw new ForbiddenException(String.format("User does not have the permission %s for one or more dealers from: dealerIds[%s]", permission, dealerIds));
        }
    }
}
