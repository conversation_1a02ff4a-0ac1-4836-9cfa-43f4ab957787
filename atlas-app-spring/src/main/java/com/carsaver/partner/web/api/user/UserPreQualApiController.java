package com.carsaver.partner.web.api.user;

import com.carsaver.partner.prequalification.PreQualRecord;
import com.carsaver.partner.prequalification.PreQualService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping
public class UserPreQualApiController {

    private final PreQualService preQualService;

    @GetMapping("/api/users/{customerId}/pre-qual")
    public ResponseEntity<List<PreQualRecord>> fetchPreQualRecords(@PathVariable String customerId) {
        var response = preQualService.getPreQualRecordsByCustomerId(customerId);
        return ResponseEntity.ok(response);
    }
}
