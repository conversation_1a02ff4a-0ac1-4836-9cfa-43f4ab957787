package com.carsaver.partner.web.api;

import com.carsaver.partner.model.QuickLink;
import com.carsaver.partner.service.QuickLinksService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/quick-links")
@RequiredArgsConstructor
public class QuickLinkApiController {

    private final QuickLinksService quickLinksService;

    @GetMapping
    public List<String> getDealerQuickLinkStatuses() {

        return Arrays.asList(QuickLinkStatus.MY_GARAGE, QuickLinkStatus.MY_DOCUMENTS, QuickLinkStatus.MY_TRADE);
    }

    @GetMapping(value = "/v2",params = {"userId", "dealerId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<QuickLink> getDealerQuickLinkStatus(@RequestParam("userId") String userId,
                                              @RequestParam("dealerId") String dealerId) {
        return quickLinksService.getDealerQuickLinkStatuses(userId, dealerId);
    }

}


class QuickLinkStatus {
    public static final String MY_TRADE = "My Trades";
    public static final String MY_GARAGE = "My Garage";
    public static final String MY_DOCUMENTS = "My Documents";

    private QuickLinkStatus() {

    }

}
