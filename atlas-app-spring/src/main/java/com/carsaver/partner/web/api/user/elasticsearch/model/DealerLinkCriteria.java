package com.carsaver.partner.web.api.user.elasticsearch.model;

import com.carsaver.search.annotation.TermQuery;
import com.carsaver.search.query.AbstractSearchCriteria;
import com.carsaver.search.query.BoolQueryOccurrence;
import com.carsaver.search.query.SearchScope;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
public class DealerLinkCriteria extends AbstractSearchCriteria {

    @TermQuery(field = "dealerLinks.dealerId", scope = SearchScope.QUERY)
    private List<String> dealerLinks;

    @TermQuery(path = "dealerLinks", field = "dealerId", scope = SearchScope.QUERY, boolQuery = BoolQueryOccurrence.MUST, nested = true)
    private List<String> dealerLinksNested;
}
