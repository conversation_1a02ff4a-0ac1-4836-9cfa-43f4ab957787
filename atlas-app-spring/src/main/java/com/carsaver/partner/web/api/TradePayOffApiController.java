package com.carsaver.partner.web.api;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.partner.converter.UserVehicleViewToPayoffQuoteConverter;
import com.carsaver.partner.model.TradePayoff;
import com.carsaver.partner.model.deal.TradeOfferModel;
import com.carsaver.partner.service.trade.DealerTradeQuoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Optional;


@Slf4j
@RestController
public class TradePayOffApiController {

    @Autowired
    private UserVehicleViewToPayoffQuoteConverter userVehicleViewToPayoffQuoteConverter;

    @Autowired
    private DealerTradeQuoteService dealerTradeQuoteService;

    @PreAuthorize("hasPermission(#dealer, 'customer:deal:edit')")
    @PatchMapping("/api/dealer/{dealer}/users/trades/{userVehicleId}/payoff")
    public ResponseEntity<TradeOfferModel.PayoffQuote> updateTradePayOff(@PathVariable DealerView dealer, @PathVariable String userVehicleId, @RequestBody @Valid TradePayoff tradePayoff){
        UserVehicleView userVehicle = dealerTradeQuoteService.updateTradePayoff(userVehicleId, tradePayoff);
        TradeOfferModel.PayoffQuote payoffQuote = userVehicleViewToPayoffQuoteConverter.convert(userVehicle);

        return ResponseEntity.of(Optional.ofNullable(payoffQuote));
    }

}
