package com.carsaver.partner.web.api.user.elasticsearch;

import com.carsaver.elasticsearch.CriteriaSearchHandler;
import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.magellan.client.ZipCodeClient;
import com.carsaver.magellan.model.ZipCodeView;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import com.carsaver.partner.web.api.user.elasticsearch.model.SearchCriteria;
import com.carsaver.search.BoolQueryCollector;
import com.carsaver.search.support.FacetParser;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

@Component
public class CustomersDocService extends CriteriaSearchHandler<UserAndProspectDoc> {

    private static final String[] INDEX = {"users-and-prospects"};
    private static final String[] USERS_AND_PROSPECTS_AND_PROSPECT_LEADS = {"users-and-prospects", "prospect-leads"};

    public static final String GLOBAL_STAGE = "globalStage";
    public static final String PROGRAM_SUBSCRIPTION_STAGE = "stage";

    private static final Map<String, String> SORT_KEY_MAP = Map.ofEntries(
        Map.entry("firstName", "firstName.raw"),
        Map.entry("lastName", "lastName.raw"),
        Map.entry("email", "email.raw"),
        Map.entry("phoneNumber", "phoneNumber.raw"),
        Map.entry("fullName", "firstName.raw,lastName.raw"),
        Map.entry("vehicleOfInterest", "vehicleOfInterest.stockType.keyword"),
        Map.entry("vehicleOfInterest.make", "vehicleOfInterest.make.keyword"),
        Map.entry("vehicleOfInterest.model", "vehicleOfInterest.model.keyword"),
        Map.entry("vehicleOfInterest.stockNumber", "vehicleOfInterest.stockNumber.keyword"),
        Map.entry("address.city", "address.city.keyword"),
        Map.entry("address", "address.street.keyword"),
        Map.entry("address.dma.name","address.dma.name.keyword"),
        Map.entry("traits.tradeInsCount","nested|traits.tradeInsCount"),
        Map.entry("traits.activeVehiclesInGarageCount","traits.dealerUserTraitsList.activeVehiclesInGarageCount"),
        Map.entry("traits.dealerUserTraitsList.vehiclesInGarageCount","traits.dealerUserTraitsList.vehiclesInGarageCount"),
        Map.entry("traits.new","traits.dealerUserTraitsList.stockTypeNewVehiclesInGarageCount"),
        Map.entry("traits.used","traits.dealerUserTraitsList.stockTypeUsedVehiclesInGarageCount"),
        Map.entry(PROGRAM_SUBSCRIPTION_STAGE,"programSubscriptionStages.rank"),
        Map.entry(GLOBAL_STAGE,"rankedStage"),
        Map.entry("clientAdvisor","clientAdvisor.keyword"),
        Map.entry("lenderName","lenderName.raw"),
        Map.entry("tradeMake","tradeMake.keyword"),
        Map.entry("tradeModel","tradeModel.keyword"),
        Map.entry("tradePaymentType","tradePaymentType.keyword"),
        Map.entry("tradeTrim","tradeTrim.keyword"),
        Map.entry("tradeVin","tradeVin.keyword"),
        Map.entry("tier","tier.keyword"),
        Map.entry("tradePurchaseType","tradePurchaseType.keyword"),
        Map.entry("traits.garageSavedVehicleCount","traits.dealerUserTraitsList.garageSavedVehicleCount"),
        Map.entry("traits.financeAppsSubmittedCount","traits.dealerUserTraitsList.financeAppsSubmittedCount"),
        Map.entry("traits.garageViewedVehicleCount","traits.dealerUserTraitsList.garageViewedVehicleCount"),
        Map.entry("stageTransition","programSubscriptionStages.lastModifiedDate"),
        Map.entry("assignedDealer","assignedDealer.keyword"),
        Map.entry("customerStatus", "dealerLinks.status"),
        Map.entry("traits.leadsCount", "traits.dealerUserTraitsList.leadsCount")
    );

    private final ZipCodeClient zipCodeClient;
    private final SplitFeatureFlags splitFeatureFlags;

    public CustomersDocService(@Qualifier("gibson") ElasticClient elasticClient, FacetParser facetParser, ZipCodeClient zipCodeClient, SplitFeatureFlags splitFeatureFlags) {
        super(elasticClient, facetParser);
        this.zipCodeClient = zipCodeClient;
        this.splitFeatureFlags = splitFeatureFlags;
    }


    @Override
    protected void appendRootQuery(Object c, BoolQueryCollector boolQueryCollector) {
        final SearchCriteria criteria = (SearchCriteria) c;

        QueryBuilder zipCodeQuery = null;
        if (criteria.isZipCodeFiltered()) {
            // if we have a distance we will do a geoDistanceQuery otherwise just a termsQuery
            if (criteria.isDistanceFiltered()) {
                Optional<ZipCodeView> zipCodeOpt = zipCodeClient.findByZipCode(criteria.getZipCode());

                if (zipCodeOpt.isPresent()) {
                    ZipCodeView zipCode = zipCodeOpt.get();
                    zipCodeQuery = QueryBuilders.geoDistanceQuery("address.geo.location")
                        .distance(criteria.getDistance(), DistanceUnit.MILES)
                        .point(zipCode.getLat(), zipCode.getLng());
                }
            } else {
                zipCodeQuery = termsQuery("address.zipCode", criteria.getZipCode());
            }
        }

        Stream.of(zipCodeQuery)
            .filter(Objects::nonNull)
            .forEach(boolQueryCollector::filter);
    }

    @Override
    protected void appendPostFilter(Object c, BoolQueryCollector boolQueryCollector) {
        SearchCriteria criteria = (SearchCriteria) c;

        if (criteria.isPurchaseTimeFrameFiltered()) {
            RangeQueryBuilder timeFrameStartRange = QueryBuilders.rangeQuery("purchaseTimeFrameStartDate")
                .from(criteria.getPurchaseTimeFrame().getStart())
                .to(criteria.getPurchaseTimeFrame().getEnd());

            RangeQueryBuilder timeFrameEndRange = QueryBuilders.rangeQuery("purchaseTimeFrameEndDate")
                .from(criteria.getPurchaseTimeFrame().getStart())
                .to(criteria.getPurchaseTimeFrame().getEnd());

            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .should(timeFrameStartRange)
                .should(timeFrameEndRange);

            boolQueryCollector.filter(boolQueryBuilder);
        }
    }

    @Override
    protected String[] getSearchIndex() {
        if (splitFeatureFlags.isIncludeProspectLeadsWhispEnabled()) {
            return USERS_AND_PROSPECTS_AND_PROSPECT_LEADS;
        } else {
            return INDEX;
        }
    }

    @Override
    protected Map<String, String> getSortKeyMap() {
        return SORT_KEY_MAP;
    }

}
