package com.carsaver.partner.web.api;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.partner.model.DealProgramAndDomain;
import com.carsaver.partner.service.ProgramApiService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@AllArgsConstructor
@RestController
@Slf4j
@RequestMapping("/api/deal-program-domain")
public class ProgramApiController {

    private final ProgramClient programClient;
    private final UserClient userClient;

    private final ProgramApiService programApiService;

    @GetMapping(params = {"campaignId", "dealerId"})
    public DealProgramAndDomain getDealProgram(@RequestParam("campaignId") String campaignId,
                                               @RequestParam("dealerId") String dealerId,
                                               @Value("${dynamoDB.dealerTable}") String dealerTable) {
        final String userId = AuthUtils.getUserIdFromSecurityContext().orElseThrow();
        return programApiService.getDealProgramAndDomain(userId, campaignId, dealerId, dealerTable);
    }

    @GetMapping(value = "/{userId}", params = {"dealerId"})
    public DealProgramAndDomain findByUserId(@PathVariable String userId, @RequestParam("dealerId") String dealerId,
                                             @Value("${dynamoDB.dealerTable}") String dealerTable) {
        return programApiService.getDealProgramAndDomain(userId, dealerId, dealerTable);
    }



}
