package com.carsaver.partner.web.api.warranty;

import com.carsaver.core.DealerStatus;
import com.carsaver.core.StockType;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.magellan.model.chrome.StyleView;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.model.warranty.PremiumsRequestModel;
import com.carsaver.partner.service.warranty.WarrantyMailService;
import com.carsaver.partner.service.warranty.WarrantyService;
import com.carsaver.warranty.model.ApplicantRegistration;
import com.carsaver.warranty.model.DealerEnrollment;
import com.carsaver.warranty.model.DealerEnrollmentException;
import com.carsaver.warranty.model.PremiumsRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.util.Base64Utils;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.beanvalidation.SpringValidatorAdapter;

import javax.validation.Valid;
import java.security.Principal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class WarrantyControllerHelper {

    public static final int MAX_PURCHASE_POST_DATED_DAYS = 3;

    public static final int USER_TAGS_WALMART_ASSOCIATE = 6;

    @Autowired
    private WarrantyService warrantyService;

    @Autowired
    private InventoryClient inventoryClient;

    @Autowired
    private WarrantyMailService warrantyMailService;

    @Autowired
    private SpringValidatorAdapter springValidatorAdapter;

    public String getRemittanceUrl(List<DealerView> dealers, Principal user) {
        try {
            String url = warrantyService.buildRemittancePortalUrlFrom(user.getName(), dealers);
            return "redirect:" + url;
        } catch(Exception e) {
            log.error("error encountered getting eRemittance launch url oauth token, cannot obtain ", e);

            //send to ePortal login screen
            return "redirect:" + warrantyService.geteRemittancePortalUrl();
        }
    }

    public String getSupplyOrdering(List<DealerView> dealers, Principal user) {
        try {
            String url = warrantyService.buildSupplyOrderingUrlFrom(user.getName(), dealers);
            return "redirect:" + url;
        } catch(Exception e) {
            log.error("error encountered getting supply ordering launch url oauth token, cannot obtain ", e);

            //send to ePortal login screen
            return "redirect:" + warrantyService.getSupplyOrderPortalUrl();
        }
    }

    public Optional<LocalDate> getEffectiveDealerEnrollmentDate(DealerView dealer) {
        DealerEnrollment existingDealerEnrollment;
        LocalDate effectiveLocalDate = null;
        try {
            existingDealerEnrollment = warrantyService.getDealerEnrollmentBy(dealer);

            if(existingDealerEnrollment != null && existingDealerEnrollment.getEffectiveDate() != null ) {
                effectiveLocalDate = ZonedDateTime.ofInstant(
                    existingDealerEnrollment.getEffectiveDate().toInstant(),
                    ZoneId.of("UTC")).toLocalDate();
            }
        } catch(DealerEnrollmentException e) {
            log.error("problem finding existing dealer enrollment", e);
        }

        return Optional.ofNullable(effectiveLocalDate);
    }

    public PremiumsRequest getPremiumsRequest(DealerView dealer, String vin, boolean isWalmartEmployee) {

        PremiumsRequest premiumsRequest = new PremiumsRequest(dealer.getDealerId());
        premiumsRequest.setPurchaseDate(new Date());
        premiumsRequest.setCarSaverTransaction(SecurityUtils.isAdminUser());
        premiumsRequest.setVin(vin);
        premiumsRequest.setWalmartEmployee(isWalmartEmployee);

        return premiumsRequest;
    }

    public BindingResult validatePremiumsRequest(
        DealerView dealer,
        PremiumsRequestModel premiumsRequest,
        UserView user) {

        BindingResult bindingResult = new BeanPropertyBindingResult(premiumsRequest, "premiumsRequest");
        springValidatorAdapter.validate(premiumsRequest, bindingResult);
        if(bindingResult.hasErrors()) {
            return bindingResult;
        }

        if(bindingResult.hasErrors()) {
            return bindingResult;
        }

        LocalDate purchaseDate = premiumsRequest.getPurchaseDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate dealerEffectiveEnrollmentLocalDate = this.getEffectiveDealerEnrollmentDate(dealer).orElse(null);

        //check purchaseDate not past max post-date
        if(purchaseDate.isAfter(LocalDate.now().plusDays(MAX_PURCHASE_POST_DATED_DAYS))) {
            bindingResult.rejectValue("purchaseDate", "purchaseDate.invalid",
                "Purchase Date CANNOT be post-dated more than " + MAX_PURCHASE_POST_DATED_DAYS + " days");
        }

        //check purchaseDate not before dealer enrollment effective date
        log.info("checking that purchaseDate = {} is NOT before effectiveDate = {}", purchaseDate, dealerEffectiveEnrollmentLocalDate);
        if(dealerEffectiveEnrollmentLocalDate != null && purchaseDate.isBefore(dealerEffectiveEnrollmentLocalDate)) {

            bindingResult.rejectValue("purchaseDate", "purchaseDate.invalid",
                "Purchase date: " + purchaseDate + " can NOT be before Dealer enrollment date of: " + dealerEffectiveEnrollmentLocalDate);
        }

        //check if PROSPECT dealer, carSaverTransaction MUST be TRUE. i.e. only CarSaver employees(isAdmin=true) can set carSaverTransaction to TRUE
        if(DealerStatus.PROSPECT == dealer.getDealerStatus() && !premiumsRequest.isCarSaverTransaction()) {
            bindingResult.rejectValue("generalError", "prospectDealer.invalid",
                "Dealer has status = 'PROSPECT', CarSaver Transaction must be 'Yes', Only a CarSaver employee can complete this registration");
        }

        // check if Odometer has discrepancy between what dealer enters for miles and what the database has
        Collection<VehicleView> vehicleInDb = inventoryClient.getVehiclesByVin(premiumsRequest.getVin()).getContent();
        if(!vehicleInDb.isEmpty() && vehicleInDb.iterator().next().getStockType() == StockType.USED) {
            VehicleView vehicle = vehicleInDb.iterator().next();
            Integer milesInDb = vehicle.getMiles();
            Integer milesFromDealer = premiumsRequest.getOdometer();

            if(milesInDb != null) {
                int milesWithCushion = milesInDb - 50;

                if (milesFromDealer < milesWithCushion) {
                    String mileageMessage = "Current vehicle data indicates the miles must not be less than " + String.format("%,d", milesWithCushion);
                    bindingResult.rejectValue("odometer", "odometer.invalid", mileageMessage);

                    String vehicleName = Optional.of(vehicle.getStyle()).map(StyleView::getFullName).orElse(null);
                    warrantyMailService.emailWarrantyOdometerNotification(dealer, toPremiumsRequest(premiumsRequest, dealer, user), user.getFullName(), vehicleName, milesFromDealer, milesInDb);
                }
            }
        }

        return bindingResult;
    }

    public PremiumsRequest toPremiumsRequest(PremiumsRequestModel premiumsRequestModel, DealerView dealer, UserView user) {
        PremiumsRequest premiumsRequest = new PremiumsRequest(dealer.getDealerId());
        BeanUtils.copyProperties(premiumsRequestModel, premiumsRequest);
        premiumsRequest.setWalmartEmployee(user.tagExists(WarrantyControllerHelper.USER_TAGS_WALMART_ASSOCIATE));

        return premiumsRequest;
    }

    public ResponseEntity<byte[]> getWarrantyContractPdf(Model model, DealerView dealer, String warrantyContractId){
        WarrantyContractView warrantyContract = warrantyService.getWarrantyContract(warrantyContractId);
        ResponseEntity<byte[]> response;

        if(warrantyContract != null){
            try {
                byte[] pdf = warrantyService.getRegistrationApplicationPdf(warrantyContract);

                response = ResponseEntity.ok()
                                         .headers(formPdfHttpHeaders(warrantyContract))
                                         .body(decodePdfResponse(pdf));
            } catch (Exception e) {
                log.error("Error getting PDF for warranty contract {}. Exception was {}", warrantyContract.getId(), e.getMessage());
                response = ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
            }
        } else {
            log.error("Error getting PDF for warranty contract. Warranty Contract not found for id {}", warrantyContractId);
            response = ResponseEntity.notFound().build();
        }

        return response;
    }

    public ResponseEntity<byte[]> getWarrantyContractPdfPreview(@Valid ApplicantRegistration applicantRegistration){

        ResponseEntity<byte[]> response;

        try {
            byte[] pdf = warrantyService.getRegistrationApplicationPdfPreview(applicantRegistration);

            response = ResponseEntity.ok()
             .headers(formPdfHttpHeaders())
             .body(decodePdfResponse(pdf));

        } catch (Exception e) {

            log.error("Error generating PDF preview. Exception was {}", e.getMessage());
            response = ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(null);
        }

        return response;
    }

    //helper methods
    private byte[] decodePdfResponse(byte[] pdfResponse){
        String pdfString = new String(pdfResponse);
        // trim leading and trailing quotes
        String pdfWithoutQuotes = pdfString.replaceAll("(?:^\")|(?:\"$)", "");
        return Base64Utils.decode(pdfWithoutQuotes.getBytes());
    }

    private HttpHeaders formPdfHttpHeaders(){
        return formPdfHttpHeaders(Optional.empty());
    }

    private HttpHeaders formPdfHttpHeaders(WarrantyContractView warrantyContract){

        String buyerName = Optional.ofNullable(warrantyContract.getApplicantRegistration().getBuyer())
                                   .map(buyer -> buyer.getFirstName() + "-" + buyer.getLastName())
                                   .orElse("unknown-buyer");

        String fileName = new StringBuilder(buyerName)
            .append("-warranty-contract-")
            .append(warrantyContract.getRegistrationId())
            .append(".pdf")
            .toString();

        return formPdfHttpHeaders(Optional.of(fileName));
    }

    /***
     * Create the correct HTTP headers for returning the PDF.
     * If a fileName is present, the headers will instruct the
     * browser to download the file automatically.
     *
     * @param fileName
     * @return
     */
    private HttpHeaders formPdfHttpHeaders(Optional<String> fileName){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/pdf"));
        if(fileName.isPresent()) {
            headers.setContentDispositionFormData(fileName.get(), fileName.get());
        }
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

        return headers;
    }
}
