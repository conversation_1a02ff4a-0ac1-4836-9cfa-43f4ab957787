package com.carsaver.partner.service;

import com.carsaver.elasticsearch.model.DealerDoc;
import com.carsaver.partner.http.HttpRequest;
import com.carsaver.partner.http.HttpService;
import com.carsaver.partner.model.domo.*;
import com.carsaver.partner.reporting.service.ProgramService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DomoReportService {

    private final ProgramService programService;
    private final HttpService httpService;

    @Value("${domo.client-id}")
    private String clientId;
    @Value("${domo.client-secret}")
    private String clientSecret;
    private String embedId;
    @Value("${domo.api-host}")
    private String apiHost;
    @Value("${domo.grant-type}")
    private String grantType;
    @Value("${domo.scopes}")
    private String scopes;

    public DomoReportService(ProgramService programService, HttpService httpService) {
        this.programService = programService;
        this.httpService = httpService;
    }

    /**
     * To get embedId and filtered embedToken for dealer or program
     **/
    public DomoResponse domoEmbedTokenGeneration(String dealerId, String programId, String dashboardId) {
        this.embedId = dashboardId;
        String accessToken = getAccessToken();
        String embedToken = getEmbedToken(accessToken, dealerId, programId);
        return DomoResponse.builder().embedId(embedId).embedToken(embedToken).build();
    }

    /**
     * To get AccessToken by calling domo api that use for generating EmbedToken
     **/
    public String getAccessToken() {
        String accessTokenUrl = apiHost + "/oauth/token";
        Map<String, List<String>> queryParams = new HashMap<>();
        queryParams.put("grant_type", List.of(grantType));
        queryParams.put("scopes", List.of(scopes));
        HttpRequest httpRequest = HttpRequest.get(accessTokenUrl)
            .queryParams(queryParams)
            .basicAuth(clientId, clientSecret);
        DomoAuthResponse domoAuthResponse = httpService.getSuccessResponse(httpRequest, new TypeReference<>() {});
        return domoAuthResponse.getAccessToken();
    }

    /**
     * To get EmbedToken by calling domo embed api using filtering by dealerId or programId
     **/
    public String getEmbedToken(String accessToken, String dealerId, String programId) {
        List<String> permissions = List.of("READ", "FILTER", "EXPORT");
        List<String> dealerIds = new ArrayList<>();
        List<DomoEmbedAuthorization> domoEmbedAuthorizations = new ArrayList<>();
        String embedTokenUrl = apiHost + "/v1/stories/embed/auth";
        dealerIds.add(dealerId);
        if (checkDealerIdAvailable(dealerId)) {
            dealerIds = getDealersForProgram(programId);
        }
        List<DomoFilter> filters = getAllFilterForDomo(dealerIds);
        DomoEmbedAuthorization domoEmbedAuthorization = DomoEmbedAuthorization.builder().token(embedId).filters(filters)
            .permissions(permissions).build();
        domoEmbedAuthorizations.add(domoEmbedAuthorization);
        DomoEmbedRequest embedTokenRequest = DomoEmbedRequest.builder().authorizations(domoEmbedAuthorizations).build();
        HttpRequest httpRequest = HttpRequest.post(embedTokenUrl).bearerToken(accessToken)
            .ofJson(embedTokenRequest);
        DomoEmbedResponse domoEmbedResponse = httpService.getSuccessResponse(httpRequest, new TypeReference<>() {});
        return domoEmbedResponse.getAuthentication();
    }

    /**
     * created filters for all cards based on dealerId
     **/
    public List<DomoFilter> getAllFilterForDomo(List<String> dealerIds) {
        String columnName = "dealer_id";
        List<DomoFilter> filters = new ArrayList<>();
        filters.add(getDomoFilter(columnName, dealerIds));
        return filters;
    }

    /**
     * setting filter for domo dashboard with column and values
     **/
    public DomoFilter getDomoFilter(String columnName, List<String> values) {
        return DomoFilter.builder().column(columnName).
            operator("IN").values(values).build();
    }

    /**
     * checking if dealer id is available
     **/
    public boolean checkDealerIdAvailable(String dealerId) {
        return Objects.isNull(dealerId);
    }

    /**
     * getting dealers using programId
     **/
    public List<String> getDealersForProgram(String programId) {
        List<String> accessDealerIds = programService.getDealerIds();
        Collection<DealerDoc> dealers = programService.getDealersByPrograms(List.of(programId));
        return dealers.stream()
            .map(DealerDoc::getId)
            .filter(accessDealerIds::contains)
            .collect(Collectors.toList());
    }
}
