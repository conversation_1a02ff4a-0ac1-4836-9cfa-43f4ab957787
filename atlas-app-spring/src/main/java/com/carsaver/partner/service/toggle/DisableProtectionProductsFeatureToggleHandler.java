package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class DisableProtectionProductsFeatureToggleHandler implements FeatureToggleHandler {

    private final FeatureSubscriptionsClient featureSubscriptionsClient;
    private final HttpSession session;

    @Value("${features-subscription.routeone-f-and-i-feature-id}")
    private String routeOneFAndIFeatureId;

    @Value("${features-subscription.nesna-f-and-i-feature-id}")
    private String nesnaFAndIFeatureId;

    public static final String PROTECTION_PRODUCTS_FEATURE_ENABLED = "protectionProductionFeatureEnabled";


    @Override
    public boolean supports(String configType) {
        return "disable_protection_products_global_toggle".equalsIgnoreCase(configType);
    }

    @Override
    public DealerProgram handleFeatureToggle(String dealerId, String programId, ToggleConfigRequest toggleConfigRequest) {
        FeatureSubscriptionRequest fiRequest = new FeatureSubscriptionRequest();
        fiRequest.setDealerId(dealerId);
        fiRequest.setEntityId(programId);
        fiRequest.setFeatureRId(nesnaFAndIFeatureId);
        fiRequest.setActive(false);
        FeatureSubscriptionResponse fiResponse = featureSubscriptionsClient.saveFeatureSubscription(fiRequest);
        FeatureSubscriptionResponse routeOneResponse = new FeatureSubscriptionResponse();

        if (fiResponse != null && fiResponse.getActive() != null) {
            FeatureSubscriptionRequest routeOneRequest = new FeatureSubscriptionRequest();
            routeOneRequest.setDealerId(dealerId);
            routeOneRequest.setEntityId(programId);
            routeOneRequest.setFeatureRId(routeOneFAndIFeatureId);
            routeOneRequest.setActive(false);
            routeOneResponse = featureSubscriptionsClient.saveFeatureSubscription(routeOneRequest);
        }

        session.setAttribute(PROTECTION_PRODUCTS_FEATURE_ENABLED, toggleConfigRequest.getIsEnabled());

        return DealerProgram.builder()
            .isRouteOneFAndIEnabled(routeOneResponse.getActive())
            .isNesnaFAndIEnabled(Optional.ofNullable(fiResponse).map(FeatureSubscriptionResponse::getActive).orElse(false))
            .build();
    }

}
