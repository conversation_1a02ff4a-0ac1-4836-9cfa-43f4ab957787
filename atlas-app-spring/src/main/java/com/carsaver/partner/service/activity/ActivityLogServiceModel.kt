package com.carsaver.partner.service.activity

import com.carsaver.partner.client.activity.ActivityLog
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap

data class ActivityMetadata(
    var referrer: String? = null,
    var origin: String? = null,
    var campaignId: String? = null,
    var dealerId: String? = null,
    var vehicleId: String? = null,
    var vin: String? = null,
    var preQualStatus: String? = null,
    var certificateId: Long? = null
)

enum class ActivityType(vararg identifiers: ActivityTypeIdentifier) {
    ACCOUNT_CREATED(ActivityTypeIdentifier("create", "account")),
    LOGIN(ActivityTypeIdentifier("login-success", "login")),
    VEHICLE_VIEWED(ActivityTypeIdentifier("vehicle-viewed", "site")),
    DEAL_SAVED(ActivityTypeIdentifier("vehicle-saved", "site")),
    LEAD_SENT(
        ActivityTypeIdentifier("adf-to-nissan-digital-shift", "email"),
        ActivityTypeIdentifier("adf-pre-flight-to-crm", "email"),
        ActivityTypeIdentifier("adf-to-crm", "email")
    ),
    PRE_QUAL_COMPLETED(ActivityTypeIdentifier("pre-qual-completed", "site")),
    CREDIT_APPLICATION_SUBMITTED(ActivityTypeIdentifier("finance-submitted", "site")),

    CHECKIN(ActivityTypeIdentifier("checkin-complete", "checkin")),
    WEBSITE_VISIT_SUCCESSFUL_LOGIN(ActivityTypeIdentifier("successful-login", "website-visits"));


    init {
        for (identifier in identifiers) {
            ActivityTypeIdentifier.MUTABLE_MAP[identifier] = this
        }
    }

    companion object {
        fun from(log: ActivityLog): ActivityType? {
            val identifier = ActivityTypeIdentifier(log.eventName!!, log.eventType!!)
            return ActivityTypeIdentifier.IMMUTABLE_MAP[identifier]

        }
    }
}


data class ActivityTypeIdentifier(
    val name: String,
    val type: String
) {
    companion object {
        val MUTABLE_MAP: MutableMap<ActivityTypeIdentifier, ActivityType> = ConcurrentHashMap()
        val IMMUTABLE_MAP: Map<ActivityTypeIdentifier, ActivityType>
            get() = MUTABLE_MAP.toMap() // Creates an immutable copy on access
    }
}

data class ActivityLogDTO(
    var activityType: ActivityType? = null,
    var userId: String? = null,
    var campaignId: String? = null,
    var vehicleId: String? = null,
    var vin: String? = null,
    var styleId: Int? = null,
    var dealerId: String? = null,
    var eventTime: ZonedDateTime? = null,
    var certificateId: Long? = null,
    var leadId: String? = null,

    var make: String? = null,
    var model: String? = null,
    var year: Int? = null,
    var isVehicleActive: Boolean? = null,
    var preQualStatus: String? = null,

    var url: String? = null,
    var origin: String? = null
)

data class DigitalRetailActivityLogContainer(
    var earliestStartTime: ZonedDateTime? = null,
    var visitType: VisitType? = null,
    var dealerId: String? = null,
    var dealerName: String? = null,
    var dealerWebsite: String? = null,
    var activityLogs: List<ActivityLogDTO> = emptyList()
)

enum class VisitType {
    SHOWROOM,
    WEBSITE
}
