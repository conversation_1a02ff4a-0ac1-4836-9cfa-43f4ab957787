package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.model.protection_products.response.ErrorResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;

@Service
@RequiredArgsConstructor
@Slf4j
public class BoostProtectionProductsToggleHandler implements FeatureToggleHandler {

    private final FeatureSubscriptionsClient featureSubscriptionsClient;
    private final HttpSession session;

    @Value("${features-subscription.routeone-f-and-i-feature-id}")
    private String routeOneFAndIFeatureId;

    @Value("${features-subscription.carsaver-f-and-i-feature-id}")
    private String carsaverFAndIFeatureId;

    public static final String BOOST_PROTECTION_PRODUCTS_FEATURE_ENABLED = "boostProtectionProductsFeatureEnabled";

    @Override
    public boolean supports(String configType) {
        return "boost_route_one_finance_and_insurance".equals(configType) ||
            "boost_carsaver_f_and_i_feature".equals(configType) ||
            "disable_boost_protection_products_global_toggle".equals(configType);
    }

    @Override
    public DealerProgram handleFeatureToggle(String dealerId, String programId, ToggleConfigRequest toggleConfigRequest) {
        // Get the protection product to enable from the product type
        boolean isEnabled = toggleConfigRequest.getIsEnabled();
        FeatureSubscriptionResponse routeOneResponse = null;
        FeatureSubscriptionResponse carsaverFAndIResponse = null;
        String configType = toggleConfigRequest.getConfigType();

        // If the toggle is enabling a specific protection product
        if (isEnabled) {
            // Handle Route One selection
            if ("boost_route_one_finance_and_insurance".equals(configType)) {
                // Enable Route One
                routeOneResponse = toggleFAndIFeature(dealerId, programId, routeOneFAndIFeatureId, true);

                if (routeOneResponse == null || routeOneResponse.getActive() == null) {
                    throw new InternalServerError(ErrorResponse.builder().errorMessage("Failed to update Route One F&I feature subscription").build());
                }

                // Disable CarSaver since they are mutually exclusive
                carsaverFAndIResponse = toggleFAndIFeature(dealerId, programId, carsaverFAndIFeatureId, false);

                if (carsaverFAndIResponse == null) {
                    // Use a default response if API call failed
                    carsaverFAndIResponse = new FeatureSubscriptionResponse();
                    carsaverFAndIResponse.setActive(false);
                }

                session.setAttribute(BOOST_PROTECTION_PRODUCTS_FEATURE_ENABLED, true);
            }
            // Handle CarSaver LTW/VSC selection
            else if ("boost_carsaver_f_and_i_feature".equals(configType)) {
                // Enable CarSaver
                carsaverFAndIResponse = toggleFAndIFeature(dealerId, programId, carsaverFAndIFeatureId, true);

                if (carsaverFAndIResponse == null || carsaverFAndIResponse.getActive() == null) {
                    throw new InternalServerError(ErrorResponse.builder().errorMessage("Failed to update CarSaver F&I feature subscription").build());
                }

                // Disable Route One since they are mutually exclusive
                routeOneResponse = toggleFAndIFeature(dealerId, programId, routeOneFAndIFeatureId, false);

                if (routeOneResponse == null) {
                    // Use a default response if API call failed
                    routeOneResponse = new FeatureSubscriptionResponse();
                    routeOneResponse.setActive(false);
                }

                session.setAttribute(BOOST_PROTECTION_PRODUCTS_FEATURE_ENABLED, true);
            }
            // Handle global toggle disable
            else if ("disable_boost_protection_products_global_toggle".equals(configType)) {
                // Disable both features
                carsaverFAndIResponse = toggleFAndIFeature(dealerId, programId, carsaverFAndIFeatureId, false);
                routeOneResponse = toggleFAndIFeature(dealerId, programId, routeOneFAndIFeatureId, false);

                session.setAttribute(BOOST_PROTECTION_PRODUCTS_FEATURE_ENABLED, false);
            }
        } else {
            // If we're disabling, turn off both features
            carsaverFAndIResponse = toggleFAndIFeature(dealerId, programId, carsaverFAndIFeatureId, false);
            routeOneResponse = toggleFAndIFeature(dealerId, programId, routeOneFAndIFeatureId, false);
            session.setAttribute(BOOST_PROTECTION_PRODUCTS_FEATURE_ENABLED, false);
        }

        // Build the response with the current state of both protection products
        return DealerProgram.builder()
            .isRouteOneFAndIEnabled(routeOneResponse != null && Boolean.TRUE.equals(routeOneResponse.getActive()))
            .isCarsaverFAndIEnabled(carsaverFAndIResponse != null && Boolean.TRUE.equals(carsaverFAndIResponse.getActive()))
            .build();
    }

    private FeatureSubscriptionResponse toggleFAndIFeature(String dealerId, String programId, String featureRId, Boolean active) {
        FeatureSubscriptionResponse response;
        FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
        request.setDealerId(dealerId);
        request.setEntityId(programId);
        request.setFeatureRId(featureRId);
        request.setActive(active);
        response = featureSubscriptionsClient.saveFeatureSubscription(request);

        return response;
    }
}
