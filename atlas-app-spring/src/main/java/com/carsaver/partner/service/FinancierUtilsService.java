package com.carsaver.partner.service;

import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.FinancierView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.FinanceConfig;
import com.carsaver.magellan.model.dealer.DealerFinancierView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FinancierUtilsService {

    @Autowired
    FinancierClient financierClient;

    public List<String> getFinanciersByDealer(CampaignView campaign, DealerView dealer) {
        Set<FinancierView> configuredFinanciers = new HashSet<>();

        Optional<FinanceConfig> financeConfig = Optional.ofNullable(campaign)
            .map(CampaignView::getFinanceConfig);

        Boolean dealerFinanciersEnabled = financeConfig
            .map(FinanceConfig::getEnableDealerFinanciers)
            .orElse(false);

        // Add campaign financier
        financeConfig.flatMap(this::getCampaignFinancier)
            .ifPresent(configuredFinanciers::add);

        // Add dealer financiers if enabled at the campaign level
        if (Boolean.TRUE.equals(dealerFinanciersEnabled)) {
            List<FinancierView> dealerFinanciers = getDealerFinanciers(dealer);
            configuredFinanciers.addAll(dealerFinanciers);
        }

        List<String> result = configuredFinanciers
            .stream()
            .map(FinancierView -> FinancierView.getName())
            .collect(Collectors.toList());
        return result;
    }

    Optional<FinancierView> getCampaignFinancier(FinanceConfig financeConfig) {
        Objects.requireNonNull(financeConfig, "Unable to get a campaign financier for a null FinanceConfig");

        Optional<FinancierView> result = Optional.ofNullable(financeConfig.getEnabledFinancier())
            .flatMap(financierClient::findById);
        return result;
    }

    List<FinancierView> getDealerFinanciers(DealerView dealer) {
        List<FinancierView> result = Optional.ofNullable(dealer)
            .map(DealerView::getFinanciers)
            .orElse(Collections.emptyList())
            .stream()
            .filter(DealerFinancierView::isEnabled)
            .map(DealerFinancierView::getFinancier)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .collect(Collectors.toList());
        return result;
    }
}
