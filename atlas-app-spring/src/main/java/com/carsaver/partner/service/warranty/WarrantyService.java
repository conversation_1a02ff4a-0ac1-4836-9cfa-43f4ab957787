package com.carsaver.partner.service.warranty;


import com.carsaver.magellan.api.PartnerService;
import com.carsaver.magellan.client.WarrantyContractClient;
import com.carsaver.magellan.client.request.UpdateRequest;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.WarrantyContractView;
import com.carsaver.magellan.model.warranty.CreatedSource;
import com.carsaver.warranty.model.ApplicantRegistration;
import com.carsaver.warranty.model.ApplicantRegistrationException;
import com.carsaver.warranty.model.ApplicantRegistrationResponse;
import com.carsaver.warranty.model.AuthorizationRequest;
import com.carsaver.warranty.model.AuthorizationRequestException;
import com.carsaver.warranty.model.AuthorizationResponse;
import com.carsaver.warranty.model.DealerEnrollment;
import com.carsaver.warranty.model.DealerEnrollmentByDealerRequest;
import com.carsaver.warranty.model.DealerEnrollmentException;
import com.carsaver.warranty.model.FindSurchargesException;
import com.carsaver.warranty.model.FindSurchargesRequest;
import com.carsaver.warranty.model.Form;
import com.carsaver.warranty.model.GetFormsException;
import com.carsaver.warranty.model.GetFormsRequest;
import com.carsaver.warranty.model.GetRegistrationByIdException;
import com.carsaver.warranty.model.GetVehicleResponse;
import com.carsaver.warranty.model.Premium;
import com.carsaver.warranty.model.PremiumsRequest;
import com.carsaver.warranty.model.PremiumsRequestException;
import com.carsaver.warranty.model.Registration;
import com.carsaver.warranty.model.RegistrationApplicationPdfRequest;
import com.carsaver.warranty.model.RegistrationByIdRequest;
import com.carsaver.warranty.model.RegistrationPdfException;
import com.carsaver.warranty.model.RegistrationPdfPreviewException;
import com.carsaver.warranty.model.Surcharge;
import com.carsaver.warranty.model.VehicleRequest;
import com.carsaver.warranty.model.VehicleRequestException;
import com.carsaver.warranty.service.NwanClient;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.ConversionService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WarrantyService {

    @Autowired
    private NwanClient nwanClient;

    @Autowired
    @Qualifier("mvcConversionService")
    private ConversionService conversionService;

    @Autowired
    private PartnerService partnerService;

    @Autowired
    private WarrantyContractClient warrantyContractClient;

    @Getter
    @Value("${warranty-service.providerCode}")
    private String nwanProviderCode;

    @Value("${warranty-service.e-remittance-portal.url}")
    private String eRemittancePortalUrl;

    @Value("${warranty-service.supply-order-portal.url}")
    private String supplyOrderPortalUrl;

    @Value("${warranty-service.e-remittance-portal.role}")
    private String eRemittanceRoleId;

    @Value("${warranty-service.supply-order-portal.role}")
    private String supplyOrderingRoleId;

    @Async
    public Future<WarrantyContractView> refreshRemoteRegistrationAsync(String warrantyContractId) throws GetRegistrationByIdException {
        return new AsyncResult<>(refreshRemoteRegistration(warrantyContractId));
    }

    public WarrantyContractView refreshRemoteRegistration(String warrantyContractId) throws GetRegistrationByIdException {
        WarrantyContractView warrantyContract = warrantyContractClient.findById(warrantyContractId);
        WarrantyContractView updatedWarrantyContract = null;

        if(warrantyContract != null){
            String providerCode = Optional.ofNullable(warrantyContract.getApplicantRegistration())
                                          .map(ApplicantRegistration::getProviderCode)
                                          .orElse(nwanProviderCode);

            Registration fetchedRegistration = getRegistration(warrantyContract.getRegistrationId(), providerCode);
            updatedWarrantyContract = warrantyContractClient.update(warrantyContract.getId(), new RemoteRegistrationUpdateRequest(fetchedRegistration));
        }

        return updatedWarrantyContract;
    }
    @Data
    public static class RemoteRegistrationUpdateRequest implements UpdateRequest {
        private final Registration remoteRegistration;
    }

    public Registration getRegistration(Integer registrationId, String providerCode) throws GetRegistrationByIdException {
        RegistrationByIdRequest request = new RegistrationByIdRequest();
        request.setRegistrationId(registrationId);
        request.setProviderCode(providerCode);
        return this.getRegistration(request);
    }
    /***
     * Use this method to return a list of registration objects by
     * registration id.
     *
     * @param request
     * @return list of registration objects
     */
    public Registration getRegistration(RegistrationByIdRequest request) throws GetRegistrationByIdException {
        return nwanClient.getRegistration(request);
    }
    public String buildRemittancePortalUrlFrom(String username, List<DealerView> dealers) throws AuthorizationRequestException {
        AuthorizationRequest request = new AuthorizationRequest();
        request.setCarSaverUserName(username);
        request.setCarSaverDealerIds(dealers.stream().map(DealerView::getDealerId).collect(Collectors.toList()));
        request.setRequestedRoleId(eRemittanceRoleId);

       AuthorizationResponse response = nwanClient.requestAuthorization(request);

        return eRemittancePortalUrl + response.getAccessToken();
    }

    public String buildSupplyOrderingUrlFrom(String username, List<DealerView> dealers) throws AuthorizationRequestException {
        AuthorizationRequest request = new AuthorizationRequest();
        request.setCarSaverUserName(username);
        request.setCarSaverDealerIds(dealers.stream().map(DealerView::getDealerId).collect(Collectors.toList()));
        request.setRequestedRoleId(supplyOrderingRoleId);

        AuthorizationResponse response = nwanClient.requestAuthorization(request);

        return supplyOrderPortalUrl + response.getAccessToken();
    }

    public String geteRemittancePortalUrl() {
        return eRemittancePortalUrl;
    }

    public String getSupplyOrderPortalUrl() {
        return supplyOrderPortalUrl;
    }

    public DealerEnrollment getDealerEnrollmentBy(DealerView dealer) throws DealerEnrollmentException {
        Objects.requireNonNull(dealer, "dealer cannot be null");
        return nwanClient.getDealerEnrollment(new DealerEnrollmentByDealerRequest(dealer.getDealerId()));
    }

    /***
     * Use this method to explode a VIN
     *
     * @param request
     * @return vehicle data
     */
    public GetVehicleResponse getVehicle(VehicleRequest request) throws VehicleRequestException {
        return nwanClient.getVehicle(request);
    }

    /***
     * Use this method to return eligible premiums for a given
     * dealer, VIN, odometer and purchase date.
     *
     * @return eligible premiums
     */
    public List<Premium> getPremiums(PremiumsRequest request) throws PremiumsRequestException {
        return nwanClient.getPremiums(request);
    }

    /***
     * Use this method to get a watermarked preview of a registration
     * application.  This method generates a PDF document with a “DRAFT ONLY”
     * watermark and returns the document as a Byte[] object.
     *
     * @param applicantRegistration
     * @return byte array containing the generated watermarked PDF PREVIEW
     */
    public byte[] getRegistrationApplicationPdfPreview(ApplicantRegistration applicantRegistration) throws RegistrationPdfPreviewException {
        return nwanClient.getRegistrationApplicationPdfPreview(applicantRegistration);
    }

    /***
     * Get a persisted WarrantyContract by id
     *
     * @param warrantyContractId
     * @return a persisted WarrantyContract
     */
    public WarrantyContractView getWarrantyContract(String warrantyContractId) {
        return warrantyContractClient.findById(warrantyContractId);
    }

    /***
     * Use this method to get a final, non-watermarked pdf of a registration
     * application.  This method generates a PDF document and returns the
     * document as a Byte[] object.
     *
     * @param request
     * @return byte array containing the generated NON-watermarked PDF
     */
    public byte[] getRegistrationApplicationPdf(RegistrationApplicationPdfRequest request) throws RegistrationPdfException {
        return nwanClient.getRegistrationApplicationPdf(request);
    }

    /***
     * Use this method to get a final, non-watermarked pdf of a registration
     * application from a saved warrantyContract.  This method generates a PDF document and returns the
     * document as a Byte[] object.
     *
     * @param warrantyContractView
     * @return byte array containing the generated NON-watermarked PDF
     */
    public byte[] getRegistrationApplicationPdf(WarrantyContractView warrantyContractView) throws RegistrationPdfException {
        String providerCode = Optional.ofNullable(warrantyContractView.getApplicantRegistration())
                                      .map(applicantRegistration -> applicantRegistration.getProviderCode())
                                      .orElse("");

        RegistrationApplicationPdfRequest request = new RegistrationApplicationPdfRequest();
        request.setProviderCode(providerCode);
        request.setRegistrationId(warrantyContractView.getRegistrationId());

        return getRegistrationApplicationPdf(request);
    }

    /***
     * Use this method to return a list of forms applicable to a given
     * schedule.
     *
     * @param request
     * @return list of forms
     */
    public List<Form> getForms(GetFormsRequest request) throws GetFormsException {
        return nwanClient.getForms(request);
    }

    /***
     * Use this method to return a list of surcharges applicable to a
     * given premium.
     *
     * @param request
     * @return list of surcharges
     */
    public List<Surcharge> getSurcharges(FindSurchargesRequest request) throws FindSurchargesException {
        return nwanClient.getSurcharges(request);
    }

    /***
     * Use this method to enroll an Applicant with NAE/NWAN and save
     * the contract to the carsaver system. Also fetches NWAN's representation
     * of the Registration and stores that along with the WarrantyContract
     *
     * @param warrantyContract
     * @return the saved warranty contract
     */
    public WarrantyContractView registerApplicant(WarrantyContractView warrantyContract) throws ApplicantRegistrationException {
        ApplicantRegistrationResponse registrationResponse = nwanClient.enroll(warrantyContract.getApplicantRegistration());
        warrantyContract.setRegistrationId(registrationResponse.getRegistrationId());

        if(registrationResponse.getRegistrationId() != null){
            String providerCode = Optional.ofNullable(warrantyContract.getApplicantRegistration())
                .map(ApplicantRegistration::getProviderCode)
                .orElse("");

            try {
                Registration fetchedRegistration = getRegistration(registrationResponse.getRegistrationId(), providerCode);
                warrantyContract.setRemoteRegistration(fetchedRegistration);
            } catch (GetRegistrationByIdException e) {
                log.error("Successfully Registered applicant but unable to get registration from NWAN using registrationId:{}, providerCode:{}. Exception was {}", registrationResponse.getRegistrationId(), providerCode, e.getMessage());
            }
        }
        warrantyContract.setCreatedSource(CreatedSource.PORTAL);

        return warrantyContractClient.createWarrantyContract(warrantyContract);
    }
}
