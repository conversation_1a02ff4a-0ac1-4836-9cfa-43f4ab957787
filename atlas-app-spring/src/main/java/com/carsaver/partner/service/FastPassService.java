package com.carsaver.partner.service;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.service.adf.AdfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class FastPassService {

    private final AtlasActivityEventService atlasActivityEventService;
    private final AdfService adfService;

    public FastPassService(AtlasActivityEventService atlasActivityEventService, AdfService adfService) {
        this.atlasActivityEventService = atlasActivityEventService;
        this.adfService = adfService;
    }

    public void triggerFastPassLeadAndEvent(UserView user, DealerView dealer) {
        if (user == null || dealer == null) {
            log.warn("User or Dealer information is missing. Unable to trigger Fast Pass lead and event.");
            return;
        }

        triggerFastPassAdfLead(user, dealer);
        triggerFastPassActivityEvent(user, dealer);
    }

    private void triggerFastPassAdfLead(UserView user, DealerView dealer) {
        try {
            adfService.sendFastPassLead(user.getId(), dealer.getId());
        } catch (Exception e) {
            log.error("Error sending Fast Pass Lead for User ID: {} and Dealer ID: {}. Error: {}", user.getId(), dealer.getId(), e);
        }
    }

    private void triggerFastPassActivityEvent(UserView user, DealerView dealer) {
        try {
            atlasActivityEventService.fastPassActivityEvent(user, dealer);
        } catch (Exception e) {
            log.error("Error triggering Fast Pass Activity Event for User ID: {} and Dealer ID: {}. Error: {}", user.getId(), dealer.getId(), e);
        }
    }
}

