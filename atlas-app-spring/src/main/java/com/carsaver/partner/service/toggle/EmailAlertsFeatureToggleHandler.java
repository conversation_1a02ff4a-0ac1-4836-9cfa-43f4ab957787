package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.model.protection_products.response.ErrorResponse;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailAlertsFeatureToggleHandler implements FeatureToggleHandler {

    private final FeatureSubscriptionsClient featureSubscriptionsClient;
    private final SplitFeatureFlags splitFeatureFlags;

    @Value("${features-subscription.email-alerts-feature-id}")
    private String emailAlertsFeatureId;

    @Override
    public boolean supports(String configType) {
        return "email_alerts".equalsIgnoreCase(configType);
    }

    @Override
    public DealerProgram handleFeatureToggle(String dealerId, String programId, ToggleConfigRequest toggleConfigRequest) {
        FeatureSubscriptionRequest emailAlertsRequest = new FeatureSubscriptionRequest();
        emailAlertsRequest.setDealerId(dealerId);
        emailAlertsRequest.setEntityId(programId);
        emailAlertsRequest.setFeatureRId(emailAlertsFeatureId);
        emailAlertsRequest.setActive(toggleConfigRequest.getIsEnabled());
        FeatureSubscriptionResponse lmResponse = featureSubscriptionsClient.saveFeatureSubscription(emailAlertsRequest);

        if (lmResponse == null || lmResponse.getActive() == null) {
            throw new InternalServerError(ErrorResponse.builder().errorMessage("Failed to update Email Alerts feature").build());
        }

        return DealerProgram.builder()
            .isEmailAlertsFeatureEnabled(splitFeatureFlags.isEmailAlertsFeatureEnabled(dealerId,programId))
            .isEmailAlertsEnabled(lmResponse.getActive())
            .build();
    }
}
