package com.carsaver.partner.service;

import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.exception.InvalidDealerDeliveryFeeException;
import com.carsaver.partner.model.DeliveryFee;
import com.carsaver.partner.model.DeliveryFees;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class DealerDeliveryFeeService {
    private static final String DELIVERY_FEES_DEALER_ENDPOINT = "/delivery-fees/dealer/";
    private final NissanWebClient nissanWebClient;

    @Value("${dealer-service.api-uri}")
    private String dealerServiceUrl;

    public DealerDeliveryFeeService(NissanWebClient nissanWebClient) {
        this.nissanWebClient = nissanWebClient;
    }

    public List<DeliveryFee> getDealerDeliveryFees(String dealerId) {
        if (dealerId == null) {
            throw new IllegalArgumentException("Dealer ID cannot be null");
        }

        String url = dealerServiceUrl + DELIVERY_FEES_DEALER_ENDPOINT + dealerId;
        DeliveryFees deliveryFees;
        List<DeliveryFee> listOfDeliveryFees = new ArrayList<>();

        try {
            deliveryFees = nissanWebClient.get(url, DeliveryFees.class, "getDeliveryFees");
        } catch (Exception e) {
            log.error("Failed to get delivery fees for dealer ID: {}", dealerId, e);
            throw new NotFoundException(String.format("Failed to get delivery fees for dealer ID: %s", dealerId));
        }

        if (!Objects.isNull(deliveryFees)) {
            listOfDeliveryFees = deliveryFees.getDeliveryFees();
        }

        return listOfDeliveryFees;
    }

    public DeliveryFees createOrUpdateDeliveryFees(String dealerId, DeliveryFee deliveryFee) {
        if (dealerId == null || deliveryFee == null) {
            throw new IllegalArgumentException("Dealer ID and delivery fee cannot be null");
        }

        String url = dealerServiceUrl + DELIVERY_FEES_DEALER_ENDPOINT + dealerId;
        DeliveryFees dealerDeliveryFees = null;
        try {
            dealerDeliveryFees = nissanWebClient.post(url, deliveryFee, DeliveryFees.class, "updateDeliveryFee");
        } catch (WebClientResponseException e) {
            log.error("Failed to update delivery fee with ID: {} for dealer ID: {}", deliveryFee.getId(), dealerId, e);
            handleException(e.getStatusCode(), null , e.getResponseBodyAsString());
        }

        return dealerDeliveryFees;
    }


    public DeliveryFees deleteDeliveryFee(String dealerId, String deliveryFeeId) {
        if (dealerId == null || deliveryFeeId == null) {
            throw new IllegalArgumentException("Dealer ID and delivery fee id cannot be null");
        }

        String url = dealerServiceUrl + DELIVERY_FEES_DEALER_ENDPOINT + dealerId + "/" + deliveryFeeId;
        DeliveryFees dealerDeliveryFees;
        try {
            dealerDeliveryFees = nissanWebClient.delete(url, DeliveryFees.class, "deleteDeliveryFee");
        } catch (Exception e) {
            log.error("Failed to delete delivery fee with ID: {} for dealer ID: {}", deliveryFeeId, dealerId, e);
            throw new RuntimeException(String.format("Failed to delete delivery fee with ID: %s for dealer ID: %s", deliveryFeeId, dealerId));
        }

        return dealerDeliveryFees;
    }

    /**
     * @param status Indicating exception that occured
     * @param message In case if called service is just giving message use it
     *                or
     * @param errorResponseBody In case if called service is returning a response body use it
     */
    private void handleException(HttpStatus status, String message, String errorResponseBody) {
        String exception = Optional.ofNullable(message).orElse(errorResponseBody);
        switch(status) {
            case BAD_REQUEST:
                throw new InvalidDealerDeliveryFeeException(exception);
            case NOT_FOUND:
                throw new NotFoundException(exception);
            default:
                throw new RuntimeException(message);
        }
    }

}
