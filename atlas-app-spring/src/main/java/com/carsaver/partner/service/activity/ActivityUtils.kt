package com.carsaver.partner.service.activity

import java.net.URI
import java.net.URISyntaxException

class ActivityUtils {

    companion object {

        fun extractDomain(urlString: String?): String? {
            if (urlString.isNullOrBlank()) {
                return null
            }

            // Add protocol if missing
            val normalizedUrl = if (!urlString.startsWith("http://") && !urlString.startsWith("https://")) {
                "https://$urlString"
            } else {
                urlString
            }

            return try {
                val uri = URI(normalizedUrl)
                val host = uri.host ?: return ""

                // Remove www. prefix if present
                if (host.startsWith("www.")) {
                    host.substring(4)
                } else {
                    host
                }
            } catch (e: URISyntaxException) {
                // Handle malformed URLs
                // Alternative parsing approach for malformed URLs
                val domainRegex = """(?:https?://)?(?:www\.)?([^/]+)""".toRegex()
                val matchResult = domainRegex.find(urlString)
                matchResult?.groupValues?.getOrNull(1)?.replace("www.", "") ?: ""
            } catch (_: Exception) {
                null
            }
        }
    }
}
