package com.carsaver.partner.service;

import com.carsaver.elasticsearch.ElasticClient;
import com.carsaver.elasticsearch.ElasticResponse;
import com.carsaver.partner.elasticsearch.criteria.KpiSearchCriteria;
import com.carsaver.partner.model.metrics.ClaimedCountWidget;
import com.carsaver.search.model.SearchMethod;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.carsaver.partner.service.SaveADealUtil.removeSaveADealLeads;
import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.existsQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;
import static org.elasticsearch.index.query.QueryBuilders.termQuery;
import static org.elasticsearch.index.query.QueryBuilders.termsQuery;

@Service
public class DashboardLeadDocService {
    private static final String INDEX = "leads";

    @Autowired
    @Qualifier("gibson")
    private ElasticClient elasticClient;

    public ClaimedCountWidget getClaimedCounts(KpiSearchCriteria criteria) {
        BoolQueryBuilder query = boolQuery();

        if(criteria.getCreatedDate() != null) {
            query.must(rangeQuery("createdDate")
                .timeZone(criteria.getCreatedDate().getTimeZone())
                .gte(criteria.getCreatedDate().getStart())
                .lte(criteria.getCreatedDate().getEnd()));
        }

        List<String> dealerIds = criteria.getDealerIds();
        if (CollectionUtils.isNotEmpty(dealerIds)) {
            query.must(termsQuery("dealer.id", dealerIds));
        } else {
            query.must(termQuery("dealer.status", "LIVE"));
            query.must(termQuery("dealer.contractDetails.certified", true));
        }
        removeSaveADealLeads(query);

        if(!org.springframework.util.CollectionUtils.isEmpty(criteria.getUserSourceHosts())) {
            boolean isNegativeUserSourceHostsSearch = criteria.getSearchMethods() != null && SearchMethod.NEGATIVE == criteria.getSearchMethods().get("userSourceHosts");
            if(isNegativeUserSourceHostsSearch) {
                query.mustNot(termsQuery("user.source.hostname", criteria.getUserSourceHosts()));
            } else {
                query.must(termsQuery("user.source.hostname", criteria.getUserSourceHosts()));
            }
        }

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();

        searchBldr.query(query);

        FilterAggregationBuilder claimedAgg = AggregationBuilders
            .filter("claimedAgg", existsQuery("timeClaimed"))
            .subAggregation(AggregationBuilders.cardinality("uniqueClaimedAgg").field("user.id"))
            .subAggregation(AggregationBuilders.terms("stockTypeAgg").field("stockType")
                .subAggregation(AggregationBuilders.cardinality("uniqueStockTypeAgg").field("user.id")));

        TermsAggregationBuilder uniqueUsersAgg = AggregationBuilders.terms("stockTypeAgg").field("stockType")
            .subAggregation(AggregationBuilders.cardinality("uniqueStockTypeAgg").field("user.id"));

        searchBldr.aggregation(uniqueUsersAgg);
        searchBldr.aggregation(claimedAgg);
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        Long totalUniqueNew = 0L;
        ParsedTerms stockTypeAgg = response.getSearchResponse().getAggregations().get("stockTypeAgg");
        if (stockTypeAgg.getBucketByKey("NEW") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeAgg.getBucketByKey("NEW").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueNew = userStockTypeCardinality.getValue();
        }
        Long totalUniqueUsed = 0L;
        if (stockTypeAgg.getBucketByKey("USED") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeAgg.getBucketByKey("USED").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueUsed = userStockTypeCardinality.getValue();
        }

        ParsedFilter claimedFilterAgg = response.getSearchResponse().getAggregations().get("claimedAgg");
        ParsedCardinality uniqueClaimedCardinality = claimedFilterAgg.getAggregations().get("uniqueClaimedAgg");
        Long uniqueClaimedCount = uniqueClaimedCardinality.getValue();

        ParsedTerms stockTypeTerms = claimedFilterAgg.getAggregations().get("stockTypeAgg");
        Long totalUniqueClaimedNew = 0L;
        if (stockTypeTerms.getBucketByKey("NEW") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeTerms.getBucketByKey("NEW").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueClaimedNew = userStockTypeCardinality.getValue();
        }
        Long totalUniqueClaimedUsed = 0L;
        if (stockTypeTerms.getBucketByKey("USED") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeTerms.getBucketByKey("USED").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueClaimedUsed = userStockTypeCardinality.getValue();
        }

        double percent = 0;
        if ((totalUniqueNew + totalUniqueUsed) > 0) {
            percent = uniqueClaimedCount.doubleValue() / (totalUniqueNew + totalUniqueUsed);
        }

        ClaimedCountWidget widget = new ClaimedCountWidget();
        widget.setNewCount(totalUniqueClaimedNew.intValue());
        widget.setUsedCount(totalUniqueClaimedUsed.intValue());
        widget.setTotalNew(totalUniqueNew.intValue());
        widget.setTotalUsed(totalUniqueUsed.intValue());
        widget.setPercent(percent);

        return widget;
    }

    public ClaimedCountWidget getContactedCounts(KpiSearchCriteria criteria) {
        BoolQueryBuilder query = boolQuery();

        if(criteria.getCreatedDate() != null) {
            query.must(rangeQuery("createdDate")
                .timeZone(criteria.getCreatedDate().getTimeZone())
                .gte(criteria.getCreatedDate().getStart())
                .lte(criteria.getCreatedDate().getEnd()));
        }

        List<String> dealerIds = criteria.getDealerIds();
        if (CollectionUtils.isNotEmpty(dealerIds)) {
            query.must(termsQuery("dealer.id", dealerIds));
        } else {
            query.must(termQuery("dealer.status", "LIVE"));
            query.must(termQuery("dealer.contractDetails.certified", true));
        }
        removeSaveADealLeads(query);

        if(!org.springframework.util.CollectionUtils.isEmpty(criteria.getUserSourceHosts())) {
            boolean isNegativeUserSourceHostsSearch = criteria.getSearchMethods() != null && SearchMethod.NEGATIVE == criteria.getSearchMethods().get("userSourceHosts");
            if(isNegativeUserSourceHostsSearch) {
                query.mustNot(termsQuery("user.source.hostname", criteria.getUserSourceHosts()));
            } else {
                query.must(termsQuery("user.source.hostname", criteria.getUserSourceHosts()));
            }
        }

        SearchSourceBuilder searchBldr = new SearchSourceBuilder();

        searchBldr.query(query);

        FilterAggregationBuilder claimedAgg = AggregationBuilders
            .filter("contactedAgg", existsQuery("firstContactDate"))
            .subAggregation(AggregationBuilders.cardinality("uniqueContactedAgg").field("user.id"))
            .subAggregation(AggregationBuilders.terms("stockTypeAgg").field("stockType")
                .subAggregation(AggregationBuilders.cardinality("uniqueStockTypeAgg").field("user.id")));

        TermsAggregationBuilder uniqueUsersAgg = AggregationBuilders.terms("stockTypeAgg").field("stockType")
            .subAggregation(AggregationBuilders.cardinality("uniqueStockTypeAgg").field("user.id"));


        searchBldr.aggregation(uniqueUsersAgg);
        searchBldr.aggregation(claimedAgg);
        searchBldr.size(0);

        SearchRequest request = new SearchRequest(INDEX);
        request.source(searchBldr);

        ElasticResponse response = this.elasticClient.search(request);

        Long totalUniqueNew = 0L;
        ParsedTerms stockTypeAgg = response.getSearchResponse().getAggregations().get("stockTypeAgg");
        if (stockTypeAgg.getBucketByKey("NEW") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeAgg.getBucketByKey("NEW").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueNew = userStockTypeCardinality.getValue();
        }
        Long totalUniqueUsed = 0L;
        if (stockTypeAgg.getBucketByKey("USED") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeAgg.getBucketByKey("USED").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueUsed = userStockTypeCardinality.getValue();
        }

        ParsedFilter contactedFilterAgg = response.getSearchResponse().getAggregations().get("contactedAgg");
        ParsedCardinality uniqueContactedCardinality = contactedFilterAgg.getAggregations().get("uniqueContactedAgg");
        Long uniqueContactedCount = uniqueContactedCardinality.getValue();

        ParsedTerms stockTypeTerms = contactedFilterAgg.getAggregations().get("stockTypeAgg");
        Long totalUniqueContactedNew = 0L;
        if (stockTypeTerms.getBucketByKey("NEW") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeTerms.getBucketByKey("NEW").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueContactedNew = userStockTypeCardinality.getValue();
        }
        Long totalUniqueContactedUsed = 0L;
        if (stockTypeTerms.getBucketByKey("USED") != null) {
            ParsedCardinality userStockTypeCardinality = stockTypeTerms.getBucketByKey("USED").getAggregations().get("uniqueStockTypeAgg");
            totalUniqueContactedUsed = userStockTypeCardinality.getValue();
        }

        double percent = 0;
        if ((totalUniqueNew + totalUniqueUsed) > 0) {
            percent = uniqueContactedCount.doubleValue() / (totalUniqueNew + totalUniqueUsed);
        }

        ClaimedCountWidget widget = new ClaimedCountWidget();
        widget.setNewCount(totalUniqueContactedNew.intValue());
        widget.setUsedCount(totalUniqueContactedUsed.intValue());
        widget.setTotalNew(totalUniqueNew.intValue());
        widget.setTotalUsed(totalUniqueUsed.intValue());
        widget.setPercent(percent);

        return widget;
    }
}
