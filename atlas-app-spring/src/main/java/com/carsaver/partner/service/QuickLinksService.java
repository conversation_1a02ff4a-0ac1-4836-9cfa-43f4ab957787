package com.carsaver.partner.service;

import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.TinyUrlClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.TinyUrlRequest;
import com.carsaver.magellan.model.TinyUrlView;
import com.carsaver.partner.model.DealProgramAndDomain;
import com.carsaver.partner.model.QuickLink;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class QuickLinksService {

    private static final String MY_TRADE = "My Trades";
    private static final String VIEW_INVENTORY = "View Inventory";
    private static final String MY_GARAGE = "My Garage";
    private static final String MY_DOCUMENTS = "My Documents";
    private static final String BASE_PATH = "/buy/search-results-page";

    private final ProgramApiService programApiService;
    private final TinyUrlClient tinyUrlClient;
    private final UserClient userClient;
    private final SplitFeatureFlags splitFeatureFlags;
    private final DealerClient dealerClient;

    @Value("${dynamoDB.dealerTable}")
    private String dealerTable;

    public List<QuickLink> getDealerQuickLinkStatuses(String userId, String dealerId) {
        DealProgramAndDomain dealProgramAndDomain = programApiService.getDealProgramAndDomain(userId, dealerId, dealerTable);

        return buildQuickLinks(dealProgramAndDomain,
            dealProgramAndDomain.getDomain(),
            dealProgramAndDomain.isEnabledForDigitalRetail(),
            dealerId);
    }


    private List<QuickLink> buildQuickLinks(DealProgramAndDomain dealProgramAndDomain, String domain, boolean enabledForDigitalRetail, String dealerId) {
        return Stream.of(
            createQuickLink(dealProgramAndDomain.getProgramName(), domain, MY_DOCUMENTS, enabledForDigitalRetail, dealerId),
            createQuickLink(dealProgramAndDomain.getProgramName(), domain, MY_GARAGE, enabledForDigitalRetail, dealerId),
            createQuickLink(dealProgramAndDomain.getProgramName(), domain, MY_TRADE, enabledForDigitalRetail, dealerId),
            createQuickLink(dealProgramAndDomain.getProgramName(), domain, VIEW_INVENTORY, enabledForDigitalRetail, dealerId)
        ).collect(Collectors.toList());
    }

    private QuickLink createQuickLink(String programName, String domain, String title, boolean enabledForDigitalRetail, String dealerId) {
        String longUrl = generateLongUrl(domain, title, enabledForDigitalRetail, dealerId);
        TinyUrlView tinyUrl = tinyUrlClient.create(TinyUrlRequest.builder().targetUrl(longUrl).build());

        return QuickLink.builder()
            .title(title)
            .programName(programName)
            .hostUrl("https://" + domain)
            .shortUrl(tinyUrl.getRequestUrl())
            .longUrl(longUrl)
            .build();
    }

    private String generateLongUrl(String domain, String linkType, boolean enabledForDigitalRetail, String dealerId) {
        String path = getPath(linkType, enabledForDigitalRetail, dealerId);

        if (enabledForDigitalRetail && !VIEW_INVENTORY.equals(linkType)) {
            String encodedRedirect = URLEncoder.encode(path, StandardCharsets.UTF_8);
            return String.format("https://%s%s?dealerId=%s&action=eager-auth&redirect=%s", domain, BASE_PATH, dealerId, encodedRedirect);
        }

        return String.format("https://%s%s", domain, path);
    }

    private String getPath(String linkType, boolean enabledForDigitalRetail, String dealerId) {
        Map<String, String> pathMap = enabledForDigitalRetail ? getDigitalRetailPaths(dealerId) : getDefaultPaths();
        return pathMap.getOrDefault(linkType, "");
    }

    private Map<String, String> getDigitalRetailPaths(String dealerId) {
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put(MY_GARAGE, String.format("/buy/my-garage-page?dealerId=%s", dealerId));
        pathMap.put(MY_TRADE, String.format("/buy/my-garage-page?dealerId=%s#TradeIns", dealerId));
        pathMap.put(MY_DOCUMENTS, String.format("/buy/my-garage-page?dealerId=%s#Documents", dealerId));
        pathMap.put(VIEW_INVENTORY, String.format("%s?dealerId=%s", BASE_PATH, dealerId));
        return pathMap;
    }

    private Map<String, String> getDefaultPaths() {
        Map<String, String> pathMap = new HashMap<>();
        pathMap.put(MY_GARAGE, "/garage");
        pathMap.put(MY_TRADE, "/garage/trades");
        pathMap.put(MY_DOCUMENTS, "/garage/myDocuments");
        pathMap.put(VIEW_INVENTORY, String.format("%s?distance=%s&stockTypes=%s", "/listings", "-1", "NEW"));
        return pathMap;
    }

    public Boolean isDealerEnabledForDigitalRetail(String dealerUUID) {
        DealerView dealer = dealerClient.findById(dealerUUID);
        return StringUtils.hasText(dealer.getNnaDealerId()) && splitFeatureFlags.isDigitalRetailEnabledForDealer(dealerUUID, null);
    }
}
