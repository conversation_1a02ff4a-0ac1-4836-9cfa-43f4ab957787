package com.carsaver.partner.service.crm

import com.carsaver.magellan.client.LeadClient
import com.carsaver.magellan.client.ProgramClient
import com.carsaver.magellan.model.AdfLeadComment
import com.carsaver.magellan.model.DealerView
import com.carsaver.magellan.model.UserView
import com.carsaver.magellan.model.campaign.CampaignView
import com.carsaver.magellan.model.foundation.ProductView
import com.carsaver.magellan.model.foundation.ProgramView
import com.carsaver.partner.service.UserAdfService
import com.carsaver.partner.service.split_io.SplitFeatureFlags
import io.netty.util.internal.StringUtil
import org.springframework.stereotype.Service
import java.util.*

const val UPGRADE_PRODUCT_ID = 103

@Service
class CrmService(
    private val splitFeatureFlags: SplitFeatureFlags,
    private val leadClient: LeadClient,
    private var userAdfService: UserAdfService,
    private var programClient: ProgramClient,
    private val dealerClient: com.carsaver.magellan.client.DealerClient,
    private val usersClient: com.carsaver.magellan.client.UserClient
) {


    fun sendToCrm(
        dealer: DealerVie<PERSON>,
        user: UserView,
        adfLeadComment: AdfLeadComment?
    ) {


        val isUpgradeProduct: Boolean = isUpgradeProduct(user.campaign)
        val comment = if (!splitFeatureFlags.isSendToCrmEnhancementFeatureEnabled || adfLeadComment == null) {
            AdfLeadComment(StringUtil.EMPTY_STRING)
        } else {
            adfLeadComment
        }
        if (isUpgradeProduct) {
            userAdfService.createdQualifiedLead(dealer, user, comment)
        } else {
            leadClient.sendToCrm(user.id, dealer.id, comment)
        }
    }

    private fun isUpgradeProduct(campaign: CampaignView): Boolean {
        val isUpgradeProduct = Optional.ofNullable<CampaignView>(campaign)
            .map { obj: CampaignView -> obj.programId }
            .flatMap { s: String? -> programClient.findById(s) }
            .map { obj: ProgramView -> obj.product }
            .map { obj: ProductView -> obj.id }
            .filter { obj: Int? -> UPGRADE_PRODUCT_ID == obj }
            .isPresent
        return isUpgradeProduct
    }

    fun sendToCrm(dealerId: String, userId: String, adfLeadComment: AdfLeadComment?) {
        val dealer = dealerClient.findById(dealerId)
        val user = usersClient.findById(userId)
        sendToCrm(dealer, user, adfLeadComment)
    }
}
