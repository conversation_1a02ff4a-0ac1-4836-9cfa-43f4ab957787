package com.carsaver.partner.service.desking;

import com.carsaver.magellan.CertificateNotFoundException;
import com.carsaver.magellan.api.deal.DealSheetService;
import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.ClonedDealResponse;
import com.carsaver.partner.model.desking.DealNotesRequest;
import com.carsaver.partner.model.subscription.Product;
import com.carsaver.partner.model.user.UserVehicle;
import com.carsaver.partner.service.UserVehicleService;
import com.carsaver.partner.service.desking.standard.ClonedRequestConverter;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class CopyDealService {

    public static final String DEFAULT_EDITING = "Standard";

    private final ProgramClient programClient;

    private final DealSheetService dealSheetService;

    private final CloneDealService cloneDealService;

    private final DealNotesService dealNotesService;

    private final CertificateClient certificateClient;

    private final SplitFeatureFlags splitFeatureFlags;

    private final DealerClient dealerClient;

    private final UserVehicleService userVehicleService;

    public CopyDealService(ProgramClient programClient, DealSheetService dealSheetService, CloneDealService cloneDealService,
                           DealNotesService dealNotesService, CertificateClient certificateClient, SplitFeatureFlags splitFeatureFlags, DealerClient dealerClient, UserVehicleService userVehicleService) {
        this.programClient = programClient;
        this.dealSheetService = dealSheetService;
        this.cloneDealService = cloneDealService;
        this.dealNotesService = dealNotesService;
        this.certificateClient = certificateClient;
        this.splitFeatureFlags = splitFeatureFlags;
        this.dealerClient = dealerClient;
        this.userVehicleService = userVehicleService;
    }

    /**
     *
     *  Copy deals for old Deal Editing
     *  Only used to copy Cash deals
     *
     * @param dealer dealer the deal is for
     * @param userId user the deal is for
     * @param deal original deal
     * @param newDealName name of the new deal
     * @return userVehicle
     * @throws Exception if there is no certificate or errors
     */
    public UserVehicle copyDeal(DealerView dealer, String userId, CertificateView deal, String newDealName) throws Exception {
        log.info("Getting Saved User Vehicles for User {}, Dealer {}", userId, dealer.getId());

        if (deal == null) {
            throw new CertificateNotFoundException();
        }

        // create a new certificate based on the inputted deal
        CertificateView newDeal = copyExistingCertificate(deal, newDealName);

        // create the dealJacket
        DealJacket dealJacket = createDealJacket(newDeal);

        // set the userVehicle
        UserVehicle result = buildUserVehicle(deal, newDeal, dealJacket);

        return result;
    }


    /**
     *  Copy deals for new Deal Editing tool
     *  This method will copy the existing deal and create a duplicate record in garage table.
     *  If the deal was a generated dealer deal then it will create a duplicate copy in the clone table
     * @throws Exception if there is no certificate or errors
     */
    public UserVehicle saveCopyDeal(DealerView dealer, String userId, CertificateView deal, String newDealName, String newDealNote, Boolean newDealDisplayNote) throws Exception {
        log.info("Copying saved deal for User {}, Dealer {}", userId, dealer.getId());

        if (deal == null) {
            throw new CertificateNotFoundException();
        }

        // create a new certificate based on the inputted deal
        CertificateView newDeal = copyExistingCertificate(deal, newDealName);

        try {
            // create the dealJacket
            DealJacket dealJacket = createDealJacket(newDeal);

            // set the userVehicle
            UserVehicle result = buildUserVehicle(deal, newDeal, dealJacket);

            // if the inputted deal is already a dealer generated deal then copy the clone data as well if there is any
            createCloneDealRecord(dealer, deal, newDealNote, newDealDisplayNote, newDeal);

            return result;

        } catch (Exception ex) {
            deleteDeal(newDeal);
            log.error("Exception thrown when copying a deal. Deal {}", deal.getId());
            throw ex;
        }
    }

    /**
     * Creates the dealJacket need for the response
     *
     * @param newDeal deal that was created
     * @return dealJacket
     * @throws Exception if there was no dealJacket created
     */
    private DealJacket createDealJacket(CertificateView newDeal) throws Exception {
        Optional<DealJacket> dealJacketOpt = dealSheetService.toDealJacket(newDeal);
        DealJacket dealJacket = dealJacketOpt.orElseThrow(() -> new Exception(String.format("Error while getting deal jacket for %s", newDeal.getId())));
        return dealJacket;
    }

    /**
     * Build the response value
     *
     * @param deal original deal
     * @param newDeal newly created deal
     * @param dealJacket created dealJacket
     * @return userVehicle
     */
    private UserVehicle buildUserVehicle(CertificateView deal, CertificateView newDeal, DealJacket dealJacket) {
        final String programId = deal.getCampaign().getProgramId();
        final ProgramView program = programClient.findById(programId)
            .orElseThrow(() -> new NotFoundException(String.format("Program by Id [%s] not found", programId)));
        final DealerView dealerView = Optional.ofNullable(dealerClient.findById(deal.getDealerId())).orElseThrow(NotFoundException::new);
        final boolean isNissanDealer = StringUtils.hasText(dealerView.getNnaDealerId());
        final boolean isDigitalRetailDeal = isNissanDealer && splitFeatureFlags.isDigitalRetailEnabledForDealer(deal.getDealerId(), deal.getVehicle().getVin());
        final boolean isUpgradeDeal =  Objects.equals(program.getProduct().getId(), Product.UPGRADE);
        final UserVehicle result = UserVehicle.from(newDeal, dealJacket, program, isDigitalRetailDeal);
        userVehicleService.doubleCheckFinanceDeepLink(newDeal, programId, result, isDigitalRetailDeal, isUpgradeDeal);
        return result;
    }

    /**
     * Code that duplicates the certificate
     *
     * @param deal original deal
     * @param newDealName name of the new deal
     * @return new certificate
     * @throws Exception if no certificate was created
     */
    private CertificateView copyExistingCertificate(CertificateView deal, String newDealName) throws Exception {
        String loggedUserId = AuthUtils.getUserIdFromSecurityContext().orElseThrow(()-> new Exception("Error getting user from context"));
        deal.setCreatedBy(loggedUserId);

        CertificateView newDeal = buildNewCertificateFromExisting(deal, newDealName);

        return  certificateClient.save(newDeal);
    }

    CertificateView buildNewCertificateFromExisting(CertificateView certificate, String newDealName) {
        CertificateView newDeal = new CertificateView();
        newDeal.setUserId(certificate.getUserId());
        newDeal.setZipCode(certificate.getZipCode());
        newDeal.setStyleId(certificate.getStyleId());
        newDeal.setInventoryId(certificate.getInventoryId());
        newDeal.setType(certificate.getType());
        newDeal.setDealerId(certificate.getDealerId());
        newDeal.setUserVehicleQuoteId(certificate.getUserVehicleQuoteId());
        newDeal.setQuote(certificate.getQuote());
        newDeal.setBaseQuote(certificate.getBaseQuote());
        newDeal.setPrices(certificate.getPrices());
        newDeal.setDealPreferences(certificate.getDealPreferences());
        newDeal.setDealName(newDealName);
        newDeal.setSaved(true);
        newDeal.setLocked(false);
        newDeal.setGeneratedByDealer(true);
        newDeal.setDealSheet(certificate.getDealSheet());
        newDeal.setTradeVehicleId(certificate.getTradeVehicleId());
        newDeal.setSource(certificate.getSource());
        newDeal.setSummaryDetails(certificate.getSummaryDetails());
        return newDeal;
    }


    /**
     * Will check if the deal was generated by a dealer
     * if it is then it will duplicate the record
     * else it will create a new record in clone table
     *
     * @param dealer dealer the deal is for
     * @param deal original deal
     * @param newDealNote note value for new deal
     * @param newDealDisplayNote boolean to either display or not display to the user
     * @param newDeal the new deal that was created
     */
    private void createCloneDealRecord(DealerView dealer, CertificateView deal, String newDealNote, Boolean newDealDisplayNote, CertificateView newDeal) {
        ClonedDealResponse response = null;
        boolean isDealerGenerated = Boolean.TRUE.equals(deal.getGeneratedByDealer());

        if (isDealerGenerated) {
            response = cloneDealService.retrieveClonedDealByCertificateId(deal.getId());
        }

        if (response != null) {
            // sets the correct values for the new clone data record
            setCertificateIds(newDeal, response);
            copyCloneDealForDealEditing(response, newDealNote, newDealDisplayNote);
        } else {
            // creates a new record for the clone table for the new deal editing tool
            createNewCloneDealForDealEditing(dealer, deal, newDealNote, newDealDisplayNote, newDeal);
        }
    }

    /**
     * This will change the certificate id so when it calls ith upsert method
     * it will not override the original clone table data
     *
     * @param newDeal deal that was created
     * @param response clone table record
     */
    private void setCertificateIds(CertificateView newDeal, ClonedDealResponse response) {
        Integer originalCertificate = response.getCertificateId();
        response.setCertificateId(newDeal.getId());
        response.setOriginalCertificateId(originalCertificate.longValue());
        response.setId(null);
    }

    /**
     * Creates a new Clone Deal and inserts it to the clone table
     *
     * @param dealer the dealer the deal is for
     * @param deal original deal
     * @param newDealNote notes for new deal
     * @param newDealDisplayNote boolean to either display or not display to the user
     * @param newDeal new deal
     */
    private void createNewCloneDealForDealEditing(DealerView dealer, CertificateView deal, String newDealNote, Boolean newDealDisplayNote, CertificateView newDeal) {
        // As current method is called for Finance and Lease deals only, clone them. Default to Standard edit for now
        ClonedDealResponse clonedDeal = cloneDealService.cloneDeal(dealer, newDeal, deal.getId(), DEFAULT_EDITING);
        insertDealNotes(newDealNote, newDealDisplayNote, clonedDeal);
    }

    /**
     * Duplicates clone deal data and creates a new record based on that data
     *
     * @param response clone data from clone table
     * @param newDealNote notes for new deal
     * @param newDealDisplayNote boolean to either display or not display to the user
     */
    private void copyCloneDealForDealEditing(ClonedDealResponse response, String newDealNote, Boolean newDealDisplayNote) {
        CloneDealRequest cloneDealRequest = ClonedRequestConverter.convert(response);
        ClonedDealResponse clonedDeal = cloneDealService.upsertDeal(cloneDealRequest);
        insertDealNotes(newDealNote, newDealDisplayNote, clonedDeal);
    }

    /**
     * Checks to see if there are any notes inserted
     *
     * @param newDealNote note values
     * @param newDealDisplayNote boolean to either display or not display to the user
     * @param clonedDeal the clone data that is associated to the note
     */
    private void insertDealNotes(String newDealNote, Boolean newDealDisplayNote, ClonedDealResponse clonedDeal) {
        long cloneDealId = determineCloneDealId(clonedDeal);

        if (StringUtils.hasText(newDealNote) && !newDealNote.equals("null")) {
            saveDealNote(cloneDealId, newDealNote, newDealDisplayNote);
        }
    }

    /**
     * Get the clone dela data id
     *
     * @param deal clone deal data
     * @return clone deal id
     */
    Long determineCloneDealId(ClonedDealResponse deal) {
        Long result = Optional.ofNullable(deal)
            .map(ClonedDealResponse::getId)
            .orElse(0L);

        return result;
    }

    /**
     *  Insert notes for the new deal
     *
     * @param cloneDealId clone deal id
     * @param newDealNote note value
     * @param newDealDisplayNote boolean to either display or not display to the user
     */
    void saveDealNote(Long cloneDealId, String newDealNote, Boolean newDealDisplayNote) {
        DealNotesRequest request = new DealNotesRequest();
        request.setCloneDealId(cloneDealId);
        request.setNote(newDealNote);
        request.setCustomerAccess(newDealDisplayNote);
        dealNotesService.saveDealNote(request);
    }

    /**
     * Deletes the deal and returns an empty optional.
     *
     */
    private void deleteDeal(CertificateView certificate) {
        certificate.setDeleted(true);
        certificateClient.save(certificate.getId(), certificate);
    }
}
