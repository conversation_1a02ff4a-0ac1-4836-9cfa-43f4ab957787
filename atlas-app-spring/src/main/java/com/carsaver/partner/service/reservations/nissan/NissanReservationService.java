package com.carsaver.partner.service.reservations.nissan;

import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.ReservationsClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.InventoryConfig;
import com.carsaver.magellan.model.certificate.DealPreferences;
import com.carsaver.magellan.model.certificate.DealType;
import com.carsaver.magellan.model.inventory.ReservationsModel;
import com.carsaver.partner.model.deal.CustomerDealModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class NissanReservationService {

    public static final BigDecimal DEPOSIT = BigDecimal.valueOf(500);

    @Value("${features-toggle.ariya-reservation-enable}")
    private boolean isAriyaReservationEndpointsEnabled;

    @Autowired
    ReservationsClient reservationsClient;

    @Autowired
    DealerClient dealerClient;


    boolean isReservationEnabled(CampaignView campaign) {
        boolean isEnabled =  Optional.ofNullable(campaign)
            .map(CampaignView::getInventoryConfig)
            .map(InventoryConfig::getIsReservationsEnabled)
            .orElse(false);

        return isEnabled;
    }

    public boolean hasReservation(CampaignView campaign, CertificateView certificate) {
        boolean hasReservation = false;
        if (isAriyaReservationEndpointsEnabled && isReservationEnabled(campaign)) {
            //Need to verify if the VIN has a reservation connected to it
            Optional<String> vin = Optional.ofNullable(certificate).map(CertificateView::getVehicle).map(VehicleView::getVin);
            if (vin.isPresent()) {
                //Checks to see if vin has a reservation
                List<ReservationsModel> storedReservationsByVin = reservationsClient.getReservationsByVin(vin.get());
                hasReservation = !CollectionUtils.isEmpty(storedReservationsByVin);
            }
        }

        return hasReservation;
    }

    /**
     * Reservation amount is the same for all users, so it is a static amount.
     * This method needs to add the deposit for the reservation,
     * since the user has already paid the deposit.
     * The front-end will display this correctly and the deposit amount was taken into account
     * as part of the down payment when doing the calculations.
     *
     * Refactored from a more modern approach to a less modern approach because of
     * Sonar issues. Sonar did not recognize that the function was updating the values, so it
     * considered it a bug and fail the build.
     * @param campaign
     * @return
     */
    public CustomerDealModel.DealModel getDealReservationDeposit(CampaignView campaign, CertificateView certificate, CustomerDealModel.DealModel dealJacketModel) {

        boolean hasReservation = this.hasReservation(campaign, certificate);
        Optional<CustomerDealModel.DealModel> dealModelOptional = Optional.ofNullable(dealJacketModel);

        if (hasReservation && dealModelOptional.isPresent()) {
            DealType dealType = Optional.of(certificate).map(CertificateView::getDealPreferences).map(DealPreferences::getDealType).orElse(null);

            if (DealType.CASH.equals(dealType)) {
                updatePurchasePrice(dealJacketModel, dealModelOptional);
            } else if (DealType.LEASE.equals(dealType)){
                updateDownPayment(dealJacketModel, dealModelOptional);
            } else if (DealType.FINANCE.equals(dealType)) {
                updateOutOfPocket(dealJacketModel, dealModelOptional);
            }

            dealJacketModel.setReservationDeposit(DEPOSIT);
        }

        return dealJacketModel;
    }

     void updatePurchasePrice(CustomerDealModel.DealModel dealJacketModel, Optional<CustomerDealModel.DealModel> dealModelOptional) {
        //update the purchase and total amount for cash deals
         if (dealModelOptional.isPresent()) {
             int purchasePrice = dealModelOptional.map(CustomerDealModel.DealModel::getPurchasePrice).orElse(0);
             dealJacketModel.setPurchasePrice(purchasePrice - DEPOSIT.intValue());
         }
    }

    void updateDownPayment(CustomerDealModel.DealModel dealJacketModel, Optional<CustomerDealModel.DealModel> dealModelOptional) {
         //Make sure the is enough down payment and dueAtSigning amount to take the reservation amount from them
         Double downPaymentDouble = dealModelOptional.map(CustomerDealModel.DealModel::getDownPayment).orElse(0D);
         Double dueAtSigningDouble = dealModelOptional.map(CustomerDealModel.DealModel::getTotalDueAtSigning).orElse(0D);

         BigDecimal downPayment = BigDecimal.valueOf(downPaymentDouble);
         BigDecimal dueAtSigning = BigDecimal.valueOf(dueAtSigningDouble);

         if (DEPOSIT.compareTo(downPayment) <= 0 && DEPOSIT.compareTo(dueAtSigning) <= 0) {
             //Need to update the deposit info and down payment amount for none Cash deals
             dealJacketModel.setTotalDueAtSigning(dueAtSigningDouble - DEPOSIT.doubleValue());
             dealJacketModel.setDownPayment(downPaymentDouble - DEPOSIT.doubleValue());
         }
    }

    void updateOutOfPocket(CustomerDealModel.DealModel dealJacketModel, Optional<CustomerDealModel.DealModel> dealModelOptional) {
         //Make sure the is enough out of pocket and dueAtSigning amount to take the reservation amount from them
         Double outOfPocketDouble = dealModelOptional.map(CustomerDealModel.DealModel::getOutOfPocket).orElse(0D);
         Double dueAtSigningDouble = dealModelOptional.map(CustomerDealModel.DealModel::getTotalDueAtSigning).orElse(0D);

         BigDecimal outOfPocket = BigDecimal.valueOf(outOfPocketDouble);
         BigDecimal dueAtSigning = BigDecimal.valueOf(dueAtSigningDouble);

         if (DEPOSIT.compareTo(outOfPocket) <= 0 && DEPOSIT.compareTo(dueAtSigning) <= 0) {
             //Need to update the deposit info and down payment amount for none Cash deals
             dealJacketModel.setTotalDueAtSigning(dueAtSigningDouble - DEPOSIT.doubleValue());
             dealJacketModel.setOutOfPocket(outOfPocketDouble - DEPOSIT.doubleValue());
         }
    }

}
