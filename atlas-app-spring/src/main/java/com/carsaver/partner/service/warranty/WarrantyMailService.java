package com.carsaver.partner.service.warranty;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.partner.model.warranty.ApplicantRegistrationForm;
import com.carsaver.partner.service.MailService;
import com.carsaver.warranty.model.PremiumsRequest;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.time.ZoneId;

@Slf4j
@Service
public class WarrantyMailService {

    private static final Email DEFAULT_FROM = new Email("<EMAIL>", "CarSaver.com");
    private static final String BETA_NOTIFICATIONS_EMAIL_TO = "<EMAIL>";
    private static final String PROD_WARRANTY_ODOMETER_EMAIL_TO = "<EMAIL>";

    private static final String ENVIRONMENT_PROD = "prod";
    private static final String ENVIRONMENT_BETA = "beta";
    private static final String ENVIRONMENT_LOCAL = "local";

    @Autowired
    private Environment env;

    @Autowired
    private MailService mailService;

    @Deprecated
    public void emailWarrantyOdometerNotification(DealerView dealer, PremiumsRequest premiumsRequest, ApplicantRegistrationForm applicantRegistrationForm, String vehicleName, Integer milesFromDealer, Integer milesInDb) {
            emailWarrantyOdometerNotification(dealer, premiumsRequest, applicantRegistrationForm.getCustomer().getFullName(), vehicleName, milesFromDealer, milesInDb);
    }

    public void emailWarrantyOdometerNotification(DealerView dealer, PremiumsRequest premiumsRequest, String customerFullName, String vehicleName, Integer milesFromDealer, Integer milesInDb) {
        String subject = "LTW ALERT: Registration Failed - Mileage Discrepancy";
        Email to = new Email();
        String body = String.format("Dealer: %s\nCustomer: %s\nVehicle: %s\nVIN: %s\nSale Date: %s\nMiles entered during eRating: %s\nMiles in database (+50 mile cushion): %s",
            dealer.getName(),
            customerFullName,
            vehicleName,
            premiumsRequest.getVin(),
            premiumsRequest.getPurchaseDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
            milesFromDealer,
            milesInDb
        );
        Content content = new Content("text/plain", body);

        if(getProfile().equalsIgnoreCase(ENVIRONMENT_PROD)) {
            to = new Email(PROD_WARRANTY_ODOMETER_EMAIL_TO);
            subject = "LTW ALERT: Registration Failed - Mileage Discrepancy (PROD)";
            log.info("Warranty alert email sent to {}", PROD_WARRANTY_ODOMETER_EMAIL_TO);
        } else if (getProfile().equalsIgnoreCase(ENVIRONMENT_BETA) || getProfile().equalsIgnoreCase(ENVIRONMENT_LOCAL)) {
            to = new Email(BETA_NOTIFICATIONS_EMAIL_TO);
            subject = "LTW ALERT: Registration Failed - Mileage Discrepancy (BETA)";
            log.info("Warranty alert email sent to {}", BETA_NOTIFICATIONS_EMAIL_TO);
        }

        Mail mail = new Mail(DEFAULT_FROM, subject, to, content);
        mailService.sendEmail(mail);
    }

    private String getProfile() {
        return env.getActiveProfiles().length > 0 ? env.getActiveProfiles()[0] : "unknown";
    }

}
