package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.model.protection_products.response.ErrorResponse;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class OttoChatbotToggleHandler implements FeatureToggleHandler {

    private final FeatureSubscriptionsClient featureSubscriptionsClient;

    @Value("${features-subscription.otto-ai-chatbot-feature-id}")
    private String ottoAIChatbotFeatureId;

    @Override
    public boolean supports(String configType) {
        return "otto_chatbot".equalsIgnoreCase(configType);
    }

    @Override
    public DealerProgram handleFeatureToggle(String dealerId, String programId, ToggleConfigRequest toggleConfigRequest) {
        FeatureSubscriptionRequest request = new FeatureSubscriptionRequest();
        request.setDealerId(dealerId);
        request.setEntityId(programId);
        request.setFeatureRId(ottoAIChatbotFeatureId);
        request.setActive(toggleConfigRequest.getIsEnabled());
        FeatureSubscriptionResponse response = featureSubscriptionsClient.saveFeatureSubscription(request);

        if (response == null || response.getActive() == null) {
            throw new InternalServerError(ErrorResponse.builder().errorMessage("Failed to update Otto AI Chatbot Feature").build());
        }

        return DealerProgram.builder()
            .isOttoChatbotEnabled(response.getActive())
            .build();
    }
}
