package com.carsaver.partner.service;

import org.elasticsearch.index.query.BoolQueryBuilder;

import static org.elasticsearch.index.query.QueryBuilders.termQuery;

public class SaveADealUtil {

    public static final String SAVE_A_DEAL_DOMAIN = "express.carsaver.com";

    /**
     * adds a mustNot query to the passed in queryBuilder excluding the SAVE_A_DEAL_DOMAIN, aka express domain, e.g. express.carsaver.com
     * @param query - the query builder to add the mustNot query to
     * @param termName - the fully qualified termName on the index referencing the hostname
     */
    public static void removeSaveADealHostname(BoolQueryBuilder query, String termName) {
        query.mustNot(termQuery(termName, SAVE_A_DEAL_DOMAIN));
    }

    public static void removeSaveADealLeads(BoolQueryBuilder query) {
        removeSaveADealHostname(query, "source.hostname");
    }

    public static void removeSaveADealSales(BoolQueryBuilder query) {
        removeSaveADealHostname(query, "user.source.hostname");
    }
}
