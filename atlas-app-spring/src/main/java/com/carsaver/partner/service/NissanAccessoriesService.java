package com.carsaver.partner.service;

import com.carsaver.accessories.api.AccessoryDetail;
import com.carsaver.accessories.api.DealerAccessory;
import com.carsaver.accessories.client.DealerAccessoryServiceClient;
import com.carsaver.nissan.accessories.api.AccessoriesResponse;
import com.carsaver.nissan.accessories.api.Accessory;
import com.carsaver.nissan.accessories.client.AccessoriesClient;
import com.carsaver.nissan.dealerinventory.api.Division;
import com.carsaver.nissan.dealerinventory.api.ModelLinesResponse;
import com.carsaver.nissan.dealerinventory.api.VehicleType;
import com.carsaver.nissan.dealerinventory.client.NissanDealerInventoryClient;
import com.carsaver.partner.model.dealer.ModelDetails;
import com.carsaver.partner.model.mapper.ModelLinesCodeResponseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NissanAccessoriesService {

    private final AccessoriesClient accessoriesClient;
    private final DealerAccessoryServiceClient dealerAccessoryServiceClient;
    private final NissanDealerInventoryClient nissanDealerInventoryClient;
    private final ModelLinesCodeResponseMapper modelLinesCodeResponseMapper;

    public NissanAccessoriesService(AccessoriesClient accessoriesClient, DealerAccessoryServiceClient dealerAccessoryServiceClient, NissanDealerInventoryClient nissanDealerInventoryClient, ModelLinesCodeResponseMapper modelLinesCodeResponseMapper) {
        this.accessoriesClient = accessoriesClient;
        this.dealerAccessoryServiceClient = dealerAccessoryServiceClient;
        this.nissanDealerInventoryClient = nissanDealerInventoryClient;
        this.modelLinesCodeResponseMapper = modelLinesCodeResponseMapper;
    }

    public List<ModelDetails> getModelList(){
        List<ModelDetails> result = new ArrayList<>();

        try {
            ModelLinesResponse modelLine = nissanDealerInventoryClient.getModelLine(Division.NISSAN, VehicleType.NEW, Locale.US);
            result = modelLine.getModelLines().stream().map(modelLinesCodeResponseMapper::toModelDetails).collect(Collectors.toList());
        }
        catch (Exception e) {
            log.error("Accessories Model lines: Exception while calling Nissan to get model lines {}", e.getMessage());
        }

        return result;
    }

    // filter and sync results

    public List<DealerAccessory> getAccessoriesByModelLineCodeAndYear(String dealerId, String modelLineCode, String modelYear) {
        List<DealerAccessory> result = syncAccessoriesByModelAndYear(dealerId, modelLineCode, modelYear);

        return result;
    }

    private List<DealerAccessory> syncAccessoriesByModelAndYear(String dealerId, String modelLineCode, String modelYear) {

        AccessoriesResponse result = accessoriesClient.getAccessories(dealerId, modelLineCode, modelYear, null);
        List<DealerAccessory> dealerAccessories = getAvailableAccessoriesByFilter(dealerId, modelLineCode, modelYear);
        List<Accessory> nissanDealerAccessories = new ArrayList<>(result.getAccessories());

        if (dealerAccessories.isEmpty()) {
            dealerAccessories = nissanDealerAccessories.stream().map(toDealerAccessory(dealerId, modelLineCode, modelYear)).collect(Collectors.toList());
            dealerAccessories = dealerAccessories.stream().map(dealerAccessoryServiceClient::saveDealerAccessory).collect(Collectors.toList());
            dealerAccessories = getUniqueDealerAccessories(dealerAccessories);
            return  dealerAccessories;
        }

        List<String> nissanPartNumberList = nissanDealerAccessories.stream().map(Accessory::getPart_number).collect(Collectors.toList());
        List<String> dealerPartList = dealerAccessories.stream().map(DealerAccessory::getPartNumber).collect(Collectors.toList());
        if( dealerPartList.size() > nissanPartNumberList.size() ){
            List<String> partsToBeDisabled = dealerPartList.stream().filter(i -> !nissanPartNumberList.contains(i)).collect(Collectors.toList());
            partsToBeDisabled.forEach( partNo -> dealerAccessoryServiceClient.disableDealerAccessoryByNissan( dealerId, AccessoryDetail.builder().partNumber(partNo).modelYear(modelYear).modelLineCode(modelLineCode).build() ));

            dealerAccessories = getAvailableAccessoriesByFilter(dealerId, modelLineCode, modelYear);
        }
        else if( nissanPartNumberList.size() > dealerPartList.size() ){
            nissanPartNumberList.removeAll(dealerPartList);

            nissanDealerAccessories.stream()
                .filter(currentAccessory -> nissanPartNumberList.contains(currentAccessory.getPart_number()))
                .map(toDealerAccessory(dealerId, modelLineCode, modelYear))
                .forEach(dealerAccessoryServiceClient::saveDealerAccessory);

            dealerAccessories = getAvailableAccessoriesByFilter(dealerId, modelLineCode, modelYear);
        }

        List<DealerAccessory> dealerAccessoryList = getUniqueDealerAccessories(dealerAccessories);
        return dealerAccessoryList;
    }

    /**
     *
     * @param dealerAccessories : all non unique accessories
     * @return uniqueAccessoryList : removing duplicates
     */
    private List<DealerAccessory> getUniqueDealerAccessories(List<DealerAccessory> dealerAccessories){
        List<DealerAccessory> uniqueAccessoryList = dealerAccessories.stream().filter(distinctByKey(DealerAccessory::getName)).collect(Collectors.toList());
        return uniqueAccessoryList;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        final Set<Object> seen = new HashSet<>();
        return t -> seen.add(keyExtractor.apply(t));
    }

    private List<DealerAccessory> getAvailableAccessoriesByFilter(String dealerId, String modelCode, String year) {
        List<DealerAccessory> result = dealerAccessoryServiceClient.findAllByDealerIdAndIsAvailableAndModelLineCodeAndModelYear(dealerId, Boolean.TRUE, modelCode, year);
        return result;
    }

    private Function<Accessory, DealerAccessory> toDealerAccessory(String dealerId, String modelLineCode, String modelYear) {

        return accessory -> DealerAccessory.builder()
            .dealerId(dealerId)
            .isAvailable(Boolean.TRUE)
            .isEnabled(Boolean.TRUE)
            .partNumber(accessory.getPart_number())
            .name(accessory.getName())
            .modelLineCode(modelLineCode)
            .modelYear(modelYear)
            .build();

    }

}
