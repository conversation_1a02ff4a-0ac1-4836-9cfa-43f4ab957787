package com.carsaver.partner.service.desking.standard.lease;

import com.carsaver.core.PaymentType;
import com.carsaver.magellan.NotFoundException;
import com.carsaver.magellan.api.deal.DealJacketRequest;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.user.CreditProfile;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.CloneDealRequest.*;
import com.carsaver.partner.model.desking.standard.LeaseQuoteRequest;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse;
import com.carsaver.partner.service.desking.standard.DealMarkUpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service("leaseQuotes")
public class LeaseQuoteService {

    public static final Integer leaseDefaultMileage = 12000;

    private final QuoteServiceClient quoteServiceClient;

    public LeaseQuoteService(QuoteServiceClient quoteServiceClient) {
        this.quoteServiceClient = quoteServiceClient;
    }

    /**
     * Makes a call to quote-service based on DealJacket request.
     * This method is used when dealer is creating a new deal
     * @param certificate
     * @param request
     * @return
     */
    public LeaseQuoteResponse getLeaseQuotes(CertificateView certificate, DealJacketRequest request) {
        LeaseQuoteResponse leaseQuoteResponse = null;
        String campaignId = request.getCampaignView().getId();

        int mileage = Optional.ofNullable(request.getMileage()).orElse(leaseDefaultMileage);
        Double tradeEquity = Optional.ofNullable(request.getDealSheet()).map(DealSheet::getTradeEquity).orElse(0D);
        BigDecimal downPayment = fetchDownPayment(request, tradeEquity);

        // Get the Campaign or Dealer's lease mark up rate
        BigDecimal leaseMarkupPct = DealMarkUpService.calculateLeaseMarkup(request.getCampaignView(), request.getDealer());
        Integer userIncome = getUserIncome(certificate);
        BigDecimal rebates = determineRebates(request);

        try {
            LeaseQuoteRequest leaseQuoteRequest = LeaseQuoteRequest.builder()
                .campaignId(campaignId)
                .certificateId(certificate.getId())
                .dealerId(request.getDealer().getId())
                .inventoryId(request.getInventoryId())
                .userId(certificate.getUserId())
                .buyerLocation(certificate.getUser().getAddressView())
                .downPayment(downPayment)
                .rebates(rebates)
                .creditScore(request.getCreditScore())
                .mileageAllowed(mileage)
                .term(request.getTerm())
                .tradeEquityPosition(tradeEquity)
                .ptiCheck(true)
                .userIncome(userIncome != null ? BigDecimal.valueOf(userIncome) : null)
                .leaseMarkupPct(leaseMarkupPct)
                .build();

            leaseQuoteResponse = quoteServiceClient.retrieveLeaseQuotes(leaseQuoteRequest);

        } catch (Exception ex) {
            log.error("Lease Quote Exception thrown. CertificateID: {}, ERROR: {}", certificate.getId(), ex);
        }

        return leaseQuoteResponse;
    }

    /**
     * * Makes a call to quote-service based on Clone Deal data request.
     * * This method is used when Standard Editing is applied and the dealer is
     * * altering values for their deal, alos known as recalculating the quote
     */
    public LeaseQuoteResponse getEditedLeaseQuotes(CertificateView certificate, CloneDealRequest cloneDealRequest) {
        LeaseQuoteResponse leaseQuoteResponse = null;
        String campaignId = certificate.getSource().getCampaignId();
        Optional<CloneDealRequest> cloneDealRequestOpt = Optional.ofNullable(cloneDealRequest);

        //credit score and term
        Integer creditScore = cloneDealRequestOpt.map(CloneDealRequest::getFinanceDetails).map(FinanceDetails::getTier).map(i -> Integer.parseInt(i)).orElse(null);
        Integer term = cloneDealRequestOpt.map(CloneDealRequest::getFinanceDetails).map(FinanceDetails::getTerm).orElse(null);

        int mileage = cloneDealRequestOpt.map(CloneDealRequest::getFinanceDetails).map(FinanceDetails::getMilesPerYear).orElse(null);
        Double tradeEquity = cloneDealRequestOpt.map(CloneDealRequest::getTradeAllowance).map(TradeAllowance::getNetTrade).orElse(0D);
        BigDecimal downPayment = cloneDealRequestOpt.map(CloneDealRequest::getDueAtSigning).map(DueAtSigning::getConsumerCash).map(s ->Double.parseDouble(s)).map(i -> BigDecimal.valueOf(i)).orElse(BigDecimal.ZERO);
        Integer invoice = cloneDealRequestOpt.map(CloneDealRequest::getVehicleDetail).map(VehicleDetail::getInvoice).map(i -> i.intValue()).orElse(null);

        // Get the Campaign or Dealer's lease mark up rate
        BigDecimal leaseMarkupPct = DealMarkUpService.calculateLeaseMarkup(certificate.getCampaign(), certificate.getDealer());
        Integer userIncome = getUserIncome(certificate);
        BigDecimal rebates = cloneDealRequestOpt.map(CloneDealRequest::getRebates).map(Rebates::getLineItems)
            .map(i -> i.stream().mapToDouble(LineItem::getAmount).sum()).map(BigDecimal::new).orElse(BigDecimal.ZERO);

        BigDecimal overrideSellingPrice = cloneDealRequestOpt.map(CloneDealRequest::getVehicleDetail).map(VehicleDetail::getSellingPrice).map(i -> BigDecimal.valueOf(i)).orElse(null);
        BigDecimal overrideMsrp = cloneDealRequestOpt.map(CloneDealRequest::getVehicleDetail).map(VehicleDetail::getMsrp).map(i -> BigDecimal.valueOf(i)).orElse(null);

        // get fees for edited deal
        BigDecimal overrideCapCostFee = getOverrideCapCostFee(cloneDealRequestOpt);
        BigDecimal overrideInceptionFee = getOverrideInceptionFee(cloneDealRequestOpt);

        try {
            LeaseQuoteRequest leaseQuoteRequest = LeaseQuoteRequest.builder()
                .campaignId(campaignId)
                .certificateId(certificate.getId())
                .dealerId(certificate.getDealerId())
                .inventoryId(certificate.getInventoryId())
                .userId(certificate.getUserId())
                .buyerLocation(certificate.getUser().getAddressView())
                .downPayment(downPayment)
                .rebates(rebates)
                .creditScore(creditScore)
                .mileageAllowed(mileage)
                .term(Collections.singletonList(term))
                .tradeEquityPosition(tradeEquity)
                .ptiCheck(true)
                .userIncome(userIncome != null ? BigDecimal.valueOf(userIncome) : null)
                .invoice(invoice)
                .leaseMarkupPct(leaseMarkupPct)
                .overrideSellingPrice(overrideSellingPrice)
                .overrideMsrp(overrideMsrp)
                .overrideCapCostFee(overrideCapCostFee)
                .overrideInceptionFee(overrideInceptionFee)
                .build();

            leaseQuoteResponse = quoteServiceClient.retrieveLeaseQuotes(leaseQuoteRequest);


        } catch (Exception ex) {
            log.error("Lease Quote Exception thrown. CloneRequest: {}, ERROR: {}", cloneDealRequest, ex);
        }

        return leaseQuoteResponse;
    }

    /**
     * * Used for Standard editing recalculation
     * @param cloneDealRequestOpt
     * @return
     */
    private BigDecimal getOverrideInceptionFee(Optional<CloneDealRequest> cloneDealRequestOpt) {
        return cloneDealRequestOpt
            .map(CloneDealRequest::getInceptions)
            .map(Inceptions::getLineItems)
            .filter(Objects::nonNull)
            .map(i -> i.stream().mapToDouble(LineItem::getAmount).sum())
            .map(v -> BigDecimal.valueOf(v))
            .orElse(null);
    }

    /**
     * * Used for Standard editing recalculation
     * @param cloneDealRequestOpt
     * @return
     */
    private BigDecimal getOverrideCapCostFee(Optional<CloneDealRequest> cloneDealRequestOpt) {
        return cloneDealRequestOpt
            .map(CloneDealRequest::getGrossCapitalCost)
            .map(GrossCapitalCost::getLineItems)
            .filter(Objects::nonNull)
            .map(i -> i.stream().mapToDouble(LineItem::getAmount).sum())
            .map(v -> BigDecimal.valueOf(v))
            .orElse(null);
    }

    /**
     * Used when creating a new deal.
     * DealJacket combines Trade equity into down payment
     * @param request
     * @param tradeEquity
     * @return
     */
    BigDecimal fetchDownPayment(DealJacketRequest request, double tradeEquity) {
        BigDecimal downPayment = request.getDownPaymentFieldAmount();
        if(downPayment == null) {
            downPayment = request.getDownPayment().subtract(BigDecimal.valueOf(tradeEquity));
        }
        return downPayment;
    }

    /**
     * Used when creating a deal
     * DealJacket gets rebates from dealSheet
     * @param request
     * @return
     */
    BigDecimal determineRebates(DealJacketRequest request) {
        BigDecimal rebates = Optional.ofNullable(request.getTotalRebates(PaymentType.LEASE))
            .map(BigDecimal::new)
            .orElse(BigDecimal.ZERO);

        return rebates;
    }

    /**
     * Used for all call to quote-service.
     * This is used to do the PTI check to make sure the user qualifies for the deal
     * @param certificate
     * @return
     */
    Integer getUserIncome(CertificateView certificate) {
        // Get user's income
        UserView user = certificate.getUser();
        Optional<CreditProfile> creditProfile = Optional.ofNullable(user).flatMap(UserView::getCreditProfile);

        // If no user income we throw as it's needed for calculations
        Integer userIncome = creditProfile.map(CreditProfile::getIncome).orElseThrow(NotFoundException::new);
        return userIncome;
    }

}
