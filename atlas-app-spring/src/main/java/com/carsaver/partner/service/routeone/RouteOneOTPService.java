package com.carsaver.partner.service.routeone;

import lombok.extern.slf4j.Slf4j;
import org.jasypt.digest.StandardStringDigester;
import org.jasypt.util.text.StrongTextEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.StringTokenizer;

@Slf4j
@Service
@ConditionalOnProperty(name = "routeone.base-uri")
public class RouteOneOTPService {

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${routeone.partner-id}")
    private String partnerId;

    @Value("${routeone.base-uri}")
    private String baseUri;

    @Value("${routeone.sso.password}")
    private String password;

    public String generateOtpUrl(String userId, OtpRequest request) {
        String ticketRequest = String.format("%s/Web/j_security_ticketRequest?uid=%s&source=%s", baseUri, userId, partnerId);
        String accessToken = restTemplate.getForObject(ticketRequest, String.class);

        // Now return encrypted string with PBEWithMD5AndTripleDES and 1000 key obtention
        StrongTextEncryptor strongTextEncryptor = new StrongTextEncryptor();
        strongTextEncryptor.setPassword(password);

        //Get String from RouteOne and decrypt it
        String checkVal = strongTextEncryptor.decrypt(accessToken);
        if(StringUtils.isEmpty(checkVal)) {
            return null;
        }

        //find "x" and "n" per the spec
        StringTokenizer stringTokenizer = new StringTokenizer(checkVal, ":");

        String sn = stringTokenizer.nextToken();
        String x = stringTokenizer.nextToken();
        int n = Integer.parseInt(sn);

        StandardStringDigester standardStringDigester = new StandardStringDigester();
        standardStringDigester.setIterations(n-1);
        standardStringDigester.setAlgorithm("SHA-1");
        standardStringDigester.setSaltSizeBytes(0); //no salt
        String clientDigest = standardStringDigester.digest(x);
        String otpCode = URLEncoder.encode(clientDigest, StandardCharsets.UTF_8);

        String ticketCheck = String.format("%s/Web/j_security_ticketCheck?DMSUserId=%s&sourceId=%s&otp=%s", baseUri, userId, partnerId, otpCode);
        ticketCheck = addRequestParams(ticketCheck, request);
        return ticketCheck;
    }

    private String addRequestParams(String url, OtpRequest request) {
        if (request.getPageAccess() != null) {
            url += "&pageAccess=" + request.getPageAccess().toString();
        }

        if (request.getDealerId() != null) {
            url += "&dealerId=" + request.getDealerId();
        }

        if (StringUtils.hasLength(request.getConversationId())) {
            url += "&conversationId=" + request.getConversationId();
        }

        if (StringUtils.hasLength(request.getAgreementId())) {
            url += "&agreementID=" + request.getAgreementId();
        }

        return url;
    }
}
