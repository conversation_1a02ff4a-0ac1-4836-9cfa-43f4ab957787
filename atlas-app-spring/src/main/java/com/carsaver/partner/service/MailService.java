package com.carsaver.partner.service;

import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class MailService {

    @Autowired
    private SendGrid sendgrid;

    public final Response sendEmail(Mail mail) {
        Request request = new Request();
        Response response = null;
        try {
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());

            log.info("Sending email '{}' to {} from {}", mail.getSubject(), mail.getPersonalization(), mail.from.getEmail());
            response = sendgrid.api(request);

            log.debug("Email Response Status={}, Body={}, Headers={}", response.getStatusCode(), response.getBody(), response.getHeaders());
        } catch (IOException ex) {
            log.error(ex.getMessage(), ex);
        }

        return response;
    }

}
