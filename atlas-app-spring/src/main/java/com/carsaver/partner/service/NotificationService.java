package com.carsaver.partner.service;

import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.model.Notification;
import com.carsaver.partner.model.UpdateNotificationEvent;
import com.carsaver.partner.notifications.NotificationClient;
import kong.unirest.UnirestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@Slf4j
@RequiredArgsConstructor
public class NotificationService {

    public static final String DISPLAYED = "displayed";
    public static final String CTA_CLICKED = "ctaClicked";
    public static final String DISPLAY_EXPIRED = "displayExpired";
    public static final String AT_SERVICE_DRIVE = "clientAtServiceDrive";
    private final DealerUserService dealerUserService;
    private final NotificationClient notificationClient;
    private final UserClient userClient;


    public List<Notification> getNotificationsForDealerUser(String dealerId, String userId) {
        // Fetch the dealer Users from selected dealer
        boolean isAssociatedProgramManager = dealerUserService.isProgramManager(dealerId, userId);

        // if isAssociatedProgramManager Fetch pending notifications
        if (isAssociatedProgramManager) {
            return Optional.ofNullable(notificationClient.fetchNotifications(dealerId))
                .orElse(Collections.emptyList())
                .stream()
                .map(this::mapNotificationDetails)
                .sorted(Comparator.comparing(Notification::getPriority,  Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
        }

        // Return the notifications empty list
        return Collections.emptyList();
    }

    public Notification updateNotification(UpdateNotificationEvent updateNotificationEvent) {
        Notification notification = null;

        try {
            String eventType = updateNotificationEvent.getEventType();

            switch (eventType) {
                case DISPLAYED:
                    notification = notificationClient.markAsDisplayed(
                        updateNotificationEvent.getUserId(),
                        updateNotificationEvent.getTime()
                    );
                    break;
                case CTA_CLICKED:
                    notification = notificationClient.markAsCtaClicked(
                        updateNotificationEvent.getUserId(),
                        updateNotificationEvent.getTime()
                    );
                    break;
                case DISPLAY_EXPIRED:
                    notification = notificationClient.markAsDisplayExpired(
                        updateNotificationEvent.getUserId(),
                        updateNotificationEvent.getTime()
                    );
                    break;
                default:
                    log.warn("Unsupported event type: {}", eventType);
            }
        } catch (UnirestException e) {
            log.error("Error while updating notification: {}", e.getMessage());
        }

        return notification;
    }




    private Notification mapNotificationDetails(Notification notification) {
        UserView userView = userClient.findById(notification.getUserId());
        notification.setFullName(userView.getFirstName() + " " + userView.getLastName());

        if (AT_SERVICE_DRIVE.equals(notification.getEventName())) {
            notification.setMessage(" is in the service drive. Please take immediate action.");
        } else {
            notification.setMessage("Unknown");
        }

        return notification;
    }


}
