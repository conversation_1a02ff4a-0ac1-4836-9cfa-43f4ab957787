package com.carsaver.partner.service.desking.standard;

import com.carsaver.core.PaymentType;
import com.carsaver.magellan.api.deal.DealJacketRequest;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.partner.model.desking.CloneDealRequest;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CloneRequestDataToDealJacketConverter {

    public void setCloneDataToDealJacketRequest(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        Objects.requireNonNull(request, "DealJacketRequest cannot be null, needed to set the clone table data");

        // making sure that there is a dealSheet to set up the correct values
        if (request.getDealSheet() == null) {
            request.setDealSheet(DealSheet.builder().build());
        }

        setDownPayment(cloneDealRequest, request);

        setMSRP(cloneDealRequest, request);

        setVehicleInvoice(cloneDealRequest, request);

        setSellingPrice(cloneDealRequest, request);

        setTerm(cloneDealRequest, request);

        setTier(cloneDealRequest, request);

        setMarkupBps(cloneDealRequest, request);

        setRebates(cloneDealRequest, request);

        setFinanceFees(cloneDealRequest, request);

        setTradeValues(cloneDealRequest, request);

        removeTaxEntries(request);

    }

    //needs testing
    void setTradeValues(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        Double payoff = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getTradeAllowance)
            .map(CloneDealRequest.TradeAllowance::getPayoff)
            .orElse(null);
        request.getDealSheet().setTradePayoff(payoff);

        Double netAmount = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getTradeAllowance)
            .map(CloneDealRequest.TradeAllowance::getNetTrade)
            .orElse(null);
        request.getDealSheet().setTradeValue(netAmount);
    }

    //needs testing
    void setFinanceFees(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set finance fee in dealSheet
        List<DealSheet.DealerFeeItem> fees = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getTaxesAndFees)
            .map(CloneDealRequest.TaxesAndFees::getLineItems)
            .stream()
            .flatMap(list -> list.stream().map(i -> {
                DealerFeeView dealerFee = DealerFeeView.builder().name(i.getName()).amount(i.getAmount()).build();
                return DealSheet.DealerFeeItem.from(dealerFee);
            }))
            .collect(Collectors.toList());
        request.getDealSheet().setDealerFees(fees);
    }

    void setRebates(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set rebates
        List<DealSheet.RebateLineItem> rebates = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getRebates)
            .map(CloneDealRequest.Rebates::getLineItems)
            .stream()
            .flatMap(list -> list.stream().map(i -> new DealSheet.RebateLineItem(i.getName(), i.getAmount(), convertDealType(request.getPaymentType()))))
            .collect(Collectors.toList());
        request.getDealSheet().setDealerRebates(rebates);
    }

    private static com.carsaver.magellan.model.dealer.DealType convertDealType(PaymentType dealType) {
        com.carsaver.magellan.model.dealer.DealType result = null;
        if (dealType != null) {
           switch (dealType) {
               case FINANCE: result = com.carsaver.magellan.model.dealer.DealType.FINANCE;
                    break;
               case LEASE: result = com.carsaver.magellan.model.dealer.DealType.LEASE;
                   break;
               default: result = null;
           }
       }
       return result;
    }

    private void setMarkupBps(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set markup
        BigDecimal bps = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getFinanceDetails)
            .map(CloneDealRequest.FinanceDetails::getBps)
            .map(b -> BigDecimal.valueOf(b))
            .orElse(null);
        if (bps != null) {
            request.getCampaignView().getFinanceConfig().setLeaseMarkupPctOverride(bps);
            request.getCampaignView().getFinanceConfig().setLoanMarkupPctOverride(bps);
        }
    }

    private void setTier(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set tier
        Integer creditScore = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getFinanceDetails)
            .map(CloneDealRequest.FinanceDetails::getTier)
            .map(s -> Integer.parseInt(s))
            .orElse(0);
        request.setCreditScore(creditScore);
    }

    private void setTerm(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set term
        Integer term = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getFinanceDetails)
            .map(CloneDealRequest.FinanceDetails::getTerm)
            .orElse(null);
        request.setTerm(Collections.singletonList(term));
    }

    private void setSellingPrice(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set selling price
        Double sellingPrice = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getVehicleDetail)
            .map(CloneDealRequest.VehicleDetail::getSellingPrice)
            .orElse(null);
        if (sellingPrice != null) {
            request.getDealSheet().setSalePriceByProgram(null);
            request.getDealSheet().setSalePriceByDealer(sellingPrice);
        }
    }

    private void setVehicleInvoice(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set vehicle invoice
        Double invoice = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getVehicleDetail)
            .map(CloneDealRequest.VehicleDetail::getInvoice)
            .orElse(null);
        if (invoice != null) {
            request.getVehicle().setInvoicePrice(invoice.intValue());
        }
    }

    private void setMSRP(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set vehicle msrp
        Double msrp = Optional.ofNullable(cloneDealRequest)
            .map(CloneDealRequest::getVehicleDetail)
            .map(CloneDealRequest.VehicleDetail::getMsrp)
            .orElse(null);
        if (msrp != null) {
            request.getDealSheet().setRetailPrice(msrp);
            request.getVehicle().setMsrp(msrp.intValue());
        }
    }

    private void setDownPayment(CloneDealRequest cloneDealRequest, DealJacketRequest request) {
        // set down payment
        BigDecimal downPayment= Optional.ofNullable(cloneDealRequest).map(CloneDealRequest::getDueAtSigning).map(CloneDealRequest.DueAtSigning::getConsumerCash)
            .map(s -> Double.parseDouble(s)).map(d -> BigDecimal.valueOf(d)).orElse(BigDecimal.ZERO);
        request.setDownPayment(downPayment);
        request.getDealSheet().setCashDownPayment(downPayment.doubleValue());
        request.setDownPaymentPercent(null);
    }

    void removeTaxEntries(DealJacketRequest request) {

        request.getDealSheet().setTitleLicenseFees(0D);
        request.getDealSheet().setSalesTax(0D);
    }

}
