package com.carsaver.partner.service;

import com.carsaver.accessories.api.DealAccessories;
import com.carsaver.accessories.api.DealAccessory;
import com.carsaver.accessories.client.AccessoriesServiceClient;
import com.carsaver.partner.exception.MissingRequiredFieldException;
import com.carsaver.partner.model.deal.AccessoriesModel;
import com.carsaver.partner.model.deal.AccessoryItemModel;
import com.carsaver.partner.model.mapper.AccessoryItemModelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccessoriesService {

    @Value("${features-toggle.accessories-enable:false}")
    private boolean featureToggleAccessories;

    @Autowired
    private AccessoriesServiceClient accessoriesServiceClient;

    @Autowired
    private AccessoryItemModelMapper accessoryItemModelMapper;

    public Optional<AccessoriesModel> getDealAccessories(Integer certificateId) {

        if (!featureToggleAccessories) {
            return Optional.empty();
        }

        if (certificateId == null) {
            log.error("Null certificate id was passed into getDealAccessories");
            throw new MissingRequiredFieldException("Invalid Certificate id");
        }

        DealAccessories dealAccessoryList = accessoriesServiceClient.getDealAccessoriesByCertificateId(certificateId);
        Optional<List<AccessoryItemModel>> accessoryItemModelList = Optional.ofNullable(dealAccessoryList)
            .map(list -> list.getAccessories().stream()
                .map(accessory -> accessoryItemModelMapper.toAccessoryItemModel(accessory))
                .collect(Collectors.toList())
            );

         if (accessoryItemModelList.isPresent()) {
             AccessoriesModel accessoriesModel = new AccessoriesModel(accessoryItemModelList.get());
             return Optional.of(accessoriesModel);
         }

        return Optional.empty();
    }
}
