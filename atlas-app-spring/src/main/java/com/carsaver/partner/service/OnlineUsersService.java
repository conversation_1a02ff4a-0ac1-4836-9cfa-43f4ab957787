package com.carsaver.partner.service;

import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.magellan.client.DealerLinkClient;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.DealerLinkView;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.partner.model.CustomerLastLoginDetails;
import com.carsaver.partner.model.user.OnlineUser;
import com.carsaver.partner.repository.DigitalRetailSession;
import com.carsaver.partner.repository.OnlineUsersRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class OnlineUsersService {
    public static final int HEARTBEAT_INTERVAL = 60;
    private final OnlineUsersRepository onlineUsersRepository;
    private final UserClient userClient;
    private final ProgramClient programClient;
    private final DealerLinkClient dealerLinkClient;

    @Value("${dynamoDB.session-heartbeat-table}")
    private String sessionHeartbeatTable;

    public List<OnlineUser> getOnlineUsers() {
        long threshold = Instant.now().getEpochSecond() - HEARTBEAT_INTERVAL;
        List<DigitalRetailSession> heartBeatSessions = onlineUsersRepository.findHeartBeatSessions(threshold);
        return findOnlineUsersFromSessions(heartBeatSessions);
    }

    public List<OnlineUser> findOnlineUsersFromSessions(List<DigitalRetailSession> onlineUserSessions) {
        return onlineUserSessions.stream().map(DigitalRetailSession::getUserId)
            .map(this::populateUserDetails)
            .filter(Objects::nonNull)
            .sorted(Comparator.comparingLong(OnlineUser::getMinutesOnline))
            .collect(Collectors.toList());
    }

    private OnlineUser populateUserDetails(String userId) {
        return Optional.ofNullable(userClient.findById(userId))
            .map(user -> {
                long minutesOnline = ChronoUnit.MINUTES.between(user.getLastLoginAt(), ZonedDateTime.now());
                String programId = Optional.ofNullable(user.getCampaign()).map(CampaignView::getProgramId).orElse(null);
                String programName = Optional.ofNullable(user.getCampaign())
                    .map(CampaignView::getProgramId)
                    .flatMap(programClient::findById)
                    .map(ProgramView::getName)
                    .orElse(null);

                return OnlineUser.builder()
                    .userId(userId)
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .referrerDomain(Optional.ofNullable(user.getSource()).map(Source::getReferrer).orElse(null))
                    .minutesOnline(minutesOnline)
                    .programName(programName)
                    .programId(programId)
                    .build();
            })
            .orElse(null);
    }

    public CustomerLastLoginDetails buildCustomerLastLoginDetails(UserAndProspectDoc userAndProspectDoc) {
        boolean onlineStatus = getCustomerOnlineStatus(userAndProspectDoc.getId());
        return new CustomerLastLoginDetails(
            userAndProspectDoc.getLastLoginAt(),
            userAndProspectDoc.getLogins(),
            onlineStatus
        );
    }

    public boolean getCustomerOnlineStatus(String userId) {
        long threshold = Instant.now().getEpochSecond() - HEARTBEAT_INTERVAL;
        Map<String, AttributeValue> expressionValues = Map.of(":userId", AttributeValue.builder().s(userId).build());

        QueryRequest queryRequest = QueryRequest.builder()
            .tableName(sessionHeartbeatTable)
            .keyConditionExpression("userId = :userId")
            .expressionAttributeValues(expressionValues)
            .build();

        return onlineUsersRepository.findByUserId(queryRequest).items().stream()
            .findFirst()
            .map(item -> item.get("currentTimestamp"))
            .map(AttributeValue::n)
            .map(Long::parseLong)
            .filter(currentTimeStamp -> currentTimeStamp >= threshold)
            .isPresent();
    }

    public List<OnlineUser> getOnlineUsersByDealer(String dealerId){
        long threshold = Instant.now().getEpochSecond() - HEARTBEAT_INTERVAL;
        List<DigitalRetailSession> heartBeatSessions = onlineUsersRepository.findHeartBeatSessionsByDealerId(dealerId,threshold);
        return findOnlineUsersFromSessionsByDealer(heartBeatSessions,dealerId);
    }

    public List<OnlineUser> findOnlineUsersFromSessionsByDealer(List<DigitalRetailSession> onlineUserSessions, String dealerId) {
        return onlineUserSessions.stream().map(DigitalRetailSession::getUserId)
            .map(this::populateUserDetails)
            .filter(Objects::nonNull)
            .filter(onlineUser -> hasDealerLinks(onlineUser, dealerId))
            .sorted(Comparator.comparingLong(OnlineUser::getMinutesOnline))
            .collect(Collectors.toList());
    }

    private boolean hasDealerLinks(OnlineUser onlineUser,String dealerId) {
        return dealerLinkClient.findByUserAndDealer(onlineUser.getUserId(), dealerId).isPresent();
    }
}
