package com.carsaver.partner.service.desking.standard;

import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.campaign.FinanceConfig;
import com.carsaver.magellan.model.dealer.PaymentConfig;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

public class DealMarkUpService {

    /**
     * Gets the finance correct markup or money factor to be used for OL
     * @param campaign CampaignView
     * @param dealerView DealerView
     * @return BigDecimal
     */
    public static BigDecimal calculateFinanceMarkup(CampaignView campaign, DealerView dealerView) {

        BigDecimal markUp;

        // get lease mark up rate off finance config from campaign
        BigDecimal campaignLoanMarkupPctOverride = getCampaignLoanMarkupPctOverride(campaign);
        if (campaignLoanMarkupPctOverride != null) {
            // set mark up rate to campaign lease mark up override
            markUp = calculateNewMarkUpValue(campaignLoanMarkupPctOverride);
        } else {
            // default to mark up rate to Dealer's payment config lease mark up rate
            markUp = getDealerFinanceMarkUpRate(dealerView);
        }

        return markUp;
    }

    /**
     * gets the campaign's markup value that will be used if it is set
     * @param campaign CampaignView
     * @return BigDecimal
     */
    @Nullable
    private static BigDecimal getCampaignLoanMarkupPctOverride(CampaignView campaign) {
        BigDecimal campaignLoanMarkupPctOverride = Optional.ofNullable(campaign).map(CampaignView::getFinanceConfig).map(FinanceConfig::getLoanMarkupPctOverride).orElse(null);
        return campaignLoanMarkupPctOverride;
    }

    /**
     * Helper method to get the dealer specific markup
     * @param dealer
     * @return
     */
    private static BigDecimal getDealerFinanceMarkUpRate(DealerView dealer) {

        BigDecimal dealerLeaseMarkUpRate = Optional.ofNullable(dealer)
            .map(DealerView::getPaymentConfig)
            .map(PaymentConfig::getFinanceMarkupPercent)
            .map(BigDecimal::new)
            .orElse(null);

        BigDecimal moneyFactor = calculateNewMarkUpValue(dealerLeaseMarkUpRate);
        return moneyFactor ;
    }

    /**
     * Gets the lease correct markup or money factor to be used for OL
     * @param campaign CampaignView
     * @param dealerView DealerView
     * @return BigDecimal
     */
    public static BigDecimal calculateLeaseMarkup(CampaignView campaign, DealerView dealerView) {
        BigDecimal markUp;

        // get lease mark up rate off finance config from campaign
        BigDecimal campaignLeaseMarkupPctOverride = getCampaignLeaseMarkupPctOverride(campaign);
        if (campaignLeaseMarkupPctOverride != null) {
            // set mark up rate to campaign lease mark up override
            markUp = calculateNewMarkUpValue(campaignLeaseMarkupPctOverride);
        } else {
            // default to mark up rate to Dealer's payment config lease mark up rate
            markUp = getDealerLeaseMarkUpRate(dealerView);
        }

        return markUp;
    }

    /**
     * gets the campaign's markup value that will be used if it is set
     * @param campaign CampaignView
     * @return BigDecimal
     */
    @Nullable
    private static BigDecimal getCampaignLeaseMarkupPctOverride(CampaignView campaign) {
        BigDecimal campaignLeaseMarkupPctOverride = Optional.ofNullable(campaign)
            .map(CampaignView::getFinanceConfig)
            .map(FinanceConfig::getLeaseMarkupPctOverride)
            .orElse(null);
        return campaignLeaseMarkupPctOverride;
    }

    /**
     * Helper method to get the dealer specific markup
     * @param dealer
     * @return
     */
    private static BigDecimal getDealerLeaseMarkUpRate(DealerView dealer) {

        BigDecimal dealerLeaseMarkUpRate = Optional.ofNullable(dealer)
            .map(DealerView::getPaymentConfig)
            .map(PaymentConfig::getLeaseMarkupPercent)
            .map(BigDecimal::new)
            .orElse(null);

        BigDecimal moneyFactor = calculateNewMarkUpValue(dealerLeaseMarkUpRate);
        return moneyFactor ;
    }

    @Nullable
    private static BigDecimal calculateNewMarkUpValue(BigDecimal leaseMarkUpRate) {
        BigDecimal divisor = BigDecimal.valueOf(2.4d);
        BigDecimal moneyFactor = leaseMarkUpRate != null ? leaseMarkUpRate.divide(divisor, 2, RoundingMode.HALF_EVEN) : null;
        return moneyFactor;
    }
}
