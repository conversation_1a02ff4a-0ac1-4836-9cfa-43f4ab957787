package com.carsaver.partner.service.vehiclesearches;

import com.carsaver.partner.client.bigquery.BigQueryClient;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Service
@AllArgsConstructor
public class VehicleSearchesService {

    private final BigQueryClient bigQueryClient;

    public static final Set<String> QUERY_PARAMS = Set.of(
        "paymentType",
        "makes",
        "models",
        "bodyStyles",
        "price",
        "years",
        "trims",
        "exteriorColors",
        "interiorColors",
        "transmissions",
        "engineCylinders",
        "driveTrains",
        "fuelTypes",
        "passengerCapacity",
        "specialPrograms",
        "miles",
        "distance",
        "stockTypes",
        "stockTypeConditions",
        "tradeInVehicleClass",
        "tradeInVehicleSubclass",
        "zipCode",
        "dealerIds",
        "certified"
    );

    private static final Set<String> QUERY_PARAMS_WITH_REQUIRED_LINK_PARAMS = queryParamsWithDealerId();

    private static Set<String> queryParamsWithDealerId() {
        Set<String> set = new HashSet<>();
        set.add("dealerId");
        set.addAll(QUERY_PARAMS);
        return set;
    }

    public List<VehicleSearches> process(String userId, String dealerId) {
        return map(bigQueryClient.getVehicleSearchesData(userId, dealerId));
    }

    public static List<VehicleSearches> map(List<Map<String, Object>> data) {
        Map<Map<String, List<String>>, List<VehicleSearches>> collect = data.stream().map(VehicleSearchesService::toQuery)
            .collect(groupingBy(VehicleSearches::getQueryParams));

        List<VehicleSearches> result = new ArrayList<>();

        collect.forEach((key, value) -> {
            var latest = value.stream().map(VehicleSearches::getLatestTimestamp).max(LocalDateTime::compareTo);
            result.add(new VehicleSearches(key, latest.orElseThrow(), value.size(), value.get(0).getLink()));
        });

        return result.stream()
            .sorted(Comparator.comparing(VehicleSearches::getLatestTimestamp).reversed())
            .collect(Collectors.toList());

    }

    private static VehicleSearches toQuery(Map<String, Object> stringObjectMap) {
        Map<String, List<String>> queryParams = (Map<String, List<String>>) stringObjectMap.get("params");

        String link = stringObjectMap.get("uri_base") + "?" + toQueryString(forLink(queryParams));

        Long timestamp = (Long) stringObjectMap.get("event_timestamp");
        return new VehicleSearches(forQuery(queryParams), convertMicrosecondsToLocalDateTime(timestamp), 1, link);
    }

    private static Map<String, List<String>> forLink(Map<String, List<String>> queryParams) {
        HashMap<String, List<String>> newQueryParams = new HashMap<>();
        queryParams.forEach((key, value) -> {
            if (QUERY_PARAMS_WITH_REQUIRED_LINK_PARAMS.contains(key)) {
                newQueryParams.put(key, value);
            }
        });
        return newQueryParams;
    }

    private static Map<String, List<String>> forQuery(Map<String, List<String>> queryParams) {
        HashMap<String, List<String>> newQueryParams = new HashMap<>();
        queryParams.forEach((key, value) -> {
            if (QUERY_PARAMS.contains(key)) {
                newQueryParams.put(key, value);
            }
        });
        return newQueryParams;
    }

    public static LocalDateTime convertMicrosecondsToLocalDateTime(long timestampMicros) {
        long timestampMillis = timestampMicros / 1_000; // Convert microseconds to milliseconds
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestampMillis), ZoneId.of("UTC"));
    }

    public static String toQueryString(Map<String, List<String>> params) {
        return params.entrySet().stream()
            .map(entry -> {
                String value = entry.getValue().stream().collect(Collectors.joining(","));
                return URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8) + "=" +
                    URLEncoder.encode(value, StandardCharsets.UTF_8);
            })
            .collect(Collectors.joining("&"));
    }
}


