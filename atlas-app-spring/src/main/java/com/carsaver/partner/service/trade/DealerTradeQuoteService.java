package com.carsaver.partner.service.trade;

import com.carsaver.magellan.api.deal.CertificateService;
import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.UserVehicleClient;
import com.carsaver.magellan.client.VehicleQuoteClient;
import com.carsaver.magellan.client.request.UpdateRequest;
import com.carsaver.magellan.client.request.UpdateRequestFactory;
import com.carsaver.magellan.client.request.uservehicle.VehicleQuoteGetOrCreateRequest;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.payoff.PayoffSource;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView;
import com.carsaver.partner.model.TradePayoff;
import com.carsaver.partner.model.user.UserTradeQuote;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.CollectionModel;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DealerTradeQuoteService {

    @Autowired
    private VehicleQuoteClient vehicleQuoteClient;

    @Autowired
    private CertificateClient certificateClient;

    @Autowired
    private CertificateService certificateService;

    @Autowired
    private UserVehicleClient userVehicleClient;

    public UserVehicleView updateTradePayoff(String userVehicleId, TradePayoff tradePayoff){
        tradePayoff.setPayoffSource(PayoffSource.DEALER_VERIFIED);
        UserVehicleView userVehicle = userVehicleClient.update(userVehicleId, tradePayoff);

        this.updateDealsWithNewPayoff(userVehicle);

        return userVehicle;
    }

    public UserVehicleQuoteView upsertDealerQuote(String dealerId, UserTradeQuote userTradeQuote) {
        var userVehicleQuoteView = vehicleQuoteClient.findById(userTradeQuote.getId()).orElseThrow();

        VehicleQuoteGetOrCreateRequest request = VehicleQuoteGetOrCreateRequest.builder()
            .dealerId(dealerId)
            .provider("dealer")
            .userId(userTradeQuote.getUserId())
            .vin(userTradeQuote.getVin())
            .quoteRequest(userVehicleQuoteView.getQuoteRequest())
            .physicalCondition(userVehicleQuoteView.getVehicleCondition())
            .vehicleValuesResponse(userVehicleQuoteView.getVehicleValuesResponse())
            .build();
        UserVehicleQuoteView vehicleQuoteView = vehicleQuoteClient.getOrCreate(request);

        UpdateRequest valueUpdate = UpdateRequestFactory.builder()
            .pair("adjustedTradeValue", userTradeQuote.getQuoteAmount())
            .pair("expirationDate", userTradeQuote.getExpiration())
            .pair("completed", true)
            .pair("dealerId", dealerId)
            .build();
        vehicleQuoteView = vehicleQuoteClient.update(vehicleQuoteView.getId(), valueUpdate);

        // TODO: this has been in place, but is it a good idea now?
        this.updateDealsWithNewQuote(vehicleQuoteView, userTradeQuote.getUserId());

        return vehicleQuoteView;
    }

    private void updateDealsWithNewPayoff(UserVehicleView userVehicleView) {
        CollectionModel<CertificateView> certificateViews = certificateClient.findByUserIdAndTradeVehicleId(userVehicleView.getUserId(), userVehicleView.getId());
        certificateViews.forEach(certificate -> {
            if (certificate.isVehicleActive()) {
                log.info("Updating certificate {}, payoff details", certificate.getId());
                certificateService.updateTradeAndQuote(certificate);
            }
        });
    }

    private void updateDealsWithNewQuote(UserVehicleQuoteView vehicleQuoteView, String userId) {
        CollectionModel<CertificateView> certificateViews = certificateClient.findByUserIdAndDealerIdAndTradeVehicleId(userId, vehicleQuoteView.getDealerId(), vehicleQuoteView.getUserVehicleId());
        certificateViews.forEach(certificate -> {
            if (certificate.isVehicleActive()) {
                log.info("Updating certificate {}, with trade quote {}", certificate.getId(), vehicleQuoteView.getId());
                certificateService.updateTradeAndQuote(certificate, vehicleQuoteView);
            }
        });
    }
}
