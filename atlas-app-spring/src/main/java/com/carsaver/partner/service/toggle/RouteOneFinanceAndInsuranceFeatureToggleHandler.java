package com.carsaver.partner.service.toggle;

import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.exception.InternalServerError;
import com.carsaver.partner.model.DealerProgram;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ToggleConfigRequest;
import com.carsaver.partner.model.protection_products.response.ErrorResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpSession;

@Service
@RequiredArgsConstructor
@Slf4j
public class RouteOneFinanceAndInsuranceFeatureToggleHandler implements FeatureToggleHandler {

    private final FeatureSubscriptionsClient featureSubscriptionsClient;
    private final HttpSession session;

    @Value("${features-subscription.routeone-f-and-i-feature-id}")
    private String routeOneFAndIFeatureId;

    @Value("${features-subscription.nesna-f-and-i-feature-id}")
    private String nesnaFAndIFeatureId;

    public static final String NESNA_FEATURE_SUBSCRIPTION_ENABLED = "nesnaFeatureSubscriptionEnabled";

    @Override
    public boolean supports(String configType) {
        return "route_one_finance_and_insurance".equalsIgnoreCase(configType);
    }

    @Override
    public DealerProgram handleFeatureToggle(String dealerId, String programId, ToggleConfigRequest toggleConfigRequest) {
        FeatureSubscriptionRequest fiRequest = new FeatureSubscriptionRequest();
        fiRequest.setDealerId(dealerId);
        fiRequest.setEntityId(programId);
        fiRequest.setFeatureRId(routeOneFAndIFeatureId);
        fiRequest.setActive(toggleConfigRequest.getIsEnabled());
        FeatureSubscriptionResponse fiResponse = featureSubscriptionsClient.saveFeatureSubscription(fiRequest);

        if (fiResponse == null || fiResponse.getActive() == null) {
            throw new InternalServerError(ErrorResponse.builder().errorMessage("Failed to update RouteOne F&I feature subscription").build());
        }

        // Toggle off nesna if route one is enabled
        deActivateFeatureSubscription(dealerId, programId, fiResponse, nesnaFAndIFeatureId);

        session.setAttribute(NESNA_FEATURE_SUBSCRIPTION_ENABLED, false);
        log.info("Selected Dealer's Nesna feature subscription is set to: {}", session.getAttribute(NESNA_FEATURE_SUBSCRIPTION_ENABLED));

        return DealerProgram.builder()
            .isNesnaFAndIEnabled(false)
            .isRouteOneFAndIEnabled(fiResponse.getActive())
            .build();
    }

    private void deActivateFeatureSubscription(String dealerId, String programId, FeatureSubscriptionResponse fiResponse, String featureRId) {
        if (Boolean.TRUE.equals(fiResponse.getActive())) {
            FeatureSubscriptionRequest roRequest = new FeatureSubscriptionRequest();
            roRequest.setDealerId(dealerId);
            roRequest.setEntityId(programId);
            roRequest.setFeatureRId(featureRId);
            roRequest.setActive(false);
            featureSubscriptionsClient.saveFeatureSubscription(roRequest);
        }
    }
}
