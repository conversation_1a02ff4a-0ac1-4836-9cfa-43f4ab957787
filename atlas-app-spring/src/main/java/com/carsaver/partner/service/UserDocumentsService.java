package com.carsaver.partner.service;

import com.carsaver.partner.client.UserDocumentsClientV2;
import com.carsaver.partner.model.document_upload.DocumentDetail;
import com.carsaver.partner.model.document_upload.UserDocuments;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class UserDocumentsService {
    final UserDocumentsClientV2 userDocumentsClient;

    public UserDocumentsService(UserDocumentsClientV2 userDocumentsClient) {
        this.userDocumentsClient = userDocumentsClient;
    }

    public List<UserDocuments.Documents> getUserDocuments(String dealerId, String userId) {
        List<UserDocuments.Documents> userDocuments = userDocumentsClient.getUserDocuments(dealerId, userId);
        return userDocuments;
    }

    public DocumentDetail loadFileByFileId(String filename) {
        DocumentDetail documentDetail = userDocumentsClient.loadS3FileByFileId(filename);
        return documentDetail;
    }
}
