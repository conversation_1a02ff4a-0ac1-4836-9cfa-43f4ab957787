package com.carsaver.partner.service.desking.standard;

import com.carsaver.core.PaymentType;
import com.carsaver.magellan.api.deal.CertificateService;
import com.carsaver.magellan.api.deal.DealJacketContext;
import com.carsaver.magellan.api.deal.DealJacketRequest;
import com.carsaver.magellan.api.deal.DealJacketRequestService;
import com.carsaver.magellan.api.deal.DealPreferenceService;
import com.carsaver.magellan.api.deal.DealUpdateRequest;
import com.carsaver.magellan.api.quote.PaymentService;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.Source;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.certificate.DealPreferences;
import com.carsaver.magellan.model.certificate.DealType;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.partner.model.LeaseDealModel;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse;
import com.carsaver.partner.model.paas.InputValues;
import com.carsaver.partner.service.desking.standard.lease.LeaseQuoteHelper;
import com.carsaver.partner.service.desking.standard.lease.LeaseQuoteService;
import com.carsaver.partner.service.desking.standard.paas.PaasDealService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
public class LowestDealService {


    public static final String FINANCE = "Finance";
    public static final String LEASE = "Lease";
    private final CertificateService certificateService;
    private final DealJacketRequestService dealJacketRequestService;
    private final PaymentService paymentService;
    private final DealPreferenceService dealPreferenceService;
    private final LeaseQuoteService leaseQuoteService;
    private final CloneRequestDataToDealJacketConverter cloneDataService;
    private final SplitFeatureFlags splitFeatureFlags;
    private final PaasDealService paasDealService;

    public LowestDealService(CertificateService certificateService,
                             DealJacketRequestService dealJacketRequestService,
                             LeaseQuoteService leaseQuoteService, PaymentService paymentService,
                             DealPreferenceService dealPreferenceService, CloneRequestDataToDealJacketConverter cloneDataService,
                             SplitFeatureFlags splitFeatureFlags, PaasDealService paasDealService) {

        this.certificateService = certificateService;
        this.dealJacketRequestService = dealJacketRequestService;
        this.paymentService = paymentService;
        this.dealPreferenceService = dealPreferenceService;
        this.leaseQuoteService = leaseQuoteService;
        this.cloneDataService = cloneDataService;
        this.splitFeatureFlags = splitFeatureFlags;
        this.paasDealService = paasDealService;
    }


    /**
     * Method used for creating a new deal
     * Sets default and certificate values
     * @param certificate
     * @return
     */
    public CertificateView fetchAndStoreFinanceLowestDeal(CertificateView certificate) {
        UserView user = certificate.getUser();
        CampaignView campaignView = user.getCampaign();

        DealJacket dealJacket;
        String campaign = Optional.ofNullable(certificate.getSource()).map(Source::getCampaignId).orElse(null);
        if (splitFeatureFlags.isPaasIntegrationEnabled(user.getId(), certificate.getDealerId(), campaign)) {
             InputValues inputValues = InputValues.builder().transaction(FINANCE).requestType(InputValues.RequestType.LOWEST).build();
             dealJacket = paasDealService.retrieveLowestPaasDeal(certificate, inputValues);
        } else {
            // create deal request that will be used to get the finance and lease quotes from OfferLogix
            DealJacketRequest request = dealJacketRequestService.createDealJacketRequest(campaignView, certificate);
            log.info("Desking | Create Deal DealJacketRequest {}", request);

            // call magellan to get OfferLogix lowest quote
             dealJacket = fetchFinanceLowestQuoteDealJacket(user, request, campaignView);
        }

        CertificateView result = saveQuoteToCertificate(certificate, dealJacket);

        return result;
    }

    /**
     * Method allows other methods to update the quoteView in the certificate
     * Called from  getFinanceLowestDeal and UpdateDealService.reCalculateAndSaveLowestQuote
     * @param certificate
     * @param dealJacket
     * @return
     */
    public CertificateView saveQuoteToCertificate(CertificateView certificate, DealJacket dealJacket) {
        CertificateView result = null;

        if (dealJacket != null) {

            // store lowest quote in garage table
            result = certificateService.updateQuoteAndPricing(certificate, dealJacket.getBaseQuote(), dealJacket.getQuote());

            //update the dealSheet in certificate
            DealUpdateRequest updateRequest = buildDealUpdateRequest(dealJacket);
            result = certificateService.updateDealSheet(result, updateRequest);

            DealPreferences dealPreferences = dealPreferenceService.getDealPreferencesOrDefault(certificate.getCampaign(), certificate.getVehicle(), certificate);
            dealPreferences.setDealType(dealJacket.isLease() ? DealType.valueOf(PaymentType.LEASE.name()): DealType.valueOf(PaymentType.FINANCE.name()));
            result = certificateService.updateDealPreferences(result, dealPreferences);
        }

        return result;
    }

    /**
     * Method is used when recalculating a deal for Standard Editing
     * Sets the values needed to make the magellan call to get finance lowest quotes
     */
    public DealJacket getFinanceLowestDealForEditedDeals(CloneDealRequest cloneDealRequest, CertificateView certificate) {

        UserView user = certificate.getUser();
        CampaignView campaignView = user.getCampaign();

        // create deal request that will be used to get the finance and lease quotes from OfferLogix
        DealJacketRequest request = dealJacketRequestService.createDealJacketRequest(campaignView, certificate);

        cloneDataService.setCloneDataToDealJacketRequest(cloneDealRequest, request);
        DealJacket dealJacket = fetchFinanceLowestQuoteDealJacket(user, request, campaignView);

        log.info("Desking | Create Deal DealJacketRequest {}", request);

        return dealJacket;
    }

    /**
     * Calls magellan who makes OfferLogix call
     * Method is used when creating and recalculating a deal.
     * Sets the deal preferences needed to make the magellan call to get finance lowest quotes
     * @param user
     * @param request
     * @param campaignView
     * @return
     */
    private DealJacket fetchFinanceLowestQuoteDealJacket(UserView user, DealJacketRequest request, CampaignView campaignView) {
        DealJacket dealJacket = null;
        try {
            // set the deal preference to be finance so the dealJacket returns with a financed deal. Lease deal is done separately
            DealPreferences dealPreferences = dealPreferenceService.getDefaultFor(campaignView, request.getVehicle());
            dealPreferences.setDealType(DealType.valueOf(PaymentType.FINANCE.name()));
            DealJacketRequest financeDealJacketRequest = request.toBuilder().applyDealPreferences(dealPreferences).build();
            financeDealJacketRequest.setTerm(request.getTerm());
            financeDealJacketRequest.setDownPayment(request.getDownPayment());

            // get finance deal from magellan
            DealJacketContext context = paymentService.getLowestDeal(campaignView, financeDealJacketRequest, user);
            dealJacket = Optional.ofNullable(context).map(DealJacketContext::getDealJacket).orElse(null);

        } catch (Exception ex) {
            log.error("Exception thrown when fetching finance lowest lease quote. Error: {}", ex);
        }

        return dealJacket;
    }


    /**
     * Method used for creating a new deal
     * Sets default and certificate values
     * @param certificate
     * @return
     */
    public LeaseDealModel fetchAndStoreLeaseLowestDeal(CertificateView certificate) {
        UserView user = certificate.getUser();
        LeaseDealModel.LeaseDealModelBuilder model = LeaseDealModel.builder();

        String campaign = Optional.ofNullable(certificate.getSource()).map(Source::getCampaignId).orElse(null);
        if (splitFeatureFlags.isPaasIntegrationEnabled(user.getId(), certificate.getDealerId(), campaign)) {
            InputValues inputValues = InputValues.builder().transaction(LEASE).requestType(InputValues.RequestType.LOWEST).build();
            DealJacket  dealJacket = paasDealService.retrieveLowestPaasDeal(certificate, inputValues);
            CertificateView result = saveQuoteToCertificate(certificate, dealJacket);
            model.certificate(result);
            model.dealJacket(dealJacket);
        } else {
            LeaseQuoteResponse.Quote quote =  getLeaseLowestDeal(certificate);
            model.quote(quote);
        }

        return model.build();
    }

    /**
     * Quote-Service calculations using OfferLogix
     * Method used for creating new deals.
     * Will set the default values needed to make the call to qyote-service to get Lease quotes
     * @param certificate
     */
    public LeaseQuoteResponse.Quote getLeaseLowestDeal(CertificateView certificate) {

        UserView user = certificate.getUser();
        CampaignView campaignView = user.getCampaign();

        // create deal request that will be used to get the finance and lease quotes from OfferLogix
        DealJacketRequest request = dealJacketRequestService.createDealJacketRequest(campaignView, certificate);

        if (request.getDownPaymentFieldAmount().compareTo(BigDecimal.ZERO) == 0 && request.getDownPaymentPercent() == null) {
            LeaseQuoteHelper.setDefaultDownPaymentToDealJacketRequest(campaignView, request);
        }

        log.info("Desking | Create Deal DealJacketRequest {}", request);

        // call quote-service to get lowest lease quote
        LeaseQuoteResponse.Quote leaseQuoteQuote = this.getLeaseLowestQuote(certificate, request);

        saveLeaseQuoteViewToCertificate(certificate, leaseQuoteQuote);

        return leaseQuoteQuote;
    }

    /**
     * Method allows other methods to update the quoteView in the certificate
     * Called from  getLeaseLowestDeal and UpdateDealService.reCalculateAndSaveLowestQuote
     * @param certificate
     * @param leaseQuoteQuote
     * @return
     */
    public void saveLeaseQuoteViewToCertificate(CertificateView certificate, LeaseQuoteResponse.Quote leaseQuoteQuote) {
        // store the data in garage table
        if (leaseQuoteQuote != null && leaseQuoteQuote.getQuoteView() != null) {
            // base quote and quote are the same for lease since taxes are calculated different from finance
            CertificateView result = certificateService.updateQuoteAndPricing(certificate, leaseQuoteQuote.getQuoteView(), leaseQuoteQuote.getQuoteView());

            DealPreferences dealPreferences = dealPreferenceService.getDealPreferencesOrDefault(certificate.getCampaign(), certificate.getVehicle(), certificate);
            dealPreferences.setDealType(DealType.valueOf(PaymentType.LEASE.name()));
            certificateService.updateDealPreferences(result, dealPreferences);
        }
    }

    /**
     * This method is used when dealer is creating a new deal.
     * Method gets the return lease values from quote-service and calls
     * a helper method to clean up the result.
     * @param certificate
     * @param request
     * @return
     */
    public LeaseQuoteResponse.Quote getLeaseLowestQuote(CertificateView certificate, DealJacketRequest request) {
        LeaseQuoteResponse leaseQuoteResponse = leaseQuoteService.getLeaseQuotes(certificate, request);

        // get the original requested down payment amount, the response quoteView will have a calculated amount
        Double downPayment = request.getDownPaymentFieldAmount().doubleValue();
        LeaseQuoteResponse.Quote lowestLeaseQuote = LeaseQuoteHelper.findAndCleanLowestLeaseQuotes(downPayment, leaseQuoteResponse);
        return lowestLeaseQuote;
    }

    /**
     * This method is uses in Standard Editing when recalculating
     * Method gets the return lease values from quote-service and calls
     * a helper method to clean up the result.
     * @param certificate
     */
    public LeaseQuoteResponse.Quote getLeaseLowestDealForEditedDeals(CloneDealRequest cloneDealRequest, CertificateView certificate) {
        // make a call to quote-service to get lowest quotes and clean up the response
        LeaseQuoteResponse leaseQuoteResponse = leaseQuoteService.getEditedLeaseQuotes(certificate, cloneDealRequest);

        // get the original requested down payment amount, the response quoteView will have a calculated amount
        Double downPayment = Optional.ofNullable(cloneDealRequest).map(CloneDealRequest::getDueAtSigning)
            .map(CloneDealRequest.DueAtSigning::getConsumerCash).map(Double::parseDouble).orElse(0D);

        LeaseQuoteResponse.Quote lowestLeaseQuote = LeaseQuoteHelper.findAndCleanLowestLeaseQuotes(downPayment, leaseQuoteResponse);
        return lowestLeaseQuote;
    }

    private DealUpdateRequest buildDealUpdateRequest(DealJacket dealJacket) {
        List<DealerFeeView> fees = dealJacket.getDealSheet().getDealerFees().stream().
            map(dealerFee -> DealerFeeView.builder().amount(dealerFee.getAmount()).name(dealerFee.getName()).build())
            .collect(Collectors.toList());

        DealUpdateRequest dealUpdateRequest = DealUpdateRequest.builder()
            .termLength(dealJacket.getQuote().getTerm())
            .dealerFees(fees)
            .dealerRebates(dealJacket.getDealSheet().getDealerRebates())
            .downPayment(dealJacket.getDealSheet().getCashDownPayment())
            .salePrice(dealJacket.getDealSheet().getSalePrice())
            .build();

        return dealUpdateRequest;
    }

}

