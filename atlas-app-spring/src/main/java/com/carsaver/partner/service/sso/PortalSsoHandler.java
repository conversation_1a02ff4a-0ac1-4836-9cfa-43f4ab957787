package com.carsaver.partner.service.sso;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.auth.SessionUtils;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.magellan.client.PasswordClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.exception.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Optional;

@Slf4j
@Component
public class PortalSsoHandler implements SsoHandler {

    public static final String PARAM_DEALER_IDS = "dealerIds";
    @Autowired
    private PasswordClient passwordClient;

    @Autowired
    private UserClient userClient;

    @Value("${portal.atlasRedirectUri}")
    private String atlasRedirectUri;

    @Value("${portal.authorizationUri}")
    private String portalAuthorizeUri;

    @Value("${portal.clientId}")
    private String portalClientId;

    @Value("${tenant.portal-id}")
    private String portalTenantId;

    @Override
    public String getProvider() {
        return "portal";
    }

    @Override
    public String getRegisterPage() {
        throw new NotImplementedException("getRegisterPage() not implemented");
    }

    @Override
    public String getLoginPage(HttpServletRequest request) {
        String scope = "openid";
        String responseType = "code";
        HttpSession session = request.getSession(false);

        String dealerId = Optional.ofNullable(request.getParameter(PARAM_DEALER_IDS)).orElseThrow(() -> new BadRequestException("dealerIds is request and should only contain one UUID dealerId"));
        String ssoOriginalRequestUri = request.getRequestURI() + "?" + PARAM_DEALER_IDS + "=" + dealerId;
        session.setAttribute("ssoOriginalRequestUri", ssoOriginalRequestUri);

        String ssoStateToken = RandomStringUtils.random(12, true, true);
        session.setAttribute("ssoStateToken", ssoStateToken);

        String loginPage = UriComponentsBuilder
            .fromUriString(portalAuthorizeUri + "/atlas")
            .queryParam("responseType", responseType)
            .queryParam("redirectUri", atlasRedirectUri)
            .queryParam("scope", scope)
            .queryParam("clientId", portalClientId)
            .queryParam("state", ssoStateToken)
            .build(true)
            .toUriString();

        log.debug("atlas-autologin:" + getProvider() + "sso  loginPage={}", loginPage);

        return loginPage;
    }

    @Override
    public String handleCallback(HttpServletRequest request) {
        HttpSession session = request.getSession(false);

        String code = Optional.ofNullable(request.getParameter("code")).orElseThrow();

        String state = Optional.ofNullable(request.getParameter("state")).orElseThrow();
        String savedSsoStateToken = (String) session.getAttribute("ssoStateToken");

        if (!state.equals(savedSsoStateToken)) {
            throw new RuntimeException("state token does not match param=" + state + "saved=" + savedSsoStateToken);
        }
        session.removeAttribute("ssoStateToken");

        String redirectUri = (String) session.getAttribute("ssoOriginalRequestUri");
        session.removeAttribute("ssoOriginalRequestUri");

        log.debug("atlas-autologin: redirectAfter logging in={} ", redirectUri);

        if (StringUtils.isEmpty(redirectUri)) {
            redirectUri = "/login-success";
        }

        if (AuthUtils.getUserIdFromSecurityContext().isPresent()) {
            return "redirect:" + redirectUri;
        }

        TokenResponse tokenResponse = passwordClient.loginTenantUser(code, portalTenantId).orElse(null);

        if (tokenResponse != null) {
            log.debug("atlas-autologin: found tokenResponse expiresIn={} ", tokenResponse.getExpiresIn());
            UserView user = userClient.findById(tokenResponse.getId());
            SessionUtils.setTokenResponse(request, tokenResponse);
            SessionUtils.setUser(request, user);
        } else {
            throw new RuntimeException("atlas-autologin: tokenResponse not found for code=" + code + " and tenantId=" + portalTenantId);
        }

        return "redirect:" + redirectUri;
    }
}
