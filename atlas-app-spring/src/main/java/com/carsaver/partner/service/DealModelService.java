package com.carsaver.partner.service;

import com.carsaver.core.PaymentType;
import com.carsaver.core.StockType;
import com.carsaver.magellan.api.deal.DealJacketService;
import com.carsaver.magellan.api.deal.DealService;
import com.carsaver.magellan.api.deal.DealSheetService;
import com.carsaver.magellan.api.externaloffers.model.ExternalOffers;
import com.carsaver.magellan.api.finance.FinanceDecisionModel;
import com.carsaver.magellan.api.finance.LoanDecisionService;
import com.carsaver.magellan.client.FinancierClient;
import com.carsaver.magellan.client.UserContractClient;
import com.carsaver.magellan.model.*;
import com.carsaver.magellan.model.certificate.DealPreferences;
import com.carsaver.magellan.model.certificate.DealType;
import com.carsaver.magellan.model.deal.DealCreditStatus;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.dealer.DealerFeeView;
import com.carsaver.magellan.model.finance.Decision;
import com.carsaver.magellan.model.finance.LoanResponseView;
import com.carsaver.magellan.model.pricing.PricesView;
import com.carsaver.magellan.model.user.UserContractView;
import com.carsaver.magellan.model.user.UserVehicleView;
import com.carsaver.magellan.model.vehicle.UserVehicleQuoteView;
import com.carsaver.partner.model.deal.CustomerDealModel;
import com.carsaver.partner.model.deal.CustomerModel;
import com.carsaver.partner.model.deal.ExternalOffersModel;
import com.carsaver.partner.model.deal.OrderStatus;
import com.carsaver.partner.model.deal.SimpleVehicleModel;
import com.carsaver.partner.model.deal.UserInfoModel;
import com.carsaver.partner.model.trade.TradeType;
import com.carsaver.partner.service.desking.standard.paas.PaasUtil;
import com.carsaver.partner.service.reservations.ReservationService;
import com.carsaver.partner.service.split_io.SplitFeatureFlags;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import static java.util.Optional.ofNullable;

@Slf4j
@Service
public class DealModelService {

    @Autowired
    private UserContractClient userContractClient;

    @Autowired
    private FinancierClient financierClient;

    @Autowired
    private DealService dealService;

    @Autowired
    private LoanDecisionService loanDecisionService;

    @Autowired
    private DealJacketService dealJacketService;

    @Autowired
    private UserVehicleService userVehicleService;

    @Autowired
    private ReservationService reservationService;

    @Value("${features-toggle.pre-approval-enable}")
    private boolean featureTogglePreApproval;

    @Autowired
    private DealerFeesService dealerFeesService;

    @Autowired
    private SplitFeatureFlags splitFeatureFlags;

    @Autowired
    private DealSheetService dealSheetService;

    @Autowired
    private ReceiptService receiptService;

    public Optional<CustomerDealModel> createDealModel(BaseLeadView lead) {
        if(lead == null) {
            return Optional.empty();
        }

        CertificateView certificate = lead.getCertificate();
        if(certificate == null) {
            log.error("No certificate found for: {}", lead);
            return Optional.empty();
        }

        Optional<DealJacket> dealJacketOpt = dealJacketService.getDealJacket(certificate);
        if(dealJacketOpt.isEmpty()) {
            log.error("No DealJacket found for: {}", certificate);
        }

        DealJacket dealJacket = dealJacketOpt.orElse(null);

        return createDealModel(certificate, lead, dealJacket);
    }

    public Optional<CustomerDealModel> createDealModel(CertificateView certificate) {
        boolean isNewUI = Optional.ofNullable(certificate.getSummaryDetails()).isPresent();
        if (isNewUI) {
            return receiptService.buildDigitalRetailPreviewModel(certificate);
        }

        var dealJacket = dealSheetService.toDealJacket(certificate).orElse(null);
        UserView assignee = Optional.ofNullable(certificate.getDealerLink())
            .map(DealerLinkView::getDealerUser)
            .orElse(null);

        return createDealModel(certificate, assignee, dealJacket);
    }

    public Optional<CustomerDealModel> createDealModel(UserContractView userOrder, DealJacket dealJacket) {
        if (userOrder == null) {
            return Optional.empty();
        }

        CertificateView certificate = userOrder.getCertificate();
        if (certificate == null) {
            log.error("No certificate found for: {}", userOrder);
            return Optional.empty();
        }

        LoanResponseView loanResponse = userOrder.getLoanResponse();

        UserView userView = certificate.getUser();

        PricesView pricesView = certificate.getVehiclePrices();
        Decision decision = loanResponse.getData().getApplication().getDecision();
        QuoteView quoteView = certificate.getQuote();
        List<DealerFeeView> dealerFeeViewList ;
        String financeType = null;

        if (quoteView != null) {
            financeType = quoteView.isLease()
                ? PaymentType.LEASE.toProperCaseStr()
                : PaymentType.FINANCE.toProperCaseStr();

            // get dealer fees by dealType
            dealerFeeViewList = getPaymentTypeDealerFeeViews(certificate, quoteView.isLease());
        } else {
            // get dealer fees fro cash deal
            dealerFeeViewList = dealerFeesService.getCashDealerFeesByDealerId(certificate.getDealerId(), certificate.getStockType());
        }

        // calculate dealer fees total
        Double dealerFeeTotal = dealerFeesService.getTotalFees(dealerFeeViewList);

        String lenderName = Optional.ofNullable(loanResponse.getLenderId())
            .flatMap(financierClient::findByHorizonId)
            .map(FinancierView::getName)
            .orElseGet(loanResponse::getLenderName);

        String dealType = ofNullable(certificate.getDealPreferences())
            .map(DealPreferences::getDealType)
            .map(Objects::toString)
            .orElseGet(() ->
                (Optional.of(certificate)
                    .map(CertificateView::getQuote)
                    .map(QuoteView::getType)
                    .map(it -> "Loan".equals(it) ? DealType.FINANCE : DealType.LEASE)
                    .orElse(DealType.CASH))
                .name()
            );

        FinancierView financier = ofNullable(loanResponse.getLenderId()).flatMap(financierClient::findByHorizonId).orElse(null);
        Optional<FinanceDecisionModel> financeDecisionModel = loanDecisionService.buildFinanceResponse(loanResponse, financier);
        var monthlyPayment = financeDecisionModel.map(FinanceDecisionModel::getMonthlyPayment).orElse(null);
        var sellRateApr = financeDecisionModel.map(FinanceDecisionModel::getSellRateApr).orElse(null);

        CustomerDealModel.DealModel.DealModelBuilder dealBuilder = CustomerDealModel.DealModel.builder()
            .orderId(userOrder.getId())
            .certificateId(userOrder.getCertificateId())
            .dealName(certificate.getDealName())
            .financeType(financeType)
            .dealType(dealType)
            .financierName(lenderName)
            .status(loanResponse.isApproved() ? "Approved" : "Declined")
            .applicationDate(loanResponse.getCreatedDate())
            .applicationNumber(loanResponse.getLenderApplicationId())
            .msrp(pricesView.getMsrp())
            .discount(pricesView.getDealerDiscount())
            .dealerFee(dealerFeeTotal)
            .dealerFees(dealerFeeViewList)
            .salePrice(dealJacket.getSalePrice())
            .purchasePrice(ofNullable(dealJacket).map(DealJacket::getPurchasePrice).orElseGet(() -> certificate.getVehicle().getPrice()))
            .monthlyPayment(monthlyPayment)
            .monthlyPaymentWithFeesAndTaxes(monthlyPayment)
            .termLength(decision.getTermDisplay())
            .interestRate(sellRateApr)
            .externalOffers(Optional.ofNullable(certificate.getExternalOffers()).map(ExternalOffersModel::new).orElse(null))
            .netCashOffer(ofNullable(dealJacket).map(DealJacket::getTradeEquity).orElse(null))
            .totalAmountFinance(dealJacket.getTotalAmountFinanced());

        if (quoteView != null) {
            dealBuilder
                .annualMileageAllowance(Optional.ofNullable(quoteView.getMileageAllowed()).map(String::valueOf).orElse(null))
                .mileagePenalty(quoteView.getMilePenalty())
                .endOfTermPurchaseOption(quoteView.getPurchaseOption())
                .acquisitionFee(quoteView.getAcquisitionFee())
                .dispositionFee(quoteView.getDispositionFee())
                .securityDeposit(quoteView.getSecurityDeposit())
                .rebates(quoteView.getRebates())
                .dealerRebates(Optional.ofNullable(dealJacket.getDealSheet().getDealerRebates()).orElse(Collections.emptyList()))
                .registration(quoteView.getLicenseRegistration())
                .totalPayments(quoteView.getTotalPayments())
                .tax(quoteView.getTotalTaxes())
                .totalDueAtSigning(quoteView.getDueAtSigning())
                .outOfPocket(ofNullable(dealJacket).map(DealJacket::getOutOfPocket).orElse(null))
                .downPayment(ofNullable(dealJacket).map(DealJacket::getCashDown).orElse(null))
                .cashBack(ofNullable(dealJacket).map(DealJacket::getCashBack).orElse(null))
                .endOfTermPurchaseOption(getPurchasePriceOption(certificate, quoteView));
        }

        CustomerDealModel.DealModel deal = dealBuilder.build();
        updatePaasDeal(certificate, dealJacket, dealerFeeTotal, dealerFeeViewList, deal);

        // for a UserOrder we get the assignee from the DealerLink
        // it should be the most accurate in case th lead switched to another person
        UserView assignee = Optional.ofNullable(certificate.getDealerLink())
            .map(DealerLinkView::getDealerUser)
            .orElse(null);

        return buildDealModel(userView, certificate, deal, assignee);
    }

    private Optional<CustomerDealModel> createDealModel(CertificateView certificate, BaseLeadView lead, DealJacket dealJacket) {
        UserView assignee = Optional.ofNullable(lead.getProgramManager())
            .orElse(null);
        return createDealModel(certificate, assignee, dealJacket);
    }

    private Optional<CustomerDealModel> createDealModel(CertificateView certificate, UserView assignee, DealJacket dealJacket) {
        if(certificate == null) {
            return Optional.empty();
        }

        if(certificate.getUserContractId() != null) {
            UserContractView userContract = userContractClient.findById(certificate.getUserContractId());
            return createDealModel(userContract, dealJacket);
        }

        UserView user = certificate.getUser();
        DealType dealType = ofNullable(certificate.getDealPreferences())
            .map(DealPreferences::getDealType)
            .orElseGet(() ->
                (Optional.of(certificate)
                    .map(CertificateView::getQuote)
                    .map(QuoteView::getType)
                    .map(it -> "Loan".equals(it) ? DealType.FINANCE : DealType.LEASE)
                    .orElse(DealType.CASH))
            );

        CustomerDealModel.DealModel dealModel;
        if (dealType == DealType.CASH) {
            List<DealerFeeView> cashDealerFees = dealerFeesService.getCashDealerFeesByDealerId(certificate.getDealerId(), certificate.getStockType());
            dealModel = CustomerDealModel.DealModel.from(certificate, dealJacket, cashDealerFees);
        } else {
            DealCreditStatus creditStatus = dealService.getCreditStatus(certificate);
            String financierName = getFinancierName(certificate, creditStatus);
            List<DealerFeeView> dealerFees = getDealTypeDealerFeeViews(certificate, dealJacket, dealType);

            // set the deal model to display correctly in front-end
            dealModel = CustomerDealModel.DealModel.from(certificate, creditStatus, financierName, dealJacket, dealerFees);

            if (!featureTogglePreApproval){
                Optional.ofNullable(dealModel).ifPresent(deal -> deal.setPreApproval(null));
            }

            Double dealerFeeTotal = dealerFeesService.getTotalFees(dealerFees);
            updatePaasDeal(certificate, dealJacket, dealerFeeTotal, dealerFees, dealModel);
        }

        dealModel = reservationService.getReservationDeposit(certificate, dealModel);

        return buildDealModel(user, certificate, dealModel, assignee);
    }

    private CustomerDealModel.RemoteDelivery fetchRemoteDelivery(CertificateView certificate) {
        CustomerDealModel.RemoteDelivery remoteDelivery = null;

        if ( certificate.getUserContractId() != null) {
            UserContractView userContract =  userContractClient.findById(certificate.getUserContractId());

           boolean deliveryMethodSelected = Optional.ofNullable(userContract)
                .map(UserContractView::getVehicleDeliveryMethod)
                .filter(i -> i.equals("DELIVERY"))
                .isPresent();
             if (deliveryMethodSelected) {
                 String address = Optional.ofNullable(userContract).map(UserContractView::getDeliveryLocation).map(i -> i.getFullAddress()).orElse(null);
                 Double amount = Optional.ofNullable(userContract).map(UserContractView::getDeliveryFee).orElse(null);
                 remoteDelivery = CustomerDealModel.RemoteDelivery.builder().address(address).amount(amount).build();
             }
        }

        return remoteDelivery;
    }

    private String getFinancierName(CertificateView certificate, DealCreditStatus creditStatus){
        QuoteView quote = certificate.getQuote();
        String financierName = null;

        if(creditStatus.isPreQualified()) {
            financierName = creditStatus.getPreQualifiedLenderName();
        } else if(creditStatus.isPreApproved()) {
            financierName = creditStatus.getPreApprovedLenderName();
        } else if(quote != null){
            financierName = Optional.ofNullable(quote.getFinancierId())
                .flatMap(financierClient::findById)
                .map(FinancierView::getName)
                .orElseGet(quote::getLenderName);
        }

        return financierName;
    }

    private Optional<CustomerDealModel> buildDealModel(UserView user, CertificateView certificate, CustomerDealModel.DealModel dealModel, UserView assignee) {
        UserContractView contract = Optional.ofNullable(certificate.getUserContractId())
            .map(userContractClient::findById)
            .orElse(null);

        UserVehicleView userTradeVehicle = certificate.getTradeVehicle();
        CustomerDealModel.Trade trade = null;

        if (userTradeVehicle != null) {
            Optional<UserVehicleQuoteView> optUserVehicleQuote = Optional.ofNullable(userVehicleService.getDealerUserVehicleQuote(userTradeVehicle, certificate.getDealerId()));

            trade = CustomerDealModel.Trade.builder()
                .userVehicleId(userTradeVehicle.getId())
                .discrepancy(userTradeVehicle.getDiscrepancy())
                .vehicleQuoteId(optUserVehicleQuote.map(BaseView::getId).orElse(null))
                .yearMakeModel(userTradeVehicle.getYearMakeModel())
                .mileage(userTradeVehicle.getMileage())
                .vin(userTradeVehicle.getVin())
                .purchaseType(userTradeVehicle.getPurchaseType())
                .leaseInspectionScheduled(Optional.ofNullable(contract).map(UserContractView::getLeaseInspectionScheduled).orElse(false))
                .build();
        }

        // fetch delivery information if this is a contract deal
        CustomerDealModel.RemoteDelivery remoteDelivery = fetchRemoteDelivery(certificate);

        Optional<CustomerDealModel> customerDealModelOpt = Optional.of(
            CustomerDealModel.builder()
                .assignee(UserInfoModel.from(assignee))
                .deal(dealModel)
                .customer(CustomerModel.from(user))
                .orderStatus(OrderStatus.from(contract))
                .vehicle(SimpleVehicleModel.from(certificate.getVehicle()))
                .inventoryId(certificate.getInventoryId())
                .trade(trade)
                .remoteDelivery(remoteDelivery)
                .build()
        );

        return customerDealModelOpt;
    }

    private void updatePaasDeal(CertificateView certificate, DealJacket dealJacket, Double dealerFeeTotal,
                                List<DealerFeeView> dealerFeeViewList, CustomerDealModel.DealModel dealModel) {

        String campaign = Optional.ofNullable(certificate.getSource()).map(Source::getCampaignId).orElse(null);
        if (splitFeatureFlags.isPaasIntegrationEnabled(certificate.getUserId(), certificate.getDealerId(), campaign)) {

            QuoteView quoteView = certificate.getQuote();
            Optional<CustomerDealModel.DealModel> dealModelOpt = Optional.ofNullable(dealModel);
            if (dealModelOpt.isEmpty()) {
                return;
            }

            Double saleTax = Optional.ofNullable(quoteView.getTaxes()).map(TaxesView::getSalesTax).orElse(0D);
            Double firstMonthPayment = dealModelOpt.map(CustomerDealModel.DealModel::getMonthlyPaymentWithFeesAndTaxes).orElse(0D);

            Double totalConditionalExternalOffersInQuote = PaasUtil.getTotalConditionalExternalOffersQuotes(quoteView.getProgramRebates());
            Double purchasePrice = quoteView.getPurchase() - quoteView.getRebates() - totalConditionalExternalOffersInQuote;
            double tradeEquity = getTradeEquity(Optional.of(dealJacket));

            Double amountFinanced = purchasePrice + quoteView.getLicenseRegistration() + dealerFeeTotal
                - quoteView.getDownPayment() + saleTax - (quoteView.isLease() ? firstMonthPayment : 0);

            // if there is no tradeType then assume it is a Finance type
            String tradeType = Optional.of(dealJacket).map(DealJacket::getTradeVehicle).map(UserVehicleView::getTradeType).orElse(null);
            boolean isFinanceTrade = tradeType == null || tradeType.equals(TradeType.FINANCE.name());

            if (tradeEquity < 0) {
                amountFinanced += tradeEquity * -1;
            } else if (!isFinanceTrade) {//Do not subtract trade equity if Trade is finance and equity is positive, It looks like is already removed from the purchase price
                amountFinanced -= tradeEquity;
            }

            Double cashBack = getCashback(quoteView, tradeEquity);
            ExternalOffersModel externalOffersModel = getExternalOffersModel(quoteView, dealModelOpt, totalConditionalExternalOffersInQuote);

            VehicleView vehicleView = certificate.getVehicle();
            Integer msrpOrRetailPrice = 0;

            if (vehicleView != null) {
                StockType stockType = vehicleView.getStockType();
                if (stockType != null) {
                    msrpOrRetailPrice = (stockType.isNew())
                        ? getNewVehicleMsrpOrRetailPrice(vehicleView, certificate)
                        : (stockType.isUsed() ? getUsedVehicleMsrpOrRetailPrice(vehicleView) : msrpOrRetailPrice);
                }
            }

            dealModel.setTax(saleTax);
            dealModel.setTotalAmountFinance(amountFinanced);
            dealModel.setPurchasePrice(purchasePrice.intValue());
            dealModel.setExternalOffers(externalOffersModel);
            dealModel.setCashBack(cashBack);
            dealModel.setSalePrice(Optional.ofNullable(quoteView.getPurchase()).map(Double::intValue).orElse(null));
            dealModel.setDiscount(msrpOrRetailPrice - Optional.ofNullable(vehicleView).map(VehicleView::getPrice).orElse(0));

            buildLeasePaasData(dealJacket, dealerFeeViewList, quoteView, dealModel);

        }
    }

    private Integer getNewVehicleMsrpOrRetailPrice(VehicleView vehicleView, CertificateView certificate) {
        Integer msrpOrRetailPriceOfNewVehicle = vehicleView.getMsrp();
        if (msrpOrRetailPriceOfNewVehicle == null && certificate.getPrices() != null && certificate.getPrices().getPrices() != null) {
            msrpOrRetailPriceOfNewVehicle = certificate.getPrices().getPrices().getMsrp();
        }
        return msrpOrRetailPriceOfNewVehicle != null ? msrpOrRetailPriceOfNewVehicle : 0;
    }

    private Integer getUsedVehicleMsrpOrRetailPrice(VehicleView vehicleView) {
        return Optional.ofNullable(vehicleView.getInternetPrice())
                .orElseGet(() -> Optional.ofNullable(vehicleView.getRetailPrice())
                .orElse(0));
    }

    public static void buildLeasePaasData(DealJacket dealJacket, List<DealerFeeView> dealerFeeViewList, QuoteView quote, CustomerDealModel.DealModel dealModel) {
        if (quote.isLease() && quote.isPaas()) {
            dealModel.setMoneyFactor(Optional.ofNullable(quote.getMoneyFactor()).map(BigDecimal::new).orElse(null));
            dealModel.setResidualPct(quote.getResidual());
            dealModel.setInceptionDealerFees(DealerFeesService.getInceptionsFees(dealerFeeViewList, dealJacket.getVehicle().getStockType()));
            dealModel.setCapCostDealerFees(DealerFeesService.getNotInceptionsFees(dealerFeeViewList, dealJacket.getVehicle().getStockType()));

            AtomicReference<BigDecimal> inceptions = new AtomicReference<>(BigDecimal.ZERO);
            Optional.ofNullable(quote.getPaasData()).ifPresent(q -> {
                inceptions.set(q.getTotalInceptions());
                dealModel.setInceptionsTotal(q.getTotalInceptions());
                dealModel.setTaxOnInception(getInceptionTaxes(q));
                dealModel.setInceptionDealerFeeTotal(q.getDealerInceptionFees());
                dealModel.setCapCostDealerFeeTotal(q.getDealerCapCostFees());
                dealModel.setTaxOnCapCost(Optional.ofNullable(q.getTaxOnAdjCapCost()).orElse(BigDecimal.ZERO));
                dealModel.setGrossCapCost(getGrossCapCost(q));
            });

            Optional.ofNullable(quote.getFinancedAmount()).map(BigDecimal::doubleValue)
                .ifPresent(i -> dealModel.setTotalAmountFinance(i));
            dealModel.setDownPayment(dealJacket.getCashDown());

            double inceptionTotal = Optional.ofNullable(inceptions.get()).map(BigDecimal::doubleValue).orElse(0.0);
            if (Optional.ofNullable(quote.getDownPayment()).orElse(0.0) < inceptionTotal) {
                dealModel.setTotalDueAtSigning(inceptions.get().doubleValue());
            } else {
                dealModel.setTotalDueAtSigning(quote.getDownPayment());
            }

        }
    }

    private static BigDecimal getGrossCapCost(PaasData q) {
        return Optional.ofNullable(q.getGrossCapCost()).orElse(BigDecimal.ZERO)
            .add(Optional.ofNullable(q.getTaxOnAdjCapCost()).orElse(BigDecimal.ZERO));
    }

    private static BigDecimal getInceptionTaxes(PaasData q) {
        BigDecimal inceptionTaxes= Optional.ofNullable(q.getTaxOnInceptionFees()).orElse(BigDecimal.ZERO)
            .add(Optional.ofNullable(q.getTaxOnTotalCapCostReduction()).orElse(BigDecimal.ZERO))
            .add(Optional.ofNullable(q.getTotalSalesTax()).orElse(BigDecimal.ZERO));
        return inceptionTaxes;
    }

    static Double getCashback(QuoteView quoteView, double tradeEquity) {
        if(tradeEquity <= 0) {
            return 0.0;
        }

        final double downPayment = Objects.requireNonNullElse(quoteView.getDownPayment(),0.0);
        if(tradeEquity <= downPayment) {
            return 0.0;
        }

        final double cashback = BigDecimal.valueOf(tradeEquity).subtract(BigDecimal.valueOf(downPayment)).doubleValue();
        return cashback;
    }

    @NotNull
    private static Double getTradeEquity(Optional<DealJacket> dealJacketOpt) {
        double tradeEquity = 0.0;
        if (dealJacketOpt.isEmpty() || dealJacketOpt.map(DealJacket::getTradeVehicle).isEmpty()) {
            return tradeEquity;
        }

        UserVehicleView userVehicleView = dealJacketOpt.get().getTradeVehicle();
        if (Objects.equals(TradeType.LEASE_PAYOFF.name(), userVehicleView.getTradeType())) {
            final Double balance = Optional.ofNullable(userVehicleView.getBalance()).orElse(0.0);
            tradeEquity = (balance < 0) ? 0.0 : balance * -1;
        } else {
            tradeEquity = dealJacketOpt.map(DealJacket::getTradeEquity).orElse(0D);
        }

        return tradeEquity;
    }

    @Nullable
    private static ExternalOffersModel getExternalOffersModel(QuoteView quoteView, Optional<CustomerDealModel.DealModel> dealModelOpt, Double totalConditionalExternalOffersInQuote) {
        ExternalOffersModel externalOffersModel = dealModelOpt.map(CustomerDealModel.DealModel::getExternalOffers).orElse(null);

        // there are conditional rebate auto applied on the deal
        if (totalConditionalExternalOffersInQuote > 0) {
            com.carsaver.magellan.model.dealer.DealType quoteDealType = quoteView.isLease() ? com.carsaver.magellan.model.dealer.DealType.LEASE : com.carsaver.magellan.model.dealer.DealType.FINANCE;
            ExternalOffers applyOffers = PaasUtil.getConditionalExternalOffersQuotes(quoteView.getProgramRebates(), quoteDealType);
            externalOffersModel = new ExternalOffersModel(applyOffers);
        }
        return externalOffersModel;
    }

    private List<DealerFeeView> getPaymentTypeDealerFeeViews(CertificateView certificate, boolean isLease) {
        List<DealerFeeView> dealerFeeViewList = Collections.emptyList();

        if (certificate != null) {
            com.carsaver.magellan.model.dealer.DealType dealType = com.carsaver.magellan.model.dealer.DealType.FINANCE;
            if (isLease){
                dealType = com.carsaver.magellan.model.dealer.DealType.LEASE;
            }

           // get dealer fees from database
           dealerFeeViewList = dealerFeesService.getDealTypeDealerFeesByDealerId(certificate.getDealerId(), certificate.getStockType(), dealType);
        }

        return dealerFeeViewList;
    }

    private List<DealerFeeView> getDealTypeDealerFeeViews(CertificateView certificate, DealJacket dealJacket, DealType dealType) {
        // get the deal type fees
        List<DealerFeeView> dealerFees = Collections.emptyList();
        if (dealJacket != null && certificate != null) {

            if (dealJacket.isLease()) {
                // get dealer fees from database for lease only
                com.carsaver.magellan.model.dealer.DealType leaseDealType =  com.carsaver.magellan.model.dealer.DealType.LEASE;
                dealerFees = dealerFeesService.getDealTypeDealerFeesByDealerId(certificate.getDealerId(), certificate.getStockType(), leaseDealType);
            } else {
                // get finance deal dealer fees from dealSheet
                DealSheet dealSheet = Optional.ofNullable(dealJacket).map(DealJacket::getDealSheet).orElse(null);
                dealerFees = dealerFeesService.getFinanceDealerFeesByDealSheet(dealSheet);
            }

        }

        return dealerFees;
    }

    private static double getPurchasePriceOption(CertificateView certificate, QuoteView quoteView) {
        double purchasePriceOption = quoteView.getPurchaseOption();
//        boolean isLeafElectricVehicle = Optional.ofNullable(certificate.getVehicle()).map(VehicleView::getFullName).map(String::toLowerCase).map(i-> i.contains("leaf") || i.contains("ariya")).orElse(false);
//        if (isLeafElectricVehicle) {
//            purchasePriceOption = 0.0;
//        }
        return purchasePriceOption;
    }


}
