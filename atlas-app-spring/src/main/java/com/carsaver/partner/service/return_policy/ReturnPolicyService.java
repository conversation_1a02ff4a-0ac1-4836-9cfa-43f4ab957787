package com.carsaver.partner.service.return_policy;

import com.carsaver.magellan.api.DealerService;
import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.model.return_policy.ReturnPolicyRequest;
import com.carsaver.partner.model.return_policy.ReturnPolicyResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ReturnPolicyService {
    private static final String LOGGING_IDENTIFIER = "RETURN_POLICY";
    private static final String RETURN_POLICY = "/api/return-policy";

    private NissanWebClient nissanWebClient;
    private DealerService dealerService;

    @Value("${dealer-service.api-uri}")
//    @Value("${dealer-service.api-uri-x}")
    String dealerServiceUrl;

    public ReturnPolicyService(NissanWebClient nissanWebClient, DealerService dealerService) {
        this.nissanWebClient = nissanWebClient;
        this.dealerService = dealerService;
    }

    public ReturnPolicyResponse retrieveReturnPoliciesForDealer(String dealerId, String programId) {
        log.info("retrieveReturnPoliciesForDealer dealerId: {} programId: {}", dealerId, programId);
        String url = new StringBuilder(dealerServiceUrl).append(RETURN_POLICY).append("/").append(dealerId)
            .append("/").append(programId).toString();
        ReturnPolicyResponse response = nissanWebClient.get(url, ReturnPolicyResponse.class, LOGGING_IDENTIFIER);

        return response;
    }

    public ReturnPolicyResponse upsertDealerReturnPolicy(ReturnPolicyRequest request) {
        log.info("upsertDealerReturnPolicy request {}", request);
        String url = new StringBuilder(dealerServiceUrl).append(RETURN_POLICY).toString();
        ReturnPolicyResponse response = nissanWebClient.post(url, request, ReturnPolicyResponse.class, LOGGING_IDENTIFIER);

        return response;
    }

}
