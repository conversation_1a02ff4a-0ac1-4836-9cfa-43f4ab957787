package com.carsaver.partner.service;

import com.carsaver.partner.client.nissan.NissanWebClient;
import com.carsaver.partner.exception.DealerCTANotFoundException;
import com.carsaver.partner.model.dealercta.AtlasCTAReqRes;
import com.carsaver.partner.model.dealercta.DealerCtaReqRes;
import com.carsaver.partner.service.dealercta.DealerCtaConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class DealerCTAService {
    private final NissanWebClient nissanWebClient;

    @Value("${dealer-service.api-uri}")
    private String dealerServiceUrl;

    // TODO: this is hardcoded in YML. Get the real value eventually.
    @Value("${program.nissan-buy-at-home-id}")
    private String buyAtHomeProgramId;

    public DealerCTAService(NissanWebClient nissanWebClient) {
        this.nissanWebClient = nissanWebClient;
    }

    public AtlasCTAReqRes upsertDealerCTA(AtlasCTAReqRes atlasCTAReqRes) {

        // convert to dealer cta request object for api request
        DealerCtaReqRes dealerCTARequest = DealerCtaConverter.toDealerCtaRequest(atlasCTAReqRes, buyAtHomeProgramId);

        //make api call to service to store in db
        DealerCtaReqRes response = upsertWebClientCall(dealerCTARequest);

        //convert to dealer cta DTO object for front-end
        AtlasCTAReqRes result = DealerCtaConverter.toAtlasCTAReRes(response);

        return result;
    }


    public DealerCtaReqRes upsertWebClientCall(DealerCtaReqRes dealerCTARequest) {
        String url = dealerServiceUrl + "/cta";

        DealerCtaReqRes response = nissanWebClient.post(url, dealerCTARequest, DealerCtaReqRes.class, "upsertDealerCTA");

        if (Objects.isNull(response)) {
            if (Objects.isNull(dealerCTARequest.getId())) {
                throw new DealerCTANotFoundException("Dealer's CTA data is failed to persist");
            } else {
                throw new DealerCTANotFoundException("Dealer's CTA data is Not Found for id " + dealerCTARequest.getId());
            }
        }

        return response;
    }


    public AtlasCTAReqRes getDealerCTA(String dealerId) {
        DealerCtaReqRes result = null;

        try {
            String url = dealerServiceUrl + "/cta/" + dealerId + "/" + buyAtHomeProgramId;
            result = nissanWebClient.get(url, DealerCtaReqRes.class, "getDealerCTA");
            if (Objects.isNull(result)) {
                log.error("Dealer's CTA data is Not Found for given program id {} and dealer id {} ", buyAtHomeProgramId, dealerId);
                throw new DealerCTANotFoundException("Dealer's CTA data is Not Found for given program id " + buyAtHomeProgramId + " and dealer id " + dealerId);
            }
        }
        catch (Exception e) {
            log.error(e.getMessage());
            throw new DealerCTANotFoundException("Dealer's CTA data is Not Found for given program id " + buyAtHomeProgramId + " and dealer id " + dealerId);
        }

        //convert to dealer cta DTO object for front-end
        AtlasCTAReqRes response = DealerCtaConverter.toAtlasCTAReRes(result);

        return response;
    }


}
