package com.carsaver.partner.service;

import com.carsaver.elasticsearch.model.DealerCheckIn;
import com.carsaver.elasticsearch.model.UserAndProspectDoc;
import com.carsaver.elasticsearch.model.program.ProgramDoc;
import com.carsaver.magellan.UserNotFoundException;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.connection.DealerConnectionRequest;
import com.carsaver.partner.inshowroom.InShowRoomClient;
import com.carsaver.partner.web.api.user.elasticsearch.model.InShowRoomUsers;
import com.carsaver.search.support.SearchResults;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class InShowroomUsersService {
    private final InShowRoomClient inShowRoomClient;


    @Value("${in-showroom.minutes-threshold}")
    private int inShowroomMinutesThreshold;

    @Value("${in-showroom.lead-hour-threshold}")
    private int inShowroomLeadHourThreshold;

    private final DealerClient dealerClient;
    private final UserClient userClient;

    public List<InShowRoomUsers> findInShowroomUsers(SearchResults<UserAndProspectDoc> results, String dealerId) {

        DealerView dealerView = dealerClient.findById(dealerId);

        ZoneId dealerZoneId = ZoneId.of(dealerView.getTimeZone());
        ZonedDateTime dealerTimeZone = ZonedDateTime.now(dealerZoneId);

        return results.getContent().parallelStream()
            .map(user -> mapToInShowRoomUser(user, dealerTimeZone))
            .flatMap(Optional::stream)
            .filter(user -> user.getMinutes() <= inShowroomMinutesThreshold)
            .sorted(Comparator.comparingLong(InShowRoomUsers::getMinutes))
            .collect(Collectors.toList());
    }

    private Optional<InShowRoomUsers> mapToInShowRoomUser(UserAndProspectDoc user, ZonedDateTime dealerTimeZone) {
        DealerCheckIn checkIn = user.getMostRecentDealerCheckIn();
        if (checkIn == null) {
            return Optional.empty();
        }

        long minutes = Duration.between(checkIn.getCreatedDate(), dealerTimeZone).toMinutes();

        String programId = Optional.of(user)
            .map(UserAndProspectDoc::getProgram)
            .map(ProgramDoc::getId)
            .orElse(null);

        return Optional.of(new InShowRoomUsers(
            user.getId(),
            String.format("%s %s", user.getFirstName(), user.getLastName()),
            programId,
            minutes
        ));
    }


    public List<InShowRoomUsers> findInShowroomUsers(String dealerId) {

        List<DealerConnectionRequest> dealerConnectionRequests = inShowRoomClient.getDealerConnectionsByType(
            inShowroomLeadHourThreshold, dealerId, List.of("in-store-customer-lookup", "in-store-check-in")
        );


        List<InShowRoomUsers> result = dealerConnectionRequests.parallelStream()
            .map(i -> buildInShowRoomUsers(i, dealerId))
            .flatMap(Optional::stream)
            .filter(user -> user.getMinutes() <= inShowroomMinutesThreshold) // Apply threshold filter
            .sorted(Comparator.comparingLong(InShowRoomUsers::getMinutes)) // Sort by minutes
            .collect(Collectors.toList());
        return result;
    }

    private Optional<InShowRoomUsers> buildInShowRoomUsers(DealerConnectionRequest request, String dealerId) {
        if (request == null || request.getUserId() == null) {
            return Optional.empty();
        }

        UserView user = Optional.ofNullable(userClient.findById(request.getUserId())).orElseThrow(UserNotFoundException::new);

        DealerView dealerView = dealerClient.findById(dealerId);
        ZonedDateTime dealerTimeZone = ZonedDateTime.now(ZoneId.of(dealerView.getTimeZone()));

        long minutes = Duration.between(request.getCreatedDate(), dealerTimeZone).toMinutes();
        String programId = Optional.ofNullable(user.getCampaign()).map(CampaignView::getProgramId).orElse(null);

        return Optional.of(new InShowRoomUsers(user.getId(), user.getFullName(), programId, minutes));
    }


}
