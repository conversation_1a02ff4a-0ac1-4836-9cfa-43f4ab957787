package com.carsaver.partner.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import org.springframework.hateoas.CollectionModel;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.carsaver.core.StockType;
import com.carsaver.magellan.client.DealerFeeClient;
import com.carsaver.magellan.model.deal.DealSheet;
import com.carsaver.magellan.model.dealer.DealType;
import com.carsaver.magellan.model.dealer.DealerFeeView;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class DealerFeesService {

    private final DealerFeeClient dealerFeeClient;


    public List<DealerFeeView> getAllDealerFeesByDealerId(String dealerId) {
        List<DealerFeeView> dealerFees = Collections.emptyList();

        if (dealerId != null) {
            CollectionModel<DealerFeeView> fees = dealerFeeClient.findByDealerId(dealerId);
            if (fees != null) {
                // if fees exist turn into list
                dealerFees = new ArrayList<>(fees.getContent());
            }
        }

        return dealerFees;
    }

    public List<DealerFeeView> getDealTypeDealerFeesByDealerId(String dealerId, StockType stockType, @NotNull DealType dealType) {
        List<DealerFeeView> leaseDealerFees = Collections.emptyList();

        if (dealerId != null && stockType != null) {
            List<DealerFeeView> dealerFees =  getAllDealerFeesByDealerId(dealerId);
            if (!CollectionUtils.isEmpty(dealerFees)) {
                leaseDealerFees = this.getDealTypeDealerFeeViews(dealerFees, stockType, dealType);
            }
        }

        return leaseDealerFees;
    }

    public List<DealerFeeView> getFinanceDealerFeesByDealSheet(DealSheet dealSheet) {
        List<DealerFeeView> dealerFees = Optional.ofNullable(dealSheet)
            .map(DealSheet::getDealerFees)
            .orElse(Collections.emptyList())
            .stream()
            .map(this::convertFees)
            .collect(Collectors.toList());

        return dealerFees;
    }

    public List<DealerFeeView> getFinanceDealerFeesByDealerId(String dealerId, StockType stockType) {
        List<DealerFeeView> financeDealerFees = Collections.emptyList();

        List<DealerFeeView> dealerFees =  getAllDealerFeesByDealerId(dealerId);
        if(!CollectionUtils.isEmpty(dealerFees)) {
            financeDealerFees = this.getFinanceDealerFeeViews(dealerFees, stockType);
        }

        return financeDealerFees;
    }

    public List<DealerFeeView> getFinanceDealerFeeViews(List<DealerFeeView> dealerFees, StockType vehicleStockType) {
        List<DealerFeeView> notInceptionFees = Optional.ofNullable(dealerFees)
            .map(i -> i.stream()
                .filter(Objects::nonNull)
                .filter(dealerFeeView -> dealerFeeView.getDealType() == DealType.FINANCE || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                .filter(dealerFeeView -> dealerFeeView.getStockType() == null || (vehicleStockType != null && vehicleStockType.equals(dealerFeeView.getStockType())))
                .peek(dealerFeeView -> dealerFeeView.setDealType(DealType.FINANCE))
                //makes sure all the dealTypes are set to Lease for display purposes
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());
        return notInceptionFees;
    }

    public List<DealerFeeView> getCashDealerFeesByDealerId(String dealerId, StockType stockType) {
        List<DealerFeeView> cashDealerFees = Collections.emptyList();

        List<DealerFeeView> dealerFees =  getAllDealerFeesByDealerId(dealerId);
        if (!CollectionUtils.isEmpty(dealerFees)) {
            cashDealerFees = this.getCashDealerFeeViews(dealerFees, stockType);
        }

        return cashDealerFees;
    }

    List<DealerFeeView> getDealTypeDealerFeeViews(List<DealerFeeView> dealerFees, StockType vehicleStockType, @NotNull DealType dealType) {
        List<DealerFeeView> notInceptionFees = Optional.ofNullable(dealerFees).map(i -> i.stream()
                .filter(Objects::nonNull)
                .filter(dealerFeeView -> dealerFeeView.getDealType() == dealType || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                .filter(dealerFeeView -> dealerFeeView.getStockType() == null || (vehicleStockType != null && vehicleStockType.equals(dealerFeeView.getStockType())))
                .map(dealerFeeView -> { dealerFeeView.setDealType(dealType); return dealerFeeView;}) //makes sure all the dealTypes are set to Lease for display purposes
                .collect(Collectors.toList())
        ).orElse(Collections.emptyList());
        return notInceptionFees;
    }

    List<DealerFeeView> getCashDealerFeeViews(List<DealerFeeView> dealerFees, StockType vehicleStockType) {
        List<DealerFeeView> cashFees = Optional.ofNullable(dealerFees).map(i -> i.stream()
                .filter(Objects::nonNull)
                .filter(dealerFeeView -> dealerFeeView.getDealType() == DealType.CASH || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                .filter(dealerFeeView -> dealerFeeView.getStockType() == null || (vehicleStockType != null && vehicleStockType.equals(dealerFeeView.getStockType())))
                .map(dealerFeeView -> { dealerFeeView.setDealType(DealType.CASH); return dealerFeeView;}) //makes sure all the dealTypes are set to Lease for display purposes
                .collect(Collectors.toList())
        ).orElse(Collections.emptyList());
        return cashFees;
    }

    public List<DealerFeeView> getLeaseDealerFeesByDealerId(String dealerId, StockType stockType) {
        List<DealerFeeView> leaseDealerFees = Collections.emptyList();

        List<DealerFeeView> dealerFees =  getAllDealerFeesByDealerId(dealerId);
        if (!CollectionUtils.isEmpty(dealerFees)) {
            leaseDealerFees = this.getLeaseDealerFeeViews(dealerFees, stockType);
        }

        return leaseDealerFees;
    }

    public List<DealerFeeView> getLeaseDealerFeeViews(List<DealerFeeView> dealerFees, StockType vehicleStockType) {
        List<DealerFeeView> notInceptionFees = Optional.ofNullable(dealerFees)
            .map(i -> i.stream()
                .filter(Objects::nonNull)
                .filter(dealerFeeView -> dealerFeeView.getDealType() == DealType.LEASE || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                .filter(dealerFeeView -> dealerFeeView.getStockType() == null || (vehicleStockType != null && vehicleStockType.equals(dealerFeeView.getStockType())))
                .peek(dealerFeeView -> dealerFeeView.setDealType(DealType.LEASE))
                //makes sure all the dealTypes are set to Lease for display purposes
                .collect(Collectors.toList())
        ).orElse(Collections.emptyList());
        return notInceptionFees;
    }

    public BigDecimal getTotalInceptionsFees(List<DealerFeeView> dealerFees, StockType vehicleStockType) {
        double totalInceptionFees = 0.00;

        if (dealerFees != null && !dealerFees.isEmpty()) {
            // Filter all lease dealer fees to get only inception fees
            List<DealerFeeView> inceptionFees = dealerFees
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(dealerFeeView -> dealerFeeView.getDealType() == DealType.LEASE || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                    .filter(dealerFeeView -> dealerFeeView.getIsInception() == null || Boolean.TRUE.equals(dealerFeeView.getIsInception()))
                    .filter(dealerFeeView -> vehicleStockType.equals(dealerFeeView.getStockType()) || dealerFeeView.getStockType() == null)
                    .collect(Collectors.toList());

            // Get the sum of all lease inception fees
            totalInceptionFees = getTotalFees(inceptionFees);
        }

        BigDecimal totalInceptionFeeResults = BigDecimal.valueOf(totalInceptionFees);
        return totalInceptionFeeResults;
    }


    public List<DealerFeeView> getInceptionsFeesList(List<DealerFeeView> dealerFees, StockType vehicleStockType) {
       return getInceptionsFees(dealerFees, vehicleStockType);
    }

    public static List<DealerFeeView> getInceptionsFees(List<DealerFeeView> dealerFees, StockType vehicleStockType) {

        if (dealerFees != null && !dealerFees.isEmpty()) {
            // Filter all lease dealer fees to get only inception fees
            List<DealerFeeView> inceptionFees = dealerFees
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(dealerFeeView -> dealerFeeView.getDealType() == DealType.LEASE || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                    .filter(dealerFeeView -> dealerFeeView.getIsInception() == null ||  Boolean.TRUE.equals(dealerFeeView.getIsInception()))
                    .filter(dealerFeeView -> vehicleStockType.equals(dealerFeeView.getStockType()) || dealerFeeView.getStockType() == null)
                    .collect(Collectors.toList());

           return inceptionFees;
        }

        List<DealerFeeView> result = List.of();
        return result;
    }

    public BigDecimal getTotalNotInceptionsFees(List<DealerFeeView> dealerFees, StockType vehicleStockType) {
        double totalNotInceptionFees = 0.00;

        if (dealerFees != null && !dealerFees.isEmpty()) {
            // Filter all lease dealer fees to get non inception fees
            List<DealerFeeView> notInceptionFees = dealerFees
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(dealerFeeView -> dealerFeeView.getDealType() == DealType.LEASE || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                    .filter(dealerFeeView -> Objects.nonNull(dealerFeeView.getIsInception()))
                    .filter(dealerFeeView -> dealerFeeView.getIsInception() == false)
                    .filter(dealerFeeView -> vehicleStockType.equals(dealerFeeView.getStockType()) || dealerFeeView.getStockType() == null)
                    .collect(Collectors.toList());

            // Get the sum of all lease not inception fees
            totalNotInceptionFees = getTotalFees(notInceptionFees);
        }

        BigDecimal totalNotInceptionFeeResults = BigDecimal.valueOf(totalNotInceptionFees);
        return totalNotInceptionFeeResults;
    }

    public List<DealerFeeView> getNotInceptionsFeesList(List<DealerFeeView> dealerFees, StockType vehicleStockType) {
        return getNotInceptionsFees(dealerFees, vehicleStockType);
    }

    public static List<DealerFeeView> getNotInceptionsFees(List<DealerFeeView> dealerFees, StockType vehicleStockType) {

        if (dealerFees != null && !dealerFees.isEmpty()) {
            // Filter all lease dealer fees to get non inception fees
            List<DealerFeeView> notInceptionFees = dealerFees
                    .stream()
                    .filter(Objects::nonNull)
                    .filter(dealerFeeView -> dealerFeeView.getDealType() == DealType.LEASE || dealerFeeView.getDealType() == DealType.ALL_DEALS)
                    .filter(dealerFeeView -> Objects.nonNull(dealerFeeView.getIsInception()))
                    .filter(dealerFeeView -> dealerFeeView.getIsInception() == false)
                    .filter(dealerFeeView -> vehicleStockType.equals(dealerFeeView.getStockType()) || dealerFeeView.getStockType() == null)
                    .collect(Collectors.toList());

            return notInceptionFees;
        }

        List<DealerFeeView> result = List.of();
        return result;
    }

    public static double getTotalFees(Collection<DealerFeeView> fees) {
        // Get the sum of all fees
        double totalFees = Optional.ofNullable(fees)
            .orElseGet(Collections::emptyList)
            .stream()
            .filter(Objects::nonNull)
            .map(DealerFeeView::getAmount)
            .filter(Objects::nonNull)
            .collect(Collectors.summingDouble(Double::doubleValue));

        return totalFees;
    }

    private DealerFeeView convertFees(DealSheet.DealerFeeItem item) {
        DealerFeeView fee = new DealerFeeView();
        fee.setFeeType(item.getFeeType());
        fee.setAmount(item.getAmount());
        fee.setName(item.getName());
        return fee;
    }

}
