package com.carsaver.partner.service;

import com.carsaver.core.InventorySource;
import com.carsaver.core.StockType;
import com.carsaver.magellan.api.InventoryService;
import com.carsaver.magellan.api.PriceAdjustmentService;
import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.InventoryClient;
import com.carsaver.magellan.client.PricingClient;
import com.carsaver.magellan.model.CarPriceAdjustmentView;
import com.carsaver.magellan.model.CarWithPriceAdjustment;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.ModelPriceAdjustmentView;
import com.carsaver.magellan.model.ModelWithPriceAdjustment;
import com.carsaver.magellan.model.PriceAdjustmentView;
import com.carsaver.magellan.model.StockTypePriceAdjustmentView;
import com.carsaver.magellan.model.StylePriceAdjustmentView;
import com.carsaver.magellan.model.StyleWithPriceAdjustment;
import com.carsaver.magellan.model.VehicleView;
import com.carsaver.magellan.model.YearMakeModelsView;
import com.carsaver.magellan.model.metrics.InventoryMetrics;
import com.carsaver.magellan.search.PriceAdjustmentSearchCriteria;
import com.carsaver.partner.client.inventory.VehiclePricingClient;
import com.carsaver.partner.model.pricing.NewVehiclePricingForm;
import com.carsaver.partner.model.pricing.PricedCarRow;
import com.carsaver.partner.model.pricing.PricedModelRow;
import com.carsaver.partner.model.pricing.PricedStyleRow;
import com.carsaver.partner.model.pricing.UsedVehiclePricingForm;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.carsaver.magellan.model.PriceAdjustmentView.AdjustedValue;
import static java.util.Comparator.comparing;
import static org.springframework.util.StringUtils.hasLength;
import static org.springframework.util.StringUtils.isEmpty;

@Slf4j
@Service
@Getter
public class VehiclePricingService {
    private static final List<InventorySource> SUPPORTED_INVENTORY_SOURCES = Arrays.asList(InventorySource.HN);

    @Autowired
    private PriceAdjustmentService priceAdjustmentService;

    @Autowired
    private InventoryClient inventoryClient;

    @Autowired
    private InventoryService inventoryService;

    @Autowired
    private PricingClient pricingClient;

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private VehiclePricingClient vehiclePricingClient;

    public static boolean isPricingPortalSupportedBy(@NonNull DealerView dealer) {
        return SUPPORTED_INVENTORY_SOURCES.contains(dealer.getInventorySource());
    }

    public NewVehiclePricingForm prepareNewVehiclePricingForm(String dealerId, NewVehiclePricingForm newVehiclePricingForm) {
        DealerView dealer = getDealer(dealerId);

        PriceAdjustmentSearchCriteria searchForm = newVehiclePricingForm.getSearchForm();

        StockTypePriceAdjustmentView stockTypePriceAdjustment = priceAdjustmentService.findStockTypePriceAdjustmentBy(dealerId, StockType.NEW);
        newVehiclePricingForm.addStockTypePriceAdjustmentWithDefaults(stockTypePriceAdjustment, dealerId);

        Integer pricingFormStyleFilter = null;
        String pricingFormInventoryIdFilter = null;
        boolean vehicleSearchAttempted = false;
        boolean vehicleSearchFoundVehicle = false;
        String vehicleSearchFailureMessage = "";

        // if vehicle VIN was provided, provide vehicle specific filters to the table view
        if(StringUtils.hasText(searchForm.getVin())) {
            vehicleSearchAttempted = true;
            Optional<VehicleView> foundVehicle = inventoryClient.findNewVehicleByDealerIdAndVinAndInInventoryTrueAndValidTrue(dealerId, searchForm.getVin());
            pricingFormStyleFilter = foundVehicle.map(VehicleView::getStyleId).orElse(null);
            pricingFormInventoryIdFilter = foundVehicle.map(VehicleView::getId).orElse(null);
            vehicleSearchFoundVehicle = foundVehicle.isPresent();
            if(!vehicleSearchFoundVehicle) {
                vehicleSearchFailureMessage = "No vehicle found for vin " + searchForm.getVin() + " so showing all models.";
            }
        }

        // if vehicle stock number was provided, provide vehicle specific filters to the table view
        if(StringUtils.hasText(searchForm.getStockNumber())){
            vehicleSearchAttempted = true;
            Optional<VehicleView> foundVehicle = inventoryClient.findNewVehicleByDealerIdAndStockNumberAndInInventoryTrueAndValidTrue(dealerId, searchForm.getStockNumber());
            pricingFormStyleFilter = foundVehicle.map(VehicleView::getStyleId).orElse(null);
            pricingFormInventoryIdFilter = foundVehicle.map(VehicleView::getId).orElse(null);
            vehicleSearchFoundVehicle = foundVehicle.isPresent();
            if(!vehicleSearchFoundVehicle) {
                vehicleSearchFailureMessage = "No vehicle found for stock number " + searchForm.getStockNumber() + " so showing all models.";
            }
        }

        newVehiclePricingForm.setSearchForm(searchForm);
        newVehiclePricingForm.setEnforcePricingRules(dealer.getEffectivePreferences().getPricingRules().enforceFor(StockType.NEW));
        newVehiclePricingForm.setVehicleSearchAttempted(vehicleSearchAttempted);
        newVehiclePricingForm.setVehicleSearchFoundVehicle(vehicleSearchFoundVehicle);
        newVehiclePricingForm.setPricingFormStyleFilter(pricingFormStyleFilter);
        newVehiclePricingForm.setPricingFormInventoryIdFilter(pricingFormInventoryIdFilter);
        newVehiclePricingForm.setVehicleSearchFailureMessage(vehicleSearchFailureMessage);
        newVehiclePricingForm.setEnableLeasePricing(dealer.getEffectivePreferences().getEnableLeasePricing());
        newVehiclePricingForm.setPricedModelRows(preparePricedModelRows(dealer, searchForm));

        return newVehiclePricingForm;
    }

    private List<PricedModelRow> preparePricedModelRows(DealerView dealer, PriceAdjustmentSearchCriteria searchForm) {
        List<ModelWithPriceAdjustment> modelWithPriceAdjustments = searchForm.isCriteriaPresent()
            ? priceAdjustmentService.findNewModelsWithPriceAdjustmentsFor(dealer.getId(), searchForm)
            : priceAdjustmentService.findNewModelsWithPriceAdjustmentsFor(dealer.getId());

        Comparator<PricedModelRow> compareByYearDescThenName = comparing(
            (PricedModelRow mwpa) -> mwpa.getModel().getYear()).reversed()
            .thenComparing(mwpa -> mwpa.getModel().getName());

        return modelWithPriceAdjustments
            .stream()
            .filter(mwpa -> searchForm.getActive() == null ||  mwpa.getInventoryCount() > 0)
            .map(PricedModelRow::new)
            .sorted(compareByYearDescThenName)
            .collect(Collectors.toList());
    }

    public NewVehiclePricingForm updateNewPriceAdjustments(String dealerId, NewVehiclePricingForm newVehiclePricingForm) {
        this.saveCarPriceAdjustments(newVehiclePricingForm.getPricedCarMapAsList(), dealerId, StockType.NEW);
        this.saveStyleWithPriceAdjustments(newVehiclePricingForm.getPricedStyleMapAsList(), dealerId);
        this.saveModelWithPriceAdjustments(newVehiclePricingForm.getPricedModelMapAsList(), dealerId);
        this.saveStockTypePriceAdjustment(newVehiclePricingForm.getStockTypePriceAdjustment());
        this.recalculateAndSaveAllNewVehiclePricesFor(dealerId);

        return prepareNewVehiclePricingForm(dealerId, newVehiclePricingForm);
    }

    public UsedVehiclePricingForm updatedUsedPriceAdjustments(String dealerId, UsedVehiclePricingForm usedVehiclePricingForm, Pageable pageable) {
        if(usedVehiclePricingForm.getStockTypePriceAdjustment() != null && usedVehiclePricingForm.getStockTypePriceAdjustment().getAdjustment() != null) {
            usedVehiclePricingForm.getStockTypePriceAdjustment().getAdjustment().setAdjustedValue(AdjustedValue.INTERNET_PRICE);
        }

        List<CarWithPriceAdjustment> carWithPriceAdjustments = Collections.emptyList();

        if(usedVehiclePricingForm.getPricedCarRows() != null) {
            carWithPriceAdjustments = usedVehiclePricingForm.getPricedCarRows()
                .stream()
                .map(PricedCarRow::toCarWithPriceAdjustment)
                .collect(Collectors.toList());
        }

        carWithPriceAdjustments.forEach(carWithPriceAdjustment -> {
            if(carWithPriceAdjustment.getAdjustment() != null) {
                carWithPriceAdjustment.getAdjustment().setAdjustedValue(AdjustedValue.INTERNET_PRICE);
            }
        });

        this.saveStockTypePriceAdjustment(usedVehiclePricingForm.getStockTypePriceAdjustment());
        this.saveCarPriceAdjustments(carWithPriceAdjustments, dealerId, StockType.USED);
        this.recalculateAndSaveAllUsedVehiclePricesFor(dealerId);

        return prepareUsedVehiclePricingForm(dealerId, usedVehiclePricingForm, pageable);
    }

    public YearMakeModelsView getYmmForDealerAndStockType(String dealerId, StockType stockType) {
        YearMakeModelsView ymm = priceAdjustmentService.getDistinctYearMakeModelsBy(dealerId, stockType);

        //front end code is guarding against null values in various places, so removing them here
        ymm.setYears(ymm.getYears().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        ymm.setMakes(ymm.getMakes().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        ymm.setModels(ymm.getModels().stream().filter(Objects::nonNull).collect(Collectors.toList()));

        return ymm;
    }

    public List<PricedStyleRow> getStyleWithPriceAdjustmentsFor(String dealerId, Integer modelId, Integer pricingFormStyleFilter, Boolean active) {
        List<StyleWithPriceAdjustment> styleWithPriceAdjustments = priceAdjustmentService
            .findStylesWithPriceAdjustmentFor(dealerId, modelId, active);

        List<PricedStyleRow> pricedStyleRows = styleWithPriceAdjustments.stream()
            .filter(swpa -> pricingFormStyleFilter == null || swpa.getStyle().getId().equals(pricingFormStyleFilter))
            .filter(swpa -> active == null || swpa.getInventoryCount() > 0)
            .map(PricedStyleRow::new)
            .collect(Collectors.toList());

        return pricedStyleRows;
    }

    public List<PricedCarRow> getNewCarWithPriceAdjustmentsFor( String dealerId, Integer styleId, String pricingFormInventoryIdFilter, Boolean active) {
        List<PricedCarRow> pricedCarRows = priceAdjustmentService
            .findNewCarsWithPriceAdjustmentFor(dealerId, styleId, active)
            .stream()
            .filter(cwpa -> isEmpty(pricingFormInventoryIdFilter) || cwpa.getVehicle().getId().equals(pricingFormInventoryIdFilter))
            .map(cwpa -> new PricedCarRow(cwpa, StockType.NEW))
            .collect(Collectors.toList());

        return pricedCarRows;
    }

    public UsedVehiclePricingForm prepareUsedVehiclePricingForm(String dealerId, UsedVehiclePricingForm usedVehiclePricingForm, Pageable pageable) {
        DealerView dealer = getDealer(dealerId);
        StockTypePriceAdjustmentView stockTypePriceAdjustment = priceAdjustmentService.findStockTypePriceAdjustmentBy(dealer.getId(), StockType.USED);
        usedVehiclePricingForm.addStockTypePriceAdjustmentWithDefaults(stockTypePriceAdjustment, dealer.getId());
        usedVehiclePricingForm.setEnforcePricingRules(dealer.getEffectivePreferences().getPricingRules().enforceFor(StockType.USED));
        usedVehiclePricingForm.setPricedCarRowsPage(prepareUsedPricedCarRows(dealer, usedVehiclePricingForm, pageable));

        if(usedVehiclePricingForm.getSearchForm().isCriteriaPresent()) {
            usedVehiclePricingForm.setVehicleSearchAttempted(true);
        } else {
            usedVehiclePricingForm.setVehicleSearchAttempted(false);
        }

        //handle no vehicle found
        if(usedVehiclePricingForm.getPricedCarRowsPage().isEmpty()) {
            usedVehiclePricingForm.setVehicleSearchFoundVehicle(false);
            String vehicleSearchFailureMessage;

            if(usedVehiclePricingForm.getSearchForm().isVinSearch()) {
                vehicleSearchFailureMessage = "No vehicle found for vin " + usedVehiclePricingForm.getSearchForm().getVin();
                usedVehiclePricingForm.setVehicleSearchFailureMessage(vehicleSearchFailureMessage);
            }
            if(usedVehiclePricingForm.getSearchForm().isStockNumberSearch()) {
                vehicleSearchFailureMessage = "No vehicle found for stock number " + usedVehiclePricingForm.getSearchForm().getStockNumber();
                usedVehiclePricingForm.setVehicleSearchFailureMessage(vehicleSearchFailureMessage);
            }


        } else if(!usedVehiclePricingForm.getPricedCarRowsPage().isEmpty() && usedVehiclePricingForm.isVehicleSearchAttempted()) {
            usedVehiclePricingForm.setVehicleSearchFoundVehicle(true);
        }

        return usedVehiclePricingForm;
    }

    public InventoryMetrics getInventoryMetrics(DealerView dealer) {
        return inventoryService.getInventoryMetrics((dealer.getId()));
    }

    public void deletePricingRules(String dealerId, StockType stockType) {
        pricingClient.deletePricingRules(dealerId, stockType);
    }

    private StockTypePriceAdjustmentView saveStockTypePriceAdjustment(StockTypePriceAdjustmentView stockTypePriceAdjustment) {
        PriceAdjustmentView stockTypeAdjustment = stockTypePriceAdjustment.getAdjustment();
        boolean shouldBeSaved = hasLength(stockTypeAdjustment.getId()) || stockTypeAdjustment.isEnabled();

        return shouldBeSaved ? priceAdjustmentService.saveStockTypePriceAdjustment(stockTypePriceAdjustment) : null;
    }

    private List<CarPriceAdjustmentView> saveCarPriceAdjustments(List<CarWithPriceAdjustment> carWithPriceAdjustments, String dealerId, StockType stockType) {
        List<CarPriceAdjustmentView> carPriceAdjustments = carWithPriceAdjustments.stream()
            .filter(cwpa -> hasLength(cwpa.getAdjustment().getId()) || cwpa.getAdjustment().isEnabled()) //only save if previously saved OR enabled
            .map(cwpa -> new CarPriceAdjustmentView(dealerId, stockType, cwpa))
            .collect(Collectors.toList());
        log.info("number of vehicles submitted = {}", carPriceAdjustments.size());

        return priceAdjustmentService.saveCarPriceAdjustmentsForDealer(carPriceAdjustments, dealerId);
    }

    private List<ModelPriceAdjustmentView> saveModelWithPriceAdjustments(List<ModelWithPriceAdjustment> modelWithPriceAdjustments, String dealerId) {
        List<ModelPriceAdjustmentView> modelPriceAdjustments = modelWithPriceAdjustments.stream()
            .filter(mwpa -> hasLength(mwpa.getPriceAdjustment().getId()) || mwpa.getPriceAdjustment().isEnabled()) //only save if previously saved OR enabled
            .map(mwpa -> new ModelPriceAdjustmentView(dealerId, mwpa))
            .collect(Collectors.toList());
        log.info("number of model priceAdjustments submitted = {}", modelPriceAdjustments.size());

        return getPriceAdjustmentService().saveModelPriceAdjustmentsForDealer(modelPriceAdjustments, dealerId);
    }

    private List<StylePriceAdjustmentView> saveStyleWithPriceAdjustments(List<StyleWithPriceAdjustment> styleWithPriceAdjustments, String dealerId) {
        List<StylePriceAdjustmentView> stylePriceAdjustments = styleWithPriceAdjustments.stream()
            .filter(swpa -> hasLength(swpa.getPriceAdjustment().getId()) || swpa.getPriceAdjustment().isEnabled()) //only save if previously saved OR enabled
            .map(swpa -> new StylePriceAdjustmentView(dealerId, swpa))
            .collect(Collectors.toList());
        log.info("number of style priceAdjustments submitted = {}", stylePriceAdjustments.size());

        return priceAdjustmentService.saveStylePriceAdjustmentsForDealer(stylePriceAdjustments, dealerId);
    }

    private void recalculateAndSaveAllNewVehiclePricesFor(String dealerId){
        priceAdjustmentService.recalculateAndSaveAllNewVehiclePricesFor(dealerId);
    }

    private void recalculateAndSaveAllUsedVehiclePricesFor(String dealerId){
        priceAdjustmentService.recalculateAndSaveAllUsedVehiclePricesFor(dealerId);
    }

    private Page<PricedCarRow> prepareUsedPricedCarRows(DealerView dealer, UsedVehiclePricingForm usedVehiclePricingForm, Pageable pageable) {
        PriceAdjustmentSearchCriteria searchForm = usedVehiclePricingForm.getSearchForm();
        Page<CarWithPriceAdjustment> carWithPriceAdjustments = Page.empty();

        if(searchForm.isStockNumberSearch()) {
            carWithPriceAdjustments = new PageImpl<CarWithPriceAdjustment>(priceAdjustmentService.findCarWithPriceAdjustmentsByStockNumber(dealer.getId(), StockType.USED, searchForm.getStockNumber()));
        } else if(searchForm.isVinSearch()) {
            carWithPriceAdjustments = new PageImpl<CarWithPriceAdjustment>(priceAdjustmentService.findCarWithPriceAdjustmentsByVin(dealer.getId(), StockType.USED, searchForm.getVin()));
        } else {
            carWithPriceAdjustments = priceAdjustmentService.findCarWithPriceAdjustmentsPageImpl(dealer.getId(), StockType.USED, searchForm, pageable);
        }

        Comparator<PricedCarRow> compareByYearDescThenVin = comparing(
            (PricedCarRow cwpa) -> cwpa.getVehicle().getYear()).reversed()
            .thenComparing(PricedCarRow::getVin);

        List<PricedCarRow> pricedCarRows = carWithPriceAdjustments.stream()
            .map(cwpa -> new PricedCarRow(cwpa, StockType.USED))
            .sorted(compareByYearDescThenVin)
            .collect(Collectors.toList());
        Page<PricedCarRow> pricedCarRowPage = new PageImpl<PricedCarRow>(pricedCarRows, pageable, carWithPriceAdjustments.getTotalElements());
        return pricedCarRowPage;

    }

    public DealerView getDealer(String dealerId){
        DealerView dealer = dealerClient.findById(dealerId);
        if(dealer ==null){
            throw new NotFoundException(String.format("Dealer not found by Id:[%s]", dealerId));
        }
        return dealer;
    }

    public Long getVehiclePricingErrorCount(DealerView dealer, String stockType) {
        String type = "ERROR";
        String jobStep = "APPLY_PRICE_RULES";

        log.info("Looking up import car logs count v2 by dealer = {}", dealer.getId());
        return  vehiclePricingClient.getVehicleCountForInvalidPrice(dealer.getId(), stockType, jobStep, type);
    }
}
