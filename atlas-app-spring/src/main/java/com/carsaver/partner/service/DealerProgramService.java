package com.carsaver.partner.service;

import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.annotation.RequestScope;

import javax.servlet.http.HttpSession;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Primarily just a wrapper to provide some caching mechanics around programs for a particular dealer and store in session
 * for the user. This would allow for a logout / login to refresh the associated programs
 */
@RequestScope
public class DealerProgramService {

    private static final String DEALER_LIVE_PROGRAM_SUBSCRIPTION_PREFIX = "_dealer_live_programs_";

    @Autowired
    private ProgramSubscriptionClient programSubscriptionClient;

    @Autowired
    private HttpSession session;

    /**
     * Will return a list of program.ids for the program subscriptions that a dealer is live on.
     * @param dealerId The dealer we want to filter program subscriptions on
     * @return a collection of the program ids that match for this dealerId and are LIVE
     */
    public List<String> findDealerProgramsLive(String dealerId) {
        if(StringUtils.isEmpty(dealerId)) {
            return Collections.emptyList();
        }

        final String sessionKey = DEALER_LIVE_PROGRAM_SUBSCRIPTION_PREFIX + dealerId;

        List<String> programs = (List<String>) session.getAttribute(sessionKey);
        if(programs == null) {
            programs = programSubscriptionClient.findByDealer(dealerId).getContent().stream()
                .filter(ProgramSubscriptionView::isLive)
                .map(ProgramSubscriptionView::getProgramId)
                .collect(Collectors.toList());

            session.setAttribute(sessionKey, programs);
        }

        return programs;
    }

}
