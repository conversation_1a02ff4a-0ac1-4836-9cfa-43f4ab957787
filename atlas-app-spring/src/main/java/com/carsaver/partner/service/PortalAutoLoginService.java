package com.carsaver.partner.service;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.auth.CarSaverUserDetails;
import com.carsaver.magellan.client.DealerPermissionsClient;
import com.carsaver.magellan.model.security.DealerPermissionView;
import com.carsaver.magellan.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * centralized place for various codes related to the portal autologin process
 */
@Slf4j
@Service
public class PortalAutoLoginService {
    private static final String AUTO_LOGIN_ATTR = "isAutoLoginPortalRequest";

    @Autowired
    private DealerPermissionsClient dealerPermissionsClient;

    /**
     * can inspect request for referer, url params etc,. to determine if request is coming from portal
     * if so determined will store an indicator in the session
     *
     * @param request
     * @return - whether the request is scoped to a portal user
     */
    public static boolean isPortalAutoLoginRequest(HttpServletRequest request) {
        //see if we previously captured in session
        boolean isPortalAutoLoginRequest = Optional.ofNullable(request.getSession(true))
            .map(session -> (Boolean) session.getAttribute(AUTO_LOGIN_ATTR))
            .orElse(false);

        if (isPortalAutoLoginRequest) {
            return true;
        }

        //we only want to check the referer below if the user is not logged in already
        if (AuthUtils.getUserIdFromSecurityContext().isPresent()) {
            return false;
        }

        isPortalAutoLoginRequest = hasPortalReferer(request);
        request.getSession(false).setAttribute(AUTO_LOGIN_ATTR, isPortalAutoLoginRequest);

        return isPortalAutoLoginRequest;
    }

    /**
     * assembles all of the dealerPermissions available to a dealer user
     * then enhances permissions as is currently done internally in {@link CarSaverUserDetails}
     * maps these permissions to authorities
     */
    public List<SimpleGrantedAuthority> getImpersonateDealerUserAuthorities() {
        List<SimpleGrantedAuthority> dealerAuthorities = new ArrayList<>();
        for (DealerPermissionView dpv : dealerPermissionsClient.getPermissionsIncludeNew().getContent()) {
            dealerAuthorities.add(new SimpleGrantedAuthority(dpv.getName()));
        }
        // always add this for admin users for now.  this will allow the Create Customer button to show up
        // for Express Customer "Save a deal" flows.  also, only show this button
        dealerAuthorities.add(new SimpleGrantedAuthority("user:save-a-deal"));

        return dealerAuthorities;
    }

    private static boolean hasPortalReferer(HttpServletRequest request) {
        String referer = request.getHeader("referer");
        log.debug("referrer={}", referer);

        if (!StringUtils.hasText(referer)) return false;

        var uri = UriComponentsBuilder.fromUriString(referer).build();

        var formattedReferer = uri.getScheme() +
            "://" +
            uri.getHost() +
            (uri.getPort() != -1
                ? ":" + uri.getPort()
                : "");

        return Arrays.asList(
            "http://localhost:3000",
            "http://localhost-portal:3000",
            "https://portal-beta.carsaver.com",
            "https://portal.beta.carsaver.com",
            "https://portal.carsaver.com"
        ).contains(formattedReferer);
    }
}
