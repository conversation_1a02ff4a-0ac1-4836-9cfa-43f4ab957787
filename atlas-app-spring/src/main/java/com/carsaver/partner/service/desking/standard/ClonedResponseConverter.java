package com.carsaver.partner.service.desking.standard;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.carsaver.magellan.model.PaasPayments;
import com.carsaver.magellan.model.ProgramRebates;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.carsaver.magellan.api.exception.NotFoundException;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.QuoteView;
import com.carsaver.partner.model.desking.CloneDealRequest;
import com.carsaver.partner.model.desking.ClonedDealResponse;
import com.carsaver.partner.model.desking.ClonedDealResponse.*;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse;
import com.carsaver.partner.model.desking.standard.LeaseQuoteResponse.Quote;
import com.carsaver.partner.model.desking.standard.LeaseStepBreakDownDTO;

@Service
public class ClonedResponseConverter {

    public static final String INCEPTION_FEE_TAX = "Tax on Inception Fees";
    public static final String CAPT_COST_FEE_TAX = "Tax on Capital Cost";

    public ClonedDealResponse dealJacketToCloneResponse(DealJacket dealJacket, CloneDealRequest request) {

        ClonedDealResponse result = this.getClonedDealResponse(request);

        QuoteView quoteView = Optional.ofNullable(dealJacket).map(DealJacket::getQuote)
            .orElseThrow(() -> new NotFoundException("No quote found"));

        Optional<PaasPayments> paasPaymentsOpt = Optional.of(dealJacket).map(DealJacket::getPaasPayments);

        if (paasPaymentsOpt.isPresent()) {
            updateMonthly(result, paasPaymentsOpt, quoteView.isLease());
            updateTaxesAndFees(result, paasPaymentsOpt);
            updateInceptions(result, paasPaymentsOpt);
            updateCapCostReduction(result, paasPaymentsOpt);
            updateRebate(result, Optional.of(quoteView));

        } else {
            result.getMonthlyPayment().setFirstMonthlyPayment(quoteView.getMonthlyPayment());
            result.getMonthlyPayment().setMonthlyTax(quoteView.getMonthlyTaxes());
            result.setMonthlyPaymentTotal(quoteView.getMonthlyPayment());

            result.getTaxesAndFees().setTitleAndRegistration(quoteView.getLicenseRegistration());
            result.getTaxesAndFees().setSalesTax(quoteView.getSalesTax());
            result.setTaxesAndFeesTotal(quoteView.getTotalTaxFees());
        }

        // update Financier values
        result.getFinanceDetails().setApr(quoteView.getInterestRate());
        result.getFinanceDetails().setBaseMoneyFactor(quoteView.getMoneyFactor());
        result.getFinanceDetails().setResidualAmount(Optional.ofNullable(quoteView.getPurchaseOption()).orElse(0D));
        result.getFinanceDetails().setAcquisitionFee(Optional.ofNullable(quoteView.getAcquisitionFee()).orElse(0D));
        result.getGrossCapitalCost().setAcquisitionFee(Optional.ofNullable(quoteView.getAcquisitionFee()).map(String::valueOf).orElse("0.0"));

        // update down payment - in case it got altered automatically by retries or PTI check
        double downPayment = Optional.of(dealJacket).map(DealJacket::getCashDown).orElse(0D);
        result.getDueAtSigning().setConsumerCash(String.valueOf(downPayment));

       return result;
    }

    private static void updateCapCostReduction(ClonedDealResponse result, Optional<PaasPayments> paasPaymentsOpt) {
        Double totalConsumerCash = Optional.ofNullable(result.getDueAtSigning()).map(DueAtSigning::getPositiveTradeEquity).orElse(0D)
                                 + Optional.ofNullable(result.getDueAtSigning()).map(DueAtSigning::getConsumerCash).map(Double::valueOf).orElse(0D);
        Double totalInception = paasPaymentsOpt.map(PaasPayments::getTotalInceptions).map(BigDecimal::doubleValue).orElse(0D);
        Double totalRebates = paasPaymentsOpt.map(PaasPayments::getRebates).map(BigDecimal::doubleValue).orElse(0D);

        double consumerCashAsCapitalReductionValue = (totalInception.compareTo(totalConsumerCash) < 0) ? totalConsumerCash - totalInception : 0D;
        Double totalCapCost = consumerCashAsCapitalReductionValue + totalRebates;

        List<LineItem> lineItemList = new ArrayList<>();
        lineItemList.add(LineItem.builder().name("Consumer Cash (including trade)").amount(totalConsumerCash).build());
        lineItemList.add(LineItem.builder().name("Total Inception Fees").amount(totalInception).build());
        lineItemList.add(LineItem.builder().name("Consumer Cash as Capital Reduction").amount(consumerCashAsCapitalReductionValue).build());
        lineItemList.add(LineItem.builder().name("Rebates").amount(totalRebates).build());

        result.getCapCostReduction().setLineItems(lineItemList);
        result.setCapCostReductionTotal(totalCapCost);
    }

    private static void updateMonthly(ClonedDealResponse result, Optional<PaasPayments> paasPaymentsOpt, boolean isLease) {
        if (isLease) {
            result.getMonthlyPayment().setFirstMonthlyPayment(paasPaymentsOpt.map(PaasPayments::getPreTaxMonthlyPayment).map(BigDecimal::doubleValue).orElse(0D));
        } else {
            result.getMonthlyPayment().setFirstMonthlyPayment(paasPaymentsOpt.map(PaasPayments::getTotalMonthlyPayment).map(BigDecimal::doubleValue).orElse(0D));
        }

        result.getMonthlyPayment().setMonthlyTax(paasPaymentsOpt.map(PaasPayments::getMonthlyTax).map(BigDecimal::doubleValue).orElse(0D));
        result.setMonthlyPaymentTotal(paasPaymentsOpt.map(PaasPayments::getTotalMonthlyPayment).map(BigDecimal::doubleValue).orElse(0D));
    }

    private static void updateTaxesAndFees(ClonedDealResponse result, Optional<PaasPayments> paasPaymentsOpt) {
        result.getTaxesAndFees().setTitleAndRegistration(paasPaymentsOpt.map(PaasPayments::getLicenseAndRegistration).map(BigDecimal::doubleValue).orElse(0D));
        result.getTaxesAndFees().setSalesTax(paasPaymentsOpt.map(PaasPayments::getTotalSalesTax).map(BigDecimal::doubleValue).orElse(0D));
        result.setTaxesAndFeesTotal(result.getTaxesAndFees().getSalesTax() + result.getTaxesAndFees().getTitleAndRegistration());
    }

    private static void updateRebate(ClonedDealResponse result, Optional<QuoteView> quoteViewOpt) {
        result.getRebates().setStaticLineItems(convertProgramRebatesToLineItem(quoteViewOpt.map(QuoteView::getProgramRebates).orElse(null)));
        double customerRebates = determineLineItemTotal(result.getRebates().getLineItems());
        double staticRebates =  determineLineItemTotal(result.getRebates().getStaticLineItems());
        result.setRebatesTotal(customerRebates + staticRebates);
    }

    private static void updateInceptions(ClonedDealResponse result, Optional<PaasPayments> paasPaymentsOpt) {

        List<ClonedDealResponse.LineItem> staticLineItems = new ArrayList<>();
        double inceptionFeeTax = paasPaymentsOpt.map(PaasPayments::getTaxOnInceptionFees).map(BigDecimal::doubleValue).orElse(0D);
        double capFeeTax = paasPaymentsOpt.map(PaasPayments::getTaxOnTotalCapCostReduction).map(BigDecimal::doubleValue).orElse(0D);

        staticLineItems.add(ClonedDealResponse.LineItem.builder().name(INCEPTION_FEE_TAX).amount(inceptionFeeTax).build());
        staticLineItems.add(ClonedDealResponse.LineItem.builder().name(CAPT_COST_FEE_TAX).amount(capFeeTax).build());
        result.getInceptions().setStaticLineItems(staticLineItems);

        result.getInceptions().setTitleAndRegistration(paasPaymentsOpt.map(PaasPayments::getLicenseAndRegistration).map(BigDecimal::doubleValue).orElse(0D));
    }

    public ClonedDealResponse leaseQuoteResponseQuoteToCloneResponse(LeaseQuoteResponse.Quote quote, CloneDealRequest request) {
        ClonedDealResponse result = this.getClonedDealResponse(request);

        QuoteView quoteView = Optional.ofNullable(quote).map(Quote::getQuoteView)
            .orElseThrow(() -> new NotFoundException("No quote found"));

        LeaseStepBreakDownDTO stepBreakDownDTO = Optional.of(quote).map(Quote::getStepCalculatedValues)
            .orElseThrow(() -> new RuntimeException("LeaseQuoteResponse.Quote must have break down values of calculated fields"));

        // update monthly payments
        Optional<LeaseStepBreakDownDTO> stepOpt = Optional.ofNullable(stepBreakDownDTO);
        result.getMonthlyPayment().setFirstMonthlyPayment(stepOpt.map(LeaseStepBreakDownDTO::getFirstMonthPayment).map(BigDecimal::doubleValue).orElse(0.0));
        result.getMonthlyPayment().setMonthlyTax(stepOpt.map(LeaseStepBreakDownDTO::getTaxesOnFirstMonthPayment).map(BigDecimal::doubleValue).orElse(0.0));
        result.setMonthlyPaymentTotal(stepOpt.map(LeaseStepBreakDownDTO::getFirstMonthPaymentWithTax).map(BigDecimal::doubleValue).orElse(0.0));

        // update Finance
        result.getFinanceDetails().setAcquisitionFee(quoteView.getAcquisitionFee());
        result.getFinanceDetails().setResidualAmount(Optional.ofNullable(quoteView.getResidual()).map(BigDecimal::doubleValue).orElse(0.0));

        // update inception
        result.getInceptions().setTitleAndRegistration(quoteView.getLicenseRegistration());

        // update capCost
        result.getCapCostReduction().setCapCostReduction(stepOpt.map(LeaseStepBreakDownDTO::getTotalCapCostReduction).map(BigDecimal::doubleValue).orElse(0.0));
        result.setCapCostReductionTotal(stepOpt.map(LeaseStepBreakDownDTO::getTotalCapCostReduction).map(BigDecimal::doubleValue).orElse(null));

        // update down payment - in case it got altered automatically by retries or PTI check
        stepOpt.map(LeaseStepBreakDownDTO::getPtiDownPayment)
            .ifPresent(ptiDownPayment -> result.getDueAtSigning().setConsumerCash(String.valueOf(ptiDownPayment)));

        return result;
    }

    private ClonedDealResponse getClonedDealResponse(CloneDealRequest request) {
        ClonedDealResponse result = ClonedDealResponse.builder()
            .financeDetails(ClonedDealResponse.FinanceDetails.builder().build())
            .monthlyPayment(ClonedDealResponse.MonthlyPayment.builder().build())
            .rebates(ClonedDealResponse.Rebates.builder().lineItems(Collections.emptyList()).staticLineItems(Collections.emptyList()).build())
            .vehicleDetail(ClonedDealResponse.VehicleDetail.builder().build())
            .tradeAllowance(ClonedDealResponse.TradeAllowance.builder().build())
            .dueAtSigning(ClonedDealResponse.DueAtSigning.builder().build())
            .taxesAndFees(ClonedDealResponse.TaxesAndFees.builder().build())
            .addons(ClonedDealResponse.AddOns.builder().build())
            .inceptions(ClonedDealResponse.Inceptions.builder().build())
            .grossCapitalCost(ClonedDealResponse.GrossCapitalCost.builder().build())
            .capCostReduction(ClonedDealResponse.CapCostReduction.builder().build())
            .build();

        BeanUtils.copyProperties(request, result);
        BeanUtils.copyProperties(request.getFinanceDetails(), result.getFinanceDetails());
        BeanUtils.copyProperties(request.getMonthlyPayment(), result.getMonthlyPayment());
        BeanUtils.copyProperties(request.getVehicleDetail(), result.getVehicleDetail());
        BeanUtils.copyProperties(request.getTradeAllowance(), result.getTradeAllowance());

        copyRebates(request, result);
        copyDueAtSigning(request, result);
        copyTaxesAndFees(request, result);
        copyAddOns(request, result);
        copyInceptions(request, result);
        copyGrossCapitalCost(request, result);
        copyCapCostReduction(request, result);

        return result;
    }

    private void copyRebates(CloneDealRequest request, ClonedDealResponse result) {
        List<ClonedDealResponse.LineItem> lineItemList = Optional.ofNullable(request.getRebates())
            .map(CloneDealRequest.Rebates::getLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getRebates().setLineItems(lineItemList);
    }

    private void copyDueAtSigning(CloneDealRequest request, ClonedDealResponse result) {
        BeanUtils.copyProperties(request.getDueAtSigning(), result.getDueAtSigning());

        List<ClonedDealResponse.LineItem> lineItemList = Optional.ofNullable(request.getDueAtSigning())
            .map(CloneDealRequest.DueAtSigning::getLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getDueAtSigning().setLineItems(lineItemList);
    }

    private void copyTaxesAndFees(CloneDealRequest request, ClonedDealResponse result) {
        BeanUtils.copyProperties(request.getTaxesAndFees(), result.getTaxesAndFees());

        List<ClonedDealResponse.LineItem> lineItemList = Optional.ofNullable(request.getTaxesAndFees())
            .map(CloneDealRequest.TaxesAndFees::getLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getTaxesAndFees().setLineItems(lineItemList);
    }

    private void copyAddOns(CloneDealRequest request, ClonedDealResponse result) {
        List<ClonedDealResponse.LineItem> lineItemList = Optional.ofNullable(request.getAddons())
            .map(CloneDealRequest.AddOns::getLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getAddons().setLineItems(lineItemList);

        List<ClonedDealResponse.LineItem> accessories = Optional.ofNullable(request.getAddons())
            .map(CloneDealRequest.AddOns::getAccessories)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getAddons().setAccessories(accessories);

        List<ClonedDealResponse.LineItem> protectionProduct = Optional.ofNullable(request.getAddons())
            .map(CloneDealRequest.AddOns::getProtectionProduct)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getAddons().setProtectionProduct(protectionProduct);
    }

    private void copyInceptions(CloneDealRequest request, ClonedDealResponse result) {
        BeanUtils.copyProperties(request.getInceptions(), result.getInceptions());

        List<ClonedDealResponse.LineItem> lineItemList = Optional.ofNullable(request.getInceptions())
            .map(CloneDealRequest.Inceptions::getLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        List<ClonedDealResponse.LineItem> staticItemList = Optional.ofNullable(request.getInceptions())
            .map(CloneDealRequest.Inceptions::getStaticLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getInceptions().setLineItems(lineItemList);
        result.getInceptions().setStaticLineItems(staticItemList);
    }

    private void copyGrossCapitalCost(CloneDealRequest request, ClonedDealResponse result) {
        BeanUtils.copyProperties(request.getGrossCapitalCost(), result.getGrossCapitalCost());

        List<ClonedDealResponse.LineItem> lineItemList = Optional.ofNullable(request.getGrossCapitalCost())
            .map(CloneDealRequest.GrossCapitalCost::getLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getGrossCapitalCost().setLineItems(lineItemList);
    }

    private void copyCapCostReduction(CloneDealRequest request, ClonedDealResponse result) {
        BeanUtils.copyProperties(request.getCapCostReduction(), result.getCapCostReduction());

        List<ClonedDealResponse.LineItem> lineItemList = Optional.ofNullable(request.getCapCostReduction())
            .map(CloneDealRequest.CapCostReduction::getLineItems)
            .map(i -> i.stream()
                .map(r -> new ClonedDealResponse.LineItem(r.getName(), r.getAmount(), r.getTaxable()))
                .collect(Collectors.toList())
            ).orElse(Collections.emptyList());

        result.getCapCostReduction().setLineItems(lineItemList);
    }

    private static List<LineItem> convertProgramRebatesToLineItem(List<ProgramRebates> programRebatesList) {
        List<LineItem> result = Optional.ofNullable(programRebatesList)
            .map(i -> i.stream()
                .filter(d -> d.getValue() != null)
                .map(j -> LineItem.builder().amount(j.getValue().doubleValue()).name(j.getDescription()).build())
                .collect(Collectors.toList()))
            .orElse(List.of());
        return result;
    }
    private static Double determineLineItemTotal(List<LineItem> items) {
        Double result = Optional.ofNullable(items)
            .map(i -> i.stream()
                .filter(d -> d.getAmount() != null)
                .mapToDouble(LineItem::getAmount)
                .sum())
            .orElse(0D);

        return result;
    }

}
