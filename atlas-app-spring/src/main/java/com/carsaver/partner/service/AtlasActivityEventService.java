package com.carsaver.partner.service;

import com.carsaver.magellan.api.ActivityService;
import com.carsaver.magellan.model.ActivityEventView;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.UserView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AtlasActivityEventService {

    @Autowired
    private ActivityService activityService;

    public void fastPassActivityEvent(UserView user, DealerView dealer) {
        String userId = user.getId();
        String dealerId = dealer.getId();
        ActivityEventView event = new ActivityEventView();
        event.setUserId(userId);
        event.setDealerId(dealerId);
        event.setEventType("fast-pass");
        event.setEventName("showroom-checkin");
        event.setDescription(String.format("Customer in showroom, %s scanned Fast Pass for %s", dealer.getName(), user.getFullName()));

        activityService.postActivityEvent(event);
        log.info("Triggering Fast Pass Event for user {} and dealer {}", userId, dealerId);
    }
}
