package com.carsaver.partner.service;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.model.TagView;
import com.carsaver.magellan.model.UserView;
import com.mixpanel.mixpanelapi.MessageBuilder;
import com.mixpanel.mixpanelapi.MixpanelAPI;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MixpanelClient {

    @Value("${mixpanel.project-token:null}")
    private String projectToken;

    private MessageBuilder messageBuilder;
    private MixpanelAPI mixpanel;

    @PostConstruct
    public void initialize() {
        this.messageBuilder = new MessageBuilder(projectToken);
        this.mixpanel = new MixpanelAPI();
    }

    public void track(String name) {
        track(name, (Map<String, Object>) null);
    }

    /**
     *
     * @param name - event name
     * @param properties - properties to send up with event
     */
    public void track(String name, Map<String, Object> properties) {
        if (projectToken == null) {
            return;
        }

        AuthUtils.getUserIdFromSecurityContext()
            .ifPresent(userId -> {
                trackWithUser(name, userId, properties);
            });
    }

    /**
     *
     * @param name - event name
     * @param keyValuePairs varargs list of {@link Pair} Pair objects. e.g. usage track("eventName", Pair.of("key1", "val1), Pair.of("key2", "val2") ...)
     */
    @SafeVarargs
    public final void track(String name, Pair<String, Object>... keyValuePairs) {
            Map<String, Object> properties = new HashMap<>();
            Arrays.stream(keyValuePairs).forEach(p -> {
                properties.put(p.key, p.value);
            });

            track(name, properties);
    }

    /**
     *
     * @param name - event name
     * @param keyValuePairs varargs list of {@link Pair} Pair objects. e.g. usage track("eventName", Pair.of("key1", "val1), Pair.of("key2", "val2") ...)
     */
    @SafeVarargs
    public final void trackWithUserAndKeyValuePairs(String name, String userId, Pair<String, Object>... keyValuePairs) {
        Map<String, Object> properties = new HashMap<>();
        Arrays.stream(keyValuePairs).forEach(p -> {
            properties.put(p.key, p.value);
        });
        //NOTE: even though trackWithUser has the @Async annotation it wont be invoked async because it's in the same class as this method
        trackWithUser(name, userId, properties);
    }

    @Async
    public void trackWithUser(String name, String userId) {
        trackWithUser(name, userId, null);
    }

    @Async
    public void trackWithUser(String name, String userId, Map<String, Object> properties) {
        if (projectToken == null) {
            return;
        }

        try {
            JSONObject event = messageBuilder.event(userId, name, Optional.ofNullable(properties).map(JSONObject::new).orElse(null));
            mixpanel.sendMessage(event);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Async
    public void updateUser(UserView user) {
        if (projectToken == null) {
            return;
        }

        try {
            JSONObject props = new JSONObject();
            props.put("tenant_id", user.getTenantId());
            props.put("$created", user.getCreatedDate().toOffsetDateTime().toString());
            props.put("$email", user.getEmail());
            props.put("$city", user.getCity());
            props.put("$first_name", user.getFirstName());
            props.put("$last_name", user.getLastName());
            props.put("$phone", user.getPhoneNumber());
            props.put("$region", user.getStateCode());
            if (user.getImageUrl() != null) {
                props.put("$avatar", user.getImageUrl());
            }

            Collection<TagView> userTags = user.getTags();
            if(CollectionUtils.isNotEmpty(userTags)) {
                List<String> tags = userTags.stream()
                    .map(TagView::getName)
                    .collect(Collectors.toList());
                props.put("tags", tags);
            }

            JSONObject event = messageBuilder.set(user.getId(), props);

            mixpanel.sendMessage(event);
        } catch (IOException | JSONException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * simple object which containing a key and value
     * @param <K> - key - typically a String but can be any object
     * @param <V> - value
     */
    @Getter
    @AllArgsConstructor(staticName = "of")
    public static class Pair<K, V> {
        private final K key;
        private final V value;
    }
}
