package com.carsaver.partner.filter;

import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.magellan.model.foundation.ProductView;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.model.DealerViewWithBoostCheck;
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation;
import com.carsaver.partner.model.FeatureSubscriptionRequest;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.model.ProgramModel;
import com.carsaver.partner.reporting.service.ProgramService;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.PortalAutoLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.hateoas.CollectionModel;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Order
@Slf4j
@Component
public class DealerUserAccessFilter extends OncePerRequestFilter {

    public static final String DEALER_LIST = "userDealerAccessList";
    public static final String PROGRAMS_LIST = "userProgramAccessList";
    public static final String PROGRAMS_DEALER_LIST = "userProgramDealerAccessList";
    public static final String DEALER_SURVEY_ENABLED = "dealerSurveyEnabled";
    public static final String DEALER_LIST_WITH_BOOST_CHECK = "userDealerAccessListWithBoostCheck";

    public static final Integer ECOMMERCE_PRODUCT_ID = 102;

    public static final AntPathRequestMatcher API_REQUEST_MATCHER = new AntPathRequestMatcher("/api/**");

    @Autowired
    private BasicUserAssociationClient basicUserAssociationClient;

    @Autowired
    private DealerClient dealerClient;

    @Autowired
    private PortalAutoLoginService portalAutoLoginService;

    @Autowired
    private ProgramService programService;

    @Autowired
    private ProgramSubscriptionClient programSubscriptionClient;

    @Autowired
    private ProgramClient programClient;

    @Value("${program.nissan-buy-at-home-id}")
    private String nissanBuyAtHomeProgramId;

    @Autowired
    private FeatureSubscriptionsClient featureSubscriptionsClient;

    @Value("${features-subscription.boost-features-feature-id}")
    private String boostFeaturesFeatureId;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        Optional<String> userId = AuthUtils.getUserIdFromSecurityContext();
        if (userId.isPresent() && !API_REQUEST_MATCHER.matches(request)) {
            HttpSession session = request.getSession(false);
            final boolean allowedUserTypes = SecurityUtils.isDealerUser() || SecurityUtils.isProgramUser();
            if (allowedUserTypes && !SecurityUtils.isAdminUser()) {
                if (session != null && session.getAttribute(DEALER_LIST) == null) {
                    List<DealerView> userDealerAccessList = getDealerAccessList(userId.get());
                    session.setAttribute(DEALER_LIST, userDealerAccessList);

                    List<DealerViewWithBoostCheck> enhancedDealerList = userDealerAccessList.stream()
                        .map(dealer -> {
                            boolean isBoostEnrolled = isDealerEnrolledInBoost(dealer.getId());
                            return DealerViewWithBoostCheck.from(dealer, isBoostEnrolled);
                        })
                        .collect(Collectors.toList());
                    session.setAttribute(DEALER_LIST_WITH_BOOST_CHECK, enhancedDealerList);

                    List<ProgramModel> userProgramAccessList = getProgramAccessList(userId.get());
                    session.setAttribute(PROGRAMS_LIST, userProgramAccessList);
                    // session attribute for dealer survey enabled - NE-473
                    session.setAttribute(DEALER_SURVEY_ENABLED, programService.doesBuyAtHomeProgramExist(userProgramAccessList));
                }
                //TODO - this is a temp way to give the admin user an accessible DEALER_LIST.
            } else if (SecurityUtils.isAdminUser()) {
                // Handle admin users - preserve existing session data or set new data based on URL params
                var parameterMap = request.getParameterMap();
                String dealerId = parameterMap.get("dealerIds") != null ? parameterMap.get("dealerIds")[0] : null;

                boolean dealerWasSet = false;
                if (dealerId != null && dealerIdNotAlreadyInSessionAccessList(session).test(dealerId)) {
                    try {
                        DealerView dealer = dealerClient.findById(dealerId);
                        if (dealer != null) {
                            session.setAttribute(DEALER_LIST, Arrays.asList(dealer));
                            // Also create enhanced dealer list for admin users
                            boolean isBoostEnrolled = isDealerEnrolledInBoost(dealer.getId());
                            List<DealerViewWithBoostCheck> enhancedList = Arrays.asList(
                                DealerViewWithBoostCheck.from(dealer, isBoostEnrolled)
                            );
                            session.setAttribute(DEALER_LIST_WITH_BOOST_CHECK, enhancedList);
                            dealerWasSet = true;
                        }
                    } catch (Exception e) {
                        log.warn("Error retrieving dealer {} for admin user: {}", dealerId, e.getMessage());
                    }
                }

                // For admin users, only set empty lists if no existing session data and no dealer was set
                if (!dealerWasSet && session.getAttribute(DEALER_LIST) == null) {
                    session.setAttribute(DEALER_LIST, Collections.emptyList());
                    session.setAttribute(DEALER_LIST_WITH_BOOST_CHECK, Collections.emptyList());
                }

                // Only set programs list if it doesn't exist
                if (session.getAttribute(PROGRAMS_LIST) == null) {
                    session.setAttribute(PROGRAMS_LIST, Collections.emptyList());
                }
            } else {
                // Non-admin, non-dealer, non-program users get empty lists
                session.setAttribute(DEALER_LIST, Collections.emptyList());
                session.setAttribute(DEALER_LIST_WITH_BOOST_CHECK, Collections.emptyList());
                session.setAttribute(PROGRAMS_LIST, Collections.emptyList());
                // session attribute for dealer survey enabled - NE-473
                session.setAttribute(DEALER_SURVEY_ENABLED, Collections.emptyList());
            }
        }

        filterChain.doFilter(request, response);
    }

    public List<DealerView> getDealerAccessList(String userId) {
        Objects.requireNonNull(userId);
        List<DealerView> userDealerAccessList = new ArrayList<>();

        if (SecurityUtils.isProgramUser()) {
            var dealerIds = programService.getDealerIdsFromProgramUserId(userId);
            dealerIds.forEach(dealerId -> {
                DealerView dealer = dealerClient.findById(dealerId);
                userDealerAccessList.add(dealer);
            });
        } else {
            CollectionModel<BasicDealerUserRoleAssociation> userPrivilegeResources = basicUserAssociationClient.findByUserId(userId);
            userPrivilegeResources.forEach(userPrivilege -> {
                DealerView dealer = dealerClient.findById(userPrivilege.getDealerId());
                userDealerAccessList.add(dealer);
            });
        }

        userDealerAccessList.sort(Comparator.comparing(DealerView::getName));
        return userDealerAccessList;
    }

    boolean isDealerEnrolledInBoost(String dealerId) {
        try {
            List<ProgramSubscriptionView> subscriptions = new ArrayList<>(programSubscriptionClient.findByDealer(dealerId).getContent());

            return subscriptions.stream()
                .filter(ProgramSubscriptionView::isActive)
                .anyMatch(subscription -> {
                    boolean isBuyAtHome = nissanBuyAtHomeProgramId.equals(subscription.getProgramId());
                    boolean isEcommerce = false;
                    Optional<ProgramView> programOpt = programClient.findById(subscription.getProgramId());
                    if (programOpt.isPresent()) {
                        ProgramView program = programOpt.get();
                        ProductView product = program.getProduct();
                        isEcommerce = product != null && ECOMMERCE_PRODUCT_ID.equals(product.getId());
                    }

                    boolean programFeatureBoostPlusActive = false;
                    if(isBuyAtHome) {
                        FeatureSubscriptionResponse response = featureSubscriptionsClient.getFeatureSubscription(
                            FeatureSubscriptionRequest.builder()
                                .dealerId(dealerId)
                                .featureRId(boostFeaturesFeatureId)
                                .entityId(subscription.getProgramId())
                                .build()
                        );
                        programFeatureBoostPlusActive = response != null && response.getActive();
                    }

                    return (!isBuyAtHome && isEcommerce) || (isBuyAtHome && programFeatureBoostPlusActive);
                });
        } catch (Exception e) {
            log.warn("Error checking Boost enrollment for dealer {}: {}", dealerId, e.getMessage());
            return false;
        }
    }

    private List<ProgramModel> getProgramAccessList(String userId) {
        if ( SecurityUtils.isProgramUser()) {
            return programService.getProgramsByProgramUserId(userId);
        }
        return programService.getPrograms(userId);
    }

    private Predicate<String> dealerIdNotAlreadyInSessionAccessList(HttpSession session) {
        return (dealerId) -> {
            List<DealerView> dealerAccessList = (List<DealerView>) session.getAttribute(DEALER_LIST);
            boolean dealerAccessListEmptyOrDifferentDealerId = CollectionUtils.isEmpty(dealerAccessList) ||
                dealerAccessList.stream().noneMatch(dealer -> dealerId.equalsIgnoreCase(dealer.getId()));

            return dealerAccessListEmptyOrDifferentDealerId;
        };
    }

}
