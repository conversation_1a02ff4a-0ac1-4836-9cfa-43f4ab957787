package com.carsaver.partner.filter;

import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.user.UserPreferences;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Optional;

@Order
@Slf4j
@Component
public class ThemeFilter extends OncePerRequestFilter {

    public static final String THEME_SESSION_NAME = "theme";

    @Autowired
    private UserClient userClient;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        HttpSession session = request.getSession(false);

        if (session != null && session.getAttribute(THEME_SESSION_NAME) == null) {
            Optional<String> userId = AuthUtils.getUserIdFromSecurityContext();

            if (userId.isPresent()) {

                UserView user = userClient.findById(userId.get());
                String theme = Optional.ofNullable(user.getUserPreferences())
                    .map(UserPreferences::getAtlasPreferences)
                    .map(UserPreferences.AtlasPreferences::getTheme)
                    .orElse("");

                session.setAttribute(THEME_SESSION_NAME, theme);
            }
        }

        filterChain.doFilter(request, response);
    }

}
