package com.carsaver.partner.filter;

import com.carsaver.partner.service.PortalAutoLoginService;
import com.carsaver.partner.service.sso.PortalSsoHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

/**
 * filter which makes an attempt to determine if user should be authorized via portal
 * if request meets criteria for being authorized by portal and the user is not signed will kick back to portal to get an auth token
 * this filter essentially forgoes having to add a Portal SSO button or similar to the login page.
 */
@Slf4j
@Component
public class PortalAutoLoginFilter extends OncePerRequestFilter {
    private static final AntPathRequestMatcher IGNORE_API_PATHS = new AntPathRequestMatcher(
        "/api/**");

    private static final AntPathRequestMatcher DEALER_PATH = new AntPathRequestMatcher(
        "/dealer/**");

    private static final AntPathRequestMatcher REPORTING_PATH = new AntPathRequestMatcher(
        "/reporting/**");

    @Autowired
    private PortalSsoHandler portalSsoHandler;

    @Autowired
    private PortalAutoLoginService portalAutoLoginService;

    @Override
    public void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        boolean ignorePath = IGNORE_API_PATHS.matches(request) || !DEALER_PATH.matches(request) || REPORTING_PATH.matches(request);
        log.debug("atlas-autologin: ignorePath={}", ignorePath);
        if(ignorePath) {
            filterChain.doFilter(request, response);
            return;
        }

        boolean notAPortalAutoLoginRequest = !PortalAutoLoginService.isPortalAutoLoginRequest(request);
        log.debug("atlas-autologin: notAPortalAutoLoginRequest={}", notAPortalAutoLoginRequest);
        if(notAPortalAutoLoginRequest) {
            filterChain.doFilter(request, response);
            return;
        }

        boolean portalUserNeedsAuthentication = !Optional.ofNullable(SecurityContextHolder.getContext())
            .map(SecurityContext::getAuthentication)
            .map(Authentication::isAuthenticated)
            .orElse(false);
        log.debug("atlas-autologin: notAPortalAutoLoginRequest={}", portalUserNeedsAuthentication);
        if(portalUserNeedsAuthentication) {
            /*
                redirect to portal:
                  if user was already logged will be redirected back to sso auth handler
                  else they will prompted to login to portal and then redirected to endpoint above
             */
            String redirect = portalSsoHandler.getLoginPage(request);
            response.sendRedirect(redirect);
            return;
        }

        filterChain.doFilter(request, response);
    }



}
