package com.carsaver.partner.filter;

import com.carsaver.magellan.auth.CarSaverJWTToken;
import com.carsaver.magellan.auth.CarSaverUserDetails;
import com.carsaver.magellan.auth.SessionUtils;
import com.carsaver.magellan.auth.TokenResponse;
import com.carsaver.magellan.auth.servlet.JwtTokenProvider;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.model.user.BasicDealerUserRoleAssociation;
import com.carsaver.partner.service.PortalAutoLoginService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.hateoas.CollectionModel;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Filter responsible to intercept the JWT in the HTTP session and attempt an authentication.
 * It delegates the authentication to the authentication manager
 * NOTE: this was originally copied from the Carsaver/Offers app and altered as needed
 * NOTE: - assumes some unauthenticated endpoint initially storing the token in the http session
 * which will then come back through this filter on the next redirect or http call and perform authentication
 * TODO - consider refactoring this filter as an AbstractAuthenticationFilter (like {@link org.springframework.security.authentication.UsernamePasswordAuthenticationToken})
 *        this would allow bypassing having to store the token in the session as this implementation would allow
 *        the filter to server as the 'endpoint' as well as handling all authentication success/failure/redirection needs
 *
 */
@Slf4j
public class AtlasTokenAuthenticationFilter extends GenericFilterBean {

    @Setter
    @Getter
    private AuthenticationManager authenticationManager;

    @Setter
    @Getter
    private AuthenticationEntryPoint entryPoint;

    @Setter
    private JwtTokenProvider jwtTokenProvider;

    @Setter
    private BasicUserAssociationClient basicUserAssociationClient;

    @Setter
    private PortalAutoLoginService portalAutoLoginService;
    /**
     * Perform filter check on this request - verify tokens exist and verify
     * the id token is valid
     */
    public void doFilter(final ServletRequest req, final ServletResponse res, final FilterChain chain) throws IOException, ServletException {
        final HttpServletRequest request = (HttpServletRequest) req;
        final HttpServletResponse response = (HttpServletResponse) res;
        if (request.getMethod().equals("OPTIONS")) {
            // CORS request
            chain.doFilter(request, response);
            return;
        }

        final TokenResponse tokenResponse = SessionUtils.getToken(request);
        boolean requestContainsAccessToken = tokenResponse != null && StringUtils.hasLength(tokenResponse.getAccessToken());
        log.debug("atlas-autologin: requestContainsAccessToken={}", requestContainsAccessToken);

        if (requestContainsAccessToken) {
            try {
                Jwt jwt = getDecoder().decode(tokenResponse.getAccessToken());
                handleLogin(tokenResponse, jwt, request);
            } catch (AuthenticationException failed) { //expired tokens are handled in JwtTokenFilter
                handleAuthenticationFailure(request, response, failed);
                return;
            }
        }
        chain.doFilter(request, response);
    }

    private void handleAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException failed) throws IOException, ServletException {
        log.error("atlas-autologin: handleAuthenticationFailure", failed);
        SecurityContextHolder.clearContext();
        entryPoint.commence(request, response, failed);
    }

    private void handleLogin(TokenResponse token, Jwt jwt, HttpServletRequest request) {
        List<String> authorities = (ArrayList<String>) jwt.getClaims().get("authorities");

        authorities = Optional.ofNullable(authorities).orElseGet(Collections::emptyList);

        Authentication authResult;
        if(PortalAutoLoginService.isPortalAutoLoginRequest(request)) {
            authResult = handlePortalAutoLogin(authorities, token);
        } else {
            authResult = handleDefaultLogin(authorities, token);
        }

        SecurityContextHolder.getContext().setAuthentication(authResult);
        SessionUtils.setTokenResponse(request, null); //this served its purpose now clear it out
    }

    /*
     get all the available permissions and translate to authorities
     the {@link com.carsaver.magellan.security.DealerPermissionEvaluator} effectively
     grants admins a given dealer permission if they have the corresponding authority
     */
    private Authentication handlePortalAutoLogin(List<String> authorities, TokenResponse tokenResponse) {

        //TODO - move this dedicated AuthenticationProvider
        if(!authorities.contains("ROLE_ADMIN")) {
            throw new ImpersonationAuthenticationException("does not have necessary authority to impersonate");
        }
        //start with clean slate of authorities
        List<SimpleGrantedAuthority> grantedAuthorities = new ArrayList<>();

        //add back admin
        grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));

        List<SimpleGrantedAuthority> dealerPermissionsAsAuthorities = portalAutoLoginService.getImpersonateDealerUserAuthorities();
        grantedAuthorities.addAll(dealerPermissionsAsAuthorities);

        CarSaverUserDetails carSaverUserDetails = new CarSaverUserDetails(tokenResponse.getId(), getPrivileges(tokenResponse), grantedAuthorities);

        final CarSaverJWTToken token = new CarSaverJWTToken(tokenResponse, grantedAuthorities);
        token.setPrincipal(carSaverUserDetails);

        return authenticationManager.authenticate(token);
    }

    private Authentication handleDefaultLogin(List<String> authorities, TokenResponse tokenResponse) {
        //TODO - move this dedicated AuthenticationProvider
        List<SimpleGrantedAuthority> grantedAuthorities = authorities.stream()
            .map(SimpleGrantedAuthority::new)
            .collect(Collectors.toList());

        CarSaverJWTToken token = new CarSaverJWTToken(tokenResponse, grantedAuthorities);
        CarSaverUserDetails carSaverUserDetails = new CarSaverUserDetails(tokenResponse.getId(), getPrivileges(tokenResponse), grantedAuthorities);
        token.setPrincipal(carSaverUserDetails);

        return authenticationManager.authenticate(token);
    }

    private Collection<BasicDealerUserRoleAssociation> getPrivileges(TokenResponse tokenResponse) {
        CollectionModel<BasicDealerUserRoleAssociation> userPrivileges = basicUserAssociationClient.findByUserId(tokenResponse.getId());
        return Optional.ofNullable(userPrivileges)
            .map(CollectionModel::getContent)
            .orElseGet(Collections::emptyList);
    }

    private NimbusJwtDecoder getDecoder() {
        return this.jwtTokenProvider.getDecoder();
    }

    public static class ImpersonationAuthenticationException extends AuthenticationException {

        public ImpersonationAuthenticationException(String msg) {
            super(msg);
        }
    }
}
