const path = require("path");
const { readdirSync } = require("fs");
const HtmlWebpackHarddiskPlugin = require("html-webpack-harddisk-plugin");
const CompressionPlugin = require("compression-webpack-plugin");
const BrotliPlugin = require("brotli-webpack-plugin");

const NODE_ENV = process.env.NODE_ENV;

const HMR_PORT = 7777;

const plugins = [new HtmlWebpackHarddiskPlugin()];

const runInParallel = !process.env.CIRCLECI;

if (NODE_ENV !== "local") {
    const BUILD_NUMBER = process.env.CIRCLE_BUILD_NUM;
    const BRANCH = process.env.CIRCLE_BRANCH;
    plugins.push(
        new CompressionPlugin({
            filename: "[path].gz[query]",
            algorithm: "gzip",
            test: /\.js$|\.css|\.html$/,
            threshold: 10240,
            minRatio: 0.7,
        })
    );
    plugins.push(
        new BrotliPlugin({
            filename: "[path].br[query]",
            test: /\.js$|\.css|\.html$/,
            threshold: 10240,
            minRatio: 0.7,
        })
    );
}

const modulesPath = path.join(__dirname, "src/main/frontend/entrypoints");
const entryPoints = {};
readdirSync(modulesPath).forEach((name) => {
    const entryPointName = name.replace(/\.js/, "");
    entryPoints[entryPointName] = {
        entry: `src/main/frontend/entrypoints/${name}`,
        template: "src/main/resources/template.ejs",
        filename: `../templates/${entryPointName}.html`,
        chunks: ["chunk-vendors", "chunk-common", entryPointName],
        alwaysWriteToDisk: true,
        devServer: NODE_ENV === "local",
        inject: false,
    };
});

module.exports = {
    outputDir: "target/classes/static",
    assetsDir: "dist",
    publicPath: NODE_ENV === "local" ? "http://localhost:" + HMR_PORT + "/" : "/",
    devServer: {
        contentBase: path.join(__dirname, "target/classes/static/dist"),
        port: HMR_PORT,
        headers: {
            "Access-Control-Allow-Origin": "*",
        },
        disableHostCheck: true,
        allowedHosts: ["localhost:3001"],
    },
    css: {
        sourceMap: NODE_ENV === "local",
        loaderOptions: {
            scss: {
                additionalData: `@import "~@/sass/install.scss";`,
            },
        },
    },
    configureWebpack: {
        plugins,
        devtool: NODE_ENV !== "local" ? "source-map" : false,
    },
    chainWebpack: (config) => {
        config.resolve.alias.set("@", path.resolve(__dirname, "src/main/frontend"));
        config.resolve.alias.set("Locales", path.resolve(__dirname, "src/main/frontend/locales"));
        config.resolve.alias.set("@sass", path.resolve(__dirname, "src/main/frontend/sass"));
        config.resolve.alias.set("Modules", path.resolve(__dirname, "src/main/frontend/modules"));
        config.resolve.alias.set("Util", path.resolve(__dirname, "src/main/frontend/util"));
        config.resolve.alias.set("Components", path.resolve(__dirname, "src/main/frontend/components"));
    },
    filenameHashing: true,
    pages: {
        ...entryPoints,
    },
    transpileDependencies: ["vuetify"],
    parallel: runInParallel,
};
