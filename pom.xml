<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.carsaver.boot</groupId>
        <artifactId>carsaver-boot-starter-parent</artifactId>
        <version>1.0.2.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.carsaver</groupId>
    <artifactId>atlas</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>atlas-app</name>
    <packaging>pom</packaging>

    <properties>
        <sonar.projectKey>CarSaver_atlas-app</sonar.projectKey>
        <sonar.organization>carsaver</sonar.organization>
        <sonar.host.url>https://sonarcloud.io</sonar.host.url>
        <sonar.sources>src/main/java,src/main/resources/templates,src/main/frontend</sonar.sources>
        <sonar.skip>false</sonar.skip>

        <java.version>11</java.version>
        <sonar.scanner.force-deprecated-java-version>true</sonar.scanner.force-deprecated-java-version>
        <carsaver-magellan.version>5.2.212</carsaver-magellan.version>
        <offerlogix.version>1.2.6</offerlogix.version>
        <carsaver-cloud-aws.version>2.0.38</carsaver-cloud-aws.version>
        <carsaver-utils.version>1.0.1.RELEASE</carsaver-utils.version>
        <carsaver-stereotype.version>1.2.11</carsaver-stereotype.version>
        <atc-client.version>4.1.65</atc-client.version>
        <google-api-services-analyticsreporting.version>v4-rev170-1.25.0</google-api-services-analyticsreporting.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <mockito.version>4.2.0</mockito.version>
        <carsaver-elasticsearch.version>3.1.26</carsaver-elasticsearch.version>
        <carsaver-auth.version>1.0.3-SNAPSHOT</carsaver-auth.version>
        <aws-sdk-v2.version>2.17.276</aws-sdk-v2.version>
        <testcontainers.version>1.20.6</testcontainers.version>
    </properties>
    <modules>
        <module>atlas-app-cdk</module>
        <module>atlas-app-spring</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>

                </executions>
            </plugin>
        </plugins>
    </build>
</project>
