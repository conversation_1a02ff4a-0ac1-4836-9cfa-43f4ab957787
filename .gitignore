target/
reports/
!.mvn/wrapper/maven-wrapper.jar
node_modules/
built/
npm-debug.log
node/
rebel.xml
hot
mix-manifest.json
/src/main/resources/static/images/vendor/
/src/main/resources/static/fonts/vendor/

### vscode ###
*.vscode
*.cursor

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
nbproject/private/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/
/src/main/resources/static/built/

### MacOS file system ###
*.DS_Store
cdk.out